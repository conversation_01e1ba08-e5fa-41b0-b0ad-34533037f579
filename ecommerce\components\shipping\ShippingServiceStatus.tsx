/**
 * Shipping Service Status Component
 * Displays current status of shipping services and provides health information
 */

import React, { useEffect, useState } from 'react';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import { Alert, AlertDescription } from '@/components/ui/alert';
import { 
  CheckCircle, 
  AlertTriangle, 
  XCircle, 
  RefreshCw, 
  Zap, 
  Truck,
  Activity,
  Clock
} from 'lucide-react';
import { cn } from '@/lib/utils';
import useApi from '@/hooks/useApi';
import { MAIN_URL, SHIPPING_STATUS, SHIPPING_HEALTH } from '@/constant/urls';
import type { ServiceStatus } from '@/types/shipping';

interface ShippingServiceStatusProps {
  className?: string;
  showDetails?: boolean;
  autoRefresh?: boolean;
  refreshInterval?: number;
}

export const ShippingServiceStatus: React.FC<ShippingServiceStatusProps> = ({
  className,
  showDetails = true,
  autoRefresh = false,
  refreshInterval = 30000 // 30 seconds
}) => {
  const [status, setStatus] = useState<ServiceStatus | null>(null);
  const [healthData, setHealthData] = useState<any>(null);
  const [lastChecked, setLastChecked] = useState<Date>(new Date());
  
  const { read: readStatus, loading: statusLoading } = useApi<ServiceStatus>(MAIN_URL);
  const { read: readHealth, loading: healthLoading } = useApi<any>(MAIN_URL);

  const fetchStatus = async () => {
    try {
      const [statusResult, healthResult] = await Promise.all([
        readStatus(SHIPPING_STATUS),
        readHealth(SHIPPING_HEALTH)
      ]);

      if (typeof statusResult !== 'string') {
        setStatus(statusResult);
      }

      if (typeof healthResult !== 'string') {
        setHealthData(healthResult);
      }

      setLastChecked(new Date());
    } catch (error) {
      console.error('Failed to fetch shipping service status:', error);
    }
  };

  useEffect(() => {
    fetchStatus();

    if (autoRefresh && refreshInterval > 0) {
      const interval = setInterval(fetchStatus, refreshInterval);
      return () => clearInterval(interval);
    }
  }, [autoRefresh, refreshInterval]);

  const getStatusColor = () => {
    if (!status) return 'gray';
    
    if (status.rapidshyp_available && status.rapidshyp_enabled) return 'green';
    if (status.fallback_available) return 'yellow';
    return 'red';
  };

  const getStatusIcon = () => {
    const color = getStatusColor();
    
    switch (color) {
      case 'green':
        return <CheckCircle className="h-4 w-4 text-green-500" />;
      case 'yellow':
        return <AlertTriangle className="h-4 w-4 text-yellow-500" />;
      case 'red':
        return <XCircle className="h-4 w-4 text-red-500" />;
      default:
        return <Activity className="h-4 w-4 text-gray-500" />;
    }
  };

  const getStatusMessage = () => {
    if (!status) return 'Checking service status...';
    
    if (status.rapidshyp_available && status.rapidshyp_enabled) {
      return 'All shipping services operational';
    }
    
    if (status.fallback_available) {
      return 'Standard shipping available (Live rates temporarily unavailable)';
    }
    
    return 'Shipping services experiencing issues';
  };

  const getHealthStatus = () => {
    if (!healthData) return null;
    
    const healthStatus = healthData.status || 'unknown';
    
    switch (healthStatus) {
      case 'healthy':
        return { color: 'green', label: 'Healthy', icon: CheckCircle };
      case 'degraded':
        return { color: 'yellow', label: 'Degraded', icon: AlertTriangle };
      case 'unhealthy':
        return { color: 'red', label: 'Unhealthy', icon: XCircle };
      default:
        return { color: 'gray', label: 'Unknown', icon: Activity };
    }
  };

  const formatTime = (date: Date) => {
    return new Intl.DateTimeFormat('en-IN', {
      hour: '2-digit',
      minute: '2-digit',
      second: '2-digit'
    }).format(date);
  };

  const loading = statusLoading || healthLoading;

  return (
    <Card className={cn("w-full", className)}>
      <CardHeader className="pb-3">
        <div className="flex items-center justify-between">
          <CardTitle className="text-base flex items-center">
            <Activity className="h-4 w-4 mr-2" />
            Shipping Service Status
          </CardTitle>
          
          <div className="flex items-center space-x-2">
            {getStatusIcon()}
            <Button
              onClick={fetchStatus}
              variant="ghost"
              size="sm"
              disabled={loading}
            >
              <RefreshCw className={cn("h-3 w-3", loading && "animate-spin")} />
            </Button>
          </div>
        </div>
      </CardHeader>

      <CardContent className="space-y-4">
        {/* Main Status */}
        <Alert className={cn(
          "border-l-4",
          getStatusColor() === 'green' && "border-l-green-500 bg-green-50",
          getStatusColor() === 'yellow' && "border-l-yellow-500 bg-yellow-50",
          getStatusColor() === 'red' && "border-l-red-500 bg-red-50"
        )}>
          <AlertDescription className="flex items-center justify-between">
            <span>{getStatusMessage()}</span>
            {status?.service_mode && (
              <Badge variant={status.service_mode === 'rapidshyp' ? 'default' : 'secondary'}>
                {status.service_mode === 'rapidshyp' ? 'Live Rates' : 'Standard'}
              </Badge>
            )}
          </AlertDescription>
        </Alert>

        {/* Detailed Status */}
        {showDetails && status && (
          <div className="grid grid-cols-2 gap-4 text-sm">
            <div className="space-y-2">
              <div className="flex items-center justify-between">
                <span className="text-gray-600">Rapidshyp Service:</span>
                <div className="flex items-center space-x-1">
                  {status.rapidshyp_enabled ? (
                    status.rapidshyp_available ? (
                      <CheckCircle className="h-3 w-3 text-green-500" />
                    ) : (
                      <XCircle className="h-3 w-3 text-red-500" />
                    )
                  ) : (
                    <XCircle className="h-3 w-3 text-gray-400" />
                  )}
                  <span className={cn(
                    status.rapidshyp_enabled ? 
                      (status.rapidshyp_available ? 'text-green-600' : 'text-red-600') : 
                      'text-gray-500'
                  )}>
                    {status.rapidshyp_enabled ? 
                      (status.rapidshyp_available ? 'Online' : 'Offline') : 
                      'Disabled'
                    }
                  </span>
                </div>
              </div>
              
              <div className="flex items-center justify-between">
                <span className="text-gray-600">API Configuration:</span>
                <div className="flex items-center space-x-1">
                  {status.rapidshyp_api_key_configured ? (
                    <CheckCircle className="h-3 w-3 text-green-500" />
                  ) : (
                    <XCircle className="h-3 w-3 text-red-500" />
                  )}
                  <span className={status.rapidshyp_api_key_configured ? 'text-green-600' : 'text-red-600'}>
                    {status.rapidshyp_api_key_configured ? 'Configured' : 'Missing'}
                  </span>
                </div>
              </div>
            </div>

            <div className="space-y-2">
              <div className="flex items-center justify-between">
                <span className="text-gray-600">Fallback Service:</span>
                <div className="flex items-center space-x-1">
                  {status.fallback_available ? (
                    <CheckCircle className="h-3 w-3 text-green-500" />
                  ) : (
                    <XCircle className="h-3 w-3 text-red-500" />
                  )}
                  <span className={status.fallback_available ? 'text-green-600' : 'text-red-600'}>
                    {status.fallback_available ? 'Available' : 'Unavailable'}
                  </span>
                </div>
              </div>
              
              <div className="flex items-center justify-between">
                <span className="text-gray-600">Standard Methods:</span>
                <span className="font-medium">
                  {status.existing_methods_count} available
                </span>
              </div>
            </div>
          </div>
        )}

        {/* Health Status */}
        {healthData && getHealthStatus() && (
          <div className="pt-2 border-t">
            <div className="flex items-center justify-between text-sm">
              <div className="flex items-center space-x-2">
                <span className="text-gray-600">System Health:</span>
                <div className="flex items-center space-x-1">
                  {React.createElement(getHealthStatus()!.icon, { 
                    className: `h-3 w-3 text-${getHealthStatus()!.color}-500` 
                  })}
                  <span className={`text-${getHealthStatus()!.color}-600`}>
                    {getHealthStatus()!.label}
                  </span>
                </div>
              </div>
              
              <div className="flex items-center space-x-1 text-gray-500">
                <Clock className="h-3 w-3" />
                <span>Last checked: {formatTime(lastChecked)}</span>
              </div>
            </div>
          </div>
        )}

        {/* Auto-refresh indicator */}
        {autoRefresh && (
          <div className="text-xs text-gray-500 text-center pt-2 border-t">
            Auto-refreshing every {Math.round(refreshInterval / 1000)} seconds
          </div>
        )}
      </CardContent>
    </Card>
  );
};

export default ShippingServiceStatus;
