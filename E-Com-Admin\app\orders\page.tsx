"use client"
import { useState, useEffect } from "react";
import { <PERSON>, CardContent, Card<PERSON>eader, CardTitle } from "@/components/ui/card";
import { OrdersTable } from "@/components/orders/OrdersTable";
import { OrdersFilter } from "@/components/orders/OrdersFilter";
import { OrderQuickView } from "@/components/orders/OrderQuickView";
import { useToast } from "@/components/ui/use-toast";
import { useOrders, Order } from "@/hooks/useOrders";
import { useSession } from "next-auth/react";
import { useRouter } from "next/navigation";
import { Loader2 } from "lucide-react";

const Orders = () => {
  const { status } = useSession();
  const router = useRouter();
  const [selectedOrder, setSelectedOrder] = useState<Order | null>(null);
  const [filters, setFilters] = useState({
    status: '',
    search: '',
    date_from: '',
    date_to: '',
    page: 1,
    page_size: 20
  });

  const {
    orders,
    loading,
    error,
    pagination,
    fetchOrders,
    updateOrderStatus,
    refundOrder
  } = useOrders();

  const { toast } = useToast();

  // Redirect to login if not authenticated
  useEffect(() => {
    if (status === 'loading') return;

    if (status === 'unauthenticated') {
      router.push('/login');
    }
  }, [status, router]);

  const handleStatusUpdate = async (orderId: string, newStatus: string) => {
    const success = await updateOrderStatus(orderId, newStatus);

    if (success) {
      toast({
        title: "Order status updated",
        description: `Order ${orderId} status changed to ${newStatus}`,
      });
    } else {
      toast({
        title: "Error",
        description: "Failed to update order status",
        variant: "destructive",
      });
    }
  };

  const handleRefund = async (orderId: string) => {
    const success = await refundOrder(orderId);

    if (success) {
      toast({
        title: "Refund initiated",
        description: `Refund process started for order ${orderId}`,
      });
    } else {
      toast({
        title: "Error",
        description: "Failed to initiate refund",
        variant: "destructive",
      });
    }
  };

  const handleFilterChange = (newFilters: Partial<typeof filters>) => {
    setFilters(prev => ({ ...prev, ...newFilters, page: 1 }));
    fetchOrders({ ...filters, ...newFilters, page: 1 });
  };

  const handlePageChange = (page: number) => {
    setFilters(prev => ({ ...prev, page }));
    fetchOrders({ ...filters, page });
  };

  // Show loading screen while checking authentication
  if (status === 'loading') {
    return (
      <div className="min-h-screen bg-gradient-to-br from-gray-50 to-white flex items-center justify-center">
        <div className="text-center">
          <Loader2 className="h-8 w-8 animate-spin mx-auto mb-4 text-blue-600" />
          <p className="text-gray-600">Loading...</p>
        </div>
      </div>
    );
  }

  // Don't render if not authenticated (will redirect)
  if (status !== 'authenticated') {
    return null;
  }

  return (
    <div className="space-y-6 animate-fadeIn">
      <div className="flex justify-between items-center">
        <h1 className="text-3xl font-bold">Orders</h1>
        <div className="text-sm text-gray-500">
          {pagination.count} total orders
        </div>
      </div>

      <Card>
        <CardHeader>
          <CardTitle>Filters</CardTitle>
        </CardHeader>
        <CardContent>
          <OrdersFilter
            onFilterChange={handleFilterChange}
            loading={loading}
          />
        </CardContent>
      </Card>

      <Card>
        <CardContent className="pt-6">
          {loading ? (
            <div className="flex items-center justify-center py-12">
              <Loader2 className="h-8 w-8 animate-spin text-blue-600" />
              <span className="ml-2 text-gray-600">Loading orders...</span>
            </div>
          ) : error ? (
            <div className="text-center py-12">
              <p className="text-red-600 mb-4">{error}</p>
              <button
                onClick={() => fetchOrders(filters)}
                className="text-blue-600 hover:text-blue-700 underline"
              >
                Try again
              </button>
            </div>
          ) : (
            <OrdersTable
              orders={orders}
              onQuickView={setSelectedOrder}
              onStatusUpdate={handleStatusUpdate}
              onRefund={handleRefund}
              pagination={pagination}
              onPageChange={handlePageChange}
            />
          )}
        </CardContent>
      </Card>

      <OrderQuickView
        order={selectedOrder}
        onClose={() => setSelectedOrder(null)}
        onStatusUpdate={handleStatusUpdate}
        onRefund={handleRefund}
      />
    </div>
  );
};

export default Orders;