"use client"
import { useState } from "react";
import { But<PERSON> } from "@/components/ui/button";
import { PromotionDialog } from "@/components/promotions/PromotionDialog";
import { PromotionsList } from "@/components/promotions/PromotionsList";
import { Plus } from "lucide-react";

 const Promotions = () => {
  const [open, setOpen] = useState(false);

  return (
    <div className="p-6 space-y-6">
      <div className="flex justify-between items-center">
        <div>
          <h1 className="text-3xl font-bold">Promotions & Discounts</h1>
          <p className="text-muted-foreground">Manage your store promotions and discounts</p>
        </div>
        <Button onClick={() => setOpen(true)}>
          <Plus className="mr-2 h-4 w-4" />
          New Promotion
        </Button>
      </div>

      <PromotionsList />
      <PromotionDialog open={open} onOpenChange={setOpen} />
    </div>
  );
};
export default Promotions;