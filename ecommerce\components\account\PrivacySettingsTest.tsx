'use client';

import React from 'react';
import { Shield } from 'lucide-react';

export const PrivacySettingsTest: React.FC = () => {
  console.log('PrivacySettingsTest component rendered');
  
  return (
    <div className="space-y-8">
      <div className="flex items-center gap-3">
        <Shield className="h-6 w-6 text-blue-600" />
        <h2 className="text-2xl font-bold text-gray-900">Privacy Settings Test</h2>
      </div>

      <div className="bg-green-50 border border-green-200 rounded-lg p-4">
        <h3 className="font-medium text-green-800 mb-1">Test Component Working!</h3>
        <p className="text-sm text-green-700">
          This is a simple test component to verify that the privacy tab is working correctly.
        </p>
      </div>

      <div className="bg-blue-50 border border-blue-200 rounded-lg p-4">
        <h3 className="font-medium text-blue-800 mb-1">Privacy Features</h3>
        <ul className="text-sm text-blue-700 space-y-1">
          <li>• Consent Management</li>
          <li>• Data Export</li>
          <li>• Data Deletion</li>
          <li>• Privacy Settings</li>
        </ul>
      </div>
    </div>
  );
};

export default PrivacySettingsTest;
