"use client";

import React from "react";
import { motion } from "framer-motion";
import Link from "next/link";
import { ChevronRight } from "lucide-react";
import { usePathname } from "next/navigation";
import { Skeleton } from "@/components/ui/skeleton";

interface Category {
  id: number;
  name: string;
  slug: string;
  image?: string;
  image_url?: string;
  product_count?: number;
  total_product_count?: number;
  subcategories?: any[];
}

interface ProductCategoriesProps {
  categories: Category[];
  title?: string;
  subtitle?: string;
  accentColor?: "primary" | "secondary" | "tertiary";
  variant?: "navigation" | "section";
  showTitle?: boolean;
  showViewAll?: boolean;
  maxCategories?: number;
}

const ProductCategories: React.FC<ProductCategoriesProps> = ({
  categories,
  title = "Shop by Category",
  subtitle,
  accentColor = "primary",
  variant = "section",
  showTitle = true,
  showViewAll = true,
  maxCategories = 12,
}) => {
  const pathName = usePathname();
  const [isLoading, setIsLoading] = React.useState(true);

  React.useEffect(() => {
    // Set loading to false when categories are available
    if (categories && Array.isArray(categories)) {
      setIsLoading(false);
    }
  }, [categories]);
  // Default categories if none are provided or if the array is empty
  const defaultCategories: Category[] = [
    {
      id: 1,
      name: "Smart Locks",
      slug: "smart-locks",
      image_url: "https://placehold.co/400x400/2ECC71/FFFFFF?text=Smart+Locks"
    },
    {
      id: 2,
      name: "Security Cameras",
      slug: "security-cameras",
      image_url: "https://placehold.co/400x400/3498DB/FFFFFF?text=Security+Cameras"
    },
    {
      id: 3,
      name: "Home Automation",
      slug: "home-automation",
      image_url: "https://placehold.co/400x400/9B59B6/FFFFFF?text=Home+Automation"
    },
    {
      id: 4,
      name: "Lighting",
      slug: "lighting",
      image_url: "https://placehold.co/400x400/F1C40F/FFFFFF?text=Lighting"
    },
    {
      id: 5,
      name: "Sensors",
      slug: "sensors",
      image_url: "https://placehold.co/400x400/E74C3C/FFFFFF?text=Sensors"
    },
    {
      id: 6,
      name: "Alarms",
      slug: "alarms",
      image_url: "https://placehold.co/400x400/1ABC9C/FFFFFF?text=Alarms"
    }
  ];

  // Show skeleton loader when categories are loading
  if (isLoading && variant === "navigation") {
    return (
      <nav className="w-full bg-white/95 backdrop-blur-sm border-b border-gray-100 shadow-sm">
        <div className="container mx-auto px-3 sm:px-4 lg:px-6 py-3 sm:py-4">
          {/* Mobile skeleton */}
          <div className="block md:hidden">
            <div className="flex gap-4 overflow-x-auto scrollbar-hide pb-2 -mx-3 px-3">
              {Array.from({ length: 8 }).map((_, index) => (
                <div key={index} className="flex-shrink-0 flex flex-col items-center">
                  <div className="w-16 h-16 rounded-2xl bg-gray-200 animate-pulse mb-2"></div>
                  <Skeleton className="w-12 h-3" />
                </div>
              ))}
            </div>
          </div>

          {/* Desktop skeleton - Compact layout */}
          <div className="hidden md:block space-y-3">
            {/* Main skeleton row */}
            <div className="grid grid-cols-8 lg:grid-cols-10 xl:grid-cols-12 gap-3 lg:gap-4">
              {Array.from({ length: 12 }).map((_, index) => (
                <div key={index} className="flex flex-col items-center fade-in-professional" style={{ animationDelay: `${index * 0.05}s` }}>
                  <div className="w-16 h-16 lg:w-18 lg:h-18 xl:w-20 xl:h-20 rounded-2xl skeleton-professional mb-2 border border-gray-100/80 shadow-md"></div>
                  <div className="w-12 lg:w-14 xl:w-16 h-3 rounded skeleton-professional"></div>
                </div>
              ))}
            </div>

            {/* Additional skeleton row */}
            <div className="relative">
              <div className="flex items-center gap-2 mb-2">
                <div className="w-20 h-3 rounded skeleton-professional opacity-60"></div>
                <div className="flex-1 h-px bg-gray-200"></div>
              </div>
              <div className="flex gap-2 overflow-x-auto scrollbar-hide pb-1 -mx-1 px-1">
                {Array.from({ length: 8 }).map((_, index) => (
                  <div key={index} className="flex-shrink-0 flex flex-col items-center">
                    <div className="w-12 h-12 lg:w-14 lg:h-14 rounded-xl skeleton-professional mb-1 border border-gray-100/60 shadow-sm"></div>
                    <div className="w-8 lg:w-10 h-2 rounded skeleton-professional"></div>
                  </div>
                ))}
              </div>
            </div>
          </div>
        </div>
      </nav>
    );
  }

  // Don't render navigation if no categories
  if (!categories || !Array.isArray(categories) || categories.length === 0) {
    if (variant === "navigation") {
      return null;
    }
  }

  // Use provided categories if available, otherwise use default categories
  // Make sure we have a valid array to work with
  const effectiveCategories = Array.isArray(categories) && categories.length > 0 ? categories : defaultCategories;

  // Process categories to ensure proper image handling
  const categoriesWithImages = effectiveCategories.map((category) => {
    // Use image_url from backend API if available, otherwise fallback to image field or generate placeholder
    let imageUrl = category.image_url || category.image;

    // If no image is available from the backend, generate a colored placeholder immediately
    if (!imageUrl) {
      const colors = [
        '2ECC71', '3498DB', '9B59B6', 'F1C40F', 'E74C3C', '1ABC9C',
        'E67E22', '34495E', '95A5A6', 'F39C12', 'D35400', '8E44AD'
      ];
      const colorIndex = category.id % colors.length;
      const color = colors[colorIndex];
      const categoryText = encodeURIComponent(category.name);
      imageUrl = `https://placehold.co/400x400/${color}/FFFFFF?text=${categoryText}`;
    }

    return {
      ...category,
      image_url: imageUrl,
      // Add a fallback image path for onError handler
      fallbackImage: `/assets/products/product-placeholder.svg`
    };
  });

  // Determine accent color classes
  const accentClasses = {
    primary: {
      bg: "bg-theme-accent-primary/20",
      text: "text-theme-accent-primary",
      line: "bg-theme-accent-primary",
      gradient: "from-theme-accent-primary/5",
      activeBg: "bg-theme-accent-primary",
      activeText: "text-white",
      hoverBg: "hover:bg-theme-accent-primary/10",
    },
    secondary: {
      bg: "bg-theme-accent-secondary/30",
      text: "text-theme-accent-secondary",
      line: "bg-theme-accent-secondary",
      gradient: "from-theme-accent-secondary/5",
      activeBg: "bg-theme-accent-secondary",
      activeText: "text-white",
      hoverBg: "hover:bg-theme-accent-secondary/10",
    },
    tertiary: {
      bg: "bg-theme-accent-primary/30",
      text: "text-theme-accent-primary",
      line: "bg-theme-accent-primary",
      gradient: "from-theme-accent-primary/10",
      activeBg: "bg-theme-accent-primary",
      activeText: "text-white",
      hoverBg: "hover:bg-theme-accent-primary/10",
    },
  };

  // Animation variants
  const containerVariants = {
    hidden: { opacity: 0 },
    show: {
      opacity: 1,
      transition: {
        staggerChildren: 0.1,
      },
    },
  };

  const itemVariants = {
    hidden: { opacity: 0, y: 20 },
    show: { opacity: 1, y: 0, transition: { duration: 0.5 } },
  };



  // Navigation variant - Professional Amazon/Flipkart style layout
  if (variant === "navigation") {
    return (
      <nav className="w-full bg-white/98 backdrop-blur-md border-b border-gray-200/60 shadow-sm relative">
        {/* Subtle top accent line */}
        <div className="absolute top-0 left-0 right-0 h-0.5 bg-gradient-to-r from-theme-accent-primary via-theme-accent-secondary to-theme-accent-primary opacity-60"></div>

        <div className="container mx-auto px-3 sm:px-4 lg:px-6 xl:px-8 py-3 sm:py-4 lg:py-5">
          {/* Mobile: Horizontal scrollable layout */}
          <div className="block md:hidden">
            <div className="flex gap-4 overflow-x-auto scrollbar-hide pb-2 -mx-3 px-3">
              {categoriesWithImages.map((category, index) => (
                <div key={category.id || `category-${index}`} className="flex-shrink-0 group">
                  <Link href={`/products/categories/${category.slug}?callbackUrl=%2F${pathName}`}>
                    <div className="flex flex-col items-center transition-all duration-300 group-active:scale-95">
                      {/* Category image - mobile optimized */}
                      <div className="relative overflow-hidden rounded-2xl w-16 h-16 bg-gradient-to-br from-gray-50 to-gray-100 shadow-lg transition-all duration-300 group-hover:shadow-xl mb-2 border border-gray-200/50">
                        <img
                          src={category.image_url}
                          alt={category.name}
                          className="absolute inset-0 w-full h-full object-cover transition-transform duration-500 group-hover:scale-110"
                          onError={(e) => {
                            const imgElement = e.target as HTMLImageElement;
                            if (imgElement.src.includes('placehold.co')) {
                              return;
                            }
                            const colors = [
                              '2ECC71', '3498DB', '9B59B6', 'F1C40F', 'E74C3C', '1ABC9C',
                              'E67E22', '34495E', '95A5A6', 'F39C12', 'D35400', '8E44AD'
                            ];
                            const colorIndex = category.id % colors.length;
                            const color = colors[colorIndex];
                            const categoryText = encodeURIComponent(category.name);
                            imgElement.src = `https://placehold.co/400x400/${color}/FFFFFF?text=${categoryText}`;
                            imgElement.onerror = null;
                          }}
                        />
                        {/* Subtle gradient overlay */}
                        <div className="absolute inset-0 bg-gradient-to-t from-black/10 to-transparent opacity-0 group-hover:opacity-100 transition-opacity duration-300"></div>
                      </div>

                      {/* Category name - mobile optimized */}
                      <span className="text-xs font-medium text-gray-700 text-center line-clamp-2 max-w-[4.5rem] leading-tight">
                        {category.name}
                      </span>
                    </div>
                  </Link>
                </div>
              ))}
            </div>
          </div>

          {/* Desktop/Tablet: Compact multi-row layout */}
          <div className="hidden md:block space-y-3">
            {/* Main categories - First row (most important) */}
            <div className="grid grid-cols-8 lg:grid-cols-10 xl:grid-cols-12 gap-3 lg:gap-4">
              {categoriesWithImages.slice(0, 12).map((category, index) => (
                <div key={category.id || `category-${index}`} className="group fade-in-professional" style={{ animationDelay: `${index * 0.05}s` }}>
                  <Link href={`/products/categories/${category.slug}?callbackUrl=%2F${pathName}`}>
                    <div className="flex flex-col items-center transition-all duration-300 group-hover:-translate-y-1">
                      {/* Compact category card */}
                      <div className="relative overflow-hidden rounded-2xl w-16 h-16 lg:w-18 lg:h-18 xl:w-20 xl:h-20 bg-white shadow-md group-hover:shadow-lg mb-2 border border-gray-100/80 group-hover:border-theme-accent-primary/30 transition-all duration-300">
                        {/* Category image */}
                        <div className="absolute inset-1 rounded-xl overflow-hidden">
                          <img
                            src={category.image_url}
                            alt={category.name}
                            className="w-full h-full object-cover transition-transform duration-300 group-hover:scale-110"
                            onError={(e) => {
                              const imgElement = e.target as HTMLImageElement;
                              if (imgElement.src.includes('placehold.co')) {
                                return;
                              }
                              const colors = [
                                '2ECC71', '3498DB', '9B59B6', 'F1C40F', 'E74C3C', '1ABC9C',
                                'E67E22', '34495E', '95A5A6', 'F39C12', 'D35400', '8E44AD'
                              ];
                              const colorIndex = category.id % colors.length;
                              const color = colors[colorIndex];
                              const categoryText = encodeURIComponent(category.name);
                              imgElement.src = `https://placehold.co/400x400/${color}/FFFFFF?text=${categoryText}`;
                              imgElement.onerror = null;
                            }}
                          />
                        </div>
                        {/* Hover overlay */}
                        <div className="absolute inset-0 bg-gradient-to-t from-theme-accent-primary/20 via-transparent to-theme-accent-primary/10 opacity-0 group-hover:opacity-100 transition-opacity duration-300 rounded-2xl"></div>
                      </div>

                      {/* Compact typography */}
                      <span className="text-xs font-medium text-gray-700 group-hover:text-theme-accent-primary transition-colors duration-300 text-center line-clamp-2 max-w-[4rem] lg:max-w-[4.5rem] xl:max-w-[5rem] leading-tight">
                        {category.name}
                      </span>
                    </div>
                  </Link>
                </div>
              ))}
            </div>

            {/* Additional categories - Horizontal scroll */}
            {categoriesWithImages.length > 12 && (
              <div className="relative">
                <div className="flex items-center gap-2 mb-2">
                  <span className="text-xs font-medium text-gray-500 uppercase tracking-wide">More Categories</span>
                  <div className="flex-1 h-px bg-gradient-to-r from-gray-200 to-transparent"></div>
                </div>
                <div className="flex gap-2 overflow-x-auto scrollbar-hide pb-1 -mx-1 px-1">
                  {categoriesWithImages.slice(12).map((category, index) => (
                    <div key={category.id || `category-more-${index}`} className="flex-shrink-0 group">
                      <Link href={`/products/categories/${category.slug}?callbackUrl=%2F${pathName}`}>
                        <div className="flex flex-col items-center transition-all duration-300 group-hover:-translate-y-0.5">
                          <div className="relative overflow-hidden rounded-xl w-12 h-12 lg:w-14 lg:h-14 bg-white shadow-sm group-hover:shadow-md mb-1 border border-gray-100/60 group-hover:border-theme-accent-primary/40 transition-all duration-300">
                            <div className="absolute inset-0.5 rounded-lg overflow-hidden">
                              <img
                                src={category.image_url}
                                alt={category.name}
                                className="w-full h-full object-cover transition-transform duration-300 group-hover:scale-110"
                                onError={(e) => {
                                  const imgElement = e.target as HTMLImageElement;
                                  if (imgElement.src.includes('placehold.co')) {
                                    return;
                                  }
                                  const colors = [
                                    '2ECC71', '3498DB', '9B59B6', 'F1C40F', 'E74C3C', '1ABC9C',
                                    'E67E22', '34495E', '95A5A6', 'F39C12', 'D35400', '8E44AD'
                                  ];
                                  const colorIndex = category.id % colors.length;
                                  const color = colors[colorIndex];
                                  const categoryText = encodeURIComponent(category.name);
                                  imgElement.src = `https://placehold.co/400x400/${color}/FFFFFF?text=${categoryText}`;
                                  imgElement.onerror = null;
                                }}
                              />
                            </div>
                            <div className="absolute inset-0 bg-theme-accent-primary/15 opacity-0 group-hover:opacity-100 transition-opacity duration-300 rounded-xl"></div>
                          </div>
                          <span className="text-xs font-medium text-gray-600 group-hover:text-theme-accent-primary transition-colors duration-300 text-center line-clamp-2 max-w-[3rem] lg:max-w-[3.5rem] leading-tight">
                            {category.name}
                          </span>
                        </div>
                      </Link>
                    </div>
                  ))}
                </div>
              </div>
            )}
          </div>
        </div>
      </nav>
    );
  }

  // Section variant - full layout with background and decorations
  return (
    <section className="py-12 sm:py-16 md:py-20 relative overflow-hidden w-full">
      {/* Background gradient */}
      <div className={`absolute inset-0 bg-gradient-to-b ${accentClasses[accentColor].gradient} to-theme-homepage z-0 opacity-70`}></div>

      {/* Decorative elements */}
      <div className="absolute top-1/3 right-[15%] w-32 h-32 rounded-full bg-theme-accent-secondary/10 blur-xl opacity-50"></div>
      <div className="absolute bottom-1/3 left-[10%] w-24 h-24 rounded-full bg-theme-accent-primary/10 blur-xl opacity-50"></div>

      <div className="container mx-auto px-4 sm:px-6 lg:px-8 relative z-10 w-full max-w-full 2xl:max-w-[1536px]">
        {/* Section header */}
        {showTitle && (
          <div className="flex flex-col items-center mb-8 sm:mb-12">
            <h2 className="text-2xl sm:text-3xl md:text-4xl font-bold text-theme-text-primary mb-3 relative">
              <span className="relative z-10">{title}</span>
              <span className={`absolute -bottom-1 left-0 right-0 h-3 ${accentClasses[accentColor].bg} transform -rotate-1 z-0`}></span>
            </h2>
            {subtitle && <p className="text-theme-text-primary/70 text-center max-w-2xl mb-4">{subtitle}</p>}
            <div className={`w-16 sm:w-24 h-1 ${accentClasses[accentColor].line} rounded-full mt-1`}></div>
          </div>
        )}

        {/* Categories grid */}
        <motion.div
          variants={containerVariants}
          initial="hidden"
          whileInView="show"
          viewport={{ once: true, margin: "-100px" }}
          className="grid grid-cols-2 sm:grid-cols-3 md:grid-cols-4 lg:grid-cols-6 gap-4 sm:gap-6"
        >
          {/* Display categories dynamically */}
          {categoriesWithImages.slice(0, maxCategories).map((category, index) => (
            <motion.div
              key={category.id || `category-${index}`}
              variants={itemVariants}
              className="group"
            >
              <Link href={`/products/categories/${category.slug}`}>
                <div className="relative overflow-hidden rounded-xl aspect-square bg-gray-100 shadow-md transition-all duration-300 group-hover:shadow-lg">
                  {/* Category image */}
                  <div className="absolute inset-0 bg-gradient-to-b from-transparent to-black/50 opacity-70 z-10"></div>
                  <img
                    src={category.image_url}
                    alt={category.name}
                    className="absolute inset-0 w-full h-full object-cover transition-transform duration-500 group-hover:scale-110"
                    onError={(e) => {
                      // Fallback to a colored placeholder if the backend image fails
                      const imgElement = e.target as HTMLImageElement;

                      // Prevent infinite error loops
                      if (imgElement.src.includes('placehold.co')) {
                        return;
                      }

                      // Generate a colored placeholder based on category name
                      const colors = [
                        '2ECC71', '3498DB', '9B59B6', 'F1C40F', 'E74C3C', '1ABC9C',
                        'E67E22', '34495E', '95A5A6', 'F39C12', 'D35400', '8E44AD'
                      ];
                      const colorIndex = category.id % colors.length;
                      const color = colors[colorIndex];
                      const categoryText = encodeURIComponent(category.name);

                      imgElement.src = `https://placehold.co/400x400/${color}/FFFFFF?text=${categoryText}`;
                      imgElement.onerror = null; // Prevent further error handling
                    }}
                  />

                  {/* Category name */}
                  <div className="absolute inset-x-0 bottom-0 p-3 z-20">
                    <h3 className="text-white font-medium text-sm sm:text-base text-shadow">{category.name}</h3>
                  </div>

                  {/* Hover overlay */}
                  <div className="absolute inset-0 bg-theme-accent-primary/20 opacity-0 group-hover:opacity-100 transition-opacity duration-300 z-10"></div>
                </div>
              </Link>
            </motion.div>
          ))}
        </motion.div>

        {/* View all link */}
        {showViewAll && (
          <div className="flex justify-center mt-8">
            <Link
              href="/shop"
              className={`flex items-center ${accentClasses[accentColor].text} hover:text-theme-accent-hover group transition-all duration-300`}
            >
              <span>View All Categories</span>
              <ChevronRight className="h-4 w-4 ml-1 group-hover:translate-x-1 transition-transform duration-300" />
            </Link>
          </div>
        )}
      </div>
    </section>
  );
};

export default ProductCategories;
