"use client";
import React, { useEffect } from "react";
import { UserRoundPen, AtSign, KeyRound, Mail } from "lucide-react";
import useApi from "@/hooks/useApi";
import { MAIN_URL, USER_SIGNUP } from "@/constant/urls";
import { signIn, useSession } from "next-auth/react";
import { Button } from "@/components/ui/button";
import { Formik, Form, Field, ErrorMessage, FormikHelpers } from "formik";
import * as Yup from "yup";
import { useToast } from "@/hooks/use-toast";
import Link from "next/link";
import Image from "next/image";

// Define TypeScript types for form values and validation schema
interface SignupFormValues {
  name: string;
  email: string;
  password: string;
}

// Define Yup validation schema with TypeScript support
const validationSchema: Yup.Schema<SignupFormValues> = Yup.object({
  name: Yup.string().required("Name is required"),
  email: Yup.string()
    .email("Enter a valid email")
    .required("Email is required"),
  password: Yup.string()
    .min(8, "Password must be at least 8 characters long")
    .required("Password is required"),
});

const Signup: React.FC = () => {
  const { status } = useSession();
  const { create, error, data } = useApi(MAIN_URL);
  const { toast } = useToast();

  // useEffect(() => {
  //   if (status === "authenticated") {
  //     router.push("/chat");
  //   }
  // }, [status, router]);

  useEffect(() => {
    if (error) {
      toast({
        title: "Problem signing up",
        description: error,
      });
    }
    if (data) {
      signIn("credentials", {
        ...data,
        callbackUrl: "/",
      });
    }
  }, [error, toast, data]);

  const handleSubmit = (
    values: SignupFormValues,
    { setSubmitting, resetForm }: FormikHelpers<SignupFormValues>
  ) => {
    setSubmitting(true);
    try {
      create(USER_SIGNUP, values);

      resetForm();
    } finally {
      setSubmitting(false);
    }
  };

  if (status === "loading") {
    return (
      <div className="flex w-full h-screen justify-center items-center">
        Loading...
      </div>
    );
  }

  return (
    <div className="flex justify-center items-center h-screen w-full">
      <div className="flex flex-col w-4/5 md:w-[30rem] mx-auto p-4 md:p-6 bg-[#ffffff] rounded-2xl shadow-xl">
        <div className="flex flex-row gap-3 pb-4">
          <div>
            <Image src="/favicon.svg" alt="Logo" width="50" />
          </div>
          <h1 className="text-3xl font-bold text-[#4B5563] my-auto">
            WeSolves
          </h1>
        </div>
        <div className="text-sm font-light text-[#6B7280] pb-8 ">
          Sign up for an account on Wesolves.
        </div>

        <Formik<SignupFormValues>
          initialValues={{ name: "", email: "", password: "" }}
          validationSchema={validationSchema}
          onSubmit={handleSubmit}
        >
          {({ isSubmitting }: { isSubmitting: boolean }) => (
            <Form className="flex flex-col">
              <div className="pb-2">
                <label
                  htmlFor="name"
                  className="block mb-2 text-sm font-medium text-[#111827]"
                >
                  Name
                </label>
                <div className="relative text-gray-400">
                  <span className="absolute inset-y-0 left-0 flex items-center p-1 pl-3">
                    <UserRoundPen />
                  </span>
                  <Field
                    type="text"
                    name="name"
                    className="pl-12 mb-2 bg-gray-50 text-gray-600 border border-gray-300 sm:text-sm rounded-lg focus:ring-1 focus:outline-none focus:ring-gray-400 block w-full p-2.5 py-3 px-4"
                    placeholder="Your Name"
                  />
                  <ErrorMessage
                    name="name"
                    component="p"
                    className="text-red-500 text-xs mt-1"
                  />
                </div>
              </div>

              <div className="pb-2">
                <label
                  htmlFor="email"
                  className="block mb-2 text-sm font-medium text-[#111827]"
                >
                  Email
                </label>
                <div className="relative text-gray-400">
                  <span className="absolute inset-y-0 left-0 flex items-center p-1 pl-3">
                    <AtSign />
                  </span>
                  <Field
                    type="email"
                    name="email"
                    className="pl-12 mb-2 bg-gray-50 text-gray-600 border border-gray-300 sm:text-sm rounded-lg focus:ring-1 focus:outline-none focus:ring-gray-400 block w-full p-2.5 py-3 px-4"
                    placeholder="<EMAIL>"
                  />
                  <ErrorMessage
                    name="email"
                    component="p"
                    className="text-red-500 text-xs mt-1"
                  />
                </div>
              </div>

              <div className="pb-6">
                <label
                  htmlFor="password"
                  className="block mb-2 text-sm font-medium text-[#111827]"
                >
                  Password
                </label>
                <div className="relative text-gray-400">
                  <span className="absolute inset-y-0 left-0 flex items-center p-1 pl-3">
                    <KeyRound />
                  </span>
                  <Field
                    type="password"
                    name="password"
                    className="pl-12 mb-2 bg-gray-50 text-gray-600 border border-gray-300 sm:text-sm rounded-lg focus:ring-1 focus:outline-none focus:ring-gray-400 block w-full p-2.5 py-3 px-4"
                    placeholder="••••••••••"
                  />
                  <ErrorMessage
                    name="password"
                    component="p"
                    className="text-red-500 text-xs mt-1"
                  />
                </div>
              </div>

              <button
                type="submit"
                className="w-full text-[#FFFFFF] bg-[#0F172A] focus:ring-4 focus:outline-none focus:ring-primary-300 font-medium rounded-lg text-sm px-5 py-2.5 text-center mb-6"
                disabled={isSubmitting}
              >
                {isSubmitting ? "Signing up..." : "Sign Up"}
              </button>
              <div className="text-sm font-light text-[#6B7280] ">
                Already have an account?{" "}
                <Link
                  href="/auth/login"
                  className="font-medium text-[#0F172A] hover:underline"
                >
                  Login
                </Link>
              </div>
            </Form>
          )}
        </Formik>

        <div className="relative flex py-8 items-center">
          <div className="flex-grow border-t border-[1px] border-gray-200"></div>
          <span className="flex-shrink mx-4 font-medium text-gray-500">OR</span>
          <div className="flex-grow border-t border-[1px] border-gray-200"></div>
        </div>

        <form onSubmit={(e) => e.preventDefault()}>
          <div className="flex flex-row gap-2 justify-center">
            <Button
              onClick={() => signIn("google")}
              className="flex flex-row w-32 gap-2 bg-gray-600 p-2 rounded-md text-gray-200"
            >
              <span className="font-medium mx-auto">Google</span>
            </Button>
            <button className="flex flex-row w-32 gap-2 bg-gray-600 p-2 rounded-md text-gray-200">
              <Mail />
              <span className="font-medium mx-auto">Email</span>
            </button>
          </div>
        </form>
      </div>
    </div>
  );
};

export default Signup;
