#!/usr/bin/env python3
"""
Test Database Synchronization

This script demonstrates how database synchronization works
for product data between primary and replica databases.
"""

import os
import sys
import django

# Setup Django environment
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'backend.settings')
django.setup()

from django.db import transaction
from django.conf import settings
from products.models import Product, Category, Brand
from backend.db_sync import DatabaseSynchronizer, sync_status


def test_automatic_sync():
    """Test automatic synchronization of product data"""
    print("🧪 TESTING AUTOMATIC DATABASE SYNCHRONIZATION")
    print("=" * 60)
    
    # Check if replicas are configured
    replica_dbs = DatabaseSynchronizer.get_replica_databases()
    
    if not replica_dbs:
        print("❌ No replica databases configured")
        print("Add DB_READ_HOST and related environment variables to test sync")
        return
    
    print(f"✅ Replica databases found: {', '.join(replica_dbs)}")
    print(f"✅ Sync status: {'Enabled' if sync_status() else 'Disabled'}")
    
    # Test 1: Category Sync
    print("\n" + "="*50)
    print("🏷️ TEST 1: Category Synchronization")
    print("="*50)
    
    try:
        # Create a test category
        test_category = Category.objects.create(
            name="Test Sync Category",
            description="Testing automatic synchronization"
        )
        print(f"✅ Created category in PRIMARY: {test_category.name}")
        
        # Check if it exists in replica
        for db_name in replica_dbs:
            try:
                replica_category = Category.objects.using(db_name).get(id=test_category.id)
                print(f"✅ Category found in {db_name}: {replica_category.name}")
            except Category.DoesNotExist:
                print(f"❌ Category NOT found in {db_name}")
        
        # Clean up
        test_category.delete()
        print("🧹 Test category cleaned up")
        
    except Exception as e:
        print(f"❌ Category sync test failed: {e}")
    
    # Test 2: Brand Sync
    print("\n" + "="*50)
    print("🏢 TEST 2: Brand Synchronization")
    print("="*50)
    
    try:
        # Create a test brand
        test_brand = Brand.objects.create(
            name="Test Sync Brand",
            description="Testing automatic synchronization"
        )
        print(f"✅ Created brand in PRIMARY: {test_brand.name}")
        
        # Check if it exists in replica
        for db_name in replica_dbs:
            try:
                replica_brand = Brand.objects.using(db_name).get(id=test_brand.id)
                print(f"✅ Brand found in {db_name}: {replica_brand.name}")
            except Brand.DoesNotExist:
                print(f"❌ Brand NOT found in {db_name}")
        
        # Clean up
        test_brand.delete()
        print("🧹 Test brand cleaned up")
        
    except Exception as e:
        print(f"❌ Brand sync test failed: {e}")
    
    # Test 3: Product Sync (more complex)
    print("\n" + "="*50)
    print("📦 TEST 3: Product Synchronization")
    print("="*50)
    
    try:
        # Get existing category and brand for the test
        category = Category.objects.first()
        brand = Brand.objects.first()
        
        if not category or not brand:
            print("❌ Need at least one category and brand for product test")
            return
        
        # Create a test product
        test_product = Product.objects.create(
            name="Test Sync Product",
            description="Testing automatic synchronization",
            price=99.99,
            category=category,
            brand=brand,
            stock=10
        )
        print(f"✅ Created product in PRIMARY: {test_product.name}")
        
        # Check if it exists in replica
        for db_name in replica_dbs:
            try:
                replica_product = Product.objects.using(db_name).select_related(
                    'category', 'brand'
                ).get(id=test_product.id)
                print(f"✅ Product found in {db_name}: {replica_product.name}")
                print(f"   Category: {replica_product.category.name}")
                print(f"   Brand: {replica_product.brand.name}")
                print(f"   Price: ${replica_product.price}")
            except Product.DoesNotExist:
                print(f"❌ Product NOT found in {db_name}")
        
        # Test update
        print("\n📝 Testing product update...")
        test_product.price = 149.99
        test_product.save()
        print(f"✅ Updated product price in PRIMARY: ${test_product.price}")
        
        # Check updated price in replica
        for db_name in replica_dbs:
            try:
                replica_product = Product.objects.using(db_name).get(id=test_product.id)
                if replica_product.price == test_product.price:
                    print(f"✅ Updated price synced to {db_name}: ${replica_product.price}")
                else:
                    print(f"❌ Price NOT synced to {db_name}: ${replica_product.price}")
            except Product.DoesNotExist:
                print(f"❌ Product NOT found in {db_name}")
        
        # Clean up
        test_product.delete()
        print("🧹 Test product cleaned up")
        
    except Exception as e:
        print(f"❌ Product sync test failed: {e}")


def test_sync_performance():
    """Test synchronization performance"""
    print("\n" + "="*60)
    print("⚡ SYNCHRONIZATION PERFORMANCE TEST")
    print("="*60)
    
    replica_dbs = DatabaseSynchronizer.get_replica_databases()
    
    if not replica_dbs:
        print("❌ No replica databases configured for performance test")
        return
    
    import time
    
    # Test category creation performance
    print("🏷️ Testing category sync performance...")
    
    start_time = time.time()
    
    # Create multiple categories
    categories = []
    for i in range(5):
        category = Category.objects.create(
            name=f"Perf Test Category {i}",
            description=f"Performance test category {i}"
        )
        categories.append(category)
    
    end_time = time.time()
    sync_time = (end_time - start_time) * 1000  # Convert to milliseconds
    
    print(f"✅ Created and synced 5 categories in {sync_time:.2f}ms")
    print(f"   Average per category: {sync_time/5:.2f}ms")
    
    # Verify all categories are synced
    synced_count = 0
    for category in categories:
        for db_name in replica_dbs:
            try:
                Category.objects.using(db_name).get(id=category.id)
                synced_count += 1
            except Category.DoesNotExist:
                pass
    
    expected_count = len(categories) * len(replica_dbs)
    print(f"✅ Sync verification: {synced_count}/{expected_count} records synced")
    
    # Clean up
    for category in categories:
        category.delete()
    
    print("🧹 Performance test data cleaned up")


def show_sync_status():
    """Show current synchronization status"""
    print("\n" + "="*60)
    print("📊 CURRENT SYNCHRONIZATION STATUS")
    print("="*60)
    
    replica_dbs = DatabaseSynchronizer.get_replica_databases()
    
    if not replica_dbs:
        print("❌ No replica databases configured")
        return
    
    models_to_check = [
        (Product, "Products"),
        (Category, "Categories"), 
        (Brand, "Brands")
    ]
    
    for model_class, model_name in models_to_check:
        print(f"\n{model_name}:")
        
        # Primary count
        primary_count = model_class.objects.count()
        print(f"   PRIMARY: {primary_count} records")
        
        # Replica counts
        all_synced = True
        for db_name in replica_dbs:
            try:
                replica_count = model_class.objects.using(db_name).count()
                if replica_count == primary_count:
                    status = "✅ SYNCED"
                else:
                    status = "❌ OUT OF SYNC"
                    all_synced = False
                
                print(f"   {db_name.upper()}: {replica_count} records {status}")
            except Exception as e:
                print(f"   {db_name.upper()}: ❌ ERROR - {e}")
                all_synced = False
        
        if all_synced:
            print(f"   🎯 {model_name} are fully synchronized!")


def main():
    """Run all synchronization tests"""
    print("🔄 DATABASE SYNCHRONIZATION TESTING")
    print("Testing automatic sync for product data")
    print("=" * 70)
    
    # Show current status
    show_sync_status()
    
    # Test automatic synchronization
    test_automatic_sync()
    
    # Test performance
    test_sync_performance()
    
    # Final status check
    show_sync_status()
    
    print("\n✅ SYNCHRONIZATION TESTING COMPLETED!")
    print("\n💡 Key Takeaways:")
    print("   • Product data syncs automatically when you add/update/delete")
    print("   • Sync happens in real-time with minimal performance impact")
    print("   • Both databases stay perfectly synchronized")
    print("   • No manual intervention required!")


if __name__ == "__main__":
    main()
