/**
 * Secure cart management with encrypted storage and security features
 */

import { secureLocalStorage, secureSessionStorage } from './secureStorage';
import { secureApiClient } from './secureApiClient';

export interface CartItem {
  id: number;
  product_id: number;
  name: string;
  price: number;
  quantity: number;
  image?: string;
  variant?: string;
  max_quantity?: number;
}

export interface Cart {
  items: CartItem[];
  total: number;
  itemCount: number;
  lastUpdated: string;
}

class SecureCartManager {
  private readonly CART_KEY = 'secure_cart';
  private readonly CART_SYNC_KEY = 'cart_last_sync';
  private storage = secureLocalStorage; // Use localStorage for cart persistence

  /**
   * Get current cart from secure storage
   */
  getCart(): Cart {
    try {
      const cartData = this.storage.getItem<Cart>(this.CART_KEY);
      if (cartData) {
        return cartData;
      }
    } catch (error) {
      console.error('Error retrieving cart:', error);
    }

    // Return empty cart if none exists or error occurred
    return this.createEmptyCart();
  }

  /**
   * Save cart to secure storage
   */
  private saveCart(cart: Cart): boolean {
    try {
      cart.lastUpdated = new Date().toISOString();
      return this.storage.setItem(this.CART_KEY, cart);
    } catch (error) {
      console.error('Error saving cart:', error);
      return false;
    }
  }

  /**
   * Add item to cart
   */
  addItem(item: Omit<CartItem, 'quantity'>, quantity: number = 1): Cart {
    const cart = this.getCart();
    const existingItemIndex = cart.items.findIndex(
      cartItem => cartItem.product_id === item.product_id && cartItem.variant === item.variant
    );

    if (existingItemIndex >= 0) {
      // Update existing item quantity
      const existingItem = cart.items[existingItemIndex];
      const newQuantity = existingItem.quantity + quantity;
      
      // Check max quantity limit
      if (item.max_quantity && newQuantity > item.max_quantity) {
        console.warn(`Cannot add more items. Maximum quantity (${item.max_quantity}) reached.`);
        return cart;
      }

      cart.items[existingItemIndex].quantity = newQuantity;
    } else {
      // Add new item
      const newItem: CartItem = {
        ...item,
        quantity: Math.min(quantity, item.max_quantity || quantity)
      };
      cart.items.push(newItem);
    }

    this.updateCartTotals(cart);
    this.saveCart(cart);
    this.logCartAction('ADD_ITEM', { product_id: item.product_id, quantity });

    return cart;
  }

  /**
   * Update item quantity
   */
  updateItemQuantity(productId: number, quantity: number, variant?: string): Cart {
    const cart = this.getCart();
    const itemIndex = cart.items.findIndex(
      item => item.product_id === productId && item.variant === variant
    );

    if (itemIndex >= 0) {
      if (quantity <= 0) {
        // Remove item if quantity is 0 or negative
        cart.items.splice(itemIndex, 1);
      } else {
        // Check max quantity limit
        const item = cart.items[itemIndex];
        if (item.max_quantity && quantity > item.max_quantity) {
          cart.items[itemIndex].quantity = item.max_quantity;
        } else {
          cart.items[itemIndex].quantity = quantity;
        }
      }

      this.updateCartTotals(cart);
      this.saveCart(cart);
      this.logCartAction('UPDATE_QUANTITY', { product_id: productId, quantity });
    }

    return cart;
  }

  /**
   * Remove item from cart
   */
  removeItem(productId: number, variant?: string): Cart {
    const cart = this.getCart();
    cart.items = cart.items.filter(
      item => !(item.product_id === productId && item.variant === variant)
    );

    this.updateCartTotals(cart);
    this.saveCart(cart);
    this.logCartAction('REMOVE_ITEM', { product_id: productId });

    return cart;
  }

  /**
   * Clear entire cart
   */
  clearCart(): Cart {
    const emptyCart = this.createEmptyCart();
    this.saveCart(emptyCart);
    this.logCartAction('CLEAR_CART', {});
    return emptyCart;
  }

  /**
   * Get cart item count
   */
  getItemCount(): number {
    const cart = this.getCart();
    return cart.itemCount;
  }

  /**
   * Get cart total
   */
  getTotal(): number {
    const cart = this.getCart();
    return cart.total;
  }

  /**
   * Check if product is in cart
   */
  isInCart(productId: number, variant?: string): boolean {
    const cart = this.getCart();
    return cart.items.some(
      item => item.product_id === productId && item.variant === variant
    );
  }

  /**
   * Get specific item from cart
   */
  getItem(productId: number, variant?: string): CartItem | null {
    const cart = this.getCart();
    return cart.items.find(
      item => item.product_id === productId && item.variant === variant
    ) || null;
  }

  /**
   * Sync cart with server (for authenticated users)
   */
  async syncWithServer(): Promise<boolean> {
    try {
      if (!secureApiClient.isAuthenticated()) {
        return false;
      }

      const cart = this.getCart();
      const lastSync = this.storage.getItem<string>(this.CART_SYNC_KEY);
      
      // Only sync if cart has been modified since last sync
      if (lastSync && cart.lastUpdated <= lastSync) {
        return true;
      }

      // Send cart to server
      const response = await secureApiClient.post('/api/cart/sync/', {
        items: cart.items,
        total: cart.total,
        last_updated: cart.lastUpdated
      });

      if (response.status === 200) {
        // Update last sync time
        this.storage.setItem(this.CART_SYNC_KEY, new Date().toISOString());
        this.logCartAction('SYNC_SUCCESS', { item_count: cart.itemCount });
        return true;
      }
    } catch (error) {
      console.error('Cart sync failed:', error);
      this.logCartAction('SYNC_FAILED', { error: error.message });
    }

    return false;
  }

  /**
   * Load cart from server (for authenticated users)
   */
  async loadFromServer(): Promise<Cart> {
    try {
      if (!secureApiClient.isAuthenticated()) {
        return this.getCart();
      }

      const response = await secureApiClient.get('/api/cart/');
      if (response.status === 200 && response.data.items) {
        const serverCart: Cart = {
          items: response.data.items,
          total: response.data.total || 0,
          itemCount: response.data.items.reduce((sum: number, item: CartItem) => sum + item.quantity, 0),
          lastUpdated: response.data.last_updated || new Date().toISOString()
        };

        this.saveCart(serverCart);
        this.logCartAction('LOAD_FROM_SERVER', { item_count: serverCart.itemCount });
        return serverCart;
      }
    } catch (error) {
      console.error('Failed to load cart from server:', error);
    }

    return this.getCart();
  }

  /**
   * Merge local cart with server cart
   */
  async mergeWithServer(): Promise<Cart> {
    try {
      const localCart = this.getCart();
      const serverCart = await this.loadFromServer();

      // Simple merge strategy: combine items, preferring higher quantities
      const mergedItems = new Map<string, CartItem>();

      // Add local items
      localCart.items.forEach(item => {
        const key = `${item.product_id}_${item.variant || ''}`;
        mergedItems.set(key, item);
      });

      // Merge server items
      serverCart.items.forEach(item => {
        const key = `${item.product_id}_${item.variant || ''}`;
        const existing = mergedItems.get(key);
        
        if (existing) {
          // Keep higher quantity
          if (item.quantity > existing.quantity) {
            mergedItems.set(key, item);
          }
        } else {
          mergedItems.set(key, item);
        }
      });

      const mergedCart: Cart = {
        items: Array.from(mergedItems.values()),
        total: 0,
        itemCount: 0,
        lastUpdated: new Date().toISOString()
      };

      this.updateCartTotals(mergedCart);
      this.saveCart(mergedCart);
      
      // Sync merged cart back to server
      await this.syncWithServer();

      this.logCartAction('MERGE_COMPLETE', { item_count: mergedCart.itemCount });
      return mergedCart;
    } catch (error) {
      console.error('Cart merge failed:', error);
      return this.getCart();
    }
  }

  /**
   * Create empty cart
   */
  private createEmptyCart(): Cart {
    return {
      items: [],
      total: 0,
      itemCount: 0,
      lastUpdated: new Date().toISOString()
    };
  }

  /**
   * Update cart totals
   */
  private updateCartTotals(cart: Cart): void {
    cart.total = cart.items.reduce((sum, item) => sum + (item.price * item.quantity), 0);
    cart.itemCount = cart.items.reduce((sum, item) => sum + item.quantity, 0);
  }

  /**
   * Log cart actions for security monitoring
   */
  private logCartAction(action: string, data: any): void {
    if (process.env.NODE_ENV === 'development') {
      console.log(`[Cart] ${action}:`, data);
    }

    // In production, this could send to analytics or monitoring service
    try {
      const logEntry = {
        action,
        data,
        timestamp: new Date().toISOString(),
        user_agent: typeof navigator !== 'undefined' ? navigator.userAgent : 'unknown'
      };

      // Store recent actions for debugging (keep last 10)
      const recentActions = this.storage.getItem<any[]>('cart_actions') || [];
      recentActions.push(logEntry);
      if (recentActions.length > 10) {
        recentActions.shift();
      }
      this.storage.setItem('cart_actions', recentActions, 60); // 1 hour expiry
    } catch (error) {
      // Ignore logging errors
    }
  }

  /**
   * Get recent cart actions for debugging
   */
  getRecentActions(): any[] {
    return this.storage.getItem<any[]>('cart_actions') || [];
  }

  /**
   * Validate cart integrity
   */
  validateCart(): { isValid: boolean; errors: string[] } {
    const cart = this.getCart();
    const errors: string[] = [];

    // Check for negative quantities
    cart.items.forEach(item => {
      if (item.quantity <= 0) {
        errors.push(`Invalid quantity for product ${item.product_id}: ${item.quantity}`);
      }
      if (item.price < 0) {
        errors.push(`Invalid price for product ${item.product_id}: ${item.price}`);
      }
    });

    // Check total calculation
    const calculatedTotal = cart.items.reduce((sum, item) => sum + (item.price * item.quantity), 0);
    if (Math.abs(calculatedTotal - cart.total) > 0.01) {
      errors.push(`Cart total mismatch. Expected: ${calculatedTotal}, Actual: ${cart.total}`);
    }

    return {
      isValid: errors.length === 0,
      errors
    };
  }
}

// Export singleton instance
export const secureCartManager = new SecureCartManager();

// Export class for custom instances
export { SecureCartManager };
