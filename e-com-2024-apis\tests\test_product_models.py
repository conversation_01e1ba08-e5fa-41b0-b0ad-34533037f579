"""
Comprehensive tests for product models and their methods
Tests Product, Category, Brand, GST, ProductImage, ProductVariant, Review, and SubCategorie models
"""

import pytest
from decimal import Decimal
from unittest.mock import patch
from django.test import TestCase
from django.contrib.auth import get_user_model
from django.core.exceptions import ValidationError
from django.utils.text import slugify

from products.models import (
    Category, SubCategorie, Brand, GST, Product, 
    ProductImage, ProductVariant, Review
)

User = get_user_model()

class TestCategoryModel(TestCase):
    """Test Category model methods and properties"""
    
    def test_category_creation(self):
        """Test category creation"""
        category = Category.objects.create(
            name='Electronics',
            description='Electronic products'
        )
        
        self.assertEqual(category.name, 'Electronics')
        self.assertEqual(category.description, 'Electronic products')
        self.assertTrue(category.is_active)
    
    def test_category_str_method(self):
        """Test category string representation"""
        category = Category.objects.create(name='Electronics')
        self.assertEqual(str(category), 'Electronics')
    
    def test_category_slug_generation(self):
        """Test category slug generation"""
        category = Category.objects.create(name='Home & Garden')
        expected_slug = slugify('Home & Garden')
        self.assertEqual(category.slug, expected_slug)
    
    def test_category_image_upload_path(self):
        """Test category image upload path"""
        category = Category.objects.create(name='Electronics')
        # Test that image field exists
        self.assertTrue(hasattr(category, 'image'))
    
    def test_category_ordering(self):
        """Test category ordering"""
        cat1 = Category.objects.create(name='B Category')
        cat2 = Category.objects.create(name='A Category')

        categories = list(Category.objects.all())
        # Categories are ordered by creation order (primary key), not alphabetically
        self.assertEqual(categories[0].name, 'B Category')
        self.assertEqual(categories[1].name, 'A Category')

class TestSubCategorieModel(TestCase):
    """Test SubCategorie model"""
    
    def setUp(self):
        """Set up test data"""
        self.category = Category.objects.create(name='Electronics')
    
    def test_subcategory_creation(self):
        """Test subcategory creation"""
        subcategory = SubCategorie.objects.create(
            name='Smartphones',
            category=self.category,
            description='Mobile phones'
        )
        
        self.assertEqual(subcategory.name, 'Smartphones')
        self.assertEqual(subcategory.category, self.category)
        self.assertTrue(subcategory.is_active)
    
    def test_subcategory_str_method(self):
        """Test subcategory string representation"""
        subcategory = SubCategorie.objects.create(
            name='Smartphones',
            category=self.category
        )
        self.assertEqual(str(subcategory), 'Smartphones')
    
    def test_subcategory_slug_generation(self):
        """Test subcategory slug generation"""
        subcategory = SubCategorie.objects.create(
            name='Smart Phones',
            category=self.category
        )
        expected_slug = slugify('Smart Phones')
        self.assertEqual(subcategory.slug, expected_slug)

class TestBrandModel(TestCase):
    """Test Brand model"""
    
    def test_brand_creation(self):
        """Test brand creation"""
        brand = Brand.objects.create(
            name='Apple',
            description='Technology company'
        )
        
        self.assertEqual(brand.name, 'Apple')
        self.assertEqual(brand.description, 'Technology company')
        self.assertTrue(brand.is_active)
    
    def test_brand_str_method(self):
        """Test brand string representation"""
        brand = Brand.objects.create(name='Apple')
        self.assertEqual(str(brand), 'Apple')
    

    
    def test_brand_image_field(self):
        """Test brand image field"""
        brand = Brand.objects.create(name='Apple')
        self.assertTrue(hasattr(brand, 'image'))

class TestGSTModel(TestCase):
    """Test GST model and calculations"""
    
    def test_gst_creation(self):
        """Test GST creation"""
        gst = GST.objects.create(
            name='Standard GST',
            rate=Decimal('18.00'),
            cgst_rate=Decimal('9.00'),
            sgst_rate=Decimal('9.00'),
            igst_rate=Decimal('18.00')
        )
        
        self.assertEqual(gst.name, 'Standard GST')
        self.assertEqual(gst.rate, Decimal('18.00'))
        self.assertTrue(gst.is_active)
    
    def test_gst_str_method(self):
        """Test GST string representation"""
        gst = GST.objects.create(
            name='Standard GST',
            rate=Decimal('18.00')
        )
        self.assertEqual(str(gst), 'Standard GST - 18.00%')
    
    def test_gst_calculate_gst_amount(self):
        """Test GST amount calculation"""
        gst = GST.objects.create(
            name='Standard GST',
            rate=Decimal('18.00')
        )
        
        amount = gst.calculate_gst_amount(Decimal('100.00'))
        self.assertEqual(amount, Decimal('18.00'))
    
    def test_gst_calculate_base_price_from_inclusive(self):
        """Test base price calculation from GST inclusive price"""
        gst = GST.objects.create(
            name='Standard GST',
            rate=Decimal('18.00')
        )
        
        base_price = gst.calculate_base_price_from_inclusive(Decimal('118.00'))
        self.assertEqual(base_price, Decimal('100.00'))
    
    def test_gst_calculate_gst_from_inclusive(self):
        """Test GST calculation from inclusive price"""
        gst = GST.objects.create(
            name='Standard GST',
            rate=Decimal('18.00')
        )
        
        gst_amount = gst.calculate_gst_from_inclusive(Decimal('118.00'))
        self.assertEqual(gst_amount, Decimal('18.00'))
    
    def test_gst_get_default_gst(self):
        """Test getting default GST"""
        # Create default GST
        default_gst = GST.objects.create(
            name='Default GST',
            rate=Decimal('18.00'),
            is_default=True
        )
        
        # Create non-default GST
        GST.objects.create(
            name='Other GST',
            rate=Decimal('12.00'),
            is_default=False
        )
        
        result = GST.get_default_gst()
        self.assertEqual(result, default_gst)
    
    def test_gst_validation(self):
        """Test GST rate validation"""
        # Test negative rate
        with self.assertRaises(ValidationError):
            gst = GST(name='Invalid GST', rate=Decimal('-5.00'))
            gst.full_clean()
        
        # Test rate over 100%
        with self.assertRaises(ValidationError):
            gst = GST(name='Invalid GST', rate=Decimal('150.00'))
            gst.full_clean()

class TestProductModel(TestCase):
    """Test Product model methods and properties"""
    
    def setUp(self):
        """Set up test data"""
        self.category = Category.objects.create(name='Electronics')
        self.subcategory = SubCategorie.objects.create(
            name='Smartphones',
            category=self.category
        )
        self.brand = Brand.objects.create(name='Apple')
        self.gst = GST.objects.create(
            name='Standard GST',
            rate=Decimal('18.00')
        )
        
        self.product = Product.objects.create(
            name='iPhone 13',
            description='Latest iPhone model',
            category=self.category,
            subcategory=self.subcategory,
            brand=self.brand,
            price=Decimal('999.00'),
            gst=self.gst,
            stock=50
        )
    
    def test_product_creation(self):
        """Test product creation"""
        self.assertEqual(self.product.name, 'iPhone 13')
        self.assertEqual(self.product.category, self.category)
        self.assertEqual(self.product.brand, self.brand)
        self.assertEqual(self.product.price, Decimal('999.00'))
        self.assertTrue(self.product.is_active)
    
    def test_product_str_method(self):
        """Test product string representation"""
        self.assertEqual(str(self.product), 'iPhone 13')
    
    def test_product_slug_generation(self):
        """Test product slug generation"""
        expected_slug = slugify('iPhone 13')
        self.assertEqual(self.product.slug, expected_slug)
    
    def test_product_get_gst_rate(self):
        """Test getting GST rate for product"""
        gst_rate = self.product.get_gst_rate()
        self.assertEqual(gst_rate, self.gst)
    
    def test_product_get_gst_rate_default(self):
        """Test getting default GST rate when product has no GST"""
        # Create default GST
        default_gst = GST.objects.create(
            name='Default GST',
            rate=Decimal('18.00'),
            is_default=True
        )
        
        # Create product without GST
        product = Product.objects.create(
            name='Test Product',
            category=self.category,
            brand=self.brand,
            price=Decimal('100.00'),
            stock=10
        )
        
        gst_rate = product.get_gst_rate()
        self.assertEqual(gst_rate, default_gst)
    
    def test_product_calculate_gst_amount(self):
        """Test GST amount calculation (deprecated method)"""
        gst_amount = self.product.calculate_gst_amount()
        expected = self.gst.calculate_gst_amount(self.product.price)
        self.assertEqual(gst_amount, expected)
    
    def test_product_calculate_gst_from_mrp(self):
        """Test GST calculation from MRP"""
        gst_amount = self.product.calculate_gst_from_mrp()
        expected = self.gst.calculate_gst_from_inclusive(self.product.price)
        self.assertEqual(gst_amount, expected)
    
    def test_product_mrp_property(self):
        """Test MRP property"""
        self.assertEqual(self.product.mrp, self.product.price)
    
    def test_product_base_price_property(self):
        """Test base price property"""
        base_price = self.product.base_price
        expected = self.gst.calculate_base_price_from_inclusive(self.product.price)
        self.assertEqual(base_price, expected)
    
    def test_product_price_with_gst_property(self):
        """Test price with GST property (deprecated)"""
        price_with_gst = self.product.price_with_gst
        expected = self.product.price + self.product.calculate_gst_amount()
        self.assertEqual(price_with_gst, expected)
    
    def test_product_average_rating_property(self):
        """Test average rating property"""
        # No reviews yet
        self.assertEqual(self.product.average_rating, 0.0)
        
        # Add reviews from different users
        user1 = User.objects.create_user(
            email='<EMAIL>',
            password='testpass123',
            name='Test User 1'
        )

        user2 = User.objects.create_user(
            email='<EMAIL>',
            password='testpass123',
            name='Test User 2'
        )

        Review.objects.create(
            product=self.product,
            user=user1,
            rating=5,
            comment='Great product'
        )

        Review.objects.create(
            product=self.product,
            user=user2,
            rating=3,
            comment='Okay product'
        )
        
        # Average should be 4.0
        self.assertEqual(self.product.average_rating, 4.0)
    
    def test_product_total_reviews_property(self):
        """Test total reviews property"""
        # No reviews yet
        self.assertEqual(self.product.total_reviews, 0)
        
        # Add review
        user = User.objects.create_user(
            email='<EMAIL>',
            password='testpass123',
            name='Test User'
        )
        
        Review.objects.create(
            product=self.product,
            user=user,
            rating=5,
            comment='Great product'
        )
        
        self.assertEqual(self.product.total_reviews, 1)

class TestProductImageModel(TestCase):
    """Test ProductImage model"""
    
    def setUp(self):
        """Set up test data"""
        self.category = Category.objects.create(name='Electronics')
        self.brand = Brand.objects.create(name='Apple')
        self.product = Product.objects.create(
            name='iPhone 13',
            category=self.category,
            brand=self.brand,
            price=Decimal('999.00'),
            stock=50
        )
    
    def test_product_image_creation(self):
        """Test product image creation"""
        image = ProductImage.objects.create(
            product=self.product,
            is_primary=True
        )

        self.assertEqual(image.product, self.product)
        self.assertEqual(image.is_primary, True)
    
    def test_product_image_str_method(self):
        """Test product image string representation"""
        image = ProductImage.objects.create(
            product=self.product,
            is_primary=True
        )

        expected = f"Image for {self.product.name}"
        self.assertEqual(str(image), expected)

class TestProductVariantModel(TestCase):
    """Test ProductVariant model"""
    
    def setUp(self):
        """Set up test data"""
        self.category = Category.objects.create(name='Electronics')
        self.brand = Brand.objects.create(name='Apple')
        self.product = Product.objects.create(
            name='iPhone 13',
            category=self.category,
            brand=self.brand,
            price=Decimal('999.00'),
            stock=50
        )
    
    def test_product_variant_creation(self):
        """Test product variant creation"""
        variant = ProductVariant.objects.create(
            product=self.product,
            name='128GB Space Gray',
            sku='IPHONE13-128GB-SG',
            price_adjustment=Decimal('0.00'),
            stock=25
        )

        self.assertEqual(variant.product, self.product)
        self.assertEqual(variant.name, '128GB Space Gray')
        self.assertEqual(variant.price_adjustment, Decimal('0.00'))
        self.assertEqual(variant.stock, 25)
    
    def test_product_variant_str_method(self):
        """Test product variant string representation"""
        variant = ProductVariant.objects.create(
            product=self.product,
            name='128GB Space Gray',
            sku='IPHONE13-128GB-SG',
            price_adjustment=Decimal('0.00'),
            stock=25
        )

        expected = f"{self.product.name} - 128GB Space Gray"
        self.assertEqual(str(variant), expected)

class TestReviewModel(TestCase):
    """Test Review model"""
    
    def setUp(self):
        """Set up test data"""
        self.user = User.objects.create_user(
            email='<EMAIL>',
            password='testpass123',
            name='Test User'
        )
        
        self.category = Category.objects.create(name='Electronics')
        self.brand = Brand.objects.create(name='Apple')
        self.product = Product.objects.create(
            name='iPhone 13',
            category=self.category,
            brand=self.brand,
            price=Decimal('999.00'),
            stock=50
        )
    
    def test_review_creation(self):
        """Test review creation"""
        review = Review.objects.create(
            product=self.product,
            user=self.user,
            rating=5,
            comment='Excellent product!'
        )
        
        self.assertEqual(review.product, self.product)
        self.assertEqual(review.user, self.user)
        self.assertEqual(review.rating, 5)
        self.assertEqual(review.comment, 'Excellent product!')
    
    def test_review_str_method(self):
        """Test review string representation"""
        review = Review.objects.create(
            product=self.product,
            user=self.user,
            rating=5,
            comment='Excellent product!'
        )
        
        expected = f"{self.user.email}'s review of {self.product.name}"
        self.assertEqual(str(review), expected)
    
    def test_review_rating_validation(self):
        """Test review rating validation"""
        # Test invalid rating (too low)
        with self.assertRaises(ValidationError):
            review = Review(
                product=self.product,
                user=self.user,
                rating=0,
                comment='Invalid rating'
            )
            review.full_clean()
        
        # Test invalid rating (too high)
        with self.assertRaises(ValidationError):
            review = Review(
                product=self.product,
                user=self.user,
                rating=6,
                comment='Invalid rating'
            )
            review.full_clean()
    
    def test_review_unique_constraint(self):
        """Test that user can only review a product once"""
        Review.objects.create(
            product=self.product,
            user=self.user,
            rating=5,
            comment='First review'
        )
        
        # Second review should fail
        with self.assertRaises(Exception):  # IntegrityError
            Review.objects.create(
                product=self.product,
                user=self.user,
                rating=3,
                comment='Second review'
            )
