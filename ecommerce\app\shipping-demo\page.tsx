"use client";

/**
 * Shipping Demo Page - Test all shipping functionality
 * This page demonstrates all shipping components and features
 */

import React, { useState } from 'react';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Separator } from '@/components/ui/separator';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { 
  Calculator, 
  Package, 
  MapPin, 
  Activity,
  TestTube,
  Zap
} from 'lucide-react';
import MainHOF from '@/layout/MainHOF';
import {
  RapidshypRateCalculator,
  PincodeValidator,
  ShippingRateSelector,
  OrderTrackingInterface,
  ShippingErrorBoundary,
  ShippingServiceStatus
} from '@/components/shipping';
import type { CourierRate } from '@/types/shipping';

const ShippingDemo = () => {
  const [selectedRate, setSelectedRate] = useState<CourierRate | null>(null);
  const [testPincode, setTestPincode] = useState('400001');
  const [testOrderId, setTestOrderId] = useState('');
  const [pincodeValid, setPincodeValid] = useState(false);
  const [pincodeServiceable, setPincodeServiceable] = useState(false);

  // Mock data for testing
  const mockRates: CourierRate[] = [
    {
      courier_code: 'DTDC',
      courier_name: 'DTDC Express',
      parent_courier_name: 'DTDC',
      total_freight: 75,
      estimated_days: 3,
      freight_mode: 'Surface',
      cutoff_time: '18:00',
      is_existing_method: false
    },
    {
      courier_code: 'BLUEDART',
      courier_name: 'Blue Dart',
      parent_courier_name: 'Blue Dart',
      total_freight: 120,
      estimated_days: 2,
      freight_mode: 'Air',
      cutoff_time: '16:00',
      is_existing_method: false
    },
    {
      courier_code: 'STANDARD',
      courier_name: 'Standard Shipping',
      total_freight: 50,
      estimated_days: 5,
      is_existing_method: true,
      description: 'Standard delivery service'
    }
  ];

  const handleRateSelect = (rate: CourierRate) => {
    setSelectedRate(rate);
    console.log('Selected rate:', rate);
  };

  const handlePincodeValidation = (isValid: boolean, isServiceable: boolean) => {
    setPincodeValid(isValid);
    setPincodeServiceable(isServiceable);
  };

  return (
    <MainHOF>
      <div className="container mx-auto px-4 py-8">
        <div className="max-w-6xl mx-auto">
          {/* Header */}
          <div className="text-center mb-8">
            <h1 className="text-3xl font-bold mb-4 flex items-center justify-center">
              <TestTube className="h-8 w-8 mr-3 text-blue-600" />
              Rapidshyp Shipping Integration Demo
            </h1>
            <p className="text-gray-600 max-w-2xl mx-auto">
              Test and explore all shipping features including live rate calculation, 
              pincode validation, order tracking, and error handling.
            </p>
            <Badge variant="secondary" className="mt-2">
              Development Testing Environment
            </Badge>
          </div>

          {/* Service Status */}
          <div className="mb-8">
            <ShippingErrorBoundary>
              <ShippingServiceStatus 
                showDetails={true}
                autoRefresh={true}
                refreshInterval={30000}
              />
            </ShippingErrorBoundary>
          </div>

          {/* Main Demo Tabs */}
          <Tabs defaultValue="calculator" className="space-y-6">
            <TabsList className="grid w-full grid-cols-4">
              <TabsTrigger value="calculator" className="flex items-center">
                <Calculator className="h-4 w-4 mr-2" />
                Rate Calculator
              </TabsTrigger>
              <TabsTrigger value="pincode" className="flex items-center">
                <MapPin className="h-4 w-4 mr-2" />
                Pincode Validator
              </TabsTrigger>
              <TabsTrigger value="tracking" className="flex items-center">
                <Package className="h-4 w-4 mr-2" />
                Order Tracking
              </TabsTrigger>
              <TabsTrigger value="components" className="flex items-center">
                <Activity className="h-4 w-4 mr-2" />
                Components
              </TabsTrigger>
            </TabsList>

            {/* Rate Calculator Tab */}
            <TabsContent value="calculator" className="space-y-6">
              <Card>
                <CardHeader>
                  <CardTitle className="flex items-center">
                    <Zap className="h-5 w-5 mr-2 text-green-600" />
                    Live Shipping Rate Calculator
                  </CardTitle>
                </CardHeader>
                <CardContent>
                  <ShippingErrorBoundary>
                    <RapidshypRateCalculator
                      deliveryPincode={testPincode}
                      orderWeight={2.5}
                      isCOD={false}
                      orderTotal={1500}
                      onRateSelect={handleRateSelect}
                      selectedRate={selectedRate}
                    />
                  </ShippingErrorBoundary>
                </CardContent>
              </Card>

              {/* Selected Rate Display */}
              {selectedRate && (
                <Card className="border-green-200 bg-green-50">
                  <CardHeader>
                    <CardTitle className="text-green-700">Selected Shipping Method</CardTitle>
                  </CardHeader>
                  <CardContent>
                    <div className="grid grid-cols-2 gap-4 text-sm">
                      <div>
                        <strong>Courier:</strong> {selectedRate.courier_name}
                      </div>
                      <div>
                        <strong>Rate:</strong> ₹{selectedRate.total_freight}
                      </div>
                      <div>
                        <strong>Delivery:</strong> {selectedRate.estimated_days} days
                      </div>
                      <div>
                        <strong>Type:</strong> {selectedRate.is_existing_method ? 'Standard' : 'Live Rate'}
                      </div>
                    </div>
                  </CardContent>
                </Card>
              )}
            </TabsContent>

            {/* Pincode Validator Tab */}
            <TabsContent value="pincode" className="space-y-6">
              <Card>
                <CardHeader>
                  <CardTitle>Pincode Validation & Serviceability</CardTitle>
                </CardHeader>
                <CardContent className="space-y-4">
                  <ShippingErrorBoundary>
                    <PincodeValidator
                      value={testPincode}
                      onChange={setTestPincode}
                      onValidationChange={handlePincodeValidation}
                      label="Test Delivery Pincode"
                      placeholder="Enter 6-digit pincode"
                    />
                  </ShippingErrorBoundary>

                  <div className="grid grid-cols-2 gap-4 mt-4">
                    <div className="p-3 bg-gray-50 rounded">
                      <strong>Valid:</strong> {pincodeValid ? '✅ Yes' : '❌ No'}
                    </div>
                    <div className="p-3 bg-gray-50 rounded">
                      <strong>Serviceable:</strong> {pincodeServiceable ? '✅ Yes' : '❌ No'}
                    </div>
                  </div>

                  <div className="text-sm text-gray-600">
                    <p><strong>Test Pincodes:</strong></p>
                    <ul className="list-disc list-inside mt-2 space-y-1">
                      <li>400001 - Mumbai (Usually serviceable)</li>
                      <li>110001 - Delhi (Usually serviceable)</li>
                      <li>560001 - Bangalore (Usually serviceable)</li>
                      <li>123456 - Invalid format</li>
                    </ul>
                  </div>
                </CardContent>
              </Card>
            </TabsContent>

            {/* Order Tracking Tab */}
            <TabsContent value="tracking" className="space-y-6">
              <Card>
                <CardHeader>
                  <CardTitle>Order Tracking Interface</CardTitle>
                </CardHeader>
                <CardContent className="space-y-4">
                  <div className="flex gap-2">
                    <input
                      type="text"
                      placeholder="Enter Order ID to track"
                      value={testOrderId}
                      onChange={(e) => setTestOrderId(e.target.value)}
                      className="flex-1 px-3 py-2 border rounded-md"
                    />
                    <Button 
                      onClick={() => {
                        if (testOrderId) {
                          // This will trigger the tracking component to load
                          console.log('Tracking order:', testOrderId);
                        }
                      }}
                      disabled={!testOrderId}
                    >
                      Track
                    </Button>
                  </div>

                  {testOrderId && (
                    <ShippingErrorBoundary>
                      <OrderTrackingInterface
                        orderId={testOrderId}
                        autoRefresh={false}
                        refreshInterval={60000}
                      />
                    </ShippingErrorBoundary>
                  )}

                  <div className="text-sm text-gray-600">
                    <p><strong>Note:</strong> Enter a valid order ID from your account to see tracking information.</p>
                  </div>
                </CardContent>
              </Card>
            </TabsContent>

            {/* Components Tab */}
            <TabsContent value="components" className="space-y-6">
              <Card>
                <CardHeader>
                  <CardTitle>Shipping Rate Selector (Standalone)</CardTitle>
                </CardHeader>
                <CardContent>
                  <ShippingErrorBoundary>
                    <ShippingRateSelector
                      rates={mockRates}
                      selectedRate={selectedRate}
                      onRateSelect={handleRateSelect}
                      source="rapidshyp"
                    />
                  </ShippingErrorBoundary>
                </CardContent>
              </Card>

              <Card>
                <CardHeader>
                  <CardTitle>Error Handling Demo</CardTitle>
                </CardHeader>
                <CardContent className="space-y-4">
                  <p className="text-sm text-gray-600">
                    Test error scenarios to see how the system handles failures gracefully.
                  </p>
                  
                  <div className="grid grid-cols-2 gap-4">
                    <Button 
                      variant="outline"
                      onClick={() => {
                        // Simulate network error
                        throw new Error('Simulated network error');
                      }}
                    >
                      Test Network Error
                    </Button>
                    
                    <Button 
                      variant="outline"
                      onClick={() => {
                        // Test with invalid pincode
                        setTestPincode('invalid');
                      }}
                    >
                      Test Invalid Pincode
                    </Button>
                  </div>
                </CardContent>
              </Card>
            </TabsContent>
          </Tabs>

          {/* Footer */}
          <div className="mt-12 text-center text-sm text-gray-500">
            <Separator className="mb-4" />
            <p>
              This demo page showcases the complete Rapidshyp shipping integration. 
              All components include error boundaries and fallback mechanisms.
            </p>
          </div>
        </div>
      </div>
    </MainHOF>
  );
};

export default ShippingDemo;
