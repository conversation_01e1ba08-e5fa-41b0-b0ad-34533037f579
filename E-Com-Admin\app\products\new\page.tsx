"use client"
import { useState } from "react";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Card, CardContent } from "@/components/ui/card";
import { ArrowLeft, Eye } from "lucide-react";
import { ProductForm } from "@/components/products/ProductForm";
import { ProductPreview } from "@/components/products/ProductPreview";
import { useToast } from "@/components/ui/use-toast";
import { useRouter } from "next/navigation";
import { useProducts } from "@/hooks/useProducts";
import { ProductFormData, ProductCreateRequest } from "@/types/product";

const AddProduct = () => {
  const router = useRouter();
  const { toast } = useToast();
  const [showPreview, setShowPreview] = useState(false);
  const { createProduct } = useProducts();

  const handleSubmit = async (data: ProductFormData) => {
    try {
      const productData: ProductCreateRequest = {
        name: data.name,
        description: data.description,
        price: parseFloat(data.price),
        stock: parseInt(data.stock),
        category: data.category,
        subcategory: data.subcategory || undefined,
        brand: data.brand || undefined,
        gst: data.gst || undefined,
        is_active: data.is_active,
      };

      const result = await createProduct(productData);

      if (result) {
        toast({
          title: "Product created",
          description: `Successfully created ${data.name}`,
        });
        router.push("/products");
      } else {
        toast({
          title: "Error",
          description: "Failed to create product",
          variant: "destructive",
        });
      }
    } catch {
      toast({
        title: "Error",
        description: "Failed to create product",
        variant: "destructive",
      });
    }
  };

  return (
    <div className="space-y-6 animate-fadeIn">
      <div className="flex items-center justify-between">
        <div className="flex items-center space-x-2">
          <Button variant="ghost" size="icon" onClick={() => router.push("/products")}>
            <ArrowLeft className="h-4 w-4" />
          </Button>
          <h1 className="text-3xl font-bold">Add Product</h1>
        </div>
        <Button onClick={() => setShowPreview(!showPreview)}>
          <Eye className="mr-2 h-4 w-4" />
          {showPreview ? "Hide Preview" : "Show Preview"}
        </Button>
      </div>

      <div className="grid gap-6 md:grid-cols-2">
        <Card>
          <CardContent className="pt-6">
            <ProductForm onSubmit={handleSubmit} />
          </CardContent>
        </Card>
        
        {showPreview && (
          <Card>
            <CardContent className="pt-6">
              <ProductPreview />
            </CardContent>
          </Card>
        )}
      </div>
    </div>
  );
};

export default AddProduct;
