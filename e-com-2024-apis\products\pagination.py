from rest_framework.pagination import PageNumberPagination


class ProductPagination(PageNumberPagination):
    page_size = 10
    page_size_query_param = 'page_size'  # Allows clients to set page size via ?page_size=4
    max_page_size = 100


class CategoryProductsPagination(PageNumberPagination):
    page_size = 10
    page_size_query_param = 'page_size'  # Allows clients to set page size via ?page_size=4
    max_page_size = 100