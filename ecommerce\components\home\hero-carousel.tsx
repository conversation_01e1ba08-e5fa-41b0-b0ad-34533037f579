// "use client";

// import { useState, useEffect } from "react";
// import Image from "next/image";
// import { ChevronLeft, ChevronRight } from "lucide-react";



// export default function HeroCarousel() {
//   const [current, setCurrent] = useState(0);

//   useEffect(() => {
//     const timer = setInterval(() => {
//       setCurrent((prev) => (prev + 1) % slides.length);
//     }, 5000);
//     return () => clearInterval(timer);
//   }, []);

//   const prev = () => {
//     setCurrent((curr) => (curr === 0 ? slides.length - 1 : curr - 1));
//   };

//   const next = () => {
//     setCurrent((curr) => (curr + 1) % slides.length);
//   };

//   return (
//     <div className="relative overflow-hidden">
//       <div
//         className="flex transition-transform duration-500 ease-out"
//         style={{ transform: `translateX(-${current * 100}%)` }}
//       >
//         {slides.map((slide, index) => (
//           <div
//             key={index}
//             className="min-w-full relative h-[400px] md:h-[600px]"
//           >
//             <Image
//               src={slide.image || "/placeholder.svg"}
//               alt={slide.title}
//               fill
//               className="object-cover"
//               priority={index === 0}
//             />
//             <div className="absolute inset-0 flex items-center justify-center">
//               <div className="text-center">
//                 <h2 className="text-4xl md:text-6xl font-bold mb-4">
//                   {slide.title}
//                 </h2>
//                 <p className="text-xl md:text-2xl mb-2">{slide.subtitle}</p>
//                 {slide.code && (
//                   <p className="text-lg md:text-xl font-medium">{slide.code}</p>
//                 )}
//               </div>
//             </div>
//           </div>
//         ))}
//       </div>

//       <button
//         onClick={prev}
//         className="absolute left-4 top-1/2 -translate-y-1/2 w-10 h-10 flex items-center justify-center bg-white/80 rounded-full shadow-md"
//       >
//         <ChevronLeft className="h-6 w-6" />
//       </button>

//       <button
//         onClick={next}
//         className="absolute right-4 top-1/2 -translate-y-1/2 w-10 h-10 flex items-center justify-center bg-white/80 rounded-full shadow-md"
//       >
//         <ChevronRight className="h-6 w-6" />
//       </button>

//       <div className="absolute bottom-4 left-1/2 -translate-x-1/2 flex gap-2">
//         {slides.map((_, index) => (
//           <button
//             key={index}
//             onClick={() => setCurrent(index)}
//             className={`w-2 h-2 rounded-full transition-colors ${
//               index === current ? "bg-white" : "bg-white/50"
//             }`}
//           />
//         ))}
//       </div>
//     </div>
//   );
// }


"use client"

import React, { useState, useEffect, useRef } from "react"
import AmazonStyleCarousel from "./AmazonStyleCarousel"
import { FUTURED_PRODUCTS, MAIN_URL } from "@/constant/urls"
import useApi from "@/hooks/useApi"
import { Skeleton } from "@/components/ui/skeleton"

// Fallback slides in case API fails
const fallbackSlides = [
  {
    image: "/home/<USER>", // Use existing image
    title: "PRIMA Smart Lock",
    subtitle: "Advanced security with fingerprint and PIN access",
    code: "NEW ARRIVAL",
    cta: "Shop Now",
    link: "/shop",
    specs: "Lock Size (inches): 9.6 x 5.6 • Min. Wooden Door Thickness: 3.2 cm Build",
    brand: "PRIMA"
  },
  {
    image: "/home/<USER>", // Use existing image
    title: "Qubo Smart Door Lock",
    subtitle: "Keyless entry with advanced security features",
    cta: "Explore Collection",
    link: "/shop",
    specs: "Compatible with standard door sizes • Multiple access methods",
    brand: "Qubo"
  },
];

export default function HeroCarousel() {
  // Use a ref to track if we've already fetched data to prevent continuous API calls
  const dataFetchedRef = useRef<boolean>(false);
  const { data: featuredProducts, loading, read } = useApi(MAIN_URL || '');
  const [slides, setSlides] = useState<any[]>([]);
  const [isLoading, setIsLoading] = useState(true);

  useEffect(() => {
    // Skip if we've already processed the data
    if (dataFetchedRef.current && slides.length > 0) {
      setIsLoading(false);
      return;
    }

    const fetchFeaturedProducts = async () => {
      try {
        setIsLoading(true);

        // Only fetch if we don't already have featuredProducts data
        let productsArray = [];
        if (featuredProducts) {
          // Use existing data if available
          if (Array.isArray(featuredProducts)) {
            productsArray = featuredProducts;
          } else if (featuredProducts.results && Array.isArray(featuredProducts.results)) {
            productsArray = featuredProducts.results;
          } else if (featuredProducts.products && Array.isArray(featuredProducts.products)) {
            productsArray = featuredProducts.products;
          }
        } else {
          // Fetch only if necessary
          const result = await read(FUTURED_PRODUCTS);

          // Process the API response
          if (Array.isArray(result)) {
            productsArray = result;
          } else if (result && result.results && Array.isArray(result.results)) {
            productsArray = result.results;
          } else if (result && result.products && Array.isArray(result.products)) {
            productsArray = result.products;
          }
        }

        // Take top 5 products
        const topProducts = productsArray.slice(0, 5);

        if (topProducts.length > 0) {
          // Transform products into slides
          const productSlides = topProducts.map(product => {
            // Ensure we have a valid image URL
            let imageUrl = '/home/<USER>'; // Default fallback image

            if (product.image) {
              if (product.image.startsWith('http')) {
                imageUrl = product.image;
              } else if (MAIN_URL) {
                // Make sure the image path is properly formatted
                const imagePath = product.image.startsWith('/') ? product.image : `/${product.image}`;
                imageUrl = MAIN_URL + imagePath;
              }
            }

            // Clean up product description to avoid HTML tags
            let cleanDescription = product.description || "Explore our premium collection";
            cleanDescription = cleanDescription.replace(/<[^>]*>?/gm, '');

            // Create specifications string from product attributes
            let specs = '';
            if (product.size) specs += `Size: ${product.size} `;
            if (product.dimensions) specs += `Dimensions: ${product.dimensions} `;
            if (product.weight) specs += `Weight: ${product.weight} `;
            if (product.material) specs += `Material: ${product.material} `;
            if (product.color) specs += `Color: ${product.color} `;

            // For products like the ones in the image (door locks, etc.)
            if (product.category && product.category.name && product.category.name.toLowerCase().includes('lock')) {
              specs = `Lock Size: ${product.dimensions || '9.6 x 5.6'} • Min. Door Thickness: ${product.min_thickness || '3.2 cm'} Build`;
            }

            return {
              image: imageUrl,
              title: product.name || "Featured Product",
              subtitle: cleanDescription.substring(0, 100),
              code: product.discount_percentage ? `SAVE ${product.discount_percentage}%` : undefined,
              cta: "Shop Now",
              link: `/product/${product.slug}`,
              specs: specs,
              brand: product.brand || undefined
            };
          });

          setSlides(productSlides);
        } else {
          // Use fallback slides if no products found
          setSlides(fallbackSlides);
        }

        // Mark as fetched to prevent continuous API calls
        dataFetchedRef.current = true;
      } catch (error) {
        console.error("Error fetching featured products for carousel:", error);
        setSlides(fallbackSlides);
        dataFetchedRef.current = true; // Mark as fetched even on error to prevent continuous retries
      } finally {
        setIsLoading(false);
      }
    };

    fetchFeaturedProducts();
  }, [featuredProducts, read]);

  if (isLoading) {
    return (
      <div className="w-full mb-4 sm:mb-8 pt-2">
        <div className="rounded-xl overflow-hidden shadow-lg h-[250px] xs:h-[300px] sm:h-[350px] md:h-[400px] lg:h-[450px] xl:h-[500px] bg-gray-100 animate-pulse">
          <div className="h-full w-full flex items-center justify-center">
            <div className="w-12 h-12 rounded-full border-4 border-theme-accent-primary border-t-transparent animate-spin"></div>
          </div>
        </div>
      </div>
    );
  }

  return (
    <div className="w-full mb-4 sm:mb-8 pt-2">
      <AmazonStyleCarousel
        slides={slides.length > 0 ? slides : fallbackSlides}
        autoplayInterval={5000}
        className="rounded-xl overflow-hidden shadow-lg transform transition-all duration-700 hover:shadow-xl"
      />
    </div>
  )
}
