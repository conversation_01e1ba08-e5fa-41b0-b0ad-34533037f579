import { Toaster } from "../components/ui/toaster";
import Footer from "../components/utils/Footer";
import NavBar from "../components/utils/Navbar";
import React, { Suspense } from "react";
import { useSearchParams } from "next/navigation";
import { ArrowLeft } from "lucide-react";
import Link from "next/link";

const MainHOFContent = ({ children }: { children: React.ReactNode }) => {
  const searchParams = useSearchParams();
  const callbackUrl = searchParams.get("callbackUrl");

  return (
    <div className="min-h-screen flex flex-col bg-theme-homepage w-full">
      <NavBar />
      <div className="w-full max-w-full mx-auto px-4 sm:px-6 lg:px-8 flex-grow">
        {Boolean(callbackUrl) && (
          <div className="my-3 flex items-center">
            <Link
              href={typeof callbackUrl === "string" ? callbackUrl?.slice(1) : "/"}
              className="flex items-center gap-1 text-gray-600 hover:text-gray-900 transition-colors duration-200"
            >
              <ArrowLeft className="h-4 w-4" />
              <span className="text-sm">Back</span>
            </Link>
          </div>
        )}
        {children}
      </div>
      <Footer />
      <Toaster />
    </div>
  );
};

const MainHOF = ({ children }: { children: React.ReactNode }) => {
  return (
    <Suspense>
      <MainHOFContent>{children}</MainHOFContent>
    </Suspense>
  );
};

export default MainHOF;
