'use client';

import { useEffect, useState } from 'react';
import { useAuthSession } from '@/hooks/useAuthSession';
import { Alert, AlertDescription } from '@/components/ui/alert';
import { Button } from '@/components/ui/button';
import { AlertTriangle, RefreshCw, LogOut } from 'lucide-react';

/**
 * Component that handles session refresh errors and provides user feedback
 */
export const SessionErrorHandler = () => {
  const { hasRefreshError, signOutWithCleanup, refreshSession } = useAuthSession();
  const [showError, setShowError] = useState(false);
  const [isRefreshing, setIsRefreshing] = useState(false);

  useEffect(() => {
    if (hasRefreshError) {
      setShowError(true);
    }
  }, [hasRefreshError]);

  const handleRefreshSession = async () => {
    setIsRefreshing(true);
    try {
      await refreshSession();
      // If refresh is successful, the error should be cleared
      if (!hasRefreshError) {
        setShowError(false);
      }
    } catch (error) {
      console.error('Failed to refresh session:', error);
    } finally {
      setIsRefreshing(false);
    }
  };

  const handleSignOut = async () => {
    await signOutWithCleanup();
  };

  if (!showError || !hasRefreshError) {
    return null;
  }

  return (
    <div className="fixed top-4 right-4 z-50 max-w-md">
      <Alert variant="destructive" className="border-red-200 bg-red-50">
        <AlertTriangle className="h-4 w-4" />
        <AlertDescription className="mt-2">
          <div className="space-y-3">
            <p className="text-sm font-medium">
              Your session has expired and could not be refreshed automatically.
            </p>
            <p className="text-xs text-red-600">
              Please try refreshing your session or sign in again to continue.
            </p>
            <div className="flex gap-2">
              <Button
                size="sm"
                variant="outline"
                onClick={handleRefreshSession}
                disabled={isRefreshing}
                className="text-xs"
              >
                {isRefreshing ? (
                  <>
                    <RefreshCw className="h-3 w-3 mr-1 animate-spin" />
                    Refreshing...
                  </>
                ) : (
                  <>
                    <RefreshCw className="h-3 w-3 mr-1" />
                    Refresh Session
                  </>
                )}
              </Button>
              <Button
                size="sm"
                variant="destructive"
                onClick={handleSignOut}
                className="text-xs"
              >
                <LogOut className="h-3 w-3 mr-1" />
                Sign Out
              </Button>
            </div>
          </div>
        </AlertDescription>
      </Alert>
    </div>
  );
};

export default SessionErrorHandler;
