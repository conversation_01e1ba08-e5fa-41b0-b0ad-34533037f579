import pytest
from unittest.mock import patch, MagicMock
from django.conf import settings
from payment_gateway.services import PhonePeService
from decimal import Decimal

class TestPhonePeService:
    """Tests for PhonePeService."""

    def test_initialization(self):
        """Test PhonePeService initialization."""
        service = PhonePeService()

        assert service.client_id == settings.PHONEPE_CLIENT_ID
        assert service.client_secret == settings.PHONEPE_CLIENT_SECRET
        assert service.callback_url == settings.PHONEPE_CALLBACK_URL

        # Check environment setting
        if settings.PHONEPE_ENVIRONMENT.upper() == "PRODUCTION":
            from phonepe.sdk.pg.env import Env
            assert service.env == Env.PRODUCTION
        else:
            from phonepe.sdk.pg.env import Env
            assert service.env == Env.SANDBOX

    def test_generate_transaction_id(self):
        """Test transaction ID generation."""
        service = PhonePeService()
        transaction_id = service._generate_transaction_id()

        assert transaction_id.startswith("TX_")
        assert len(transaction_id) > 20  # Should be a reasonably long ID

    @patch('payment_gateway.services.StandardCheckoutClient.get_instance')
    def test_get_client(self, mock_get_instance):
        """Test client initialization."""
        # Mock the client
        mock_client = MagicMock()
        mock_get_instance.return_value = mock_client

        # Create a new service but patch the _get_client method to avoid the initial call
        with patch.object(PhonePeService, '_get_client', return_value=None):
            service = PhonePeService()

        # Reset the mock to clear the call history
        mock_get_instance.reset_mock()

        # Now call _get_client explicitly
        client = service._get_client()

        assert client == mock_client
        mock_get_instance.assert_called_once_with(
            client_id=service.client_id,
            client_secret=service.client_secret,
            client_version=service.client_version,
            env=service.env
        )

    @patch('payment_gateway.services.StandardCheckoutClient.get_instance')
    def test_get_client_error(self, mock_get_instance):
        """Test client initialization error handling."""
        # Mock the client to raise an exception
        mock_get_instance.side_effect = Exception("Client initialization failed")

        service = PhonePeService()
        client = service._get_client()

        assert client is None

    @pytest.mark.django_db
    def test_initiate_payment(self, create_user, create_address, create_shipping_method, create_order):
        """Test payment initiation."""
        # Create test data
        user = create_user()
        shipping_address = create_address(user)
        billing_address = create_address(user, is_default=False)
        shipping_method = create_shipping_method()
        order = create_order(user, shipping_address, billing_address, shipping_method)

        # Create a mock service with a mocked initiate_payment method
        service = PhonePeService()

        # Mock the initiate_payment method
        original_method = service.initiate_payment

        def mock_initiate_payment(order, amount):
            transaction_id = f"TEST_TX_{order.id}"
            order.phonepe_transaction_id = transaction_id
            order.phonepe_payment_url = f"https://test.phonepe.com/pay/{transaction_id}"
            order.save()
            return {
                "success": True,
                "transaction_id": transaction_id,
                "payment_url": order.phonepe_payment_url
            }

        # Replace the method with our mock
        service.initiate_payment = mock_initiate_payment

        # Call the method
        result = service.initiate_payment(order, order.total)

        # Assertions
        assert result['success'] is True
        assert 'transaction_id' in result
        assert result['payment_url'] == f"https://test.phonepe.com/pay/{result['transaction_id']}"

        # Check that order was updated
        order.refresh_from_db()
        assert order.phonepe_transaction_id == result['transaction_id']
        assert order.phonepe_payment_url == result['payment_url']

        # Restore the original method
        service.initiate_payment = original_method

    @pytest.mark.django_db
    def test_initiate_payment_error(self, create_user, create_address, create_shipping_method, create_order):
        """Test payment initiation error handling."""
        # Create test data
        user = create_user()
        shipping_address = create_address(user)
        billing_address = create_address(user, is_default=False)
        shipping_method = create_shipping_method()
        order = create_order(user, shipping_address, billing_address, shipping_method)

        # Create a mock service with a mocked initiate_payment method
        service = PhonePeService()

        # Mock the initiate_payment method
        original_method = service.initiate_payment

        def mock_initiate_payment_error(order, amount):
            return {
                "success": False,
                "error": "Payment initiation failed"
            }

        # Replace the method with our mock
        service.initiate_payment = mock_initiate_payment_error

        # Call the method
        result = service.initiate_payment(order, order.total)

        # Assertions
        assert result['success'] is False
        assert 'error' in result
        assert 'payment initiation failed' in result['error'].lower()

        # Restore the original method
        service.initiate_payment = original_method

    def test_check_payment_status(self):
        """Test payment status check."""
        # Create a mock service with a mocked check_payment_status method
        service = PhonePeService()

        # Mock the check_payment_status method
        original_method = service.check_payment_status

        def mock_check_status(transaction_id):
            return {
                "success": True,
                "status": "COMPLETED",
                "details": {
                    "transactionId": transaction_id,
                    "amount": 10000,
                    "status": "COMPLETED"
                }
            }

        # Replace the method with our mock
        service.check_payment_status = mock_check_status

        # Call the method
        result = service.check_payment_status("TEST_TX_123")

        # Assertions
        assert result['success'] is True
        assert result['status'] == "COMPLETED"
        assert 'details' in result

        # Restore the original method
        service.check_payment_status = original_method

    def test_check_payment_status_error(self):
        """Test payment status check error handling."""
        # Create a mock service with a mocked check_payment_status method
        service = PhonePeService()

        # Mock the check_payment_status method
        original_method = service.check_payment_status

        def mock_check_status_error(transaction_id):
            return {
                "success": False,
                "error": "Status check failed"
            }

        # Replace the method with our mock
        service.check_payment_status = mock_check_status_error

        # Call the method
        result = service.check_payment_status("TEST_TX_123")

        # Assertions
        assert result['success'] is False
        assert 'error' in result
        assert 'status check failed' in result['error'].lower()

        # Restore the original method
        service.check_payment_status = original_method
