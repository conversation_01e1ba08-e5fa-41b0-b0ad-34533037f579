/**
 * This script generates multiple sizes of the logo for different platforms
 * Run with: node scripts/generate-logo-sizes.js
 */

const fs = require('fs');
const path = require('path');
const sharp = require('sharp');

// Define the source logo path
const sourceLogo = path.join(__dirname, '../public/logotriumph.png');

// Define the output directory
const outputDir = path.join(__dirname, '../public/logo');

// Create the output directory if it doesn't exist
if (!fs.existsSync(outputDir)) {
  fs.mkdirSync(outputDir, { recursive: true });
}

// Define the sizes to generate
const sizes = [
  { width: 16, height: 16, name: 'favicon-16x16.png' },
  { width: 32, height: 32, name: 'favicon-32x32.png' },
  { width: 48, height: 48, name: 'favicon-48x48.png' },
  { width: 64, height: 64, name: 'favicon-64x64.png' },
  { width: 96, height: 96, name: 'favicon-96x96.png' },
  { width: 128, height: 128, name: 'favicon-128x128.png' },
  { width: 192, height: 192, name: 'logo-192x192.png' },
  { width: 256, height: 256, name: 'logo-256x256.png' },
  { width: 384, height: 384, name: 'logo-384x384.png' },
  { width: 512, height: 512, name: 'logo-512x512.png' },
  { width: 1024, height: 1024, name: 'logo-1024x1024.png' },
  { width: 180, height: 180, name: 'apple-touch-icon.png' },
  { width: 192, height: 192, name: 'android-chrome-192x192.png' },
  { width: 512, height: 512, name: 'android-chrome-512x512.png' },
  { width: 1200, height: 630, name: 'og-image.png' }, // Open Graph image
  { width: 1024, height: 512, name: 'twitter-card.png' }, // Twitter card image
];

// Generate each size
async function generateLogos() {
  try {
    // Check if source logo exists
    if (!fs.existsSync(sourceLogo)) {
      console.error(`Source logo not found at ${sourceLogo}`);
      return;
    }

    console.log(`Generating logo sizes from ${sourceLogo}...`);

    // Process each size
    for (const size of sizes) {
      const outputPath = path.join(outputDir, size.name);
      
      await sharp(sourceLogo)
        .resize(size.width, size.height)
        .toFile(outputPath);
      
      console.log(`Generated ${size.name} (${size.width}x${size.height})`);
    }

    // Also generate an ICO file for favicon
    await sharp(sourceLogo)
      .resize(32, 32)
      .toFile(path.join(__dirname, '../public/favicon.ico'));
    
    console.log('Generated favicon.ico');

    console.log('All logo sizes generated successfully!');
  } catch (error) {
    console.error('Error generating logo sizes:', error);
  }
}

// Run the function
generateLogos();
