'use client';

import { usePathname } from 'next/navigation';
import Script from 'next/script';

interface JsonLdProps {
  organizationName?: string;
  url?: string;
  logo?: string;
  siteTitle?: string;
  siteDescription?: string;
}

export default function JsonLd({
  organizationName = 'Triumph Enterprises',
  url,
  logo,
  siteTitle = 'Triumph Enterprises | Premium Hardware & Security Solutions',
  siteDescription = 'Triumph Enterprises offers high-quality hardware, security products, and home solutions. Shop our wide range of door locks, digital safes, and more.',
}: JsonLdProps) {
  const pathname = usePathname();

  // Dynamically determine the base URL based on the current environment
  const baseUrl = url || (typeof window !== 'undefined' ? window.location.origin : '');
  const logoUrl = logo || `${baseUrl}/logotriumph.png`;
  const currentUrl = `${baseUrl}${pathname}`;

  const organizationSchema = {
    '@context': 'https://schema.org',
    '@type': 'Organization',
    name: organizationName,
    url: baseUrl,
    logo: logoUrl,
    sameAs: [
      'https://facebook.com/triumphenterprises',
      'https://twitter.com/triumphenterprises',
      'https://instagram.com/triumphenterprises',
    ],
    contactPoint: {
      '@type': 'ContactPoint',
      telephone: '+91-1234567890',
      contactType: 'customer service',
      availableLanguage: ['English', 'Hindi'],
    },
  };

  const websiteSchema = {
    '@context': 'https://schema.org',
    '@type': 'WebSite',
    name: siteTitle,
    url: baseUrl,
    description: siteDescription,
    potentialAction: {
      '@type': 'SearchAction',
      target: `${baseUrl}/shop?search={search_term_string}`,
      'query-input': 'required name=search_term_string',
    },
  };

  const breadcrumbSchema = {
    '@context': 'https://schema.org',
    '@type': 'BreadcrumbList',
    itemListElement: [
      {
        '@type': 'ListItem',
        position: 1,
        name: 'Home',
        item: baseUrl,
      },
      ...(pathname !== '/'
        ? [
            {
              '@type': 'ListItem',
              position: 2,
              name: pathname.split('/')[1].charAt(0).toUpperCase() + pathname.split('/')[1].slice(1),
              item: currentUrl,
            },
          ]
        : []),
    ],
  };

  return (
    <>
      <Script
        id="organization-schema"
        type="application/ld+json"
        dangerouslySetInnerHTML={{ __html: JSON.stringify(organizationSchema) }}
      />
      <Script
        id="website-schema"
        type="application/ld+json"
        dangerouslySetInnerHTML={{ __html: JSON.stringify(websiteSchema) }}
      />
      <Script
        id="breadcrumb-schema"
        type="application/ld+json"
        dangerouslySetInnerHTML={{ __html: JSON.stringify(breadcrumbSchema) }}
      />
    </>
  );
}
