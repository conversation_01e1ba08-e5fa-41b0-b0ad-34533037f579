# Data Deletion Request Email Notifications

This document describes the email notification system implemented for data deletion requests in the e-commerce API.

## Overview

The system now sends email notifications for data deletion requests to both users and administrators, ensuring transparency and compliance with data protection regulations.

## Features Implemented

### 1. Email Notifications for Deletion Requests ✅

When a user submits a data deletion request via `POST /api/v1/users/privacy/delete/`, the system now:

- **Sends confirmation email to the user** - Confirms receipt of their deletion request
- **Sends notification email to administrators** - Alerts admins about new deletion requests
- **Prevents duplicate requests** - Users cannot submit multiple pending requests

### 2. Status Update Notifications ✅

When deletion requests are processed, users receive email updates for:

- **Request Completed** - When data has been successfully deleted
- **Request Rejected** - When request cannot be processed (with reason)
- **Request In Progress** - When request is being processed

### 3. Automated Processing Notifications ✅

The security cleanup command automatically sends emails when:

- Processing pending deletion requests after grace period
- Completing automated anonymization

## Email Templates

### User Confirmation Email
- **Template**: `emails/privacy/deletion_confirmation.html`
- **Subject**: "Data Deletion Request Received - Triumph Enterprises"
- **Content**: Request details, next steps, important warnings, contact information

### Admin Notification Email
- **Template**: `emails/privacy/deletion_admin_notification.html`
- **Subject**: "New Data Deletion Request - User: {email}"
- **Content**: User details, request information, compliance requirements, action items

### Status Update Email
- **Template**: `emails/privacy/deletion_status_update.html`
- **Subject**: "Data Deletion Request Update - Triumph Enterprises"
- **Content**: Status-specific information (completed/rejected/in-progress)

## Implementation Details

### New Functions Added

#### In `users/utils.py`:
- `send_deletion_request_emails(deletion_request)` - Main function to send both user and admin emails
- `send_deletion_confirmation_email(deletion_request)` - User confirmation email
- `send_deletion_admin_notification(deletion_request)` - Admin notification email
- `send_deletion_status_update_email(deletion_request)` - Status update email

#### In `users/models.py`:
- `complete_deletion(processed_by, notes)` - Enhanced to send email notifications
- `reject_deletion(processed_by, notes)` - New method with email notifications
- `update_status(new_status, processed_by, notes)` - Generic status update with emails

### Updated Views

#### `DataDeletionView.post()` in `users/privacy_views.py`:
- Added email sending after successful deletion request creation
- Includes error handling to ensure request creation succeeds even if emails fail
- Updated response message to mention email notifications

## Email Recipients

### User Emails
- Sent to the user who submitted the deletion request

### Admin Emails
- Sent to: `settings.DEFAULT_FROM_EMAIL` (<EMAIL>)
- CC: `<EMAIL>`

## Error Handling

- Email failures are logged but don't prevent deletion request creation
- Graceful degradation ensures core functionality remains available
- Detailed logging for troubleshooting email issues

## Testing

### Test File: `users/tests/test_deletion_emails.py`

Tests cover:
- Email sending on deletion request creation
- Email content validation
- Status update email notifications
- Error handling scenarios

## API Response Changes

The deletion request endpoint now returns:

```json
{
    "message": "Deletion request created successfully",
    "request_id": 123,
    "status": "PENDING",
    "note": "Your request will be processed within 30 days as per data protection regulations. You will receive email updates about the status of your request."
}
```

## Compliance Features

- **GDPR Compliance**: 30-day processing timeline mentioned in emails
- **Transparency**: Clear communication about what happens next
- **User Rights**: Information about contacting support if needed
- **Audit Trail**: All email activities are logged for compliance

## Configuration

Email settings are configured in `backend/settings.py`:

```python
EMAIL_BACKEND = 'django.core.mail.backends.smtp.EmailBackend'
EMAIL_HOST = 'smtp.hostinger.com'
EMAIL_PORT = 465
EMAIL_USE_SSL = True
DEFAULT_FROM_EMAIL = '<EMAIL>'
```

## Security Considerations

- Email templates are sanitized to prevent XSS
- Sensitive information is not included in email content
- Email sending failures are logged for security monitoring
- Rate limiting prevents email spam

## Future Enhancements

Potential improvements:
- Email templates with company branding
- Multi-language support for emails
- Email delivery status tracking
- Admin dashboard for managing deletion requests
- Automated reminders for pending requests

## Troubleshooting

### Common Issues:
1. **Emails not sending**: Check SMTP configuration in settings
2. **Template not found**: Ensure template files are in correct directory
3. **Import errors**: Verify all dependencies are properly imported

### Logs to Check:
- Django logs: `logs/django.log`
- Security logs: `logs/security.log`
- Console output for immediate debugging
