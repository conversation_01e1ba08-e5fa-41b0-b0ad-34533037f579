import { useForm } from "react-hook-form";
import { zodResolver } from "@hookform/resolvers/zod";
import { useEffect, useState } from "react";
import * as z from "zod";
import {
  Form,
  FormControl,
  FormDescription,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from "@/components/ui/form";
import { Input } from "@/components/ui/input";
import { Button } from "@/components/ui/button";
import { Textarea } from "@/components/ui/textarea";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import { Switch } from "@/components/ui/switch";
import { ImageUpload } from "./ImageUpload";
import { useProducts } from "@/hooks/useProducts";
import useApi from "@/hooks/useApi";
import { MAIN_URL, CATEGORIES, BRANDS, GST_RATES, SUBCATEGORIES_BY_CATEGORY } from "@/constant/urls";
import { ProductFormData, Category, Brand, GST, SubCategory } from "@/types/product";
import { useToast } from "@/components/ui/use-toast";
import { Loader2 } from "lucide-react";

const formSchema = z.object({
  name: z.string().min(1, "Product name is required"),
  description: z.string().min(1, "Description is required"),
  price: z.string().regex(/^\d+(\.\d{1,2})?$/, "Invalid price format"),
  category: z.number().min(1, "Category is required"),
  subcategory: z.number().optional(),
  brand: z.number().optional(),
  gst: z.number().optional(),
  stock: z.string().regex(/^\d+$/, "Stock must be a number"),
  is_active: z.boolean(),
  images: z.array(z.string()),
});

interface ProductFormProps {
  onSubmit: (data: ProductFormData) => void;
  productId?: string;
  initialData?: Partial<ProductFormData>;
}

export const ProductForm = ({ onSubmit, productId, initialData }: ProductFormProps) => {
  const [categories, setCategories] = useState<Category[]>([]);
  const [subcategories, setSubcategories] = useState<SubCategory[]>([]);
  const [brands, setBrands] = useState<Brand[]>([]);
  const [gstRates, setGstRates] = useState<GST[]>([]);
  const { loading: productLoading } = useProducts();
  const { read } = useApi(MAIN_URL);
  const { toast } = useToast();

  const form = useForm<z.infer<typeof formSchema>>({
    resolver: zodResolver(formSchema),
    defaultValues: {
      name: initialData?.name || "",
      description: initialData?.description || "",
      price: initialData?.price || "",
      category: initialData?.category || undefined,
      subcategory: initialData?.subcategory || undefined,
      brand: initialData?.brand || undefined,
      gst: initialData?.gst || undefined,
      stock: initialData?.stock || "",
      is_active: initialData?.is_active ?? true,
      images: initialData?.images || [],
    },
  });

  // Load categories, brands, and GST rates
  useEffect(() => {
    const loadData = async () => {
      try {
        const [categoriesResponse, brandsResponse, gstResponse] = await Promise.all([
          read(CATEGORIES),
          read(BRANDS),
          read(GST_RATES)
        ]);

        if (Array.isArray(categoriesResponse)) {
          setCategories(categoriesResponse);
        }
        if (Array.isArray(brandsResponse)) {
          setBrands(brandsResponse);
        }
        if (Array.isArray(gstResponse)) {
          setGstRates(gstResponse);
        }
      } catch {
        toast({
          title: "Error",
          description: "Failed to load form data",
          variant: "destructive",
        });
      }
    };

    loadData();
  }, [read, toast]);

  // Load subcategories when category changes
  const selectedCategory = form.watch("category");
  useEffect(() => {
    if (selectedCategory) {
      const loadSubcategories = async () => {
        try {
          const response = await read(SUBCATEGORIES_BY_CATEGORY(selectedCategory));
          if (Array.isArray(response)) {
            setSubcategories(response);
          }
        } catch {
          toast({
            title: "Error",
            description: "Failed to load subcategories",
            variant: "destructive",
          });
        }
      };
      loadSubcategories();
    } else {
      setSubcategories([]);
      form.setValue("subcategory", undefined);
    }
  }, [selectedCategory, read, toast, form]);

  // Load subcategories for initial data (when editing existing product)
  useEffect(() => {
    if (initialData?.category && initialData?.subcategory && typeof initialData.category === 'number') {
      const loadInitialSubcategories = async () => {
        try {
          const response = await read(SUBCATEGORIES_BY_CATEGORY(initialData.category!));
          if (Array.isArray(response)) {
            setSubcategories(response);
          }
        } catch {
          toast({
            title: "Error",
            description: "Failed to load subcategories for existing product",
            variant: "destructive",
          });
        }
      };
      loadInitialSubcategories();
    }
  }, [initialData?.category, initialData?.subcategory, read, toast]);

  return (
    <Form {...form}>
      <form onSubmit={form.handleSubmit(onSubmit)} className="space-y-6">
        <FormField
          control={form.control}
          name="name"
          render={({ field }) => (
            <FormItem>
              <FormLabel>Product Name</FormLabel>
              <FormControl>
                <Input placeholder="Product name" {...field} />
              </FormControl>
              <FormMessage />
            </FormItem>
          )}
        />

        <FormField
          control={form.control}
          name="description"
          render={({ field }) => (
            <FormItem>
              <FormLabel>Description</FormLabel>
              <FormControl>
                <Textarea placeholder="Product description" {...field} />
              </FormControl>
              <FormMessage />
            </FormItem>
          )}
        />

        <div className="grid gap-4 md:grid-cols-2">
          <FormField
            control={form.control}
            name="price"
            render={({ field }) => (
              <FormItem>
                <FormLabel>Price (MRP including GST)</FormLabel>
                <FormControl>
                  <Input placeholder="0.00" {...field} />
                </FormControl>
                <FormDescription>
                  Maximum Retail Price including GST
                </FormDescription>
                <FormMessage />
              </FormItem>
            )}
          />

          <FormField
            control={form.control}
            name="stock"
            render={({ field }) => (
              <FormItem>
                <FormLabel>Stock</FormLabel>
                <FormControl>
                  <Input type="number" placeholder="0" {...field} />
                </FormControl>
                <FormMessage />
              </FormItem>
            )}
          />
        </div>

        <div className="grid gap-4 md:grid-cols-2">
          <FormField
            control={form.control}
            name="category"
            render={({ field }) => (
              <FormItem>
                <FormLabel>Category</FormLabel>
                <Select
                  onValueChange={(value) => field.onChange(Number(value))}
                  value={field.value?.toString()}
                >
                  <FormControl>
                    <SelectTrigger>
                      <SelectValue placeholder="Select category" />
                    </SelectTrigger>
                  </FormControl>
                  <SelectContent>
                    {categories.map((category) => (
                      <SelectItem key={category.id} value={category.id.toString()}>
                        {category.name}
                      </SelectItem>
                    ))}
                  </SelectContent>
                </Select>
                <FormMessage />
              </FormItem>
            )}
          />

          <FormField
            control={form.control}
            name="subcategory"
            render={({ field }) => (
              <FormItem>
                <FormLabel>Subcategory</FormLabel>
                <Select
                  onValueChange={(value) => field.onChange(value ? Number(value) : undefined)}
                  value={field.value?.toString() || ""}
                  disabled={!selectedCategory || subcategories.length === 0}
                >
                  <FormControl>
                    <SelectTrigger>
                      <SelectValue placeholder={
                        !selectedCategory
                          ? "Select category first"
                          : subcategories.length === 0
                            ? "Loading subcategories..."
                            : "Select subcategory (optional)"
                      } />
                    </SelectTrigger>
                  </FormControl>
                  <SelectContent>
                    {subcategories.map((subcategory) => (
                      <SelectItem key={subcategory.id} value={subcategory.id.toString()}>
                        {subcategory.name}
                      </SelectItem>
                    ))}
                  </SelectContent>
                </Select>
                <FormMessage />
              </FormItem>
            )}
          />
        </div>

        <div className="grid gap-4 md:grid-cols-2">
          <FormField
            control={form.control}
            name="brand"
            render={({ field }) => (
              <FormItem>
                <FormLabel>Brand</FormLabel>
                <Select
                  onValueChange={(value) => field.onChange(value ? Number(value) : undefined)}
                  value={field.value?.toString()}
                >
                  <FormControl>
                    <SelectTrigger>
                      <SelectValue placeholder="Select brand (optional)" />
                    </SelectTrigger>
                  </FormControl>
                  <SelectContent>
                    {brands.map((brand) => (
                      <SelectItem key={brand.id} value={brand.id.toString()}>
                        {brand.name}
                      </SelectItem>
                    ))}
                  </SelectContent>
                </Select>
                <FormMessage />
              </FormItem>
            )}
          />

          <FormField
            control={form.control}
            name="gst"
            render={({ field }) => (
              <FormItem>
                <FormLabel>GST Rate</FormLabel>
                <Select
                  onValueChange={(value) => field.onChange(value ? Number(value) : undefined)}
                  value={field.value?.toString()}
                >
                  <FormControl>
                    <SelectTrigger>
                      <SelectValue placeholder="Select GST rate (optional)" />
                    </SelectTrigger>
                  </FormControl>
                  <SelectContent>
                    {gstRates.map((gst) => (
                      <SelectItem key={gst.id} value={gst.id.toString()}>
                        {gst.name} - {gst.rate}%
                      </SelectItem>
                    ))}
                  </SelectContent>
                </Select>
                <FormDescription>
                  If not selected, default 18% GST will be applied
                </FormDescription>
                <FormMessage />
              </FormItem>
            )}
          />
        </div>

        <FormField
          control={form.control}
          name="is_active"
          render={({ field }) => (
            <FormItem className="flex items-center justify-between rounded-lg border p-4">
              <div className="space-y-0.5">
                <FormLabel>Product Status</FormLabel>
                <FormDescription>
                  Make this product active and visible to customers
                </FormDescription>
              </div>
              <FormControl>
                <Switch
                  checked={field.value}
                  onCheckedChange={field.onChange}
                />
              </FormControl>
            </FormItem>
          )}
        />

        <FormField
          control={form.control}
          name="images"
          render={({ field }) => (
            <FormItem>
              <FormLabel>Images</FormLabel>
              <FormControl>
                <ImageUpload
                  value={field.value}
                  onChange={field.onChange}
                />
              </FormControl>
              <FormMessage />
            </FormItem>
          )}
        />

        <Button type="submit" className="w-full" disabled={productLoading}>
          {productLoading && <Loader2 className="mr-2 h-4 w-4 animate-spin" />}
          {productId ? "Update Product" : "Create Product"}
        </Button>
      </form>
    </Form>
  );
};