// import { ArrowDownWideNarrow } from "lucide-react";
// import {
//   Select,
//   SelectContent,
//   SelectItem,
//   SelectTrigger,
//   SelectValue,
// } from "../../components/ui/select";

// interface SortingOptionsProps {
//   value: string;
//   onChange: (value: string) => void;
// }
// const selectedLabel = (value:string) => {
//   switch (value) {
//     case "-created_at":
//       return "Newest";
//     case "price":
//       return "Price: Low to High";
//     case "-price":
//       return "Price: High to Low";
//     case "name":
//       return "A to Z";
//     case "-name":
//       return "Z to A";
//     default:
//       return "Sort by"; // Fallback placeholder if value is empty or undefined
//   }
// };

// export const SortingOptions = ({ value, onChange }: SortingOptionsProps) => {
//   return (
//     <Select value={value} onValueChange={onChange}>
//       <SelectTrigger className="w-12 md:w-[180px]">
//         <ArrowDownWideNarrow className="md:hidden" />
//         <span className="hidden md:inline">{selectedLabel(value)}</span>
//       </SelectTrigger>
//       <SelectContent>
//         <SelectItem value="-created_at">Newest</SelectItem>
//         <SelectItem value="price">Price: Low to High</SelectItem>
//         <SelectItem value="-price">Price: High to Low</SelectItem>
//         <SelectItem value="name">A to Z</SelectItem>
//         <SelectItem value="-name">Z to A</SelectItem>
//       </SelectContent>
//     </Select>
//   );
// };

import React, { useRef } from "react";
import { ArrowDownWideNarrow } from "lucide-react";

interface SortingOptionsProps {
  value: string;
  onChange: (value: string) => void;
}

const selectedLabel = (value: string) => {
  switch (value) {
    case "-created_at":
      return "Newest";
    case "price":
      return "Price: Low to High";
    case "-price":
      return "Price: High to Low";
    case "name":
      return "A to Z";
    case "-name":
      return "Z to A";
    default:
      return "Sort by";
  }
};

const options = [
  { value: "-created_at", label: "Newest" },
  { value: "price", label: "Price: Low to High" },
  { value: "-price", label: "Price: High to Low" },
  { value: "name", label: "A to Z" },
  { value: "-name", label: "Z to A" },
];

export const SortingOptions = ({ value, onChange }: SortingOptionsProps) => {
  const selectRef = useRef<HTMLSelectElement>(null);

  // Custom styles to hide the default select arrow
  const customSelectStyles = {
    WebkitAppearance: "none",
    MozAppearance: "none",
    appearance: "none",
  } as const;

  return (
    <div className="relative">
      <div className="pointer-events-none absolute inset-y-0 left-0 md:hidden flex items-center px-3">
        <ArrowDownWideNarrow className="h-4 w-4" />
      </div>
      <select
        ref={selectRef}
        value={value}
        onChange={(e) => onChange(e.target.value)}
        className="block w-8 md:w-auto h-10 pl-2 pr-8 py-2 text-sm border rounded-md bg-white 
                 hover:bg-gray-50 focus:outline-none
                 cursor-pointer text-gray-900"
        style={customSelectStyles}
      >
        {options.map((option) => (
          <option key={option.value} value={option.value} className="py-2 px-3">
            {option.label}
          </option>
        ))}
      </select>
    </div>
  );
};

export default SortingOptions;
