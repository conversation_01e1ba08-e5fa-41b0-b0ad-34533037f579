import { Info } from "lucide-react";
import { useState } from "react";

interface GSTBreakdownProps {
  gstAmount: number;
  cgstAmount?: number;
  sgstAmount?: number;
  igstAmount?: number;
  showDetails?: boolean;
  gstBreakdown?: {
    total_gst_amount: number;
    total_cgst_amount: number;
    total_sgst_amount: number;
    total_igst_amount: number;
    is_inter_state?: boolean;
    item_details?: Array<{
      product: any;
      quantity: number;
      gst_rate: number;
      gst_amount: number;
      cgst_amount?: number;
      sgst_amount?: number;
      igst_amount?: number;
      hsn_code: string;
    }>;
  };
  billingState?: string;
  shippingState?: string;
  className?: string;
}

export const GSTBreakdown = ({
  gstAmount,
  cgstAmount,
  sgstAmount,
  igstAmount,
  showDetails = false,
  gstBreakdown,
  billingState,
  shippingState,
  className = "",
}: GSTBreakdownProps) => {
  const [showGstDetails, setShowGstDetails] = useState(false);

  // Determine transaction type
  const isInterState = billingState && shippingState 
    ? billingState.toLowerCase().trim() !== shippingState.toLowerCase().trim()
    : gstBreakdown?.is_inter_state || (igstAmount && igstAmount > 0) || false;

  // Calculate GST amounts based on transaction type
  const calculatedGstAmount = gstBreakdown?.total_gst_amount || gstAmount;
  
  let calculatedCgstAmount = 0;
  let calculatedSgstAmount = 0;
  let calculatedIgstAmount = 0;

  if (isInterState) {
    // Inter-state: Only IGST
    calculatedIgstAmount = gstBreakdown?.total_igst_amount || igstAmount || calculatedGstAmount;
  } else {
    // Intra-state: CGST + SGST (split equally)
    calculatedCgstAmount = gstBreakdown?.total_cgst_amount || cgstAmount || (calculatedGstAmount / 2);
    calculatedSgstAmount = gstBreakdown?.total_sgst_amount || sgstAmount || (calculatedGstAmount / 2);
  }

  // Check if we have dynamic GST rates
  const hasDynamicGst = gstBreakdown?.item_details && gstBreakdown.item_details.length > 0;
  const uniqueGstRates = hasDynamicGst
    ? [...new Set(gstBreakdown!.item_details.map(item => item.gst_rate))]
    : [18]; // Default rate

  const displayGstRate = uniqueGstRates.length === 1
    ? `${uniqueGstRates[0]}%`
    : 'Mixed Rates';

  return (
    <div className={`space-y-2 ${className}`}>
      {/* Main GST Line */}
      <div className="flex justify-between items-center">
        <div className="flex items-center gap-2">
          <span className="text-gray-600">GST ({displayGstRate})</span>
          {showDetails && (
            <button
              onClick={() => setShowGstDetails(!showGstDetails)}
              className="text-blue-600 hover:text-blue-800"
            >
              <Info className="h-4 w-4" />
            </button>
          )}
        </div>
        <span className="font-medium">₹{calculatedGstAmount.toFixed(2)}</span>
      </div>

      {/* GST Breakdown Details */}
      {showGstDetails && showDetails && (
        <div className="ml-4 space-y-1 text-sm">
          {/* Transaction Type Indicator */}
          <div className="text-xs font-medium text-blue-600 mb-2">
            {isInterState ? '🔄 Inter-state Transaction' : '🏠 Intra-state Transaction'}
            {billingState && shippingState && (
              <div className="text-gray-500 mt-1">
                {isInterState 
                  ? `${billingState} → ${shippingState}` 
                  : `Within ${billingState}`}
              </div>
            )}
          </div>

          {/* Product-wise GST breakdown if available */}
          {hasDynamicGst && (
            <div className="space-y-2 mb-3">
              <div className="text-xs font-medium text-gray-700">Product-wise GST:</div>
              {gstBreakdown!.item_details.map((item, index) => (
                <div key={index} className="space-y-1">
                  <div className="flex justify-between text-xs text-gray-500">
                    <span className="truncate max-w-[120px]">
                      {item.product?.name || 'Product'} ({item.gst_rate}%)
                    </span>
                    <span>₹{item.gst_amount.toFixed(2)}</span>
                  </div>
                  {/* Show individual product GST breakdown */}
                  <div className="ml-2 text-xs text-gray-400">
                    {isInterState ? (
                      <div className="flex justify-between">
                        <span>IGST ({item.gst_rate}%)</span>
                        <span>₹{(item.igst_amount || item.gst_amount).toFixed(2)}</span>
                      </div>
                    ) : (
                      <>
                        <div className="flex justify-between">
                          <span>CGST ({(item.gst_rate / 2)}%)</span>
                          <span>₹{(item.cgst_amount || item.gst_amount / 2).toFixed(2)}</span>
                        </div>
                        <div className="flex justify-between">
                          <span>SGST ({(item.gst_rate / 2)}%)</span>
                          <span>₹{(item.sgst_amount || item.gst_amount / 2).toFixed(2)}</span>
                        </div>
                      </>
                    )}
                  </div>
                </div>
              ))}
              <div className="border-t pt-1 mt-2">
                <div className="text-xs font-medium text-gray-700">Total GST Breakdown:</div>
              </div>
            </div>
          )}

          {/* Standard GST breakdown following Indian GST rules */}
          {isInterState ? (
            <div className="flex justify-between text-gray-500">
              <span>IGST ({uniqueGstRates.length === 1 ? uniqueGstRates[0] : 'Mixed'}%)</span>
              <span>₹{calculatedIgstAmount.toFixed(2)}</span>
            </div>
          ) : (
            <>
              <div className="flex justify-between text-gray-500">
                <span>CGST ({uniqueGstRates.length === 1 ? (uniqueGstRates[0] / 2) : 'Mixed'}%)</span>
                <span>₹{calculatedCgstAmount.toFixed(2)}</span>
              </div>
              <div className="flex justify-between text-gray-500">
                <span>SGST ({uniqueGstRates.length === 1 ? (uniqueGstRates[0] / 2) : 'Mixed'}%)</span>
                <span>₹{calculatedSgstAmount.toFixed(2)}</span>
              </div>
            </>
          )}

          {/* GST Compliance Note */}
          <div className="text-xs text-gray-400 mt-2 pt-2 border-t">
            <p>
              {isInterState 
                ? 'Inter-state transaction: IGST applicable'
                : 'Intra-state transaction: CGST + SGST applicable'}
            </p>
          </div>
        </div>
      )}

      {/* Simple breakdown without details */}
      {!showDetails && (
        <div className="ml-4 space-y-1 text-sm text-gray-500">
          {isInterState ? (
            <div className="flex justify-between">
              <span>IGST ({uniqueGstRates.length === 1 ? uniqueGstRates[0] : 'Mixed'}%)</span>
              <span>₹{calculatedIgstAmount.toFixed(2)}</span>
            </div>
          ) : (
            <>
              <div className="flex justify-between">
                <span>CGST ({uniqueGstRates.length === 1 ? (uniqueGstRates[0] / 2) : 'Mixed'}%)</span>
                <span>₹{calculatedCgstAmount.toFixed(2)}</span>
              </div>
              <div className="flex justify-between">
                <span>SGST ({uniqueGstRates.length === 1 ? (uniqueGstRates[0] / 2) : 'Mixed'}%)</span>
                <span>₹{calculatedSgstAmount.toFixed(2)}</span>
              </div>
            </>
          )}
        </div>
      )}
    </div>
  );
};

export default GSTBreakdown;
