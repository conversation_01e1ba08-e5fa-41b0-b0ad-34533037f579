// import { useEffect, useState } from "react";
// import {
//   View,
//   StyleSheet,
//   FlatList,
//   Image,
//   ActivityIndicator,
//   Text,
// } from "react-native";
// import { SafeAreaView } from "react-native-safe-area-context";

// import ProductCard from "../components/components/ProductCard";
// import axios from "axios";
// import { BaseURL } from "@/constants/ApiEndpoint";
// import { addToCart } from "@/constants/ApiAddToCart";

// // Mock data



// export default function CategoriesProductScreen({ route, navigation }) {
//   const [product, setProduct] = useState([]);
//   const [isNext, setIsNext] = useState<boolean>(false);
//   const [page, setPage] = useState(1);
//   const [loading, setLoading] = useState<boolean>(false);
// const slug = route.params.category.slug

// console.log("rotess", route.params.category.slug)
//   const fetchProductDetails = async () => {
//     setLoading(true);
//     try {
        
//       const response = await axios.get(
//         `${BaseURL}/api/v1/products/categories/${slug}/products/?page=${page}`
//       );

//       console.log(response)
//       if (response.data.count > 0) {
//         setProduct((prev) => {
//           if (prev) {
//             return [...prev, ...response.data.results];
//           }
//           return [];
//         });

//         setIsNext(response.data.next ? true : false);
//       }
//       // setProduct();
//     } catch (error) {
//       console.log("error fetching product", error);
//     } finally {
//       setLoading(false);
//     }
//   };

//   const handleNext = () => {
//     if (isNext) {
//       setPage(page + 1);
//     }
//   };

//   useEffect(() => {
//     fetchProductDetails();
//   }, [page]);

//   const renderFooter = () => {
//     return loading ? (
//       <View
//         style={{
//           padding: 10,
//           justifyContent: "center",
//           alignItems: "center",
//         }}
//       >
//         <ActivityIndicator size="large" color="#0000ff" />
//         <Text style={{ marginTop: 5 }}>Loading more...</Text>
//       </View>
//     ) : null;
//   };

//   return (
//     <SafeAreaView style={styles.container}>
    
//       <FlatList
//         ListHeaderComponent={<></>}
//         data={product}
//         renderItem={({ item }) => (
//           <ProductCard
//             {...item}
//             onPress={() =>
//               navigation.navigate("ProductDetails", { product: item })
//             }
//             onAddToCart={() => addToCart(item?.id, 1)}
//             onWishlist={() => {}}
//           />
//         )}
//         numColumns={2}
//         keyExtractor={(item) => item?.id}
//         showsVerticalScrollIndicator={false}
//         contentContainerStyle={styles.productList}
//         columnWrapperStyle={styles.productRow}
//         scrollEventThrottle={16}
//         ListEmptyComponent={null}
//         onEndReachedThreshold={0.5}
//         onEndReached={handleNext}
//         ListFooterComponent={renderFooter}
//       />
//     </SafeAreaView>
//   );
// }

// const styles = StyleSheet.create({
//   container: {
//     flex: 1,
//     backgroundColor: "#ffffff",
//   },
//   header: {
//     paddingTop: 8,
//   },
//   bannerContainer: {
//     height: 200,
//     marginHorizontal: 16,
//     marginVertical: 16,
//     borderRadius: 12,
//     overflow: "hidden",
//   },
//   banner: {
//     width: "100%",
//     height: "100%",
//     resizeMode: "cover",
//   },
//   productList: {
//     paddingHorizontal: 8,
//     paddingBottom: 24,
//   },
//   productRow: {
//     justifyContent: "space-evenly",
//   },
// });

import { useEffect, useState } from "react";
import {
  View,
  StyleSheet,
  FlatList,
  Image,
  ActivityIndicator,
  Text,
} from "react-native";
import { SafeAreaView } from "react-native-safe-area-context";

import ProductCard from "../components/components/ProductCard";
import axios from "axios";
import { BaseURL } from "@/constants/ApiEndpoint";
import { addToCart } from "@/hooks/API";
import { useAuth } from "@/context/AuthContext";


// Define types for the API response
interface Product {
  id: number;
  name: string;
  slug: string;
  description: string;
  price: string;
  category: {
    id: number;
    name: string;
    slug: string;
    subcategories: any[];
  };
  stock: number;
  is_active: boolean;
  images: {
    id: number;
    image: string;
    is_primary: boolean;
  }[];
  variants: any[];
  average_rating: number | null;
  review_count: number;
  created_at: string;
  updated_at: string;
  brand: {
    id: number;
    name: string;
  };
  image: string;
}

interface ApiResponse {
  count: number;
  next: string | null;
  previous: string | null;
  results: {
    category: {
      id: number;
      name: string;
      slug: string;
    };
    products: Product[];
  };
}

export default function CategoriesProductScreen({ route, navigation }) {
  const [products, setProducts] = useState<Product[]>([]);
  const [isNextAvailable, setIsNextAvailable] = useState<boolean>(false);
  const [page, setPage] = useState(1);
  const [loading, setLoading] = useState<boolean>(false);
  const [categoryInfo, setCategoryInfo] = useState<any>(null);
  const {accessToken, isAuthenticated} = useAuth()
  const slug = route.params.category.slug;


  const fetchProductDetails = async () => {
    setLoading(true);
    try {
      const response = await axios.get<ApiResponse>(
        `${BaseURL}/api/v1/products/categories/${slug}/products/?page=${page}`
      );

      
      if (response.data && response.data.count > 0) {
        // Store category information from the first request
        if (page === 1) {
          setCategoryInfo(response.data.results.category);
        }
        
        // Extract the products array from the nested structure
        const newProducts = response.data.results.products || [];
        
        setProducts((prev) => {
          if (prev && prev.length > 0) {
            return [...prev, ...newProducts];
          }
          return newProducts;
        });

        // Update pagination state
        setIsNextAvailable(response.data.next ? true : false);
      }
    } catch (error) {
      console.log("Error fetching products:", error);
    } finally {
      setLoading(false);
    }
  };

  const handleNext = () => {
    if (isNextAvailable) {
      setPage(page + 1);
    }
  };

  useEffect(() => {
    fetchProductDetails();
  }, [page]);

  const renderFooter = () => {
    return loading ? (
      <View style={styles.loadingContainer}>
        <ActivityIndicator size="large" color="#0000ff" />
        <Text style={styles.loadingText}>Loading more products...</Text>
      </View>
    ) : null;
  };

  const renderHeader = () => {
    return categoryInfo ? (
      <View style={styles.categoryHeader}>
        <Text style={styles.categoryTitle}>{categoryInfo.name}</Text>
      </View>
    ) : null;
  };
  const handleAddToCart = async (productId:any, quantity:number, accessToken:string)=>{
    if (!isAuthenticated) {
      navigation.navigate("Auth", {
        returnTo: "CategoriesProduct",
      });
      return;
    }
    try {
      const reponse = await addToCart(productId, quantity, accessToken)
      console.log("Product added to cart", reponse);
    } catch (error) {
      console.log("Error adding to cart", error);
    }
       }
    

  return (
    <SafeAreaView style={styles.container}>
      <FlatList
        ListHeaderComponent={renderHeader()}
        data={products}
        renderItem={({ item }) => (
          <ProductCard
            {...item}
            onPress={() =>
              navigation.navigate("ProductDetails", { product: item })
            }
            onAddToCart={() => handleAddToCart(item?.id, 1, accessToken)}
            onWishlist={() => {}}
          />
        )}
        numColumns={2}
        keyExtractor={(item) => item?.id.toString()}
        showsVerticalScrollIndicator={false}
        contentContainerStyle={styles.productList}
        columnWrapperStyle={styles.productRow}
        scrollEventThrottle={16}
        ListEmptyComponent={
          !loading ? (
            <View style={styles.emptyContainer}>
              <Text style={styles.emptyText}>No products found</Text>
            </View>
          ) : null
        }
        onEndReachedThreshold={0.5}
        onEndReached={handleNext}
        ListFooterComponent={renderFooter}
      />
    </SafeAreaView>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: "#ffffff",
  },
  categoryHeader: {
    padding: 16,
    borderBottomWidth: 1,
    borderBottomColor: "#eeeeee",
  },
  categoryTitle: {
    fontSize: 18,
    fontWeight: "bold",
  },
  productList: {
    paddingHorizontal: 8,
    paddingBottom: 24,
  },
  productRow: {
    justifyContent: "space-evenly",
  },
  loadingContainer: {
    flex: 1,
    alignItems: "center",
    justifyContent: "center",
  },
  loadingText: {
    marginTop: 8,
    color: "#666666",
  },
  emptyContainer: {
    padding: 32,
    alignItems: "center",
    justifyContent: "center",
  },
  emptyText: {
    fontSize: 16,
    color: "#666666",
  },
});