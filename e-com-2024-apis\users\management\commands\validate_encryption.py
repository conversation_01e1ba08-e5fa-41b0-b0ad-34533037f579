"""
Management command to validate encryption implementation.
This command tests encryption/decryption functionality and data integrity.
"""

from django.core.management.base import BaseCommand
from django.db import connection
from users.models import Customer, Address, ContactMessage
from backend.encryption_utils import encryption_manager
import logging
import time

logger = logging.getLogger(__name__)


class Command(BaseCommand):
    help = 'Validate encryption is working correctly'
    
    def add_arguments(self, parser):
        parser.add_argument(
            '--performance',
            action='store_true',
            help='Run performance tests for encryption operations',
        )
        parser.add_argument(
            '--data-integrity',
            action='store_true',
            help='Check data integrity for encrypted fields',
        )
        parser.add_argument(
            '--sample-size',
            type=int,
            default=10,
            help='Number of sample records to check (default: 10)',
        )
    
    def handle(self, *args, **options):
        self.stdout.write('Starting encryption validation...')
        
        # Basic encryption test
        if not self._test_basic_encryption():
            return
        
        # Performance tests
        if options['performance']:
            self._test_performance()
        
        # Data integrity tests
        if options['data_integrity']:
            self._test_data_integrity(options['sample_size'])
        
        # Database-level validation
        self._validate_database_encryption()
        
        self.stdout.write(
            self.style.SUCCESS('✅ All encryption validation tests passed!')
        )
    
    def _test_basic_encryption(self):
        """Test basic encryption/decryption functionality"""
        self.stdout.write('Testing basic encryption/decryption...')
        
        test_cases = [
            "test_phone_9876543210",
            "<EMAIL>",
            "123 Test Street, Apartment 4B",
            "Test City",
            "Test State",
            "123456",
            "Special chars: !@#$%^&*()",
            "Unicode: 测试数据 🔐",
            "",  # Empty string
        ]
        
        for i, test_data in enumerate(test_cases):
            try:
                encrypted = encryption_manager.encrypt(test_data)
                decrypted = encryption_manager.decrypt(encrypted)
                
                if test_data == decrypted:
                    self.stdout.write(f'  ✅ Test case {i+1}: PASSED')
                else:
                    self.stdout.write(
                        self.style.ERROR(f'  ❌ Test case {i+1}: FAILED - Data mismatch')
                    )
                    return False
                    
            except Exception as e:
                self.stdout.write(
                    self.style.ERROR(f'  ❌ Test case {i+1}: FAILED - {str(e)}')
                )
                return False
        
        self.stdout.write(self.style.SUCCESS('✅ Basic encryption tests passed'))
        return True
    
    def _test_performance(self):
        """Test encryption performance"""
        self.stdout.write('Testing encryption performance...')
        
        test_data = "performance_test_phone_9876543210"
        iterations = 1000
        
        # Test encryption performance
        start_time = time.time()
        for _ in range(iterations):
            encryption_manager.encrypt(test_data)
        encrypt_time = time.time() - start_time
        
        # Test decryption performance
        encrypted_data = encryption_manager.encrypt(test_data)
        start_time = time.time()
        for _ in range(iterations):
            encryption_manager.decrypt(encrypted_data)
        decrypt_time = time.time() - start_time
        
        avg_encrypt_time = (encrypt_time / iterations) * 1000  # Convert to ms
        avg_decrypt_time = (decrypt_time / iterations) * 1000  # Convert to ms
        
        self.stdout.write(f'  Average encryption time: {avg_encrypt_time:.3f}ms')
        self.stdout.write(f'  Average decryption time: {avg_decrypt_time:.3f}ms')
        
        # Performance thresholds
        if avg_encrypt_time > 1.0:  # 1ms threshold
            self.stdout.write(
                self.style.WARNING(f'⚠️  Encryption performance may be slow: {avg_encrypt_time:.3f}ms')
            )
        
        if avg_decrypt_time > 1.0:  # 1ms threshold
            self.stdout.write(
                self.style.WARNING(f'⚠️  Decryption performance may be slow: {avg_decrypt_time:.3f}ms')
            )
        
        self.stdout.write(self.style.SUCCESS('✅ Performance tests completed'))
    
    def _test_data_integrity(self, sample_size):
        """Test data integrity for encrypted fields"""
        self.stdout.write(f'Testing data integrity (sample size: {sample_size})...')
        
        # Test Customer data
        customers = Customer.objects.exclude(phone_number='')[:sample_size]
        for customer in customers:
            if customer.phone_number:
                # Check if phone number is valid after decryption
                if not customer.phone_number.replace('+', '').replace('-', '').replace(' ', '').isdigit():
                    self.stdout.write(
                        self.style.WARNING(
                            f'⚠️  Customer {customer.id}: phone_number may have integrity issues'
                        )
                    )
        
        # Test Address data
        addresses = Address.objects.all()[:sample_size]
        for address in addresses:
            # Check if address fields are properly decrypted
            if address.street_address and len(address.street_address) < 3:
                self.stdout.write(
                    self.style.WARNING(
                        f'⚠️  Address {address.id}: street_address may have integrity issues'
                    )
                )
        
        # Test ContactMessage data
        contacts = ContactMessage.objects.all()[:sample_size]
        for contact in contacts:
            if contact.email and '@' not in contact.email:
                self.stdout.write(
                    self.style.WARNING(
                        f'⚠️  ContactMessage {contact.id}: email may have integrity issues'
                    )
                )
        
        self.stdout.write(self.style.SUCCESS('✅ Data integrity tests completed'))
    
    def _validate_database_encryption(self):
        """Validate that data is actually encrypted in the database"""
        self.stdout.write('Validating database-level encryption...')
        
        with connection.cursor() as cursor:
            # Check if phone numbers in database look encrypted
            cursor.execute("""
                SELECT phone_number FROM users_customer 
                WHERE phone_number != '' AND phone_number IS NOT NULL 
                LIMIT 5
            """)
            
            phone_numbers = cursor.fetchall()
            for phone_tuple in phone_numbers:
                phone = phone_tuple[0]
                if phone and phone.isdigit():
                    self.stdout.write(
                        self.style.WARNING(
                            f'⚠️  Found unencrypted phone number in database: {phone[:3]}***'
                        )
                    )
            
            # Check if addresses look encrypted
            cursor.execute("""
                SELECT street_address FROM users_address 
                WHERE street_address != '' AND street_address IS NOT NULL 
                LIMIT 5
            """)
            
            addresses = cursor.fetchall()
            for addr_tuple in addresses:
                addr = addr_tuple[0]
                if addr and not self._looks_encrypted(addr):
                    self.stdout.write(
                        self.style.WARNING(
                            f'⚠️  Found potentially unencrypted address in database'
                        )
                    )
        
        self.stdout.write(self.style.SUCCESS('✅ Database encryption validation completed'))
    
    def _looks_encrypted(self, data):
        """Check if data looks like it's encrypted (base64-like)"""
        if not data:
            return True
        
        # Encrypted data should be base64-encoded and longer than original
        try:
            import base64
            base64.urlsafe_b64decode(data.encode('utf-8'))
            return len(data) > 50  # Encrypted data should be significantly longer
        except:
            return False
