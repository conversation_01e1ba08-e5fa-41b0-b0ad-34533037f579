import shutil

def copy_updated_json():
    source_file = 'output/json/haier_kitchen_products_updated.json'
    destination_file = 'output/json/haier_kitchen_products.json'
    
    try:
        shutil.copy2(source_file, destination_file)
        print(f"Successfully copied {source_file} to {destination_file}")
    except Exception as e:
        print(f"Error copying file: {e}")

if __name__ == "__main__":
    copy_updated_json()
