/**
 * Utility functions for formatting data safely
 */

/**
 * Safely formats a number as currency
 * @param value - The value to format (can be string, number, null, undefined)
 * @param decimals - Number of decimal places (default: 2)
 * @returns Formatted currency string
 */
export const formatCurrency = (value: any, decimals: number = 2): string => {
  const numValue = Number(value);
  if (isNaN(numValue)) {
    return '0.00';
  }
  return numValue.toFixed(decimals);
};

/**
 * Safely formats a number
 * @param value - The value to format (can be string, number, null, undefined)
 * @param fallback - Fallback value if conversion fails (default: 0)
 * @returns Formatted number
 */
export const formatNumber = (value: any, fallback: number = 0): number => {
  const numValue = Number(value);
  if (isNaN(numValue)) {
    return fallback;
  }
  return numValue;
};

/**
 * Safely formats a date string
 * @param dateString - The date string to format
 * @param fallback - Fallback string if date is invalid (default: "N/A")
 * @returns Formatted date string
 */
export const formatDateSafe = (dateString: string | undefined | null, fallback: string = "N/A"): string => {
  if (!dateString) return fallback;
  
  try {
    const date = new Date(dateString);
    if (isNaN(date.getTime())) {
      return fallback;
    }
    return date.toLocaleDateString();
  } catch (error) {
    return fallback;
  }
};

/**
 * Safely gets a string value
 * @param value - The value to convert to string
 * @param fallback - Fallback string if value is null/undefined (default: "")
 * @returns String value
 */
export const formatStringSafe = (value: any, fallback: string = ""): string => {
  if (value === null || value === undefined) {
    return fallback;
  }
  return String(value);
};
