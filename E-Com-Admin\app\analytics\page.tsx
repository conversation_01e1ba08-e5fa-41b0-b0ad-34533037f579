"use client";

import { useState } from "react";
import { <PERSON>, <PERSON><PERSON><PERSON><PERSON>, Card<PERSON>eader, CardTitle } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { AnalyticsCharts } from "@/components/dashboard/AnalyticsCharts";
import { useDashboard } from "@/hooks/useDashboard";
import {
  Download,
  RefreshCw,
  Calendar,
  TrendingUp,
  BarChart3,
  Pie<PERSON>hart,
  Filter
} from "lucide-react";
import { useToast } from "@/components/ui/use-toast";

const Analytics = () => {
  const { toast } = useToast();
  const [dateRange, setDateRange] = useState('30d');
  const [refreshing, setRefreshing] = useState(false);

  const {
    analytics,
    graphData,
    loading,
    error,
    refreshDashboard
  } = useDashboard();

  const handleExport = (format: 'pdf' | 'csv' | 'excel') => {
    toast({
      title: "Export Started",
      description: `Your report is being exported as ${format.toUpperCase()}`,
    });
  };

  const handleRefresh = async () => {
    setRefreshing(true);
    await refreshDashboard();
    setTimeout(() => setRefreshing(false), 1000);
  };

  return (
    <div className="min-h-screen bg-gradient-to-br from-gray-50 to-white">
      <div className="space-y-8 p-6 animate-fadeIn">
        {/* Header */}
        <div className="flex flex-col lg:flex-row lg:items-center lg:justify-between gap-4">
          <div>
            <h1 className="text-4xl font-bold bg-gradient-to-r from-gray-900 to-gray-600 bg-clip-text text-transparent">
              Analytics Dashboard
            </h1>
            <p className="text-gray-600 mt-2">
              Comprehensive insights into your business performance and trends
            </p>
          </div>

          <div className="flex items-center gap-3">
            <div className="flex items-center gap-2">
              <Filter className="h-4 w-4 text-gray-500" />
              <select
                value={dateRange}
                onChange={(e) => setDateRange(e.target.value)}
                className="border border-gray-200 rounded-md px-3 py-1.5 text-sm bg-white"
              >
                <option value="7d">Last 7 days</option>
                <option value="30d">Last 30 days</option>
                <option value="90d">Last 90 days</option>
                <option value="1y">Last year</option>
              </select>
            </div>
            <Button variant="outline" size="sm" onClick={() => handleExport('pdf')} className="gap-2">
              <Download className="h-4 w-4" />
              Export Report
            </Button>
            <Button
              variant="outline"
              size="sm"
              onClick={handleRefresh}
              disabled={refreshing}
              className="gap-2"
            >
              <RefreshCw className={`h-4 w-4 ${refreshing ? 'animate-spin' : ''}`} />
              Refresh
            </Button>
          </div>
        </div>

        {/* Quick Stats */}
        <div className="grid gap-4 md:grid-cols-3">
          <Card className="border-0 shadow-lg bg-gradient-to-br from-white to-blue-50/30">
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium text-gray-600">Total Products</CardTitle>
              <BarChart3 className="h-5 w-5 text-blue-500" />
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold text-gray-900">
                {analytics?.product_performance?.length || 0}
              </div>
              <p className="text-xs text-blue-600 flex items-center gap-1 mt-1">
                <TrendingUp className="h-3 w-3" />
                Performance tracked
              </p>
            </CardContent>
          </Card>

          <Card className="border-0 shadow-lg bg-gradient-to-br from-white to-green-50/30">
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium text-gray-600">Customer Segments</CardTitle>
              <PieChart className="h-5 w-5 text-green-500" />
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold text-gray-900">
                {analytics?.customer_demographics?.length || 0}
              </div>
              <p className="text-xs text-green-600 flex items-center gap-1 mt-1">
                <TrendingUp className="h-3 w-3" />
                Age groups analyzed
              </p>
            </CardContent>
          </Card>

          <Card className="border-0 shadow-lg bg-gradient-to-br from-white to-purple-50/30">
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium text-gray-600">Data Points</CardTitle>
              <Calendar className="h-5 w-5 text-purple-500" />
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold text-gray-900">
                {graphData?.sales_data?.length || 0}
              </div>
              <p className="text-xs text-purple-600 flex items-center gap-1 mt-1">
                <TrendingUp className="h-3 w-3" />
                Time series data
              </p>
            </CardContent>
          </Card>
        </div>

        {/* Analytics Charts */}
        <AnalyticsCharts
          analyticsData={analytics}
          graphData={graphData}
          loading={loading.analytics || loading.graphData}
          error={error.analytics || error.graphData}
        />
      </div>
    </div>
  );
};
export default Analytics;