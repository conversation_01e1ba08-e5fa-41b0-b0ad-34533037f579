"""
Management command to restore JSON backup to replica database with exact ID preservation.

This command restores a JSON backup to replica databases while maintaining
exact ID consistency and preventing auto-increment conflicts.
"""

import json
import gzip
from django.core.management.base import BaseCommand, CommandError
from django.core import serializers
from django.db import transaction, connection, connections
from django.conf import settings
from django.apps import apps
from backend.db_sync import DatabaseSynchronizer, disable_sync, enable_sync


class Command(BaseCommand):
    help = 'Restore JSON backup to replica database with exact ID preservation'

    def add_arguments(self, parser):
        parser.add_argument(
            'backup_file',
            type=str,
            help='Path to the JSON backup file',
        )
        parser.add_argument(
            '--target-db',
            type=str,
            help='Target database name (default: first replica database)',
        )
        parser.add_argument(
            '--dry-run',
            action='store_true',
            help='Show what would be restored without actually doing it',
        )
        parser.add_argument(
            '--force',
            action='store_true',
            help='Force restore even if target database has data',
        )
        parser.add_argument(
            '--reset-sequences',
            action='store_true',
            help='Reset auto-increment sequences after restore (optional)',
        )
        parser.add_argument(
            '--skip-sequences',
            action='store_true',
            help='Skip sequence reset (faster, but may cause ID conflicts on new records)',
        )
        parser.add_argument(
            '--exclude-models',
            type=str,
            help='Comma-separated list of models to exclude from restore',
        )

    def handle(self, *args, **options):
        self.stdout.write(
            self.style.SUCCESS('Database Restore Tool (ID Preserving)')
        )
        self.stdout.write('=' * 60)

        # Validate backup file
        backup_data = self.load_backup_file(options['backup_file'])
        
        # Determine target database
        target_db = self.get_target_database(options['target_db'])
        
        # Show restore plan
        self.show_restore_plan(backup_data, target_db, options)
        
        if options['dry_run']:
            self.stdout.write(
                self.style.WARNING('DRY RUN MODE - No changes will be made')
            )
            return

        # Confirm restore
        if not options['force']:
            confirm = input('\nProceed with restore? (yes/no): ')
            if confirm.lower() != 'yes':
                self.stdout.write('Restore cancelled.')
                return

        # Perform restore
        self.perform_restore(backup_data, target_db, options)

        self.stdout.write(
            self.style.SUCCESS('Restore completed successfully!')
        )

    def load_backup_file(self, backup_file):
        """Load and validate backup file"""
        self.stdout.write(f'📂 Loading backup file: {backup_file}')
        
        try:
            # Handle compressed files
            if backup_file.endswith('.gz'):
                with gzip.open(backup_file, 'rt', encoding='utf-8') as f:
                    backup_data = json.load(f)
            else:
                with open(backup_file, 'r', encoding='utf-8') as f:
                    backup_data = json.load(f)
            
            # Validate backup structure
            if 'metadata' not in backup_data or 'data' not in backup_data:
                raise CommandError('Invalid backup file format')
            
            metadata = backup_data['metadata']
            total_records = metadata.get('total_records', 0)
            
            self.stdout.write(f'   ✅ Backup loaded successfully')
            self.stdout.write(f'   📊 Total records: {total_records}')
            self.stdout.write(f'   📅 Created: {metadata.get("created_at", "Unknown")}')
            
            return backup_data
            
        except Exception as e:
            raise CommandError(f'Failed to load backup file: {e}')

    def get_target_database(self, target_db_name):
        """Determine target database for restore"""
        replica_dbs = DatabaseSynchronizer.get_replica_databases()
        
        if target_db_name:
            if target_db_name not in settings.DATABASES:
                raise CommandError(f'Database "{target_db_name}" not configured')
            target_db = target_db_name
        elif replica_dbs:
            target_db = replica_dbs[0]
        else:
            raise CommandError('No replica databases configured. Specify --target-db')
        
        self.stdout.write(f'🎯 Target database: {target_db}')
        
        # Test connection
        try:
            connections[target_db].ensure_connection()
            self.stdout.write(f'   ✅ Connection successful')
        except Exception as e:
            raise CommandError(f'Cannot connect to target database: {e}')
        
        return target_db

    def show_restore_plan(self, backup_data, target_db, options):
        """Show what will be restored"""
        self.stdout.write(f'\n📋 Restore Plan')
        self.stdout.write('-' * 30)
        
        metadata = backup_data['metadata']
        exclude_models = []
        
        if options['exclude_models']:
            exclude_models = [m.strip() for m in options['exclude_models'].split(',')]
        
        self.stdout.write(f'Source: {options["backup_file"]}')
        self.stdout.write(f'Target: {target_db} database')
        self.stdout.write(f'Total records in backup: {metadata.get("total_records", 0)}')
        
        if exclude_models:
            self.stdout.write(f'Excluded models: {", ".join(exclude_models)}')
        
        self.stdout.write('\nModels to restore:')
        for model_info in metadata.get('models_included', []):
            model_name = model_info['model']
            if not any(excluded in model_name for excluded in exclude_models):
                self.stdout.write(f'  ✅ {model_name}: {model_info["count"]} records')
            else:
                self.stdout.write(f'  ⏭️  {model_name}: {model_info["count"]} records (excluded)')

    def perform_restore(self, backup_data, target_db, options):
        """Perform the actual restore with ID preservation"""
        self.stdout.write(f'\n🔄 Starting restore to {target_db}...')
        
        # Disable sync during restore to avoid conflicts
        disable_sync()
        
        try:
            with transaction.atomic(using=target_db):
                # Clear existing data
                self.clear_target_database(target_db, options)
                
                # Restore data with ID preservation
                self.restore_data_with_ids(backup_data, target_db, options)
                
                # Reset sequences if requested and not skipped
                if options['reset_sequences'] and not options['skip_sequences']:
                    self.reset_sequences(target_db, backup_data)
                elif options['skip_sequences']:
                    self.stdout.write('⏭️  Skipping sequence reset (as requested)')
                else:
                    self.stdout.write('ℹ️  Sequence reset not requested (use --reset-sequences if needed)')
                
        except Exception as e:
            self.stdout.write(
                self.style.ERROR(f'Restore failed: {e}')
            )
            raise
        finally:
            # Re-enable sync
            enable_sync()

    def clear_target_database(self, target_db, options):
        """Clear existing data from target database"""
        self.stdout.write('🧹 Clearing existing data...')
        
        # Get all models that will be restored
        models_to_clear = []
        
        # Import models
        from products.models import Product, Category, Brand, SubCategorie, GST, ProductImage, ProductVariant
        from users.models import Customer, Address
        from orders.models import Order, OrderItem, Cart, CartItem, ShippingMethod
        from promotions.models import Promotion, PromotionUsage
        
        # Order matters for foreign key constraints
        models_to_clear = [
            ProductImage, ProductVariant, Product,  # Products first
            CartItem, Cart,  # Cart items before carts
            OrderItem, Order,  # Order items before orders
            Address, Customer,  # Addresses before customers
            SubCategorie, Category,  # Subcategories before categories
            Brand, GST,  # Independent models
            Promotion, PromotionUsage,  # Promotions
            ShippingMethod,  # Shipping
        ]
        
        exclude_models = []
        if options.get('exclude_models'):
            exclude_models = [m.strip().lower() for m in options['exclude_models'].split(',')]
        
        for model_class in models_to_clear:
            model_name = model_class.__name__.lower()
            if model_name not in exclude_models:
                try:
                    count = model_class.objects.using(target_db).count()
                    if count > 0:
                        model_class.objects.using(target_db).all().delete()
                        self.stdout.write(f'   🗑️  Cleared {count} {model_class.__name__} records')
                except Exception as e:
                    self.stdout.write(f'   ⚠️  Could not clear {model_class.__name__}: {e}')

    def restore_data_with_ids(self, backup_data, target_db, options):
        """Restore data preserving original IDs"""
        self.stdout.write('📥 Restoring data with ID preservation...')
        
        exclude_models = []
        if options.get('exclude_models'):
            exclude_models = [m.strip() for m in options['exclude_models'].split(',')]
        
        # Group data by model for ordered restoration
        model_data = {}
        for item in backup_data['data']:
            model_name = item['model']
            if not any(excluded in model_name for excluded in exclude_models):
                if model_name not in model_data:
                    model_data[model_name] = []
                model_data[model_name].append(item)
        
        # Restore in dependency order
        restoration_order = [
            'users.Customer',
            'users.Address',
            'orders.ShippingMethod',
            'products.GST',
            'products.Brand',
            'products.Category',
            'products.SubCategorie',
            'products.Product',
            'products.ProductImage',
            'products.ProductVariant',
            'promotions.Promotion',
            'promotions.PromotionUsage',
            'orders.Cart',
            'orders.CartItem',
            'orders.Order',
            'orders.OrderItem',
        ]
        
        total_restored = 0
        
        for model_name in restoration_order:
            if model_name in model_data:
                records = model_data[model_name]
                count = self.restore_model_data(model_name, records, target_db)
                total_restored += count
                self.stdout.write(f'   ✅ {model_name}: {count} records restored')
        
        # Restore any remaining models not in the order
        for model_name, records in model_data.items():
            if model_name not in restoration_order:
                count = self.restore_model_data(model_name, records, target_db)
                total_restored += count
                self.stdout.write(f'   ✅ {model_name}: {count} records restored')
        
        self.stdout.write(f'📊 Total records restored: {total_restored}')

    def restore_model_data(self, model_name, records, target_db):
        """Restore data for a specific model"""
        try:
            # Create JSON string for deserialization
            json_data = json.dumps(records)
            
            # Deserialize and save to target database
            objects = serializers.deserialize('json', json_data, using=target_db)
            
            count = 0
            for obj in objects:
                # Save with original ID preserved
                obj.save(using=target_db)
                count += 1
            
            return count
            
        except Exception as e:
            self.stdout.write(
                self.style.ERROR(f'Error restoring {model_name}: {e}')
            )
            return 0

    def reset_sequences(self, target_db, backup_data):
        """Reset auto-increment sequences to prevent ID conflicts"""
        self.stdout.write('🔢 Resetting auto-increment sequences...')
        
        db_connection = connections[target_db]
        
        if 'postgresql' in db_connection.settings_dict['ENGINE']:
            self.reset_postgresql_sequences(db_connection, backup_data)
        elif 'mysql' in db_connection.settings_dict['ENGINE']:
            self.reset_mysql_sequences(db_connection, backup_data)
        else:
            self.stdout.write('   ⚠️  Sequence reset not implemented for this database engine')

    def reset_postgresql_sequences(self, db_connection, backup_data):
        """Reset PostgreSQL sequences"""
        with db_connection.cursor() as cursor:
            try:
                # Try modern PostgreSQL (10+) approach first
                cursor.execute("""
                    SELECT c.relname as sequence_name,
                           t.relname as table_name
                    FROM pg_class c
                    JOIN pg_depend d ON d.objid = c.oid
                    JOIN pg_class t ON d.refobjid = t.oid
                    WHERE c.relkind = 'S'
                    AND t.relkind = 'r'
                    AND c.relnamespace = (SELECT oid FROM pg_namespace WHERE nspname = 'public')
                """)

                sequences = cursor.fetchall()

            except Exception:
                # Fallback for older PostgreSQL versions
                try:
                    cursor.execute("""
                        SELECT c.relname as sequence_name,
                               pg_get_serial_sequence(t.schemaname||'.'||t.tablename, 'id') as full_seq_name,
                               t.tablename as table_name
                        FROM pg_tables t
                        JOIN pg_class c ON c.relname = t.tablename||'_id_seq'
                        WHERE t.schemaname = 'public'
                        AND pg_get_serial_sequence(t.schemaname||'.'||t.tablename, 'id') IS NOT NULL
                    """)

                    sequences = [(row[0], row[2]) for row in cursor.fetchall()]

                except Exception:
                    # Manual approach - get sequences by naming convention
                    cursor.execute("""
                        SELECT relname FROM pg_class
                        WHERE relkind = 'S'
                        AND relnamespace = (SELECT oid FROM pg_namespace WHERE nspname = 'public')
                        AND relname LIKE '%_id_seq'
                    """)

                    sequences = []
                    for (seq_name,) in cursor.fetchall():
                        # Extract table name from sequence name
                        table_name = seq_name.replace('_id_seq', '')
                        sequences.append((seq_name, table_name))

            # Reset sequences
            for seq_name, table_name in sequences:
                try:
                    # Check if table exists and has an id column
                    cursor.execute(f"""
                        SELECT column_name FROM information_schema.columns
                        WHERE table_name = '{table_name}'
                        AND column_name = 'id'
                        AND table_schema = 'public'
                    """)

                    if cursor.fetchone():
                        # Get max ID from table
                        cursor.execute(f'SELECT MAX(id) FROM "{table_name}"')
                        result = cursor.fetchone()
                        max_id = result[0] if result and result[0] else 0

                        if max_id > 0:
                            # Reset sequence to max_id + 1
                            cursor.execute(f"SELECT setval('{seq_name}', {max_id + 1})")
                            self.stdout.write(f'   🔢 Reset {seq_name} to {max_id + 1}')
                        else:
                            # Reset to 1 if no records
                            cursor.execute(f"SELECT setval('{seq_name}', 1, false)")
                            self.stdout.write(f'   🔢 Reset {seq_name} to 1 (no records)')

                except Exception as e:
                    self.stdout.write(f'   ⚠️  Could not reset {seq_name}: {e}')

    def reset_mysql_sequences(self, db_connection, backup_data):
        """Reset MySQL auto-increment values"""
        with db_connection.cursor() as cursor:
            # Get all tables with auto-increment
            cursor.execute("""
                SELECT table_name 
                FROM information_schema.tables 
                WHERE table_schema = DATABASE() 
                AND auto_increment IS NOT NULL
            """)
            
            tables = cursor.fetchall()
            
            for (table_name,) in tables:
                try:
                    # Get max ID
                    cursor.execute(f'SELECT MAX(id) FROM {table_name}')
                    max_id = cursor.fetchone()[0]
                    
                    if max_id:
                        # Reset auto-increment
                        cursor.execute(f'ALTER TABLE {table_name} AUTO_INCREMENT = {max_id + 1}')
                        self.stdout.write(f'   🔢 Reset {table_name} auto-increment to {max_id + 1}')
                        
                except Exception as e:
                    self.stdout.write(f'   ⚠️  Could not reset {table_name}: {e}')
