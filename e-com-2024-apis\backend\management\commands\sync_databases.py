"""
Management command to synchronize data between primary and replica databases with ID preservation.

This command uses the backup/restore approach to ensure exact ID preservation
and perfect synchronization between primary and replica databases.
"""

import os
import tempfile
from django.core.management.base import BaseCommand, CommandError
from django.core.management import call_command
from django.conf import settings
from backend.db_sync import DatabaseSynchronizer


class Command(BaseCommand):
    help = 'Synchronize data between primary and replica databases with exact ID preservation'

    def add_arguments(self, parser):
        parser.add_argument(
            '--target-db',
            type=str,
            help='Target replica database name (default: all replicas)',
        )
        parser.add_argument(
            '--exclude-users',
            action='store_true',
            help='Exclude user data from sync',
        )
        parser.add_argument(
            '--exclude-orders',
            action='store_true',
            help='Exclude order data from sync',
        )
        parser.add_argument(
            '--dry-run',
            action='store_true',
            help='Show what would be synced without actually doing it',
        )
        parser.add_argument(
            '--force',
            action='store_true',
            help='Force sync without confirmation',
        )
        parser.add_argument(
            '--check',
            action='store_true',
            help='Check current sync status',
        )

    def handle(self, *args, **options):
        self.stdout.write(
            self.style.SUCCESS('Database Synchronization Tool (ID Preserving)')
        )
        self.stdout.write('=' * 60)

        # Check if this is a status check
        if options['check']:
            self.check_sync_status()
            return

        # Check replica configuration
        replica_dbs = self.get_target_databases(options['target_db'])

        # Show sync plan
        self.show_sync_plan(replica_dbs, options)

        if options['dry_run']:
            self.stdout.write(
                self.style.WARNING('DRY RUN MODE - No changes will be made')
            )
            return

        # Confirm sync
        if not options['force']:
            confirm = input('\nProceed with sync? (yes/no): ')
            if confirm.lower() != 'yes':
                self.stdout.write('Sync cancelled.')
                return

        # Perform ID-preserving sync using backup/restore
        self.perform_id_preserving_sync(replica_dbs, options)

        self.stdout.write(
            self.style.SUCCESS('ID-preserving synchronization completed successfully!')
        )

    def get_target_databases(self, target_db_name):
        """Get list of target replica databases"""
        replica_dbs = DatabaseSynchronizer.get_replica_databases()

        if not replica_dbs:
            raise CommandError('No replica databases configured. Add DB_READ_HOST and related environment variables.')

        if target_db_name:
            if target_db_name not in replica_dbs:
                raise CommandError(f'Replica database "{target_db_name}" not found')
            return [target_db_name]

        return replica_dbs

    def show_sync_plan(self, replica_dbs, options):
        """Show what will be synced"""
        self.stdout.write(f'\n📋 Sync Plan')
        self.stdout.write('-' * 30)

        self.stdout.write(f'Source: PRIMARY database')
        self.stdout.write(f'Target(s): {", ".join(replica_dbs)}')
        self.stdout.write(f'Method: ID-preserving backup/restore')

        # Show what will be included/excluded
        inclusions = []
        exclusions = []

        if not options['exclude_users']:
            inclusions.append('User data')
        else:
            exclusions.append('User data')

        if not options['exclude_orders']:
            inclusions.append('Order data')
        else:
            exclusions.append('Order data')

        inclusions.extend(['Product catalog', 'Categories', 'Brands', 'Promotions'])

        if inclusions:
            self.stdout.write(f'Included: {", ".join(inclusions)}')
        if exclusions:
            self.stdout.write(f'Excluded: {", ".join(exclusions)}')

        # Show current record counts
        self.show_current_counts(replica_dbs)

    def show_current_counts(self, replica_dbs):
        """Show current record counts in primary and replicas"""
        self.stdout.write(f'\n📊 Current Record Counts')
        self.stdout.write('-' * 30)

        from products.models import Product, Category, Brand
        from users.models import Customer
        from orders.models import Order

        models_to_check = [
            (Product, 'Products'),
            (Category, 'Categories'),
            (Brand, 'Brands'),
            (Customer, 'Users'),
            (Order, 'Orders'),
        ]

        for model_class, model_name in models_to_check:
            primary_count = model_class.objects.count()
            self.stdout.write(f'\n{model_name}:')
            self.stdout.write(f'  PRIMARY: {primary_count} records')

            for db_name in replica_dbs:
                try:
                    replica_count = model_class.objects.using(db_name).count()
                    if replica_count == primary_count:
                        status = '✅ SYNCED'
                    else:
                        status = f'❌ OUT OF SYNC (diff: {abs(primary_count - replica_count)})'

                    self.stdout.write(f'  {db_name.upper()}: {replica_count} records {status}')
                except Exception as e:
                    self.stdout.write(f'  {db_name.upper()}: ❌ ERROR - {e}')

    def perform_id_preserving_sync(self, replica_dbs, options):
        """Perform ID-preserving sync using backup/restore approach"""
        self.stdout.write(f'\n🔄 Starting ID-preserving sync...')

        # Use the sync_primary_to_replica command which preserves IDs
        for db_name in replica_dbs:
            self.stdout.write(f'\n📥 Syncing to {db_name} database...')

            # Prepare sync command arguments
            sync_args = ['--target-db', db_name, '--force']

            if options['exclude_users']:
                sync_args.append('--exclude-users')

            if options['exclude_orders']:
                sync_args.append('--exclude-orders')

            # Call the ID-preserving sync command
            call_command('sync_primary_to_replica', *sync_args)

            self.stdout.write(f'   ✅ Sync to {db_name} completed with exact ID preservation')

    def check_sync_status(self):
        """Check the current synchronization status"""
        self.stdout.write('\n📊 Synchronization Status')
        self.stdout.write('-' * 30)

        replica_dbs = DatabaseSynchronizer.get_replica_databases()

        if not replica_dbs:
            self.stdout.write('❌ No replica databases configured')
            return

        from products.models import Product, Category, Brand, SubCategorie, GST

        models_to_check = [Product, Category, Brand, SubCategorie, GST]

        for model_class in models_to_check:
            self.stdout.write(f'\n{model_class.__name__}:')

            # Primary count
            primary_count = model_class.objects.count()
            self.stdout.write(f'   Primary: {primary_count} records')

            # Replica counts
            for db_name in replica_dbs:
                try:
                    replica_count = model_class.objects.using(db_name).count()
                    if replica_count == primary_count:
                        status = self.style.SUCCESS('✅ Synced')
                    else:
                        status = self.style.WARNING(f'⚠️  Out of sync (diff: {abs(primary_count - replica_count)})')

                    self.stdout.write(f'   {db_name}: {replica_count} records {status}')
                except Exception as e:
                    self.stdout.write(f'   {db_name}: ❌ Error - {e}')

        self.stdout.write(f'\n💡 To fix sync issues, run:')
        self.stdout.write(f'   python manage.py sync_databases --force')

    def show_help_commands(self):
        """Show useful sync commands"""
        self.stdout.write('\n💡 Available Commands')
        self.stdout.write('-' * 30)

        commands = [
            ('Check sync status', 'python manage.py sync_databases --check'),
            ('Sync all data (ID preserving)', 'python manage.py sync_databases --force'),
            ('Sync without users/orders', 'python manage.py sync_databases --exclude-users --exclude-orders --force'),
            ('Dry run sync', 'python manage.py sync_databases --dry-run'),
            ('Sync to specific replica', 'python manage.py sync_databases --target-db read_replica --force'),
        ]

        for description, command in commands:
            self.stdout.write(f'   {description}:')
            self.stdout.write(f'     {command}')
            self.stdout.write('')

        self.stdout.write('🎯 Key Features:')
        self.stdout.write('   • Preserves exact IDs (no auto-increment changes)')
        self.stdout.write('   • Uses backup/restore for perfect consistency')
        self.stdout.write('   • Safe and reliable operation')
        self.stdout.write('   • Production-ready performance')
