"use client"
import { useState, useMemo } from "react";
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from "@/components/ui/table";
import { Input } from "@/components/ui/input";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import { Button } from "@/components/ui/button";
import { Card } from "@/components/ui/card";
import { ExternalLink, Mail, Search, Loader2, AlertCircle } from "lucide-react";
import { format } from "date-fns";
import { useRouter } from "next/navigation";
import { useCustomers } from "@/hooks/useCustomers";
import { Alert, AlertDescription } from "@/components/ui/alert";
import { useSession } from "next-auth/react";

const Customers = () => {
  const router = useRouter();
  const [searchQuery, setSearchQuery] = useState("");
  const [statusFilter, setStatusFilter] = useState("all");

  // Check session status
  const { data: session, status } = useSession();

  // Use the custom hook to fetch customers
  const { customers, loading, error, fetchCustomers, searchCustomers } = useCustomers();

  // Filter customers based on search query and status
  const filteredCustomers = useMemo(() => {
    return searchCustomers(searchQuery, statusFilter);
  }, [searchCustomers, searchQuery, statusFilter]);

  // Debug logging
  console.log('Customers component rendered - updated');
  console.log('Session status:', status);
  console.log('Session data:', session);
  console.log('Customers:', customers);
  console.log('Loading:', loading);
  console.log('Error:', error);

  // Manual test function
  const testApiCall = async () => {
    console.log('Manual API test triggered');
    try {
      await fetchCustomers();
      console.log('Manual API call completed');
    } catch (err) {
      console.error('Manual API call failed:', err);
    }
  };

  return (
    <div className="space-y-6 animate-fadeIn">
      <div className="flex items-center justify-between">
        <h1 className="text-3xl font-bold">Customers</h1>
      </div>

      <Card className="p-6">
        <div className="flex gap-4 mb-6">
          <div className="relative flex-1">
            <Search className="absolute left-3 top-3 h-4 w-4 text-muted-foreground" />
            <Input
              placeholder="Search customers..."
              value={searchQuery}
              onChange={(e) => setSearchQuery(e.target.value)}
              className="pl-9"
            />
          </div>
          <Select
            value={statusFilter}
            onValueChange={setStatusFilter}
          >
            <SelectTrigger className="w-[180px]">
              <SelectValue placeholder="Filter by status" />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="all">All Customers</SelectItem>
              <SelectItem value="active">Active</SelectItem>
              <SelectItem value="inactive">Inactive</SelectItem>
            </SelectContent>
          </Select>
          <Button
            variant="outline"
            onClick={testApiCall}
            disabled={loading}
          >
            {loading ? (
              <Loader2 className="h-4 w-4 animate-spin" />
            ) : (
              "Test API Call"
            )}
          </Button>
        </div>

        {/* Error Alert */}
        {error && (
          <Alert variant="destructive" className="mb-6">
            <AlertCircle className="h-4 w-4" />
            <AlertDescription>
              Failed to load customers: {error}
            </AlertDescription>
          </Alert>
        )}

        {/* Loading State */}
        {loading && customers.length === 0 && (
          <div className="flex items-center justify-center py-8">
            <Loader2 className="h-8 w-8 animate-spin" />
            <span className="ml-2">Loading customers...</span>
          </div>
        )}

        {/* Empty State */}
        {!loading && customers.length === 0 && !error && (
          <div className="text-center py-8">
            <p className="text-muted-foreground">No customers found.</p>
          </div>
        )}

        {/* Customers Table */}
        {!loading && filteredCustomers.length > 0 && (
          <Table>
            <TableHeader>
              <TableRow>
                <TableHead>Customer</TableHead>
                <TableHead>Total Orders Amount</TableHead>
                {/* <TableHead>Total Spent</TableHead> */}
                <TableHead>Last Purchase</TableHead>
                <TableHead>Actions</TableHead>
              </TableRow>
            </TableHeader>
            <TableBody>
              {filteredCustomers.map((customer) => (
                <TableRow key={customer.id}>
                  <TableCell>
                    <div>
                      <p className="font-medium">{customer.name}</p>
                      <p className="text-sm text-muted-foreground">
                        {customer.email}
                      </p>
                    </div>
                  </TableCell>
                  <TableCell>₹{customer.totalOrders || 0}</TableCell>
                  {/* <TableCell>${(customer.totalSpent || 0).toFixed(2)}</TableCell> */}
                  <TableCell>
                    {customer.lastPurchase ? (
                      format(new Date(customer.lastPurchase), "MMM d, yyyy")
                    ) : (
                      "Never"
                    )}
                  </TableCell>
                  <TableCell>
                    <div className="flex space-x-2">
                      <Button
                        variant="ghost"
                        size="icon"
                        onClick={() => router.push(`/customers/${customer.id}`)}
                      >
                        <ExternalLink className="h-4 w-4" />
                      </Button>
                      <Button
                        variant="ghost"
                        size="icon"
                        onClick={() => window.location.href = `mailto:${customer.email}`}
                      >
                        <Mail className="h-4 w-4" />
                      </Button>
                    </div>
                  </TableCell>
                </TableRow>
              ))}
            </TableBody>
          </Table>
        )}
      </Card>
    </div>
  );
};
export default Customers