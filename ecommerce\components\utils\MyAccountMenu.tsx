"use client";

import React from "react";
import {
  Camera,
  Wallet,
  Heart,
  ArrowRight,
  UserCircle,
  Settings,
  Lock,
  Bell,
  HelpCircle,
  Pen,
  Book,
} from "lucide-react";
import Link from "next/link";

const menuItems = [
  { icon: Camera, label: "My Orders", href: "/account" },
  { icon: Heart, label: "Favourites Items", href: "/account" },
  //   { icon: ArrowRight, label: "My Returns" },
  { icon: UserCircle, label: "Account", href: "/account" },
  //   { icon: Settings, label: "Settings" },
  { icon: Lock, label: "Privacy" }, //TODO: add privacy policy
  { icon: Book, label: "Addresses", href: "/account" },
  { icon: Pen, label: "Edit Profile", href: "/account" },
  { icon: HelpCircle, label: "Help Center" }, //TODO: add help center
];

const MyAccountMenu = () => {
  return (
    <div className="relative p-4 md:p-6 rounded-lg shadow-lg w-full max-w-[95vw] md:max-w-md mx-auto md:mx-0 bg-white">
      <h2 className="text-xl font-bold mb-4 text-gray-800">My Account</h2>
      <div className="w-full overflow-x-auto">
        <table className="w-full">
          <tbody>
            {menuItems.map((item, index) => (
              <tr
                key={index}
                className="border-b transition-colors duration-300 hover:bg-gray-50"
              >
                <td className="py-2 md:py-3 pr-2 md:pr-4">
                  <Link href={item.href ?? '#'} className="flex items-center text-gray-700 hover:text-theme-accent-primary">
                    <item.icon className="w-5 h-5 md:w-6 md:h-6 mr-2 flex-shrink-0" />
                    <span className="text-sm md:text-base">{item.label}</span>
                  </Link>
                </td>
              </tr>
            ))}
          </tbody>
        </table>
      </div>
    </div>
  );
};

export default MyAccountMenu;
