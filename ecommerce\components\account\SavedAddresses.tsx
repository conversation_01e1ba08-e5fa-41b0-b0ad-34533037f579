import { Button } from "../../components/ui/button";
import { Plus } from "lucide-react";
import { useState } from "react";
import DeliveryForm from "../checkout/DeliveryForm";
import EditOrAddAddressForm from "./EditOrAddAddressForm";
import AddressCard from "./AddressCard";

export const SavedAddresses = (props: any) => {
  const [addOpen, setAddOpen] = useState(false);
  const [addresses, setAddresses] = useState<any>(props?.addresses ?? []);

  return (
    <div className="space-y-6">
      <div className="flex flex-col sm:flex-row justify-between items-start sm:items-center gap-4">
        <h2 className="text-lg sm:text-xl font-bold text-gray-800">Saved Addresses</h2>
        <EditOrAddAddressForm
          open={addOpen}
          onOpenChange={setAddOpen}
          title="Add New Address"
          form={
            <DeliveryForm setAddresses={setAddresses} onClose={setAddOpen} />
          }
          button={
            <Button
              variant="outline"
              className="flex items-center gap-2 bg-blue-50 text-blue-700 hover:bg-blue-100 border border-blue-200 transition-all duration-300 rounded-lg shadow-sm"
            >
              <Plus className="h-4 w-4" />
              Add New Address
            </Button>
          }
        />
      </div>

      {(!Array.isArray(addresses) || addresses.length === 0) && (
        <div className="text-center py-12 bg-gray-50 rounded-xl border border-gray-100">
          <svg xmlns="http://www.w3.org/2000/svg" className="h-12 w-12 mx-auto text-gray-400 mb-4" fill="none" viewBox="0 0 24 24" stroke="currentColor">
            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={1.5} d="M17.657 16.657L13.414 20.9a1.998 1.998 0 01-2.827 0l-4.244-4.243a8 8 0 1111.314 0z" />
            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={1.5} d="M15 11a3 3 0 11-6 0 3 3 0 016 0z" />
          </svg>
          <p className="text-gray-600 font-medium">No addresses found</p>
          <p className="text-gray-500 text-sm mt-1">Add a new address to get started</p>
        </div>
      )}

      <div className="grid gap-6 md:grid-cols-2">
        {Array.isArray(addresses) &&
          addresses.map((address) => (
            <AddressCard key={address.id} setAddresses={setAddresses} {...address} />
          ))}
      </div>
    </div>
  );
};
