"use client";
import { useState } from "react";
import { ProductGrid } from "../../components/shop/ProductGrid";
import { FilterSidebar } from "../../components/shop/FilterSidebar";
import { SortingOptions } from "../../components/shop/SortingOptions";
import { Button } from "../../components/ui/button";
import { Filter } from "lucide-react";
import MainHOF from "../../layout/MainHOF";

const Shop = () => {
  const [isSidebarOpen, setIsSidebarOpen] = useState(false);
  const [currentPage, setCurrentPage] = useState(1);
  const [sortBy, setSortBy] = useState("-created_at");
  const [filters, setFilters] = useState({
    priceRange: [0, 10000],
    categories: [] as string[],
    brands: [] as string[],
    rating: 0,
  });

  return (
    <MainHOF>
      <div className="container mx-auto px-4 py-8">
        <div className="flex items-center justify-between mb-6">
          <h1 className="text-2xl font-bold">Shop</h1>
          <div className="flex items-center gap-4">
            <Button
              variant="outline"
              className="lg:hidden md:w-auto w-12"
              onClick={() => setIsSidebarOpen(true)}
            >
              <Filter className="h-4 w-4 md:mr-2" />
              <span className="md:block hidden">Filters</span>
            </Button>
            <SortingOptions value={sortBy} onChange={setSortBy} />
          </div>
        </div>

        <div className="flex flex-col lg:flex-row gap-8">
          <FilterSidebar
            filters={filters}
            onFiltersChange={setFilters}
            isOpen={isSidebarOpen}
            onClose={() => setIsSidebarOpen(false)}
            onPageChange={setCurrentPage}
          />
          <div className="flex-1">
            <ProductGrid
              filters={filters}
              sortBy={sortBy}
              currentPage={currentPage}
              onPageChange={setCurrentPage}
            />
          </div>
        </div>
      </div>
    </MainHOF>
  );
};

export default Shop;
