#!/usr/bin/env python3
"""
Test script for Haier High-Quality Image Scraper
This script tests the scraper with a single product to verify functionality
"""

import json
import os
import sys

def test_selenium_scraper():
    """Test the Selenium-based scraper with one product"""
    try:
        from haier_hq_image_scraper import HaierImageScraper
        
        print("Testing Selenium scraper...")
        scraper = HaierImageScraper()
        
        # Test with a sample product
        test_model = "HIH-V90HM-P1"
        test_name = "90 cm, T-Shaped-Smart Hood"
        
        print(f"Testing with model: {test_model}")
        
        # Search for images
        images = scraper.search_product_images(test_model, test_name)
        
        print(f"Found {len(images)} high-quality images")
        for i, img_url in enumerate(images[:3]):  # Show first 3
            print(f"  {i+1}. {img_url}")
        
        scraper.close()
        return True
        
    except Exception as e:
        print(f"Error testing Selenium scraper: {e}")
        return False

def test_playwright_scraper():
    """Test the Playwright-based scraper with one product"""
    try:
        import asyncio
        from haier_hq_image_scraper_playwright import Hai<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>laywright
        from playwright.async_api import async_playwright
        
        async def run_test():
            print("Testing Playwright scraper...")
            scraper = HaierImageScraperPlaywright()
            
            async with async_playwright() as p:
                browser = await p.chromium.launch(headless=True)
                page = await browser.new_page()
                
                # Test with a sample product
                test_model = "HIH-V90HM-P1"
                test_name = "90 cm, T-Shaped-Smart Hood"
                
                print(f"Testing with model: {test_model}")
                
                # Search for images
                images = await scraper.search_product_images(page, test_model, test_name)
                
                print(f"Found {len(images)} high-quality images")
                for i, img_url in enumerate(images[:3]):  # Show first 3
                    print(f"  {i+1}. {img_url}")
                
                await browser.close()
                return True
        
        return asyncio.run(run_test())
        
    except Exception as e:
        print(f"Error testing Playwright scraper: {e}")
        return False

def test_json_loading():
    """Test loading the JSON file"""
    try:
        json_file_path = 'output/json/haier_kitchen_products.json'
        
        if not os.path.exists(json_file_path):
            print(f"JSON file not found: {json_file_path}")
            return False
        
        with open(json_file_path, 'r', encoding='utf-8') as f:
            products = json.load(f)
        
        print(f"Successfully loaded {len(products)} products from JSON")
        
        # Show first few products
        for i, product in enumerate(products[:3]):
            model = product.get('model', 'Unknown')
            name = product.get('name', 'Unknown')
            hq_images = len(product.get('hq_images', []))
            print(f"  {i+1}. {model} - {name} ({hq_images} HQ images)")
        
        return True
        
    except Exception as e:
        print(f"Error loading JSON: {e}")
        return False

def check_dependencies():
    """Check if required dependencies are installed"""
    print("Checking dependencies...")
    
    dependencies = {
        'requests': 'requests',
        'PIL': 'Pillow',
        'selenium': 'selenium',
        'playwright': 'playwright (optional)'
    }
    
    missing = []
    for module, package in dependencies.items():
        try:
            if module == 'PIL':
                import PIL
            else:
                __import__(module)
            print(f"  ✓ {package}")
        except ImportError:
            print(f"  ✗ {package}")
            missing.append(package)
    
    if missing:
        print(f"\nMissing dependencies: {', '.join(missing)}")
        print("Install with: pip install " + " ".join(missing))
        return False
    
    return True

def main():
    """Main test function"""
    print("Haier HQ Image Scraper - Test Suite")
    print("=" * 40)
    
    # Check dependencies
    if not check_dependencies():
        print("\nPlease install missing dependencies before running tests.")
        return
    
    print("\n1. Testing JSON loading...")
    if not test_json_loading():
        print("JSON loading test failed!")
        return
    
    print("\n2. Choose scraper to test:")
    print("1. Selenium")
    print("2. Playwright")
    print("3. Both")
    print("4. Skip scraper tests")
    
    choice = input("\nEnter your choice (1-4): ").strip()
    
    if choice == '1':
        test_selenium_scraper()
    elif choice == '2':
        test_playwright_scraper()
    elif choice == '3':
        test_selenium_scraper()
        print("\n" + "-" * 40)
        test_playwright_scraper()
    elif choice == '4':
        print("Skipping scraper tests.")
    else:
        print("Invalid choice.")
    
    print("\nTest completed!")

if __name__ == "__main__":
    main()
