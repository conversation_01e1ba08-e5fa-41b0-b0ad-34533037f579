"use client";
import { useEffect, useState, Suspense } from "react";
import { useRouter, useSearchParams } from "next/navigation";
import MainHOF from "../../layout/MainHOF";
import { Button } from "../../components/ui/button";
import { CheckCircle } from "lucide-react";
import { useToast } from "../../components/ui/use-toast";
import useApi from "@/hooks/useApi";
import { MAIN_URL, ORDERS } from "@/constant/urls";
import { useSession } from "next-auth/react";
import SpinnerLoader from "@/components/ui/loading/SpinnerLoader";

const PaymentSuccessContent = () => {
  const router = useRouter();
  const searchParams = useSearchParams();
  const orderId = searchParams.get("order_id");
  const { toast } = useToast();
  const { status } = useSession();
  const { read, data: orderData, loading } = useApi(MAIN_URL);
  const [orderDetails, setOrderDetails] = useState<any>(null);

  useEffect(() => {
    if (!orderId) {
      router.push("/");
      return;
    }

    if (status === "authenticated") {
      fetchOrderDetails();
    }
  }, [orderId, status]);

  const fetchOrderDetails = async () => {
    try {
      const data = await read(`${ORDERS}${orderId}/`);
      setOrderDetails(data);
    } catch (error) {
      console.error("Error fetching order details:", error);
      toast({
        title: "Error",
        description: "Could not fetch order details. Please try again later.",
      });
    }
  };

  if (loading || !orderDetails) {
    return (
      <MainHOF>
        <div className="container h-96 flex justify-center items-center mx-auto px-4 py-8">
          <SpinnerLoader />
        </div>
      </MainHOF>
    );
  }

  return (
    <MainHOF>
      <div className="container mx-auto px-4 py-16">
        <div className="max-w-md mx-auto text-center">
          <div className="flex justify-center mb-6">
            <CheckCircle className="h-16 w-16 text-green-500" />
          </div>
          <h1 className="text-2xl font-bold mb-4">Payment Successful!</h1>
          <p className="text-muted-foreground mb-8">
            Your order has been placed successfully and your payment has been processed.
          </p>

          <div className="bg-muted p-6 rounded-lg mb-8">
            <h2 className="font-semibold mb-4">Order Summary</h2>
            <div className="flex justify-between mb-2">
              <span>Order ID:</span>
              <span className="font-medium">{orderDetails?.id}</span>
            </div>
            <div className="flex justify-between mb-2">
              <span>Date:</span>
              <span className="font-medium">
                {new Date(orderDetails?.created_at).toLocaleDateString()}
              </span>
            </div>
            <div className="flex justify-between mb-2">
              <span>Total Amount:</span>
              <span className="font-medium">₹{orderDetails?.total}</span>
            </div>
            <div className="flex justify-between">
              <span>Payment Method:</span>
              <span className="font-medium">PhonePe</span>
            </div>
          </div>

          <div className="space-y-4">
            <Button
              className="w-full"
              onClick={() => router.push(`/order-details?order_id=${orderId}`)}
            >
              View Order Details
            </Button>
            <Button
              variant="outline"
              className="w-full"
              onClick={() => router.push("/")}
            >
              Continue Shopping
            </Button>
          </div>
        </div>
      </div>
    </MainHOF>
  );
};

const PaymentSuccess = () => {
  return (
    <Suspense fallback={<div>Loading...</div>}>
      <PaymentSuccessContent />
    </Suspense>
  );
};

export default PaymentSuccess;
