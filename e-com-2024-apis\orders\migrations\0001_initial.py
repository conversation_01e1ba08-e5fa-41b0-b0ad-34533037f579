# Generated by Django 5.0.2 on 2025-06-20 09:25

import django.core.validators
import uuid
from django.db import migrations, models


class Migration(migrations.Migration):

    initial = True

    dependencies = [
    ]

    operations = [
        migrations.CreateModel(
            name='Cart',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('updated_at', models.DateTimeField(auto_now=True)),
            ],
        ),
        migrations.CreateModel(
            name='CartItem',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('quantity', models.PositiveIntegerField(default=1, validators=[django.core.validators.MinValueValidator(1)])),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('updated_at', models.DateTimeField(auto_now=True)),
            ],
        ),
        migrations.CreateModel(
            name='Invoice',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('invoice_number', models.CharField(help_text='Format: INV-YYYY-MM-XXXXXX', max_length=50, unique=True)),
                ('generated_at', models.DateTimeField(auto_now_add=True)),
                ('pdf_file', models.FileField(blank=True, null=True, upload_to='invoices/')),
                ('company_name', models.CharField(default='Triumph Enterprises', max_length=200)),
                ('company_address', models.TextField(default='D.No. 5-5-190/65A, Ehata Nooruddin Shah Qadri, Patel Nagar, Darussalam, Hyderabad, Telangana 500001, India')),
                ('company_email', models.EmailField(default='<EMAIL>', max_length=254)),
                ('company_phone', models.CharField(default='+91 9848486452', max_length=20)),
                ('gst_number', models.CharField(blank=True, help_text='Company GST registration number', max_length=20)),
            ],
            options={
                'ordering': ['-generated_at'],
            },
        ),
        migrations.CreateModel(
            name='Order',
            fields=[
                ('id', models.UUIDField(default=uuid.uuid4, editable=False, primary_key=True, serialize=False, unique=True)),
                ('status', models.CharField(choices=[('PENDING', 'Pending'), ('PROCESSING', 'Processing'), ('PAID', 'Paid'), ('SHIPPED', 'Shipped'), ('DELIVERED', 'Delivered'), ('CANCELLED', 'Cancelled'), ('REFUNDED', 'Refunded')], default='PENDING', max_length=20)),
                ('subtotal', models.DecimalField(decimal_places=2, max_digits=10)),
                ('gst_amount', models.DecimalField(decimal_places=2, default=0, help_text='Total GST amount', max_digits=10)),
                ('cgst_amount', models.DecimalField(decimal_places=2, default=0, help_text='CGST amount', max_digits=10)),
                ('sgst_amount', models.DecimalField(decimal_places=2, default=0, help_text='SGST amount', max_digits=10)),
                ('igst_amount', models.DecimalField(decimal_places=2, default=0, help_text='IGST amount (for inter-state)', max_digits=10)),
                ('shipping_cost', models.DecimalField(decimal_places=2, max_digits=10)),
                ('total', models.DecimalField(decimal_places=2, max_digits=10)),
                ('stripe_payment_intent_id', models.CharField(blank=True, max_length=100)),
                ('stripe_client_secret', models.CharField(blank=True, max_length=100)),
                ('phonepe_transaction_id', models.CharField(blank=True, max_length=100)),
                ('phonepe_payment_url', models.URLField(blank=True, max_length=500)),
                ('tracking_number', models.CharField(blank=True, max_length=100)),
                ('estimated_delivery_date', models.DateField(blank=True, null=True)),
                ('notes', models.TextField(blank=True)),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('updated_at', models.DateTimeField(auto_now=True)),
                ('rapidshyp_enabled', models.BooleanField(default=False, help_text='Whether this order uses Rapidshyp shipping')),
                ('pickup_pincode', models.CharField(blank=True, help_text='Pickup pincode for Rapidshyp', max_length=6)),
                ('delivery_pincode', models.CharField(blank=True, help_text='Delivery pincode for Rapidshyp', max_length=6)),
                ('package_weight', models.DecimalField(blank=True, decimal_places=2, help_text='Package weight in kg for Rapidshyp', max_digits=8, null=True)),
                ('package_length', models.DecimalField(blank=True, decimal_places=2, help_text='Package length in cm', max_digits=8, null=True)),
                ('package_breadth', models.DecimalField(blank=True, decimal_places=2, help_text='Package breadth in cm', max_digits=8, null=True)),
                ('package_height', models.DecimalField(blank=True, decimal_places=2, help_text='Package height in cm', max_digits=8, null=True)),
                ('rapidshyp_rate_data', models.JSONField(blank=True, help_text='Selected Rapidshyp rate data', null=True)),
                ('selected_courier_code', models.CharField(blank=True, help_text='Selected courier code from Rapidshyp', max_length=20)),
                ('rapidshyp_order_id', models.CharField(blank=True, help_text='Rapidshyp order ID', max_length=100)),
            ],
            options={
                'ordering': ['-created_at'],
            },
        ),
        migrations.CreateModel(
            name='OrderItem',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('quantity', models.PositiveIntegerField(validators=[django.core.validators.MinValueValidator(1)])),
                ('unit_price', models.DecimalField(decimal_places=2, max_digits=10)),
                ('total_price', models.DecimalField(decimal_places=2, max_digits=10)),
                ('product_name', models.CharField(max_length=255)),
                ('variant_name', models.CharField(blank=True, max_length=100)),
            ],
        ),
        migrations.CreateModel(
            name='Payment',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('amount', models.DecimalField(decimal_places=2, max_digits=10)),
                ('status', models.CharField(choices=[('PENDING', 'Pending'), ('PROCESSING', 'Processing'), ('COMPLETED', 'Completed'), ('FAILED', 'Failed'), ('REFUNDED', 'Refunded')], default='PENDING', max_length=20)),
                ('payment_method', models.CharField(choices=[('CARD', 'Credit/Debit Card'), ('STRIPE', 'Stripe'), ('PHONEPE', 'PhonePe'), ('COD', 'Cash on Delivery')], default='CARD', max_length=50)),
                ('transaction_id', models.CharField(blank=True, max_length=100)),
                ('phonepe_transaction_details', models.JSONField(blank=True, null=True)),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('updated_at', models.DateTimeField(auto_now=True)),
            ],
        ),
        migrations.CreateModel(
            name='ShippingMethod',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('name', models.CharField(max_length=100)),
                ('description', models.TextField()),
                ('price', models.DecimalField(decimal_places=2, max_digits=10)),
                ('estimated_days', models.PositiveIntegerField()),
                ('is_active', models.BooleanField(default=True)),
            ],
        ),
    ]
