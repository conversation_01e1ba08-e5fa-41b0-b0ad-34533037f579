<!DOCTYPE html>
<html>
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Multiple Failed Login Attempts - Security Alert</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            line-height: 1.6;
            color: #333;
            max-width: 600px;
            margin: 0 auto;
            padding: 20px;
            background-color: #f5f5f5;
        }
        .container {
            background-color: #ffffff;
            border-radius: 8px;
            padding: 30px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        .header {
            text-align: center;
            margin-bottom: 30px;
            border-bottom: 2px solid #f39c12;
            padding-bottom: 20px;
        }
        .priority-high {
            background-color: #f39c12;
            color: white;
            padding: 8px 16px;
            border-radius: 20px;
            font-weight: bold;
            display: inline-block;
            margin-bottom: 15px;
        }
        .alert-icon {
            font-size: 48px;
            color: #f39c12;
            margin-bottom: 10px;
        }
        h1 {
            color: #2c3e50;
            margin: 0;
            font-size: 24px;
        }
        .failed-attempts {
            background-color: #fff3e0;
            border: 2px solid #f39c12;
            border-radius: 8px;
            padding: 20px;
            margin: 20px 0;
            text-align: center;
        }
        .attempt-count {
            font-size: 36px;
            font-weight: bold;
            color: #f39c12;
            margin: 10px 0;
        }
        .event-details {
            background-color: #f8f9fa;
            border-left: 4px solid #f39c12;
            padding: 20px;
            margin: 20px 0;
            border-radius: 0 5px 5px 0;
        }
        .detail-row {
            display: flex;
            justify-content: space-between;
            margin-bottom: 10px;
            padding: 8px 0;
            border-bottom: 1px solid #ecf0f1;
        }
        .detail-label {
            font-weight: bold;
            color: #34495e;
            min-width: 120px;
        }
        .detail-value {
            color: #2c3e50;
            word-break: break-all;
        }
        .timestamp {
            background-color: #ecf0f1;
            padding: 15px;
            border-radius: 5px;
            text-align: center;
            margin: 20px 0;
            font-family: monospace;
            color: #7f8c8d;
        }
        .action-section {
            background-color: #e8f5e8;
            border: 1px solid #27ae60;
            border-radius: 5px;
            padding: 15px;
            margin: 20px 0;
        }
        .action-title {
            color: #27ae60;
            font-weight: bold;
            margin-bottom: 10px;
        }
        .urgent-notice {
            background-color: #ffebee;
            border: 1px solid #f44336;
            border-radius: 5px;
            padding: 15px;
            margin: 20px 0;
            color: #c62828;
        }
        .footer {
            margin-top: 30px;
            padding-top: 20px;
            border-top: 1px solid #ecf0f1;
            font-size: 12px;
            color: #7f8c8d;
            text-align: center;
        }
        .username-highlight {
            background-color: #fff;
            padding: 5px 10px;
            border-radius: 3px;
            font-family: monospace;
            border: 1px solid #ddd;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <div class="alert-icon">🔐</div>
            <div class="priority-high">HIGH PRIORITY</div>
            <h1>Multiple Failed Login Attempts</h1>
            <p style="margin: 10px 0 0 0; color: #7f8c8d;">{{ site_name }} Security Monitoring</p>
        </div>
        
        <div class="failed-attempts">
            <h3 style="margin-top: 0; color: #f39c12;">⚠️ SUSPICIOUS LOGIN ACTIVITY</h3>
            <div class="attempt-count">{{ event_data.attempt_count }}</div>
            <p style="margin-bottom: 0; color: #666;">Failed login attempts detected within {{ event_data.time_window }}</p>
        </div>
        
        <div class="event-details">
            <h3 style="margin-top: 0; color: #f39c12;">Attack Details</h3>
            
            <div class="detail-row">
                <span class="detail-label">Target Username:</span>
                <span class="detail-value">
                    <span class="username-highlight">{{ event_data.username }}</span>
                </span>
            </div>
            
            <div class="detail-row">
                <span class="detail-label">Source IP:</span>
                <span class="detail-value">{{ ip_address }}</span>
            </div>
            
            <div class="detail-row">
                <span class="detail-label">Failed Attempts:</span>
                <span class="detail-value">{{ event_data.attempt_count }} attempts</span>
            </div>
            
            <div class="detail-row">
                <span class="detail-label">Time Window:</span>
                <span class="detail-value">{{ event_data.time_window }}</span>
            </div>
            
            <div class="detail-row">
                <span class="detail-label">Action Taken:</span>
                <span class="detail-value">{{ event_data.action_taken }}</span>
            </div>
        </div>
        
        <div class="timestamp">
            <strong>Alert Generated:</strong> {{ timestamp|date:"F d, Y, g:i A T" }}
        </div>
        
        <div class="action-section">
            <div class="action-title">🛡️ Security Response Activated</div>
            <p>The system is now monitoring this IP address more closely. Additional failed attempts may result in automatic blocking.</p>
        </div>
        
        <div class="urgent-notice">
            <strong>🚨 Immediate Investigation Required:</strong>
            <ul style="margin: 10px 0;">
                <li><strong>Brute Force Attack:</strong> This pattern suggests a potential brute force attack</li>
                <li><strong>Check User Account:</strong> Verify if the target user account is legitimate</li>
                <li><strong>Review Logs:</strong> Examine detailed security logs for this IP and username</li>
                <li><strong>Consider Blocking:</strong> If attacks continue, consider blocking the IP address</li>
                <li><strong>User Notification:</strong> If legitimate user, notify them of the security concern</li>
            </ul>
        </div>
        
        <div style="background-color: #e3f2fd; border: 1px solid #2196f3; border-radius: 5px; padding: 15px; margin: 20px 0;">
            <h4 style="margin-top: 0; color: #1976d2;">🔍 Security Analysis</h4>
            <p><strong>Risk Level:</strong> High - Multiple failed attempts indicate potential unauthorized access attempt</p>
            <p><strong>Pattern:</strong> {{ event_data.attempt_count }} failed logins for username "{{ event_data.username }}" from IP {{ ip_address }}</p>
            <p style="margin-bottom: 0;"><strong>Recommendation:</strong> Monitor closely and consider implementing additional security measures</p>
        </div>
        
        <div class="footer">
            <p>This is an automated security alert from {{ site_name }}.</p>
            <p>Alert generated at {{ timestamp|date:"F d, Y, g:i A T" }}</p>
            <p>Continue monitoring this IP address and username for additional suspicious activity.</p>
        </div>
    </div>
</body>
</html>
