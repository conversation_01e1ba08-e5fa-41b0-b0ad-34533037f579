"""
Django management command for security audit and compliance checking.
This command performs various security checks and generates audit reports.
"""

import os
import json
from django.core.management.base import BaseCommand
from django.conf import settings
from django.contrib.auth import get_user_model
from django.utils import timezone
from datetime import timedelta
from backend.security_monitoring import SecurityEvent, FailedLoginAttempt
from users.models import UserConsent, DataDeletionRequest
import logging

logger = logging.getLogger('security')
User = get_user_model()

class Command(BaseCommand):
    help = 'Perform security audit and generate compliance reports'
    
    def add_arguments(self, parser):
        parser.add_argument(
            '--output',
            type=str,
            default='security_audit_report.json',
            help='Output file for the audit report'
        )
        parser.add_argument(
            '--days',
            type=int,
            default=30,
            help='Number of days to analyze (default: 30)'
        )
        parser.add_argument(
            '--format',
            choices=['json', 'text'],
            default='json',
            help='Output format (json or text)'
        )
    
    def handle(self, *args, **options):
        output_file = options['output']
        days = options['days']
        output_format = options['format']
        
        self.stdout.write(
            self.style.SUCCESS(f'Starting security audit (analyzing last {days} days)')
        )
        
        # Perform audit checks
        audit_results = self._perform_audit(days)
        
        # Generate report
        if output_format == 'json':
            self._generate_json_report(audit_results, output_file)
        else:
            self._generate_text_report(audit_results, output_file)
        
        # Display summary
        self._display_summary(audit_results)
        
        self.stdout.write(
            self.style.SUCCESS(f'Security audit completed. Report saved to: {output_file}')
        )
    
    def _perform_audit(self, days):
        """Perform comprehensive security audit"""
        now = timezone.now()
        start_date = now - timedelta(days=days)
        
        audit_results = {
            'audit_timestamp': now.isoformat(),
            'analysis_period_days': days,
            'analysis_start_date': start_date.isoformat(),
            'security_configuration': self._check_security_configuration(),
            'authentication_security': self._check_authentication_security(start_date),
            'data_protection': self._check_data_protection(),
            'privacy_compliance': self._check_privacy_compliance(),
            'user_activity': self._analyze_user_activity(start_date),
            'security_events': self._analyze_security_events(start_date),
            'recommendations': [],
            'risk_score': 0
        }
        
        # Calculate risk score and generate recommendations
        audit_results['risk_score'] = self._calculate_risk_score(audit_results)
        audit_results['recommendations'] = self._generate_recommendations(audit_results)
        
        return audit_results
    
    def _check_security_configuration(self):
        """Check Django security configuration"""
        config_checks = {
            'debug_mode': settings.DEBUG,
            'secret_key_secure': len(settings.SECRET_KEY) > 50 and settings.SECRET_KEY != 'your-secret-key-here',
            'allowed_hosts_configured': len(settings.ALLOWED_HOSTS) > 0 and '*' not in settings.ALLOWED_HOSTS,
            'https_redirect': getattr(settings, 'SECURE_SSL_REDIRECT', False),
            'hsts_enabled': getattr(settings, 'SECURE_HSTS_SECONDS', 0) > 0,
            'session_cookie_secure': getattr(settings, 'SESSION_COOKIE_SECURE', False),
            'csrf_cookie_secure': getattr(settings, 'CSRF_COOKIE_SECURE', False),
            'content_type_nosniff': getattr(settings, 'SECURE_CONTENT_TYPE_NOSNIFF', False),
            'browser_xss_filter': getattr(settings, 'SECURE_BROWSER_XSS_FILTER', False),
            'x_frame_options': getattr(settings, 'X_FRAME_OPTIONS', None) == 'DENY',
        }
        
        return config_checks
    
    def _check_authentication_security(self, start_date):
        """Check authentication security metrics"""
        total_login_attempts = FailedLoginAttempt.objects.filter(timestamp__gte=start_date).count()
        unique_ips_failed = FailedLoginAttempt.objects.filter(
            timestamp__gte=start_date
        ).values('ip_address').distinct().count()
        
        successful_logins = SecurityEvent.objects.filter(
            event_type='LOGIN_SUCCESS',
            timestamp__gte=start_date
        ).count()
        
        failed_logins = SecurityEvent.objects.filter(
            event_type='LOGIN_FAILED',
            timestamp__gte=start_date
        ).count()
        
        return {
            'total_failed_attempts': total_login_attempts,
            'unique_ips_with_failures': unique_ips_failed,
            'successful_logins': successful_logins,
            'failed_logins': failed_logins,
            'failure_rate': (failed_logins / max(successful_logins + failed_logins, 1)) * 100,
            'jwt_token_lifetime_minutes': getattr(settings, 'SIMPLE_JWT', {}).get('ACCESS_TOKEN_LIFETIME', timedelta(minutes=60)).total_seconds() / 60,
        }
    
    def _check_data_protection(self):
        """Check data protection measures"""
        # Check if encryption utilities are being used
        encryption_configured = os.path.exists(os.path.join(settings.BASE_DIR, 'backend', 'encryption_utils.py'))
        
        # Check database configuration
        db_config = settings.DATABASES.get('default', {})
        db_ssl_enabled = 'sslmode' in db_config.get('OPTIONS', {})
        
        return {
            'encryption_utilities_available': encryption_configured,
            'database_ssl_enabled': db_ssl_enabled,
            'media_files_secure': not settings.DEBUG,  # In production, media should be served securely
            'static_files_secure': not settings.DEBUG,
        }
    
    def _check_privacy_compliance(self):
        """Check privacy compliance status"""
        total_users = User.objects.count()
        users_with_consent = UserConsent.objects.values('user').distinct().count()
        
        pending_deletions = DataDeletionRequest.objects.filter(status='PENDING').count()
        completed_deletions = DataDeletionRequest.objects.filter(status='COMPLETED').count()
        
        # Check consent types coverage
        consent_coverage = {}
        for consent_type, _ in UserConsent.CONSENT_TYPES:
            granted_count = UserConsent.objects.filter(
                consent_type=consent_type,
                granted=True
            ).count()
            consent_coverage[consent_type] = {
                'granted_count': granted_count,
                'percentage': (granted_count / max(total_users, 1)) * 100
            }
        
        return {
            'total_users': total_users,
            'users_with_consent_records': users_with_consent,
            'consent_coverage_percentage': (users_with_consent / max(total_users, 1)) * 100,
            'consent_type_coverage': consent_coverage,
            'pending_deletion_requests': pending_deletions,
            'completed_deletion_requests': completed_deletions,
        }
    
    def _analyze_user_activity(self, start_date):
        """Analyze user activity patterns"""
        active_users = SecurityEvent.objects.filter(
            event_type='LOGIN_SUCCESS',
            timestamp__gte=start_date
        ).values('user').distinct().count()
        
        admin_logins = SecurityEvent.objects.filter(
            event_type='LOGIN_SUCCESS',
            timestamp__gte=start_date,
            user__is_superuser=True
        ).count()
        
        suspicious_activities = SecurityEvent.objects.filter(
            event_type='SUSPICIOUS_ACTIVITY',
            timestamp__gte=start_date
        ).count()
        
        return {
            'active_users': active_users,
            'admin_logins': admin_logins,
            'suspicious_activities': suspicious_activities,
        }
    
    def _analyze_security_events(self, start_date):
        """Analyze security events"""
        events_by_type = {}
        for event_type, _ in SecurityEvent.EVENT_TYPES:
            count = SecurityEvent.objects.filter(
                event_type=event_type,
                timestamp__gte=start_date
            ).count()
            events_by_type[event_type] = count
        
        total_events = SecurityEvent.objects.filter(timestamp__gte=start_date).count()
        
        return {
            'total_events': total_events,
            'events_by_type': events_by_type,
        }
    
    def _calculate_risk_score(self, audit_results):
        """Calculate overall risk score (0-100, lower is better)"""
        risk_score = 0
        
        # Security configuration risks
        config = audit_results['security_configuration']
        if config['debug_mode']:
            risk_score += 20
        if not config['secret_key_secure']:
            risk_score += 15
        if not config['allowed_hosts_configured']:
            risk_score += 10
        if not config['https_redirect']:
            risk_score += 10
        if not config['hsts_enabled']:
            risk_score += 5
        
        # Authentication risks
        auth = audit_results['authentication_security']
        if auth['failure_rate'] > 20:
            risk_score += 10
        if auth['jwt_token_lifetime_minutes'] > 60:
            risk_score += 5
        
        # Privacy compliance risks
        privacy = audit_results['privacy_compliance']
        if privacy['consent_coverage_percentage'] < 50:
            risk_score += 10
        if privacy['pending_deletion_requests'] > 10:
            risk_score += 5
        
        return min(risk_score, 100)
    
    def _generate_recommendations(self, audit_results):
        """Generate security recommendations based on audit results"""
        recommendations = []
        
        config = audit_results['security_configuration']
        if config['debug_mode']:
            recommendations.append({
                'priority': 'CRITICAL',
                'category': 'Configuration',
                'issue': 'Debug mode is enabled in production',
                'recommendation': 'Set DEBUG=False in production settings'
            })
        
        if not config['secret_key_secure']:
            recommendations.append({
                'priority': 'CRITICAL',
                'category': 'Configuration',
                'issue': 'Weak or default secret key',
                'recommendation': 'Generate a strong, unique secret key for production'
            })
        
        if not config['https_redirect']:
            recommendations.append({
                'priority': 'HIGH',
                'category': 'Security',
                'issue': 'HTTPS redirect not enabled',
                'recommendation': 'Enable SECURE_SSL_REDIRECT=True to force HTTPS'
            })
        
        auth = audit_results['authentication_security']
        if auth['failure_rate'] > 20:
            recommendations.append({
                'priority': 'MEDIUM',
                'category': 'Authentication',
                'issue': f"High login failure rate: {auth['failure_rate']:.1f}%",
                'recommendation': 'Investigate potential brute force attacks and strengthen rate limiting'
            })
        
        privacy = audit_results['privacy_compliance']
        if privacy['consent_coverage_percentage'] < 50:
            recommendations.append({
                'priority': 'HIGH',
                'category': 'Privacy',
                'issue': f"Low consent coverage: {privacy['consent_coverage_percentage']:.1f}%",
                'recommendation': 'Implement consent collection mechanisms for all users'
            })
        
        return recommendations
    
    def _generate_json_report(self, audit_results, output_file):
        """Generate JSON audit report"""
        with open(output_file, 'w') as f:
            json.dump(audit_results, f, indent=2, default=str)
    
    def _generate_text_report(self, audit_results, output_file):
        """Generate text audit report"""
        with open(output_file, 'w') as f:
            f.write("SECURITY AUDIT REPORT\n")
            f.write("=" * 50 + "\n\n")
            f.write(f"Audit Date: {audit_results['audit_timestamp']}\n")
            f.write(f"Analysis Period: {audit_results['analysis_period_days']} days\n")
            f.write(f"Risk Score: {audit_results['risk_score']}/100\n\n")
            
            f.write("RECOMMENDATIONS:\n")
            f.write("-" * 20 + "\n")
            for rec in audit_results['recommendations']:
                f.write(f"[{rec['priority']}] {rec['category']}: {rec['issue']}\n")
                f.write(f"  → {rec['recommendation']}\n\n")
    
    def _display_summary(self, audit_results):
        """Display audit summary to console"""
        risk_score = audit_results['risk_score']
        
        if risk_score >= 70:
            risk_level = self.style.ERROR('CRITICAL')
        elif risk_score >= 40:
            risk_level = self.style.WARNING('HIGH')
        elif risk_score >= 20:
            risk_level = self.style.WARNING('MEDIUM')
        else:
            risk_level = self.style.SUCCESS('LOW')
        
        self.stdout.write(f"\nSECURITY AUDIT SUMMARY:")
        self.stdout.write(f"Risk Score: {risk_score}/100 ({risk_level})")
        self.stdout.write(f"Recommendations: {len(audit_results['recommendations'])}")
        
        if audit_results['recommendations']:
            self.stdout.write("\nTop Recommendations:")
            for rec in audit_results['recommendations'][:3]:
                priority_style = self.style.ERROR if rec['priority'] == 'CRITICAL' else self.style.WARNING
                self.stdout.write(f"  {priority_style(rec['priority'])}: {rec['issue']}")
