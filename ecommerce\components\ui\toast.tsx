import * as React from "react"
import * as ToastPrimitives from "@radix-ui/react-toast"
import { cva, type VariantProps } from "class-variance-authority"
import { X, CheckCircle2, AlertCircle, AlertTriangle } from "lucide-react"

import { cn } from "../../lib/utils"

// Custom Info icon component for the toast
const Info = React.forwardRef<
  SVGSVGElement,
  React.SVGProps<SVGSVGElement>
>((props, ref) => (
  <svg
    ref={ref}
    xmlns="http://www.w3.org/2000/svg"
    viewBox="0 0 24 24"
    fill="currentColor"
    stroke="currentColor"
    strokeWidth="0.5"
    strokeLinecap="round"
    strokeLinejoin="round"
    {...props}
  >
    <circle cx="12" cy="12" r="10" />
    <path d="M12 8v4M12 16h.01" stroke="white" strokeWidth="2" />
  </svg>
))
Info.displayName = "InfoIcon"

const ToastProvider = ToastPrimitives.Provider

const ToastViewport = React.forwardRef<
  React.ElementRef<typeof ToastPrimitives.Viewport>,
  React.ComponentPropsWithoutRef<typeof ToastPrimitives.Viewport>
>(({ className, ...props }, ref) => (
  <ToastPrimitives.Viewport
    ref={ref}
    className={cn(
      "fixed z-[100] flex max-h-screen flex-col-reverse gap-2 p-4 outline-none",
      "top-0 right-0 left-0 sm:right-0 sm:left-auto sm:top-auto sm:bottom-0 sm:flex-col md:max-w-[420px]",
      className
    )}
    {...props}
  />
))
ToastViewport.displayName = ToastPrimitives.Viewport.displayName

const toastVariants = cva(
  "group pointer-events-auto relative flex w-full items-center overflow-hidden rounded-lg border p-4 pr-6 shadow-lg transition-all data-[swipe=cancel]:translate-x-0 data-[swipe=end]:translate-x-[var(--radix-toast-swipe-end-x)] data-[swipe=move]:translate-x-[var(--radix-toast-swipe-move-x)] data-[swipe=move]:transition-none data-[state=open]:animate-in data-[state=closed]:animate-out data-[swipe=end]:animate-out data-[state=closed]:fade-out-80 data-[state=closed]:slide-out-to-right-full data-[state=open]:slide-in-from-top-full data-[state=open]:sm:slide-in-from-bottom-full",
  {
    variants: {
      variant: {
        default: "border-2 border-gray-200 bg-white text-gray-900",
        destructive: "destructive group border-2 border-red-200 bg-red-50 text-red-900",
        success: "border-2 border-green-200 bg-green-50 text-green-900",
        warning: "border-2 border-yellow-200 bg-yellow-50 text-yellow-900",
        info: "border border-blue-100 bg-blue-50 text-blue-800",
      },
    },
    defaultVariants: {
      variant: "default",
    },
  }
)

const ToastIcon = React.forwardRef<
  HTMLDivElement,
  React.HTMLAttributes<HTMLDivElement> & {
    variant?: "default" | "destructive" | "success" | "warning" | "info"
  }
>(({ className, variant = "default", ...props }, ref) => {
  const IconComponent = {
    default: Info,
    destructive: AlertCircle,
    success: CheckCircle2,
    warning: AlertTriangle,
    info: Info,
  }[variant]

  return (
    <div
      ref={ref}
      className={cn("flex h-6 w-6 items-center justify-center", className)}
      {...props}
    >
      <IconComponent className={cn(
        "h-5 w-5",
        variant === "default" && "text-gray-600",
        variant === "destructive" && "text-red-600",
        variant === "success" && "text-green-600",
        variant === "warning" && "text-yellow-600",
        variant === "info" && "text-blue-600",
      )} />
    </div>
  )
})
ToastIcon.displayName = "ToastIcon"

const Toast = React.forwardRef<
  React.ElementRef<typeof ToastPrimitives.Root>,
  React.ComponentPropsWithoutRef<typeof ToastPrimitives.Root> &
    VariantProps<typeof toastVariants>
>(({ className, variant, ...props }, ref) => {
  return (
    <ToastPrimitives.Root
      ref={ref}
      className={cn(toastVariants({ variant }), className)}
      {...props}
    />
  )
})
Toast.displayName = ToastPrimitives.Root.displayName

const ToastAction = React.forwardRef<
  React.ElementRef<typeof ToastPrimitives.Action>,
  React.ComponentPropsWithoutRef<typeof ToastPrimitives.Action>
>(({ className, ...props }, ref) => (
  <ToastPrimitives.Action
    ref={ref}
    className={cn(
      "inline-flex h-8 shrink-0 items-center justify-center rounded-md border text-sm font-medium shadow-sm transition-colors focus:outline-none focus:ring-1 focus:ring-ring disabled:pointer-events-none disabled:opacity-50",
      "bg-white text-gray-900 border-gray-200 hover:bg-gray-50 hover:text-gray-900",
      "group-[.destructive]:bg-white group-[.destructive]:text-red-600 group-[.destructive]:border-red-200 group-[.destructive]:hover:bg-red-50",
      "group-[.success]:bg-white group-[.success]:text-green-600 group-[.success]:border-green-200 group-[.success]:hover:bg-green-50",
      "group-[.warning]:bg-white group-[.warning]:text-yellow-600 group-[.warning]:border-yellow-200 group-[.warning]:hover:bg-yellow-50",
      "group-[.info]:bg-white group-[.info]:text-blue-600 group-[.info]:border-blue-200 group-[.info]:hover:bg-blue-50",
      className
    )}
    {...props}
  />
))
ToastAction.displayName = ToastPrimitives.Action.displayName

const ToastClose = React.forwardRef<
  React.ElementRef<typeof ToastPrimitives.Close>,
  React.ComponentPropsWithoutRef<typeof ToastPrimitives.Close>
>(({ className, ...props }, ref) => (
  <ToastPrimitives.Close
    ref={ref}
    className={cn(
      "absolute right-2 top-2 rounded-full p-1.5 bg-gray-100/80 text-gray-500 opacity-70 transition-all hover:bg-gray-200 hover:text-gray-900 hover:opacity-100 focus:opacity-100 focus:outline-none group-hover:opacity-100 group-[.destructive]:bg-red-100 group-[.destructive]:text-red-600 group-[.destructive]:hover:bg-red-200 group-[.success]:bg-green-100 group-[.success]:text-green-600 group-[.success]:hover:bg-green-200 group-[.warning]:bg-yellow-100 group-[.warning]:text-yellow-600 group-[.warning]:hover:bg-yellow-200 group-[.info]:bg-blue-100 group-[.info]:text-blue-600 group-[.info]:hover:bg-blue-200",
      className
    )}
    toast-close=""
    {...props}
  >
    <X className="h-3.5 w-3.5" />
  </ToastPrimitives.Close>
))
ToastClose.displayName = ToastPrimitives.Close.displayName

const ToastTitle = React.forwardRef<
  React.ElementRef<typeof ToastPrimitives.Title>,
  React.ComponentPropsWithoutRef<typeof ToastPrimitives.Title>
>(({ className, ...props }, ref) => (
  <ToastPrimitives.Title
    ref={ref}
    className={cn("text-sm font-medium leading-none tracking-tight", className)}
    {...props}
  />
))
ToastTitle.displayName = ToastPrimitives.Title.displayName

const ToastDescription = React.forwardRef<
  React.ElementRef<typeof ToastPrimitives.Description>,
  React.ComponentPropsWithoutRef<typeof ToastPrimitives.Description>
>(({ className, ...props }, ref) => (
  <ToastPrimitives.Description
    ref={ref}
    className={cn("text-sm opacity-90 leading-relaxed mt-1", className)}
    {...props}
  />
))
ToastDescription.displayName = ToastPrimitives.Description.displayName

type ToastProps = React.ComponentPropsWithoutRef<typeof Toast>

type ToastActionElement = React.ReactElement<typeof ToastAction>

export {
  type ToastProps,
  type ToastActionElement,
  ToastProvider,
  ToastViewport,
  Toast,
  ToastTitle,
  ToastDescription,
  ToastClose,
  ToastAction,
  ToastIcon,
}
