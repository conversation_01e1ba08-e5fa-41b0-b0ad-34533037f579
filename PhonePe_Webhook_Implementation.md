# PhonePe Webhook Implementation Guide

## Overview

Webhooks are essential for real-time payment status updates from PhonePe to your application. This document outlines the implementation details for handling PhonePe webhooks securely and efficiently.

## 1. Webhook Basics

### 1.1 What are Webhooks?
Webhooks are HTTP callbacks that are triggered by specific events in PhonePe's system. When a payment event occurs (such as successful payment, failed payment, or refund), PhonePe sends an HTTP POST request to a URL you specify, containing data about the event.

### 1.2 Why Use Webhooks?
- **Real-time updates**: Get immediate notification of payment events
- **Reliability**: Ensure payment status is updated even if the customer doesn't return to your site
- **Automation**: Automatically process orders upon successful payment
- **Reconciliation**: Keep your system in sync with PhonePe's payment records

## 2. Webhook Setup

### 2.1 Configuring Webhook URL
1. Create a dedicated endpoint in your application for PhonePe webhooks
2. Register this URL in the PhonePe merchant dashboard
3. Ensure the URL is publicly accessible and uses HTTPS
4. Configure the webhook URL in your environment variables:
   ```
   PHONEPE_WEBHOOK_URL=https://your-domain.com/api/v1/payments/phonepe/webhook
   ```

### 2.2 Webhook Events
PhonePe sends webhooks for various payment events, including:
- Payment Success
- Payment Failure
- Payment Pending
- Refund Initiated
- Refund Processed
- Refund Failed

## 3. Webhook Security

### 3.1 Signature Verification
PhonePe includes a signature in the `X-VERIFY` header of webhook requests. This signature must be verified to ensure the webhook is legitimate:

```python
def verify_webhook_signature(request_data, signature, salt_key, salt_index):
    """Verify the signature of a webhook request"""
    # Calculate expected signature
    expected_signature = hashlib.sha256(
        (request_data + salt_key).encode()
    ).hexdigest() + "###" + salt_index
    
    # Compare with provided signature
    return expected_signature == signature
```

### 3.2 IP Whitelisting
Implement IP whitelisting to accept webhook requests only from PhonePe's official IP ranges:

```python
# Example middleware for IP whitelisting
def phonepe_ip_whitelist_middleware(get_response):
    # PhonePe IP ranges (example - get actual ranges from PhonePe)
    PHONEPE_IP_RANGES = [
        '*************/22',
        '*************/22',
        # Add more IP ranges as provided by PhonePe
    ]
    
    def middleware(request):
        # Only check for webhook endpoint
        if request.path == '/api/v1/payments/phonepe/webhook/':
            client_ip = get_client_ip(request)
            if not is_ip_in_ranges(client_ip, PHONEPE_IP_RANGES):
                return JsonResponse(
                    {"error": "Unauthorized IP"}, 
                    status=403
                )
        
        return get_response(request)
    
    return middleware
```

### 3.3 Idempotency
Implement idempotency to handle duplicate webhook events:

```python
def process_webhook_idempotently(webhook_id, process_func):
    """Process webhook with idempotency check"""
    # Check if webhook has already been processed
    if WebhookProcessingRecord.objects.filter(webhook_id=webhook_id).exists():
        logger.info(f"Webhook {webhook_id} already processed, skipping")
        return False
    
    # Create a record to mark this webhook as being processed
    WebhookProcessingRecord.objects.create(webhook_id=webhook_id)
    
    try:
        # Process the webhook
        process_func()
        return True
    except Exception as e:
        logger.error(f"Error processing webhook {webhook_id}: {str(e)}")
        # Don't delete the record - this prevents reprocessing on error
        return False
```

## 4. Webhook Implementation

### 4.1 Webhook Handler
Create a dedicated webhook handler in your Django application:

```python
@csrf_exempt
@require_POST
def phonepe_webhook(request):
    """Handle PhonePe webhooks"""
    try:
        # Get request data
        request_data = request.body.decode('utf-8')
        payload = json.loads(request_data)
        
        # Get signature from headers
        signature = request.headers.get('X-VERIFY')
        if not signature:
            logger.error("No signature provided in webhook")
            return JsonResponse({"status": "error", "message": "No signature provided"}, status=400)
        
        # Initialize PhonePe service
        phonepe_service = PhonePeService()
        
        # Verify signature
        if not phonepe_service.verify_webhook_signature(request_data, signature):
            logger.error("Invalid signature in webhook")
            return JsonResponse({"status": "error", "message": "Invalid signature"}, status=400)
        
        # Extract webhook ID for idempotency
        webhook_id = payload.get("webhookId", "")
        
        # Process webhook idempotently
        def process_webhook():
            # Process webhook event
            event_type = payload.get("event")
            transaction_id = payload.get("data", {}).get("merchantTransactionId")
            
            if not transaction_id:
                logger.error("No transaction ID in webhook payload")
                raise ValueError("No transaction ID provided")
            
            # Get the order
            try:
                order = Order.objects.get(phonepe_transaction_id=transaction_id)
            except Order.DoesNotExist:
                logger.error(f"Order not found for transaction ID: {transaction_id}")
                raise ValueError(f"Order not found for transaction ID: {transaction_id}")
            
            # Process different event types
            if event_type == "PAYMENT_SUCCESS":
                handle_payment_success(order, payload)
            elif event_type == "PAYMENT_FAILED":
                handle_payment_failure(order, payload)
            elif event_type == "PAYMENT_REFUNDED":
                handle_payment_refund(order, payload)
            else:
                logger.warning(f"Unhandled webhook event type: {event_type}")
        
        # Process the webhook with idempotency
        if webhook_id:
            process_webhook_idempotently(webhook_id, process_webhook)
        else:
            # If no webhook ID, still process but log a warning
            logger.warning("No webhook ID provided for idempotency")
            process_webhook()
        
        # Always return 200 OK to acknowledge receipt
        return JsonResponse({"status": "success"})
    except json.JSONDecodeError:
        logger.error("Invalid JSON in webhook payload")
        return JsonResponse({"status": "error", "message": "Invalid JSON"}, status=400)
    except Exception as e:
        logger.error(f"Error in PhonePe webhook: {str(e)}")
        # Still return 200 to prevent PhonePe from retrying
        # Log the error for investigation
        return JsonResponse({"status": "error", "message": str(e)})
```

### 4.2 Event Handlers
Implement specific handlers for different webhook events:

```python
def handle_payment_success(order, payload):
    """Handle successful payment webhook"""
    # Update order status
    order.status = "PAID"
    order.save()
    
    # Create payment record if it doesn't exist
    payment_data = payload.get("data", {})
    transaction_id = payment_data.get("merchantTransactionId")
    
    Payment.objects.get_or_create(
        order=order,
        transaction_id=transaction_id,
        defaults={
            "amount": order.total,
            "status": "COMPLETED",
            "payment_method": "PHONEPE",
            "phonepe_transaction_details": payment_data
        }
    )
    
    # Additional business logic (e.g., send confirmation email)
    send_order_confirmation_email(order)
    
    logger.info(f"Payment success processed for order {order.id}")

def handle_payment_failure(order, payload):
    """Handle failed payment webhook"""
    # Update order status
    order.status = "PAYMENT_FAILED"
    order.save()
    
    # Create failed payment record
    payment_data = payload.get("data", {})
    transaction_id = payment_data.get("merchantTransactionId")
    
    Payment.objects.create(
        order=order,
        amount=order.total,
        status="FAILED",
        payment_method="PHONEPE",
        transaction_id=transaction_id,
        phonepe_transaction_details=payment_data
    )
    
    # Additional business logic (e.g., notify customer)
    send_payment_failure_notification(order)
    
    logger.info(f"Payment failure processed for order {order.id}")

def handle_payment_refund(order, payload):
    """Handle refund webhook"""
    # Update order status
    order.status = "REFUNDED"
    order.save()
    
    # Create refund payment record
    payment_data = payload.get("data", {})
    transaction_id = payment_data.get("merchantTransactionId")
    refund_id = payment_data.get("merchantRefundId", f"REFUND_{transaction_id}")
    refund_amount = payment_data.get("amount", order.total) / 100  # Convert from paise
    
    Payment.objects.create(
        order=order,
        amount=-refund_amount,  # Negative amount for refunds
        status="COMPLETED",
        payment_method="PHONEPE_REFUND",
        transaction_id=refund_id,
        phonepe_transaction_details=payment_data
    )
    
    # Additional business logic (e.g., notify customer)
    send_refund_notification(order, refund_amount)
    
    logger.info(f"Refund processed for order {order.id}")
```

### 4.3 Webhook Processing Model
Create a model to track processed webhooks for idempotency:

```python
# Add to models.py
class WebhookProcessingRecord(models.Model):
    webhook_id = models.CharField(max_length=100, unique=True)
    created_at = models.DateTimeField(auto_now_add=True)
    
    def __str__(self):
        return f"Webhook {self.webhook_id} processed at {self.created_at}"
```

## 5. Webhook Testing

### 5.1 Local Testing
For local development and testing:

1. Use a service like ngrok to expose your local server:
   ```
   ngrok http 8000
   ```

2. Set your webhook URL in the PhonePe dashboard to your ngrok URL:
   ```
   https://your-ngrok-url.ngrok.io/api/v1/payments/phonepe/webhook/
   ```

3. Create a test endpoint to simulate webhook events:
   ```python
   @require_POST
   @staff_member_required
   def simulate_webhook(request):
       """Simulate a PhonePe webhook for testing"""
       event_type = request.POST.get('event_type')
       transaction_id = request.POST.get('transaction_id')
       
       # Create sample payload based on event type
       payload = create_sample_webhook_payload(event_type, transaction_id)
       
       # Calculate signature
       signature = calculate_test_signature(payload)
       
       # Send webhook to your endpoint
       response = requests.post(
           settings.PHONEPE_WEBHOOK_URL,
           json=payload,
           headers={'X-VERIFY': signature}
       )
       
       return JsonResponse({
           'status': 'webhook_sent',
           'response_status': response.status_code,
           'response_body': response.text
       })
   ```

### 5.2 Webhook Logging
Implement comprehensive logging for webhook events:

```python
# Add to settings.py
LOGGING = {
    # ... existing logging configuration ...
    'loggers': {
        # ... existing loggers ...
        'phonepe_webhooks': {
            'handlers': ['file', 'console'],
            'level': 'INFO',
            'propagate': True,
        },
    },
}

# In your webhook handler
logger = logging.getLogger('phonepe_webhooks')
```

## 6. Webhook Monitoring

### 6.1 Monitoring Strategies
- Log all webhook events with appropriate detail
- Set up alerts for webhook failures
- Monitor webhook processing times
- Track webhook volumes and patterns
- Implement a dashboard for webhook activity

### 6.2 Error Handling
- Log detailed error information for failed webhook processing
- Implement a retry mechanism for transient failures
- Create a process for manual intervention when needed
- Monitor and alert on repeated webhook failures

## 7. Best Practices

1. **Always return 200 OK**: Even if processing fails, acknowledge receipt to prevent retries
2. **Process asynchronously**: For complex operations, acknowledge the webhook and process in the background
3. **Implement idempotency**: Handle duplicate webhooks gracefully
4. **Verify signatures**: Always verify webhook signatures
5. **Log comprehensively**: Maintain detailed logs of all webhook activity
6. **Monitor actively**: Set up alerts for webhook failures
7. **Test thoroughly**: Test all webhook scenarios before going live
8. **Have fallbacks**: Implement alternative mechanisms for payment status updates

## Conclusion

A robust webhook implementation is critical for reliable payment processing with PhonePe. By following these guidelines, you can ensure that your application handles payment events securely and reliably, providing a seamless experience for your customers while maintaining accurate payment records.
