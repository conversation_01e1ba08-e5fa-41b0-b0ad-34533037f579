"""
Utility functions for shipping module
"""

import re
import hashlib
import hmac
import json
from decimal import Decimal
from datetime import datetime, timed<PERSON>ta
from typing import Dict, List, Optional, Union
from django.utils import timezone
from django.core.cache import cache
from django.conf import settings

from .constants import VALIDATION_SETTINGS, CACHE_SETTINGS


def validate_pincode(pincode: str) -> bool:
    """
    Validate Indian pincode format
    
    Args:
        pincode: Pincode string to validate
        
    Returns:
        bool: True if valid, False otherwise
    """
    if not pincode:
        return False
    
    pattern = VALIDATION_SETTINGS['PINCODE_PATTERN']
    return bool(re.match(pattern, str(pincode).strip()))


def validate_phone_number(phone: str) -> bool:
    """
    Validate Indian phone number format
    
    Args:
        phone: Phone number string to validate
        
    Returns:
        bool: True if valid, False otherwise
    """
    if not phone:
        return False
    
    # Remove any non-digit characters
    phone_digits = re.sub(r'\D', '', str(phone))
    
    # Check if it matches Indian mobile number pattern
    pattern = VALIDATION_SETTINGS['PHONE_PATTERN']
    return bool(re.match(pattern, phone_digits))


def validate_weight(weight: Union[str, int, float, Decimal]) -> bool:
    """
    Validate package weight
    
    Args:
        weight: Weight to validate
        
    Returns:
        bool: True if valid, False otherwise
    """
    try:
        weight_decimal = Decimal(str(weight))
        min_weight = Decimal(str(VALIDATION_SETTINGS['MIN_WEIGHT']))
        max_weight = Decimal(str(VALIDATION_SETTINGS['MAX_WEIGHT']))
        
        return min_weight <= weight_decimal <= max_weight
    except (ValueError, TypeError):
        return False


def validate_order_value(value: Union[str, int, float, Decimal]) -> bool:
    """
    Validate order value
    
    Args:
        value: Order value to validate
        
    Returns:
        bool: True if valid, False otherwise
    """
    try:
        value_decimal = Decimal(str(value))
        min_value = Decimal(str(VALIDATION_SETTINGS['MIN_ORDER_VALUE']))
        
        return value_decimal >= min_value
    except (ValueError, TypeError):
        return False


def generate_cache_key(prefix: str, **kwargs) -> str:
    """
    Generate a cache key from prefix and parameters
    
    Args:
        prefix: Cache key prefix
        **kwargs: Parameters to include in cache key
        
    Returns:
        str: Generated cache key
    """
    # Sort kwargs for consistent key generation
    sorted_params = sorted(kwargs.items())
    params_str = '_'.join(f"{k}:{v}" for k, v in sorted_params)
    
    # Create hash for long parameter strings
    if len(params_str) > 100:
        params_hash = hashlib.md5(params_str.encode()).hexdigest()
        return f"{prefix}_{params_hash}"
    
    return f"{prefix}_{params_str}"


def get_cached_shipping_rates(pickup_pincode: str, delivery_pincode: str, 
                            weight: float, cod: bool = False) -> Optional[Dict]:
    """
    Get cached shipping rates
    
    Args:
        pickup_pincode: Pickup pincode
        delivery_pincode: Delivery pincode
        weight: Package weight
        cod: Cash on delivery flag
        
    Returns:
        Dict or None: Cached rates if available
    """
    cache_key = generate_cache_key(
        'shipping_rates',
        pickup=pickup_pincode,
        delivery=delivery_pincode,
        weight=weight,
        cod=cod
    )
    
    return cache.get(cache_key)


def set_cached_shipping_rates(pickup_pincode: str, delivery_pincode: str, 
                            weight: float, rates_data: Dict, cod: bool = False) -> None:
    """
    Cache shipping rates
    
    Args:
        pickup_pincode: Pickup pincode
        delivery_pincode: Delivery pincode
        weight: Package weight
        rates_data: Rates data to cache
        cod: Cash on delivery flag
    """
    cache_key = generate_cache_key(
        'shipping_rates',
        pickup=pickup_pincode,
        delivery=delivery_pincode,
        weight=weight,
        cod=cod
    )
    
    timeout = CACHE_SETTINGS['SHIPPING_RATES_TIMEOUT']
    cache.set(cache_key, rates_data, timeout)


def format_phone_number(phone: str) -> str:
    """
    Format phone number for API calls
    
    Args:
        phone: Raw phone number
        
    Returns:
        str: Formatted phone number
    """
    if not phone:
        return ''
    
    # Remove all non-digit characters
    digits = re.sub(r'\D', '', str(phone))
    
    # If it starts with country code, remove it
    if digits.startswith('91') and len(digits) == 12:
        digits = digits[2:]
    
    return digits


def format_address_for_rapidshyp(address) -> Dict:
    """
    Format Django address object for Rapidshyp API
    
    Args:
        address: Django Address model instance
        
    Returns:
        Dict: Formatted address for Rapidshyp
    """
    return {
        'firstName': getattr(address, 'first_name', '') or address.user.first_name,
        'lastName': getattr(address, 'last_name', '') or address.user.last_name,
        'addressLine1': address.street_address,
        'addressLine2': getattr(address, 'apartment', '') or '',
        'pinCode': address.postal_code,
        'email': getattr(address, 'order_user_email', '') or address.user.email,
        'phone': format_phone_number(
            getattr(address, 'order_user_phone', '') or address.user.phone or ''
        )
    }


def calculate_package_dimensions(order_items) -> Dict:
    """
    Calculate package dimensions from order items
    
    Args:
        order_items: QuerySet or list of order items
        
    Returns:
        Dict: Package dimensions
    """
    total_weight = 0
    max_length = 0
    max_breadth = 0
    total_height = 0
    
    for item in order_items:
        # Get product dimensions if available
        product = item.product
        variant = item.variant
        
        # Calculate weight
        item_weight = 0
        if variant and hasattr(variant, 'weight') and variant.weight:
            item_weight = float(variant.weight) * item.quantity
        elif product and hasattr(product, 'weight') and product.weight:
            item_weight = float(product.weight) * item.quantity
        else:
            # Default weight if not specified
            item_weight = 0.5 * item.quantity
        
        total_weight += item_weight
        
        # Calculate dimensions (simplified logic)
        if variant and hasattr(variant, 'length'):
            max_length = max(max_length, float(variant.length or 0))
            max_breadth = max(max_breadth, float(variant.breadth or 0))
            total_height += float(variant.height or 0) * item.quantity
        elif product and hasattr(product, 'length'):
            max_length = max(max_length, float(product.length or 0))
            max_breadth = max(max_breadth, float(product.breadth or 0))
            total_height += float(product.height or 0) * item.quantity
    
    # Default dimensions if not specified
    if max_length == 0:
        max_length = 20.0
    if max_breadth == 0:
        max_breadth = 15.0
    if total_height == 0:
        total_height = 10.0
    if total_weight == 0:
        total_weight = 1.0
    
    return {
        'packageLength': max_length,
        'packageBreadth': max_breadth,
        'packageHeight': total_height,
        'packageWeight': total_weight * 1000  # Convert to grams for Rapidshyp
    }


def verify_webhook_signature(payload: str, signature: str, secret: str) -> bool:
    """
    Verify webhook signature from Rapidshyp
    
    Args:
        payload: Raw webhook payload
        signature: Signature from webhook headers
        secret: Webhook secret
        
    Returns:
        bool: True if signature is valid
    """
    if not all([payload, signature, secret]):
        return False
    
    try:
        # Calculate expected signature
        expected_signature = hmac.new(
            secret.encode('utf-8'),
            payload.encode('utf-8'),
            hashlib.sha256
        ).hexdigest()
        
        # Compare signatures
        return hmac.compare_digest(signature, expected_signature)
    except Exception:
        return False


def get_business_days_between(start_date: datetime, end_date: datetime) -> int:
    """
    Calculate business days between two dates
    
    Args:
        start_date: Start date
        end_date: End date
        
    Returns:
        int: Number of business days
    """
    if start_date > end_date:
        return 0
    
    business_days = 0
    current_date = start_date
    
    while current_date <= end_date:
        # Monday = 0, Sunday = 6
        if current_date.weekday() < 5:  # Monday to Friday
            business_days += 1
        current_date += timedelta(days=1)
    
    return business_days


def estimate_delivery_date(pickup_date: datetime, estimated_days: int) -> datetime:
    """
    Estimate delivery date considering business days
    
    Args:
        pickup_date: Pickup date
        estimated_days: Estimated delivery days
        
    Returns:
        datetime: Estimated delivery date
    """
    current_date = pickup_date
    business_days_added = 0
    
    while business_days_added < estimated_days:
        current_date += timedelta(days=1)
        # Count only business days
        if current_date.weekday() < 5:  # Monday to Friday
            business_days_added += 1
    
    return current_date


def sanitize_for_api(data: Dict) -> Dict:
    """
    Sanitize data for API calls (remove None values, convert types)
    
    Args:
        data: Dictionary to sanitize
        
    Returns:
        Dict: Sanitized dictionary
    """
    sanitized = {}
    
    for key, value in data.items():
        if value is not None:
            if isinstance(value, Decimal):
                sanitized[key] = float(value)
            elif isinstance(value, datetime):
                sanitized[key] = value.isoformat()
            elif isinstance(value, dict):
                sanitized[key] = sanitize_for_api(value)
            elif isinstance(value, list):
                sanitized[key] = [
                    sanitize_for_api(item) if isinstance(item, dict) else item
                    for item in value
                ]
            else:
                sanitized[key] = value
    
    return sanitized


def log_api_call(method: str, endpoint: str, request_data: Dict, 
                response_data: Dict = None, response_time: int = None,
                is_success: bool = False, error_message: str = '') -> None:
    """
    Log API call for debugging and monitoring
    
    Args:
        method: API method name
        endpoint: API endpoint
        request_data: Request data
        response_data: Response data
        response_time: Response time in milliseconds
        is_success: Whether the call was successful
        error_message: Error message if failed
    """
    try:
        from .models import RapidshypAPILog
        
        RapidshypAPILog.objects.create(
            method=method,
            endpoint=endpoint,
            request_data=request_data,
            response_data=response_data,
            response_time_ms=response_time,
            is_success=is_success,
            error_message=error_message
        )
    except Exception as e:
        # Don't let logging errors break the main flow
        import logging
        logger = logging.getLogger(__name__)
        logger.error(f"Failed to log API call: {e}")


def get_minimum_rate(rates: List[Dict]) -> Optional[Dict]:
    """
    Get the minimum rate from a list of shipping rates
    
    Args:
        rates: List of rate dictionaries
        
    Returns:
        Dict or None: Rate with minimum cost
    """
    if not rates:
        return None
    
    return min(rates, key=lambda rate: float(rate.get('total_freight', float('inf'))))


def format_currency(amount: Union[str, int, float, Decimal]) -> str:
    """
    Format amount as currency
    
    Args:
        amount: Amount to format
        
    Returns:
        str: Formatted currency string
    """
    try:
        amount_decimal = Decimal(str(amount))
        return f"₹{amount_decimal:.2f}"
    except (ValueError, TypeError):
        return "₹0.00"
