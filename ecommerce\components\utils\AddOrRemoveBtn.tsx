import { MAIN_URL, UPDATE_CART } from "../../constant/urls";
import useApi from "../../hooks/useApi";
import { Minus, Plus, Trash2 } from "lucide-react";
import React from "react";

const AddOrRemoveBtn = ({
  quantity,
  itemId,
  data,
  handleAddOrRemoveToCart,
}: {
  quantity: number;
  itemId: number;
  data: any;
  handleAddOrRemoveToCart: any;
}) => {
  return (
    <div className="flex items-center justify-center space-x-2">
      <button
        onClick={() => {
          handleAddOrRemoveToCart(itemId, "remove");
        }}
        disabled={quantity <= 1}
        className={`border border-gray-300 p-1 rounded-md transition-all duration-300 ${
          quantity <= 1
            ? "cursor-not-allowed opacity-50"
            : "cursor-pointer hover:bg-gray-100 text-theme-text-primary"
        }`}
        aria-label="Decrease quantity"
      >
        <Minus className="w-4 h-4" />
      </button>
      <span className="font-medium text-theme-text-primary">{quantity}</span>
      <button
        onClick={() => {
          handleAddOrRemoveToCart(itemId, "add");
        }}
        className="cursor-pointer border border-gray-300 p-1 rounded-md hover:bg-gray-100 text-theme-text-primary transition-all duration-300"
        aria-label="Increase quantity"
      >
        <Plus className="w-4 h-4" />
      </button>
      <button
        onClick={() => handleAddOrRemoveToCart(itemId, "delete")}
        className="cursor-pointer p-1 rounded-md hover:text-theme-out-of-stock transition-all duration-300 text-theme-text-primary"
        aria-label="Remove item"
      >
        <Trash2 className="w-4 h-4" />
      </button>
    </div>
  );
};

export default AddOrRemoveBtn;
