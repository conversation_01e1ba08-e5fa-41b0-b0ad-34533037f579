import { format } from "date-fns";
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from "@/components/ui/table";
import { Button } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import { Package, Archive } from "lucide-react";

interface InventoryTableProps {
  // eslint-disable-next-line @typescript-eslint/no-explicit-any
  items: any[];
  onRestock: (itemId: string) => void;
  onDiscontinue: (itemId: string) => void;
}

export const InventoryTable = ({
  items,
  onRestock,
  onDiscontinue,
}: InventoryTableProps) => {
  // eslint-disable-next-line @typescript-eslint/no-explicit-any
  const getStockStatus = (item: any) => {
    if (item.currentStock <= 0) return { label: "Out of Stock", variant: "destructive" as const };
    if (item.currentStock <= item.reorderLevel) return { label: "Low Stock", variant: "secondary" as const };
    return { label: "In Stock", variant: "default" as const };
  };

  return (
    <Table>
      <TableHeader>
        <TableRow>
          <TableHead>Product</TableHead>
          <TableHead>SKU</TableHead>
          <TableHead>Current Stock</TableHead>
          <TableHead>Reorder Level</TableHead>
          <TableHead>Last Restocked</TableHead>
          <TableHead>30-Day Sales</TableHead>
          <TableHead>Status</TableHead>
          <TableHead>Actions</TableHead>
        </TableRow>
      </TableHeader>
      <TableBody>
        {items.map((item) => {
          const status = getStockStatus(item);
          return (
            <TableRow key={item.id}>
              <TableCell className="font-medium">{item.name}</TableCell>
              <TableCell>{item.sku}</TableCell>
              <TableCell>{item.currentStock}</TableCell>
              <TableCell>{item.reorderLevel}</TableCell>
              <TableCell>{format(new Date(item.lastRestocked), "MMM d, yyyy")}</TableCell>
              <TableCell>{item.salesLast30Days}</TableCell>
              <TableCell>
                <Badge variant={status.variant}>{status.label}</Badge>
              </TableCell>
              <TableCell>
                <div className="flex space-x-2">
                  <Button
                    variant="outline"
                    size="sm"
                    onClick={() => onRestock(item.id)}
                  >
                    <Package className="mr-2 h-4 w-4" />
                    Restock
                  </Button>
                  <Button
                    variant="outline"
                    size="sm"
                    onClick={() => onDiscontinue(item.id)}
                  >
                    <Archive className="mr-2 h-4 w-4" />
                    Discontinue
                  </Button>
                </div>
              </TableCell>
            </TableRow>
          );
        })}
      </TableBody>
    </Table>
  );
};