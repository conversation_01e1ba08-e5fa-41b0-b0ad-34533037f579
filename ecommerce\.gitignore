# See https://help.github.com/articles/ignoring-files/ for more about ignoring files.

# dependencies
/node_modules
/.pnp
.pnp.*
.yarn/*
!.yarn/patches
!.yarn/plugins
!.yarn/releases
!.yarn/versions

# testing
/coverage

# next.js
/.next/
/out/

# production
/build

# misc
.DS_Store
*.pem

# debug
npm-debug.log*
yarn-debug.log*
yarn-error.log*

# env files (can opt-in for commiting if needed)
.env*

# vercel
.vercel

# typescript
*.tsbuildinfo
next-env.d.ts
# lock files
yarn.lock
package-lock.json
FRONTEND_GST_RULES_IMPLEMENTATION.md
FRONTEND_GST_RULES_IMPLEMENTATION.md


COMPATIBILITY_ANALYSIS.md
NAVIGATION_IMPROVEMENTS.md
test_navigation_compatibility.js
CLOUDFLARE_MINIO_CDN_INTEGRATION_ANALYSIS.md
MINIO_STATIC_BUCKET_INTEGRATION_ANALYSIS.md
MOBILE_NAVBAR_IMPROVEMENTS.md
test-mobile-navbar.js


FORGOT_PASSWORD_FRONTEND_IMPLEMENTATION.md
SETUP_TESTING_GUIDE.md
test-forgot-password.js
MOBILE_PRODUCT_PAGE_IMPROVEMENTS.md


MOBILE_HEADER_ENHANCEMENTS.md
test-mobile-header-enhancements.js