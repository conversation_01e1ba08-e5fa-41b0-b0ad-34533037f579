// types/next-auth.d.ts
import { DefaultSession, DefaultUser } from "next-auth";

declare module "next-auth" {
  interface Session extends DefaultSession {
    user: {
      name?: string;
      email?: string;
      phone?: string;
      dob?: string;
      access?: string;
      refresh?: string;
    } & DefaultSession["user"];
  }

  interface User extends DefaultUser {
    access?: string;
    refresh?: string;
    phone?: string;
    dob?: string;
  }
}

declare module "next-auth/jwt" {
  interface JWT {
    access?: string;
    refresh?: string;
    phone?: string;
    dob?: string;
  }
}
