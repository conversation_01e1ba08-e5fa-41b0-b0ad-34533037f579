'use client';

import React, { createContext, useContext, useEffect, useState, useCallback } from 'react';
import { useSession } from 'next-auth/react';
import { secureCartManager, Cart, CartItem } from '../lib/secureCartManager';
import { secureApiClient } from '../lib/secureApiClient';

interface CartContextType {
  cart: Cart;
  isLoading: boolean;
  addItem: (item: Omit<CartItem, 'quantity'>, quantity?: number) => Promise<void>;
  updateQuantity: (productId: number, quantity: number, variant?: string) => Promise<void>;
  removeItem: (productId: number, variant?: string) => Promise<void>;
  clearCart: () => Promise<void>;
  syncCart: () => Promise<void>;
  refreshCart: () => Promise<void>;
  isInCart: (productId: number, variant?: string) => boolean;
  getItemQuantity: (productId: number, variant?: string) => number;
}

const CartContext = createContext<CartContextType | undefined>(undefined);

export const useSecureCart = () => {
  const context = useContext(CartContext);
  if (!context) {
    throw new Error('useSecureCart must be used within a SecureCartProvider');
  }
  return context;
};

interface SecureCartProviderProps {
  children: React.ReactNode;
}

export const SecureCartProvider: React.FC<SecureCartProviderProps> = ({ children }) => {
  const [cart, setCart] = useState<Cart>(secureCartManager.getCart());
  const [isLoading, setIsLoading] = useState(false);
  const { status } = useSession();

  // Update cart state when cart changes
  const updateCartState = useCallback(() => {
    setCart(secureCartManager.getCart());
  }, []);

  // Initialize cart on mount and when authentication changes
  useEffect(() => {
    const initializeCart = async () => {
      if (status === 'authenticated') {
        setIsLoading(true);
        try {
          // Try to merge local cart with server cart
          await secureCartManager.mergeWithServer();
          updateCartState();
        } catch (error) {
          console.error('Failed to initialize cart:', error);
          // Fallback to local cart
          updateCartState();
        } finally {
          setIsLoading(false);
        }
      } else if (status === 'unauthenticated') {
        // Just use local cart for unauthenticated users
        updateCartState();
      }
    };

    initializeCart();
  }, [status, updateCartState]);

  // Auto-sync cart periodically for authenticated users
  useEffect(() => {
    if (status === 'authenticated') {
      const syncInterval = setInterval(async () => {
        try {
          await secureCartManager.syncWithServer();
          updateCartState();
        } catch (error) {
          console.error('Auto-sync failed:', error);
        }
      }, 5 * 60 * 1000); // Sync every 5 minutes

      return () => clearInterval(syncInterval);
    }
  }, [status, updateCartState]);

  const addItem = useCallback(async (item: Omit<CartItem, 'quantity'>, quantity: number = 1) => {
    try {
      setIsLoading(true);
      
      // Add to local cart first for immediate UI update
      secureCartManager.addItem(item, quantity);
      updateCartState();

      // If authenticated, also add to server
      if (status === 'authenticated') {
        try {
          await secureApiClient.post('/api/orders/cart/add-item/', {
            product_id: item.product_id,
            quantity: quantity,
            variant: item.variant
          });
          
          // Sync to ensure consistency
          await secureCartManager.syncWithServer();
          updateCartState();
        } catch (error) {
          console.error('Failed to add item to server cart:', error);
          // Item is still in local cart, so this is not critical
        }
      }
    } catch (error) {
      console.error('Failed to add item to cart:', error);
      throw error;
    } finally {
      setIsLoading(false);
    }
  }, [status, updateCartState]);

  const updateQuantity = useCallback(async (productId: number, quantity: number, variant?: string) => {
    try {
      setIsLoading(true);
      
      // Update local cart first
      secureCartManager.updateItemQuantity(productId, quantity, variant);
      updateCartState();

      // If authenticated, update server
      if (status === 'authenticated') {
        try {
          const action = quantity === 0 ? 'delete' : 'update';
          await secureApiClient.post('/api/orders/cart/update/', {
            product_id: productId,
            quantity: quantity,
            action: action,
            variant: variant
          });
          
          // Sync to ensure consistency
          await secureCartManager.syncWithServer();
          updateCartState();
        } catch (error) {
          console.error('Failed to update item on server:', error);
          // Local update is still applied
        }
      }
    } catch (error) {
      console.error('Failed to update cart item:', error);
      throw error;
    } finally {
      setIsLoading(false);
    }
  }, [status, updateCartState]);

  const removeItem = useCallback(async (productId: number, variant?: string) => {
    try {
      setIsLoading(true);
      
      // Remove from local cart first
      secureCartManager.removeItem(productId, variant);
      updateCartState();

      // If authenticated, remove from server
      if (status === 'authenticated') {
        try {
          await secureApiClient.post('/api/orders/cart/remove/', {
            product_id: productId,
            variant: variant
          });
          
          // Sync to ensure consistency
          await secureCartManager.syncWithServer();
          updateCartState();
        } catch (error) {
          console.error('Failed to remove item from server:', error);
          // Local removal is still applied
        }
      }
    } catch (error) {
      console.error('Failed to remove cart item:', error);
      throw error;
    } finally {
      setIsLoading(false);
    }
  }, [status, updateCartState]);

  const clearCart = useCallback(async () => {
    try {
      setIsLoading(true);
      
      // Clear local cart first
      secureCartManager.clearCart();
      updateCartState();

      // If authenticated, clear server cart
      if (status === 'authenticated') {
        try {
          await secureApiClient.post('/api/orders/cart/clear/');
        } catch (error) {
          console.error('Failed to clear server cart:', error);
          // Local cart is still cleared
        }
      }
    } catch (error) {
      console.error('Failed to clear cart:', error);
      throw error;
    } finally {
      setIsLoading(false);
    }
  }, [status, updateCartState]);

  const syncCart = useCallback(async () => {
    if (status === 'authenticated') {
      try {
        setIsLoading(true);
        await secureCartManager.syncWithServer();
        updateCartState();
      } catch (error) {
        console.error('Failed to sync cart:', error);
        throw error;
      } finally {
        setIsLoading(false);
      }
    }
  }, [status, updateCartState]);

  const refreshCart = useCallback(async () => {
    try {
      setIsLoading(true);
      
      if (status === 'authenticated') {
        // Load fresh data from server
        await secureCartManager.loadFromServer();
      }
      
      updateCartState();
    } catch (error) {
      console.error('Failed to refresh cart:', error);
      // Fallback to local cart
      updateCartState();
    } finally {
      setIsLoading(false);
    }
  }, [status, updateCartState]);

  const isInCart = useCallback((productId: number, variant?: string) => {
    return secureCartManager.isInCart(productId, variant);
  }, []);

  const getItemQuantity = useCallback((productId: number, variant?: string) => {
    const item = secureCartManager.getItem(productId, variant);
    return item ? item.quantity : 0;
  }, []);

  const contextValue: CartContextType = {
    cart,
    isLoading,
    addItem,
    updateQuantity,
    removeItem,
    clearCart,
    syncCart,
    refreshCart,
    isInCart,
    getItemQuantity,
  };

  return (
    <CartContext.Provider value={contextValue}>
      {children}
    </CartContext.Provider>
  );
};

export default SecureCartProvider;
