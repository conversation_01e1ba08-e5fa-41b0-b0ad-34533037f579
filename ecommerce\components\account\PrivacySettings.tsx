"use client";

import React, { useState, useEffect } from "react";
import {
  Download,
  Trash2,
  <PERSON>,
  Eye,
  Settings,
  <PERSON>ertTriangle,
  CheckCircle,
  XCircle,
} from "lucide-react";
import { secureApiClient } from "../../lib/secureApiClient";
import { secureLocalStorage } from "../../lib/secureStorage";
import { useToast } from "../../hooks/use-toast";
import { useSession } from "next-auth/react";

interface ConsentStatus {
  ESSENTIAL: {
    granted: boolean;
    granted_at: string | null;
    description: string;
  };
  MARKETING: {
    granted: boolean;
    granted_at: string | null;
    description: string;
  };
  ANALYTICS: {
    granted: boolean;
    granted_at: string | null;
    description: string;
  };
  PERSONALIZATION: {
    granted: boolean;
    granted_at: string | null;
    description: string;
  };
  THIRD_PARTY: {
    granted: boolean;
    granted_at: string | null;
    description: string;
  };
  COOKIES: { granted: boolean; granted_at: string | null; description: string };
}

export const PrivacySettings: React.FC = () => {
  const [consentStatus, setConsentStatus] = useState<ConsentStatus | null>(
    null
  );
  const [isLoading, setIsLoading] = useState(false); // Set to false initially for testing
  const [isUpdating, setIsUpdating] = useState(false);
  const [showDeleteConfirm, setShowDeleteConfirm] = useState(false);
  const [deleteReason, setDeleteReason] = useState("");
  const { toast } = useToast();
  const { data: session, status } = useSession();

  useEffect(() => {
    console.log("PrivacySettings component mounted");
    console.log("PrivacySettings - Session status:", status);
    console.log("PrivacySettings - Session data:", session);

    // For testing, just load local consent status
    setTimeout(() => {
      loadLocalConsentStatus();
    }, 100);
  }, []);

  const loadLocalConsentStatus = () => {
    try {
      setIsLoading(true);
      const localConsent =
        secureLocalStorage.getItem("consent_preferences") || {};
      const mappedConsent: ConsentStatus = {
        ESSENTIAL: {
          granted: true,
          granted_at: null,
          description: "Essential - Required for service operation",
        },
        MARKETING: {
          granted: localConsent.marketing || false,
          granted_at: null,
          description: "Marketing - Email marketing and promotions",
        },
        ANALYTICS: {
          granted: localConsent.analytics || false,
          granted_at: null,
          description: "Analytics - Usage analytics and tracking",
        },
        PERSONALIZATION: {
          granted: localConsent.personalization || false,
          granted_at: null,
          description:
            "Personalization - Personalized content and recommendations",
        },
        THIRD_PARTY: {
          granted: localConsent.third_party || false,
          granted_at: null,
          description: "Third Party - Data sharing with third parties",
        },
        COOKIES: {
          granted: localConsent.cookies || false,
          granted_at: null,
          description: "Cookies - Non-essential cookies",
        },
      };
      setConsentStatus(mappedConsent);
    } catch (error) {
      console.error("Failed to load local consent status:", error);
    } finally {
      setIsLoading(false);
    }
  };

  const loadConsentStatus = async () => {
    try {
      setIsLoading(true);
      console.log("Attempting to load consent status...");

      // Use session token directly if available
      const token = session?.user?.access;
      if (!token) {
        console.log("No access token available, falling back to local storage");
        loadLocalConsentStatus();
        return;
      }

      // Make API call with session token using axios
      const response = await secureApiClient.get(
        "/api/v1/users/privacy/consent/",
        {
          headers: {
            Authorization: `Bearer ${token}`,
          },
        }
      );

      console.log("Consent status loaded successfully:", response.data);
      setConsentStatus(response.data);
    } catch (error: any) {
      console.error("Failed to load consent status:", error);
      console.log("API call failed, status:", error.response?.status);
      // Load from local storage as fallback
      loadLocalConsentStatus();
    } finally {
      setIsLoading(false);
    }
  };

  const updateConsent = async (consentType: string, granted: boolean) => {
    if (consentType === "ESSENTIAL") return; // Cannot change essential consent

    try {
      setIsUpdating(true);

      // Update local state first
      if (consentStatus) {
        setConsentStatus({
          ...consentStatus,
          [consentType]: {
            ...consentStatus[consentType as keyof ConsentStatus],
            granted,
            granted_at: granted ? new Date().toISOString() : null,
          },
        });
      }

      // Update local storage
      const localPrefs =
        secureLocalStorage.getItem("consent_preferences") || {};
      const keyMap: Record<string, string> = {
        MARKETING: "marketing",
        ANALYTICS: "analytics",
        PERSONALIZATION: "personalization",
        THIRD_PARTY: "third_party",
        COOKIES: "cookies",
      };

      if (keyMap[consentType]) {
        localPrefs[keyMap[consentType]] = granted;
        secureLocalStorage.setItem(
          "consent_preferences",
          localPrefs,
          365 * 24 * 60
        );
      }

      // Try to sync with server if authenticated
      if (status === "authenticated" && session?.user?.access) {
        try {
          const updatedConsents = {
            ...Object.fromEntries(
              Object.entries(consentStatus || {}).map(([key, value]) => [
                key,
                key === consentType ? granted : value.granted,
              ])
            ),
          };

          await secureApiClient.post(
            "/api/v1/users/privacy/consent/",
            { consents: updatedConsents },
            {
              headers: {
                Authorization: `Bearer ${session.user.access}`,
              },
            }
          );

          console.log("Consent synced with server successfully");
        } catch (serverError: any) {
          console.error("Failed to sync consent with server:", serverError);
          console.log("Server error status:", serverError.response?.status);
          // Continue anyway - local storage is updated
        }
      }

      toast({
        title: "Consent Updated",
        description: `${consentType
          .toLowerCase()
          .replace("_", " ")} consent has been ${
          granted ? "granted" : "withdrawn"
        }.`,
      });

      // Dispatch custom event to notify consent banner
      window.dispatchEvent(new CustomEvent("consentUpdated"));
    } catch (error) {
      console.error("Failed to update consent:", error);
      toast({
        title: "Error",
        description: "Failed to update consent preferences. Please try again.",
        variant: "destructive",
      });
    } finally {
      setIsUpdating(false);
    }
  };

  const exportData = async () => {
    if (status !== "authenticated") {
      toast({
        title: "Authentication Required",
        description: "Please log in to export your data.",
        variant: "destructive",
      });
      return;
    }

    try {
      const response = await secureApiClient.get(
        "/api/v1/users/privacy/export/",
        {
          headers: {
            Authorization: `Bearer ${session?.user?.access}`,
          },
          responseType: "text", // Handle CSV response as text
        }
      );

      // The backend returns CSV content directly
      const csvData = response.data;

      // Create and download CSV file
      const blob = new Blob([csvData], {
        type: "text/csv",
      });
      const url = URL.createObjectURL(blob);
      const a = document.createElement("a");
      a.href = url;
      a.download = `user-data-export-${
        new Date().toISOString().split("T")[0]
      }.csv`;
      document.body.appendChild(a);
      a.click();
      document.body.removeChild(a);
      URL.revokeObjectURL(url);

      toast({
        title: "Data Exported",
        description: "Your data has been exported successfully as CSV.",
      });
    } catch (error: any) {
      console.error("Failed to export data:", error);
      console.log("Export error status:", error.response?.status);
      toast({
        title: "Export Failed",
        description: "Failed to export data. Please try again.",
        variant: "destructive",
      });
    }
  };

  const requestDataDeletion = async () => {
    if (status !== "authenticated") {
      toast({
        title: "Authentication Required",
        description: "Please log in to request data deletion.",
        variant: "destructive",
      });
      return;
    }

    if (!deleteReason.trim()) {
      toast({
        title: "Reason Required",
        description: "Please provide a reason for data deletion.",
        variant: "destructive",
      });
      return;
    }

    try {
      await secureApiClient.post(
        "/api/v1/users/privacy/delete/",
        { reason: deleteReason },
        {
          headers: {
            Authorization: `Bearer ${session?.user?.access}`,
          },
        }
      );

      toast({
        title: "Deletion Requested",
        description:
          "Your data deletion request has been submitted. You will receive a confirmation email shortly.",
      });
      setShowDeleteConfirm(false);
      setDeleteReason("");
    } catch (error: any) {
      console.error("Failed to request data deletion:", error);
      console.log("Deletion error status:", error.response?.status);
      toast({
        title: "Request Failed",
        description: "Failed to submit deletion request. Please try again.",
        variant: "destructive",
      });
    }
  };

  // Simplified loading check
  if (isLoading) {
    return (
      <div className="space-y-6">
        <div className="flex items-center gap-3">
          <Shield className="h-6 w-6 text-blue-600" />
          <h2 className="text-2xl font-bold text-gray-900">Privacy Settings</h2>
        </div>
        <div className="bg-blue-50 border border-blue-200 rounded-lg p-4">
          <p className="text-blue-700">Loading privacy settings...</p>
        </div>
      </div>
    );
  }

  return (
    <div className="space-y-8">
      {/* Debug Info - Remove in production */}
      {!process.env.NEXT_PUBLIC_IS_PROD && (
        <div className="bg-gray-100 border border-gray-300 rounded-lg p-4 text-xs">
          <h4 className="font-bold mb-2">Debug Info:</h4>
          <p>Session Status: {status}</p>
          <p>Session Data: {session ? "Present" : "None"}</p>
          <p>User Email: {session?.user?.email || "N/A"}</p>
        </div>
      )}

      {/* Header */}
      <div className="flex items-center gap-3">
        <Shield className="h-6 w-6 text-blue-600" />
        <h2 className="text-2xl font-bold text-gray-900">Privacy Settings</h2>
      </div>

      {/* Authentication Notice */}
      {status !== "authenticated" && (
        <div className="bg-yellow-50 border border-yellow-200 rounded-lg p-4">
          <div className="flex items-start gap-3">
            <AlertTriangle className="h-5 w-5 text-yellow-600 mt-0.5" />
            <div>
              <h3 className="font-medium text-yellow-800 mb-1">
                Limited Functionality
              </h3>
              <p className="text-sm text-yellow-700">
                Some privacy features require authentication. Please log in to
                access data export and deletion requests. Consent preferences
                are saved locally and will be synced when you log in.
              </p>
            </div>
          </div>
        </div>
      )}
      {!process.env.NEXT_PUBLIC_IS_PROD /* Test Message */ && (
        <div className="bg-green-50 border border-green-200 rounded-lg p-4">
          <h3 className="font-medium text-green-800 mb-1">
            Privacy Component Loaded Successfully!
          </h3>
          <p className="text-sm text-green-700">
            If you can see this message, the privacy component is working
            correctly.
          </p>
        </div>
      )}

      {/* Consent Management */}
      <div className="bg-gray-50 rounded-lg p-6">
        <div className="flex items-center gap-3 mb-6">
          <Settings className="h-5 w-5 text-blue-600" />
          <h3 className="text-lg font-semibold text-gray-900">
            Consent Preferences
          </h3>
        </div>

        <div className="space-y-4">
          {consentStatus &&
            Object.entries(consentStatus).map(([key, value]) => (
              <div
                key={key}
                className="flex items-center justify-between p-4 bg-white border border-gray-200 rounded-lg"
              >
                <div className="flex-1">
                  <h4 className="font-medium text-gray-900 mb-1">
                    {key
                      .replace("_", " ")
                      .toLowerCase()
                      .replace(/\b\w/g, (l) => l.toUpperCase())}
                  </h4>
                  <p className="text-sm text-gray-600">{value.description}</p>
                  {value.granted_at && (
                    <p className="text-xs text-gray-500 mt-1">
                      Granted on:{" "}
                      {new Date(value.granted_at).toLocaleDateString()}
                    </p>
                  )}
                </div>

                <div className="flex items-center gap-3">
                  <div
                    className={`flex items-center gap-1 px-2 py-1 text-xs rounded-full ${
                      value.granted
                        ? "bg-green-100 text-green-800"
                        : "bg-red-100 text-red-800"
                    }`}
                  >
                    {value.granted ? (
                      <CheckCircle className="h-3 w-3" />
                    ) : (
                      <XCircle className="h-3 w-3" />
                    )}
                    {value.granted ? "Granted" : "Denied"}
                  </div>

                  {key !== "ESSENTIAL" && (
                    <label className="relative inline-flex items-center cursor-pointer">
                      <input
                        type="checkbox"
                        checked={value.granted}
                        onChange={(e) => updateConsent(key, e.target.checked)}
                        disabled={isUpdating}
                        className="sr-only peer"
                      />
                      <div className="w-11 h-6 bg-gray-200 peer-focus:outline-none peer-focus:ring-4 peer-focus:ring-blue-300 rounded-full peer peer-checked:after:translate-x-full peer-checked:after:border-white after:content-[''] after:absolute after:top-[2px] after:left-[2px] after:bg-white after:border-gray-300 after:border after:rounded-full after:h-5 after:w-5 after:transition-all peer-checked:bg-blue-600"></div>
                    </label>
                  )}

                  {key === "ESSENTIAL" && (
                    <div className="bg-gray-100 text-gray-600 text-xs px-3 py-1 rounded">
                      Always Active
                    </div>
                  )}
                </div>
              </div>
            ))}
        </div>
      </div>

      {/* Data Rights */}
      <div className="bg-gray-50 rounded-lg p-6">
        <div className="flex items-center gap-3 mb-6">
          <Eye className="h-5 w-5 text-blue-600" />
          <h3 className="text-lg font-semibold text-gray-900">
            Your Data Rights
          </h3>
        </div>

        <div className="grid gap-4 md:grid-cols-2">
          {/* Data Export */}
          <div className="p-4 bg-white border border-gray-200 rounded-lg">
            <div className="flex items-center gap-3 mb-3">
              <Download className="h-5 w-5 text-green-600" />
              <h4 className="font-medium text-gray-900">Export Your Data</h4>
            </div>
            <p className="text-sm text-gray-600 mb-4">
              Download a copy of all your personal data in CSV format.
            </p>
            <button
              onClick={exportData}
              disabled={status !== "authenticated"}
              className={`w-full px-4 py-2 rounded-md transition-colors ${
                status === "authenticated"
                  ? "bg-green-600 text-white hover:bg-green-700"
                  : "bg-gray-300 text-gray-500 cursor-not-allowed"
              }`}
            >
              Export Data
            </button>
          </div>

          {/* Data Deletion */}
          <div className="p-4 bg-white border border-gray-200 rounded-lg">
            <div className="flex items-center gap-3 mb-3">
              <Trash2 className="h-5 w-5 text-red-600" />
              <h4 className="font-medium text-gray-900">Delete Your Account</h4>
            </div>
            <p className="text-sm text-gray-600 mb-4">
              Permanently delete your account and all associated data.
            </p>
            <button
              onClick={() => setShowDeleteConfirm(true)}
              disabled={status !== "authenticated"}
              className={`w-full px-4 py-2 rounded-md transition-colors ${
                status === "authenticated"
                  ? "bg-red-600 text-white hover:bg-red-700"
                  : "bg-gray-300 text-gray-500 cursor-not-allowed"
              }`}
            >
              Request Deletion
            </button>
          </div>
        </div>
      </div>

      {/* Privacy Information */}
      <div className="bg-blue-50 rounded-lg p-6">
        <div className="flex items-start gap-3">
          <AlertTriangle className="h-6 w-6 text-blue-600 mt-1" />
          <div>
            <h3 className="font-medium text-blue-900 mb-2">
              Your Privacy Matters
            </h3>
            <p className="text-sm text-blue-800 mb-3">
              We are committed to protecting your privacy and complying with
              data protection laws. Your data is encrypted and stored securely.
            </p>
            <ul className="text-sm text-blue-800 space-y-1">
              <li>• We never sell your personal data to third parties</li>
              <li>• You can withdraw consent at any time</li>
              <li>• Data deletion requests are processed within 30 days</li>
              <li>• All data transfers are encrypted and secure</li>
            </ul>
          </div>
        </div>
      </div>

      {/* Delete Confirmation Modal */}
      {showDeleteConfirm && (
        <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
          <div className="bg-white rounded-lg p-6 max-w-md w-full mx-4">
            <h3 className="text-lg font-semibold text-gray-900 mb-4">
              Confirm Account Deletion
            </h3>
            <p className="text-sm text-gray-600 mb-4">
              This action cannot be undone. All your data will be permanently
              deleted.
            </p>
            <div className="mb-4">
              <label className="block text-sm font-medium text-gray-700 mb-2">
                Reason for deletion (required):
              </label>
              <textarea
                value={deleteReason}
                onChange={(e) => setDeleteReason(e.target.value)}
                className="w-full p-3 border border-gray-300 rounded-md focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                rows={3}
                placeholder="Please tell us why you're deleting your account..."
              />
            </div>
            <div className="flex gap-3">
              <button
                onClick={() => setShowDeleteConfirm(false)}
                className="flex-1 px-4 py-2 border border-gray-300 rounded-md hover:bg-gray-50 transition-colors"
              >
                Cancel
              </button>
              <button
                onClick={requestDataDeletion}
                className="flex-1 px-4 py-2 bg-red-600 text-white rounded-md hover:bg-red-700 transition-colors"
              >
                Delete Account
              </button>
            </div>
          </div>
        </div>
      )}
    </div>
  );
};

export default PrivacySettings;
