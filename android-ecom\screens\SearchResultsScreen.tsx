import { useState, useEffect } from "react";
import {
  View,
  Text,
  StyleSheet,
  FlatList,
  TouchableOpacity,
  ActivityIndicator,
} from "react-native";
import { SafeAreaView } from "react-native-safe-area-context";
import { MaterialIcons } from "@expo/vector-icons";
import SearchBar from "../components/components/SearchBar";
import ProductCard from "../components/components/ProductCard";
import { useAuth } from "../context/AuthContext";
import { BaseURL } from "@/constants/ApiEndpoint";
import { addToCart } from "@/hooks/API";

interface Product {
  id: number;
  name: string;
  price: string;
  images: Array<{ image: string }>;
  image: string | null;
  average_rating: number | null;
  category: {
    name: string;
    slug: string;
  };
  brand: {
    name: string;
  };
}

const SORT_OPTIONS = [
  { label: "Newest", value: "created_at" },
  { label: "A to Z", value: "name" },
  { label: "Z to A", value: "-name" },
  { label: "Price: Low to High", value: "price" },
  { label: "Price: High to Low", value: "-price" },
];

export default function SearchResultsScreen({ route, navigation }) {
  const { query = "" } = route.params || {};
  const [searchQuery, setSearchQuery] = useState(query);
  const [selectedSort, setSelectedSort] = useState(SORT_OPTIONS[0].value);
  const [products, setProducts] = useState<Product[]>([]);
  const [loading, setLoading] = useState(false);
  const [page, setPage] = useState(1);
  const [hasMore, setHasMore] = useState(true);
  const { accessToken, isAuthenticated } = useAuth();

  const fetchProducts = async (searchTerm: string, pageNum: number = 1) => {
    try {
      setLoading(true);
      const response = await fetch(
        `${BaseURL}/api/v1/products/?search=${searchTerm}&page=${pageNum}&ordering=${selectedSort}`
      );
      const data = await response.json();

      if (pageNum === 1) {
        setProducts(data.results);
      } else {
        setProducts((prev) => [...prev, ...data.results]);
      }

      setHasMore(!!data.next);
      setPage(pageNum);
    } catch (error) {
      console.error("Error fetching products:", error);
    } finally {
      setLoading(false);
    }
  };

  const handleSearch = () => {
    if (searchQuery.trim()) {
      setPage(1);
      fetchProducts(searchQuery);
    }
  };

  useEffect(() => {
    fetchProducts(query);
  }, [selectedSort]);

  // const handleAddToCart = (productId: number) => {
  //   if (requireAuth("addToCart", navigation)) {
  //     // Implement add to cart functionality
  //     //TODO
  //   }
  // };

  const handleAddToCart = async (
    productId: any,
    quantity: number,
    accessToken: string
  ) => {
    if (!isAuthenticated) {
      navigation.navigate("Auth", {
        returnTo: "Cart",
      });
      return;
    }
    try {
      const response = await addToCart(productId, quantity, accessToken);
      return response;
    } catch (error) {
      console.log("Error adding to cart", error);
    }
  };

  const renderFooter = () => {
    return loading ? (
      <View
        style={{
          padding: 10,
          justifyContent: "center",
          alignItems: "center",
        }}
      >
        <ActivityIndicator size="large" color="#0000ff" />
        <Text style={{ marginTop: 5 }}>Loading more...</Text>
      </View>
    ) : null;
  };

  const handleNext = () => {
    if (!loading && hasMore) {
      fetchProducts(searchQuery, page + 1);
    }
  };

  return (
    <SafeAreaView style={styles.container}>
      <View style={styles.header}>
        <TouchableOpacity
          style={styles.backButton}
          onPress={() => navigation.goBack()}
        >
          <MaterialIcons name="arrow-back" size={24} color="#333" />
        </TouchableOpacity>
        <View style={styles.searchBarContainer}>
          <SearchBar
            value={searchQuery}
            onChangeText={setSearchQuery}
            onSubmit={handleSearch}
            autoFocus={true}
          />
        </View>
      </View>

      <View style={styles.sortContainer}>
        <FlatList
          horizontal
          data={SORT_OPTIONS}
          showsHorizontalScrollIndicator={false}
          renderItem={({ item }) => (
            <TouchableOpacity
              style={[
                styles.sortOption,
                selectedSort === item?.value && styles.selectedSort,
              ]}
              onPress={() => setSelectedSort(item?.value)}
            >
              <Text
                style={[
                  styles.sortText,
                  selectedSort === item?.value && styles.selectedSortText,
                ]}
              >
                {item?.label}
              </Text>
            </TouchableOpacity>
          )}
          keyExtractor={(item) => item?.value}
          contentContainerStyle={styles.sortList}
        />
      </View>

      {/* <FlatList
        data={products}
        renderItem={({ item }) => (
          <ProductCard
            id={item?.id.toString()}
            name={item?.name}
            price={parseFloat(item.price)}
            image={item.images[0]?.image || item?.image || 'https://via.placeholder.com/150'}
            average_rating={item.average_rating || 0}
            onAddToCart={() => handleAddToCart(item?.id)}
            onPress={() => navigation.navigate('ProductDetails', { product: item })}
          />
        )}
        numColumns={2}
        keyExtractor={(item) => item?.id}
        contentContainerStyle={styles.productList}
        columnWrapperStyle={styles.productRow}
        scrollEventThrottle={16}
        onEndReached={() => {
          if (!loading && hasMore) {
            fetchProducts(searchQuery, page + 1);
          }
        }}
        onEndReachedThreshold={0.5}
        ListFooterComponent={renderFooter}
        ListEmptyComponent={
          <View style={styles.emptyState}>
            <Text style={styles.emptyStateText}>No results found</Text>
          </View>
        }
      /> */}
      <FlatList
        data={products}
        renderItem={({ item }) => (
          <ProductCard
            {...item}
            onPress={() =>
              navigation.navigate("ProductDetails", { product: item })
            }
            onAddToCart={() => handleAddToCart(item?.id, 1, accessToken)}
          />
        )}
        numColumns={2}
        keyExtractor={(item) => item?.id}
        showsVerticalScrollIndicator={false}
        contentContainerStyle={styles.productList}
        columnWrapperStyle={styles.productRow}
        scrollEventThrottle={16}
        ListEmptyComponent={null}
        onEndReachedThreshold={0.5}
        onEndReached={handleNext}
        ListFooterComponent={renderFooter}
      />
    </SafeAreaView>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: "#ffffff",
  },
  header: {
    flexDirection: "row",
    alignItems: "center",
    padding: 16,
    borderBottomWidth: 1,
    borderBottomColor: "#f3f4f6",
  },
  backButton: {
    padding: 8,
    marginRight: 8,
  },
  searchBarContainer: {
    flex: 1,
  },
  sortContainer: {
    borderBottomWidth: 1,
    borderBottomColor: "#f3f4f6",
  },
  sortList: {
    padding: 12,
  },
  sortOption: {
    paddingHorizontal: 16,
    paddingVertical: 8,
    borderRadius: 20,
    backgroundColor: "#f3f4f6",
    marginRight: 8,
  },
  selectedSort: {
    backgroundColor: "#2563EB",
  },
  sortText: {
    fontSize: 14,
    color: "#666",
  },
  selectedSortText: {
    color: "#ffffff",
  },
  productList: {
    padding: 8,
  },
  productRow: {
    justifyContent: "space-evenly",
  },
  emptyState: {
    flex: 1,
    alignItems: "center",
    justifyContent: "center",
    paddingVertical: 32,
  },
  emptyStateText: {
    fontSize: 16,
    color: "#666",
  },
  loadingContainer: {
    padding: 20,
    alignItems: "center",
  },
});
