//

// import { NavigationContainer } from '@react-navigation/native';
// import { createBottomTabNavigator } from '@react-navigation/bottom-tabs';
// import { createNativeStackNavigator } from '@react-navigation/native-stack';
// import { StyleSheet } from 'react-native';
// import { SafeAreaProvider } from "react-native-safe-area-context";
// import { Toaster } from 'sonner-native';
// import { MaterialIcons } from '@expo/vector-icons';
// import HomeScreen from "../../screens/HomeScreen";
// import CartScreen from "../../screens/CartScreen";
// import ProfileScreen from "../../screens/ProfileScreen";
// import WishlistScreen from "../../screens/WishlistScreen";
// import CategoriesScreen from "../../screens/CategoriesScreen";
// import ProductDetailsScreen from "../../screens/ProductDetailsScreen";
// import CheckoutScreen from "../../screens/CheckoutScreen";

// const Stack = createNativeStackNavigator();
// const Tab = createBottomTabNavigator();

// function TabNavigator() {
//   return (
//     <Tab.Navigator
//       screenOptions={({ route }) => ({
//         tabBarIcon: ({ focused, color, size }) => {
//           let iconName;
//           switch (route.name) {
//             case 'HomeTab':
//               iconName = 'home';
//               break;
//             case 'Categories':
//               iconName = 'category';
//               break;
//             case 'Cart':
//               iconName = 'shopping-cart';
//               break;
//             case 'Wishlist':
//               iconName = 'favorite';
//               break;
//             case 'Profile':
//               iconName = 'person';
//               break;
//             default:
//               iconName = 'home';
//           }
//           return <MaterialIcons name={iconName} size={size} color={color} />;
//         },
//         tabBarActiveTintColor: '#2563EB',
//         tabBarInactiveTintColor: 'gray',
//         headerShown: false,
//       })}
//     >
//       <Tab.Screen name="HomeTab" component={HomeScreen} options={{ title: 'Home' }} />
//       <Tab.Screen name="Categories" component={CategoriesScreen} />
//       <Tab.Screen name="Cart" component={CartScreen} />
//       <Tab.Screen name="Wishlist" component={WishlistScreen} />
//       <Tab.Screen name="Profile" component={ProfileScreen} />
//     </Tab.Navigator>
//   );
// }

// function RootStack() {
//   return (
//     <Stack.Navigator screenOptions={{ headerShown: false }}>
//       <Stack.Screen name="MainTabs" component={TabNavigator} />
//       <Stack.Screen name="ProductDetails" component={ProductDetailsScreen} />
//       <Stack.Screen name="Checkout" component={CheckoutScreen} />
//     </Stack.Navigator>
//   );
// }

//   export default function App() {
//     return (
//       <SafeAreaProvider style={styles.container}>
//       <Toaster />
//         <NavigationContainer>
//           <RootStack />
//         </NavigationContainer>
//       </SafeAreaProvider>
//     );
//   }

//   const styles = StyleSheet.create({
//     container: {
//       flex: 1
//     }
//   });

import { NavigationContainer } from "@react-navigation/native";
import { createBottomTabNavigator } from "@react-navigation/bottom-tabs";
import { createNativeStackNavigator } from "@react-navigation/native-stack";
import { StyleSheet } from "react-native";
import { SafeAreaProvider } from "react-native-safe-area-context";
import { Toaster } from "sonner-native";
import { MaterialIcons } from "@expo/vector-icons";
import HomeScreen from "../../screens/HomeScreen";
import CartScreen from "../../screens/CartScreen";
import ProfileScreen from "../../screens/ProfileScreen";
import WishlistScreen from "../../screens/WishlistScreen";
import CategoriesScreen from "../../screens/CategoriesScreen";
import ProductDetailsScreen from "../../screens/ProductDetailsScreen";
import CheckoutScreen from "../../screens/CheckoutScreen";
import { AuthProvider } from "@/context/AuthContext";
import AuthScreen from "@/screens/AuthScreen";

const Stack = createNativeStackNavigator();
const Tab = createBottomTabNavigator();

function HomeStack() {
  return (
    <Stack.Navigator screenOptions={{ headerShown: false }}>
      <Stack.Screen name="Home" component={HomeScreen} />
      <Stack.Screen name="ProductDetails" component={ProductDetailsScreen} />
    </Stack.Navigator>
  );
}

function TabNavigator() {
  return (
    <Tab.Navigator
      screenOptions={({ route }) => ({
        tabBarIcon: ({ focused, color, size }) => {
          let iconName;
          switch (route.name) {
            case "HomeTab":
              iconName = "home";
              break;
            case "Categories":
              iconName = "category";
              break;
            case "Cart":
              iconName = "shopping-cart";
              break;
            case "Wishlist":
              iconName = "favorite";
              break;
            case "Profile":
              iconName = "person";
              break;
            default:
              iconName = "home";
          }
          return <MaterialIcons name={iconName} size={size} color={color} />;
        },
        tabBarActiveTintColor: "#2563EB",
        tabBarInactiveTintColor: "gray",
        headerShown: false,
      })}
    >
      <Tab.Screen
        name="HomeTab"
        component={HomeStack}
        options={{ title: "Home" }}
      />
      <Tab.Screen name="Categories" component={CategoriesScreen} />
      <Tab.Screen name="Cart" component={CartScreen} />
      <Tab.Screen name="Wishlist" component={WishlistScreen} />
      <Tab.Screen name="Profile" component={ProfileScreen} />
    </Tab.Navigator>
  );
}

function RootStack() {
  return (
    <Stack.Navigator screenOptions={{ headerShown: false }}>
      <Stack.Screen name="MainTabs" component={TabNavigator} />
      <Stack.Screen name="ProductDetails" component={ProductDetailsScreen} />
      <Stack.Screen name="Checkout" component={CheckoutScreen} />
      <Stack.Screen name="Auth" component={AuthScreen} />
    </Stack.Navigator>
  );
}

export default function App() {
  return (
    <AuthProvider>
      <RootStack />
    </AuthProvider>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
});
