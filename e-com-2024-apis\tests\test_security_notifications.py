"""
Tests for the security notification system.
Ensures email notifications are sent for appropriate security events while preventing spam.
"""

import pytest
from unittest.mock import patch, MagicMock
from django.test import TestCase, RequestFactory
from django.contrib.auth import get_user_model
from django.core.cache import cache
from django.core import mail
from django.conf import settings
from backend.security_notifications import (
    SecurityNotificationManager,
    send_ip_blocked_alert,
    send_multiple_failed_logins_alert,
    send_suspicious_activity_alert,
    send_admin_action_alert
)
from backend.security_monitoring import (
    block_ip,
    check_failed_attempts,
    log_security_event,
    FailedLoginAttempt,
    SecurityEvent
)

User = get_user_model()


class SecurityNotificationManagerTests(TestCase):
    """Test the SecurityNotificationManager class."""
    
    def setUp(self):
        self.manager = SecurityNotificationManager()
        self.factory = RequestFactory()
        cache.clear()  # Clear cache before each test
        
    def test_should_send_notification_enabled(self):
        """Test notification sending when enabled."""
        # High priority event should be sent
        result = self.manager.should_send_notification('IP_BLOCKED')
        self.assertTrue(result)
        
        # Medium priority event should be sent
        result = self.manager.should_send_notification('ADMIN_ACTION')
        self.assertTrue(result)
        
        # Low priority event should not be sent
        result = self.manager.should_send_notification('LOGIN_SUCCESS')
        self.assertFalse(result)
    
    def test_should_send_notification_disabled(self):
        """Test notification sending when disabled."""
        self.manager.enabled = False
        result = self.manager.should_send_notification('IP_BLOCKED')
        self.assertFalse(result)
    
    def test_throttling_mechanism(self):
        """Test that throttling prevents spam emails."""
        # First notification should be sent
        result1 = self.manager.should_send_notification('IP_BLOCKED', '***********')
        self.assertTrue(result1)
        
        # Second notification for same event and IP should be throttled
        result2 = self.manager.should_send_notification('IP_BLOCKED', '***********')
        self.assertFalse(result2)
        
        # Different IP should not be throttled
        result3 = self.manager.should_send_notification('IP_BLOCKED', '***********')
        self.assertTrue(result3)
    
    def test_get_priority_level(self):
        """Test priority level determination."""
        self.assertEqual(self.manager.get_priority_level('IP_BLOCKED'), 'HIGH')
        self.assertEqual(self.manager.get_priority_level('ADMIN_ACTION'), 'MEDIUM')
        self.assertEqual(self.manager.get_priority_level('LOGIN_SUCCESS'), 'LOW')
    
    @patch('backend.security_notifications.EmailMultiAlternatives')
    def test_send_security_alert_success(self, mock_email):
        """Test successful security alert sending."""
        mock_email_instance = MagicMock()
        mock_email.return_value = mock_email_instance
        
        event_data = {'test': 'data'}
        result = self.manager.send_security_alert(
            'IP_BLOCKED',
            event_data,
            ip_address='***********',
            user_email='<EMAIL>'
        )
        
        self.assertTrue(result)
        mock_email_instance.send.assert_called_once()
    
    @patch('backend.security_notifications.EmailMultiAlternatives')
    def test_send_security_alert_throttled(self, mock_email):
        """Test that throttled alerts are not sent."""
        # First alert should be sent
        event_data = {'test': 'data'}
        result1 = self.manager.send_security_alert(
            'IP_BLOCKED',
            event_data,
            ip_address='***********'
        )
        self.assertTrue(result1)
        
        # Second alert should be throttled
        result2 = self.manager.send_security_alert(
            'IP_BLOCKED',
            event_data,
            ip_address='***********'
        )
        self.assertFalse(result2)


class SecurityNotificationFunctionTests(TestCase):
    """Test individual security notification functions."""
    
    def setUp(self):
        cache.clear()
        mail.outbox = []  # Clear email outbox
    
    @patch('backend.security_notifications.security_notifier.send_security_alert')
    def test_send_ip_blocked_alert(self, mock_send_alert):
        """Test IP blocked alert function."""
        mock_send_alert.return_value = True
        
        result = send_ip_blocked_alert('***********', 30, 'Test reason')
        
        self.assertTrue(result)
        mock_send_alert.assert_called_once_with(
            'IP_BLOCKED',
            {
                'duration_minutes': 30,
                'reason': 'Test reason',
                'action_taken': 'IP blocked for 30 minutes'
            },
            ip_address='***********'
        )
    
    @patch('backend.security_notifications.security_notifier.send_security_alert')
    def test_send_multiple_failed_logins_alert(self, mock_send_alert):
        """Test multiple failed logins alert function."""
        mock_send_alert.return_value = True
        
        result = send_multiple_failed_logins_alert('***********', 'testuser', 5)
        
        self.assertTrue(result)
        mock_send_alert.assert_called_once_with(
            'MULTIPLE_FAILED_LOGINS',
            {
                'username': 'testuser',
                'attempt_count': 5,
                'time_window': '15 minutes',
                'action_taken': 'IP address monitoring increased'
            },
            ip_address='***********',
            user_email='testuser'
        )


class SecurityMonitoringIntegrationTests(TestCase):
    """Test integration with existing security monitoring."""
    
    def setUp(self):
        self.factory = RequestFactory()
        self.user = User.objects.create_user(
            email='<EMAIL>',
            password='testpass123'
        )
        cache.clear()
        mail.outbox = []
    
    @patch('backend.security_monitoring.send_ip_blocked_alert')
    def test_block_ip_sends_notification(self, mock_send_alert):
        """Test that blocking an IP sends a notification."""
        mock_send_alert.return_value = True
        
        block_ip('***********', 30)
        
        mock_send_alert.assert_called_once_with(
            '***********', 
            30, 
            'Multiple failed login attempts'
        )
    
    @patch('backend.security_monitoring.send_multiple_failed_logins_alert')
    def test_check_failed_attempts_sends_notification(self, mock_send_alert):
        """Test that multiple failed attempts trigger notification."""
        mock_send_alert.return_value = True

        # Create 2 failed attempts (below blocking threshold but above notification threshold)
        for i in range(2):
            FailedLoginAttempt.objects.create(
                ip_address='***********',
                username='testuser',
                attempt_count=1
            )

        result = check_failed_attempts('***********', 'testuser')

        # Should not block yet (threshold is 5 for IP, 3 for username)
        self.assertFalse(result)
        # But should send notification (threshold is 3 for IP, 2 for username)
        mock_send_alert.assert_called_once()
    
    @patch('backend.security_notifications.security_notifier.send_security_alert')
    def test_log_security_event_sends_notification(self, mock_send_alert):
        """Test that logging security events sends notifications for high priority events."""
        mock_send_alert.return_value = True

        request = self.factory.get('/')
        request.META['REMOTE_ADDR'] = '***********'
        request.META['HTTP_USER_AGENT'] = 'Test Agent'

        log_security_event('SUSPICIOUS_ACTIVITY', request, self.user, {'test': 'data'})

        mock_send_alert.assert_called_once()


class SecurityNotificationSettingsTests(TestCase):
    """Test security notification settings and configuration."""
    
    def test_settings_configuration(self):
        """Test that security notification settings are properly configured."""
        config = getattr(settings, 'SECURITY_EMAIL_NOTIFICATIONS', {})
        
        # Check that configuration exists
        self.assertIsInstance(config, dict)
        
        # Check required keys
        self.assertIn('ENABLED', config)
        self.assertIn('RECIPIENTS', config)
        self.assertIn('THROTTLE_MINUTES', config)
        self.assertIn('HIGH_PRIORITY_EVENTS', config)
        self.assertIn('MEDIUM_PRIORITY_EVENTS', config)
        
        # Check that recipients include admin emails
        recipients = config.get('RECIPIENTS', [])
        self.assertIn('<EMAIL>', recipients)
    
    def test_email_backend_configuration(self):
        """Test that email backend is properly configured."""
        # In test environment, we use locmem backend
        expected_backend = getattr(settings, 'EMAIL_BACKEND', 'django.core.mail.backends.locmem.EmailBackend')
        self.assertIn(expected_backend, [
            'django.core.mail.backends.smtp.EmailBackend',
            'django.core.mail.backends.locmem.EmailBackend'
        ])
        self.assertIsNotNone(settings.DEFAULT_FROM_EMAIL)


class SecurityNotificationTemplateTests(TestCase):
    """Test security notification email templates."""
    
    def setUp(self):
        self.manager = SecurityNotificationManager()
    
    def test_template_name_mapping(self):
        """Test that event types map to correct templates."""
        template = self.manager._get_template_name('IP_BLOCKED')
        self.assertEqual(template, 'emails/security/ip_blocked.html')
        
        template = self.manager._get_template_name('MULTIPLE_FAILED_LOGINS')
        self.assertEqual(template, 'emails/security/multiple_failed_logins.html')
        
        template = self.manager._get_template_name('UNKNOWN_EVENT')
        self.assertEqual(template, 'emails/security/generic_security_alert.html')


@pytest.mark.django_db
class SecurityNotificationEndToEndTests(TestCase):
    """End-to-end tests for the complete security notification system."""
    
    def setUp(self):
        self.factory = RequestFactory()
        self.user = User.objects.create_user(
            email='<EMAIL>',
            password='testpass123'
        )
        cache.clear()
        mail.outbox = []
    
    def test_complete_failed_login_flow(self):
        """Test the complete flow from failed login to email notification."""
        # Simulate multiple failed login attempts
        request = self.factory.post('/login/')
        request.META['REMOTE_ADDR'] = '***********00'
        request.META['HTTP_USER_AGENT'] = 'Test Browser'
        
        # Create failed attempts to trigger notification
        for i in range(3):
            FailedLoginAttempt.objects.create(
                ip_address='***********00',
                username='testuser',
                attempt_count=1
            )
        
        # This should trigger email notification
        with patch('backend.security_monitoring.send_multiple_failed_logins_alert') as mock_alert:
            mock_alert.return_value = True
            result = check_failed_attempts('***********00', 'testuser')
            mock_alert.assert_called_once()
    
    def test_ip_blocking_flow(self):
        """Test the complete flow from IP blocking to email notification."""
        with patch('backend.security_monitoring.send_ip_blocked_alert') as mock_alert:
            mock_alert.return_value = True
            block_ip('***********00', 60)
            mock_alert.assert_called_once_with('***********00', 60, 'Multiple failed login attempts')

    def test_attempt_count_increments_properly(self):
        """Test that attempt_count increments for repeated failed logins from same IP/username."""
        from django.contrib.auth.signals import user_login_failed

        # Simulate multiple failed login attempts from same IP/username
        request = self.factory.post('/login/')
        request.META['REMOTE_ADDR'] = '************'
        request.META['HTTP_USER_AGENT'] = 'Test Browser'

        # First failed login
        user_login_failed.send(
            sender=None,
            credentials={'username': '<EMAIL>'},
            request=request
        )

        # Check that one record was created with attempt_count=1
        attempts = FailedLoginAttempt.objects.filter(
            ip_address='************',
            username='<EMAIL>'
        )
        self.assertEqual(attempts.count(), 1)
        self.assertEqual(attempts.first().attempt_count, 1)

        # Second failed login from same IP/username
        user_login_failed.send(
            sender=None,
            credentials={'username': '<EMAIL>'},
            request=request
        )

        # Check that still only one record exists but attempt_count=2
        attempts = FailedLoginAttempt.objects.filter(
            ip_address='************',
            username='<EMAIL>'
        )
        self.assertEqual(attempts.count(), 1)
        self.assertEqual(attempts.first().attempt_count, 2)

        # Third failed login from same IP/username
        user_login_failed.send(
            sender=None,
            credentials={'username': '<EMAIL>'},
            request=request
        )

        # Check that still only one record exists but attempt_count=3
        attempts = FailedLoginAttempt.objects.filter(
            ip_address='************',
            username='<EMAIL>'
        )
        self.assertEqual(attempts.count(), 1)
        self.assertEqual(attempts.first().attempt_count, 3)
