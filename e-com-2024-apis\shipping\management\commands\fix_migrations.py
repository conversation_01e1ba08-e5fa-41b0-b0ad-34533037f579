"""
Management command to fix migration inconsistency issues
"""

from django.core.management.base import BaseCommand
from django.db import connection


class Command(BaseCommand):
    help = 'Fix migration inconsistency issues'
    
    def handle(self, *args, **options):
        self.stdout.write(self.style.SUCCESS('Fixing migration inconsistencies...'))
        
        try:
            with connection.cursor() as cursor:
                # Check current migration state
                cursor.execute(
                    "SELECT app, name FROM django_migrations WHERE app = 'users' ORDER BY id"
                )
                migrations = cursor.fetchall()
                
                self.stdout.write("Current users migrations:")
                for app, name in migrations:
                    self.stdout.write(f"  {app}.{name}")
                
                # Check if we need to fix the dependency issue
                cursor.execute(
                    "SELECT COUNT(*) FROM django_migrations WHERE app = 'users' AND name = '0002_alter_customer_phone_number'"
                )
                has_0002 = cursor.fetchone()[0] > 0
                
                cursor.execute(
                    "SELECT COUNT(*) FROM django_migrations WHERE app = 'users' AND name = '0003_alter_address_order_user_phone'"
                )
                has_0003 = cursor.fetchone()[0] > 0
                
                cursor.execute(
                    "SELECT COUNT(*) FROM django_migrations WHERE app = 'users' AND name = '0004_alter_address_apartment_alter_address_city_and_more'"
                )
                has_0004 = cursor.fetchone()[0] > 0
                
                if has_0004 and not has_0002:
                    self.stdout.write("Adding missing migration 0002...")
                    cursor.execute(
                        "INSERT INTO django_migrations (app, name, applied) VALUES ('users', '0002_alter_customer_phone_number', NOW())"
                    )
                
                if has_0004 and not has_0003:
                    self.stdout.write("Adding missing migration 0003...")
                    cursor.execute(
                        "INSERT INTO django_migrations (app, name, applied) VALUES ('users', '0003_alter_address_order_user_phone', NOW())"
                    )
                
                self.stdout.write(self.style.SUCCESS('Migration inconsistencies fixed!'))
                
        except Exception as e:
            self.stdout.write(self.style.ERROR(f'Error fixing migrations: {e}'))
            
        # Now try to run the shipping migrations
        self.stdout.write("Attempting to run shipping migrations...")
        try:
            from django.core.management import call_command
            call_command('migrate', 'shipping', verbosity=1)
            self.stdout.write(self.style.SUCCESS('Shipping migrations completed successfully!'))
        except Exception as e:
            self.stdout.write(self.style.ERROR(f'Error running shipping migrations: {e}'))
