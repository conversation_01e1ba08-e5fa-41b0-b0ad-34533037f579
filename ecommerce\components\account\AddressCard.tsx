import React, { useState } from "react";
import { Card, CardContent } from "../../components/ui/card";
import { Button } from "../../components/ui/button";
import { Badge } from "../ui/badge";
import EditOrAddAddressForm from "./EditOrAddAddressForm";
import DeliveryForm from "../checkout/DeliveryForm";
import { Pencil, Trash2 } from "lucide-react";
import { useToast } from "@/hooks/use-toast";
import useApi from "@/hooks/useApi";
import { MAIN_URL, USER_ADDRESS } from "@/constant/urls";

const AddressCard = (props: any) => {
  const [editOpen, setEditOpen] = useState(false);
  const { remove } = useApi(MAIN_URL);
  const { toast } = useToast();

  const {
    id,
    address_type,
    is_default,
    street_address,
    apartment,
    city,
    state,
    postal_code,
    country,
  } = props;

  const handleDelete = async (id: number) => {
    const response: any = await remove(`${USER_ADDRESS}${id}/`);

    if (<PERSON><PERSON>an(response?.id)) {
      props.setAddresses((prev: any) =>
        prev.filter((item: any) => item.id !== id)
      );
      toast({
        title: "Address deleted",
        description: "The address has been removed from your account.",
      });
    } else {
      toast({
        title: "Error",
        variant: "destructive",
        description: JSON.parse(response)?.detail,
      });
    }
  };

  return (
    <div>
      <Card key={id} className="border border-gray-200 rounded-xl overflow-hidden shadow-sm hover:shadow-md transition-all duration-300">
        <CardContent className="p-6">
          <div className="flex justify-between items-start mb-4">
            <div className="flex flex-col gap-2">
              <div className="flex items-center gap-2">
                <h3 className="font-semibold text-gray-800 text-base sm:text-lg">{address_type}</h3>
                {is_default && (
                  <Badge className="bg-blue-100 text-blue-700 border border-blue-200 hover:bg-blue-200 px-2 py-0.5 text-xs rounded-full">
                    Default
                  </Badge>
                )}
              </div>
            </div>
            <div className="flex gap-2">
              <EditOrAddAddressForm
                open={editOpen}
                onOpenChange={setEditOpen}
                title="Edit Address"
                form={
                  <DeliveryForm
                    setAddresses={props?.setAddresses}
                    address={{ ...props }}
                    onClose={setEditOpen}
                  />
                }
                button={
                  <Button
                    variant="ghost"
                    size="icon"
                    className="rounded-full hover:bg-blue-50 hover:text-blue-600 transition-colors"
                  >
                    <Pencil className="h-4 w-4" />
                  </Button>
                }
              />
              <Button
                variant="ghost"
                size="icon"
                onClick={() => handleDelete(id)}
                className="rounded-full hover:bg-red-50 hover:text-red-600 transition-colors"
              >
                <Trash2 className="h-4 w-4" />
              </Button>
            </div>
          </div>

          <div className="bg-gray-50 p-3 rounded-lg border border-gray-100">
            <div className="flex gap-3">
              <div className="mt-0.5">
                <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5 text-gray-500" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={1.5} d="M17.657 16.657L13.414 20.9a1.998 1.998 0 01-2.827 0l-4.244-4.243a8 8 0 1111.314 0z" />
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={1.5} d="M15 11a3 3 0 11-6 0 3 3 0 016 0z" />
                </svg>
              </div>
              <div>
                <p className="text-sm text-gray-700 leading-relaxed">
                  {street_address}
                  {Boolean(apartment) && (
                    <span>
                      <br />{apartment}
                    </span>
                  )}
                  <br />
                  {city}, {state} {postal_code}
                  <br />
                  {country}
                </p>
              </div>
            </div>
          </div>
        </CardContent>
      </Card>
    </div>
  );
};

export default AddressCard;
