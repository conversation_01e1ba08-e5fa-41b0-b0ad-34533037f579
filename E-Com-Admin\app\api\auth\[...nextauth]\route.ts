import NextAuth from "next-auth";
import GoogleProvider from "next-auth/providers/google";
import CredentialsProvider from "next-auth/providers/credentials";
import { MAIN_URL, TOKEN_REFFRESH, USER_SOCIAL_LOGIN, ADMIN_LOGIN } from "@/constant/urls";
import axios from "axios";
import { debugToken, isTokenExpired, getTimeUntilExpiry } from "@/lib/tokenUtils";

const authHandler = NextAuth({
  providers: [
    GoogleProvider({
      clientId: process.env.GOOGLE_CLIENT_ID || "",
      clientSecret: process.env.GOOGLE_CLIENT_SECRET || "",
    }),
    CredentialsProvider({
      name: "credentials",
      credentials: {
        email: { label: "Email", type: "email" },
        password: { label: "Password", type: "password" }
      },
      async authorize(credentials) {
        if (!credentials?.email || !credentials?.password) {
          throw new Error("Email and password are required");
        }

        try {
          const response = await axios.post(`${MAIN_URL}${ADMIN_LOGIN}`, {
            email: credentials.email,
            password: credentials.password,
          });

          const user = response.data;

          if (user && user.accessToken) {
            return {
              id: user.id,
              email: user.email,
              name: user.name || user.email,
              access: user.accessToken,
              refresh: user.refreshToken,
              user: user,
            };
          }

          throw new Error("Invalid credentials");
        } catch (error) {
          console.error("Authentication error:", error);

          if (axios.isAxiosError(error)) {
            if (error.response?.status === 401) {
              throw new Error("Invalid email or password");
            }
            if (error.response?.data?.detail) {
              throw new Error(error.response.data.detail);
            }
            if (error.response?.data?.message) {
              throw new Error(error.response.data.message);
            }
          }

          throw new Error("Authentication failed. Please try again.");
        }
      }
    }),
  ],
  callbacks: {
    // eslint-disable-next-line @typescript-eslint/no-explicit-any
    async signIn({ user, account }: { user: any; account: any; profile?: any }) {
      if (account?.provider === "google") {
        try {
          // Send Google user data to your backend API
          const res = await fetch(`${MAIN_URL}${USER_SOCIAL_LOGIN}`, {
            method: "POST",
            headers: { "Content-Type": "application/json" },
            body: JSON.stringify({
              email: user.email,
              name: user.name,
            }),
          });

          if (res.ok) {
            const data = await res.json();

            // Attach custom tokens to user object
            user.access = data.accessToken;
            user.refresh = data.refreshToken;
            user.phone = data.phone_number;
            user.dob = data.date_of_birth;
            user.id = Number(data.id);
            user.email = data.email;
            user.name = data.name;

            return true;
          } else {
            console.error("Backend login failed:", await res.json());
            return false;
          }
        } catch (error) {
          console.error("Error during Google sign-in:", error);
          return false;
        }
      }
      return true;
    },
    
    // eslint-disable-next-line @typescript-eslint/no-explicit-any
    async jwt({ token, user, account }: { token: any; user?: any; account?: any }) {
      // Initial sign in
      if (account && user) {
        console.log('🔐 Initial JWT token creation');

        // Debug the tokens we received
        if (user.access) {
          debugToken(user.access, 'access');
        }
        if (user.refresh) {
          debugToken(user.refresh, 'refresh');
        }

        return {
          ...token,
          access: user.access,
          refresh: user.refresh,
          user: user.user,
          accessTokenExpires: Date.now() + 60 * 60 * 1000, // 1 hour
        };
      }

      // Check if we have a valid access token
      if (token.access) {
        const timeUntilExpiry = getTimeUntilExpiry(token.access);
        const isExpired = isTokenExpired(token.access);

        console.log('🕐 Token expiry check:', {
          isExpired,
          timeUntilExpiry: timeUntilExpiry ? `${Math.floor(timeUntilExpiry / 1000)}s` : 'unknown',
          nextAuthExpiry: token.accessTokenExpires ? new Date(token.accessTokenExpires).toISOString() : 'not set'
        });

        // Only refresh if token is actually expired or expiring within 2 minutes (reduced from 5 minutes)
        // This prevents unnecessary refresh attempts
        if (!isExpired && timeUntilExpiry && timeUntilExpiry > 2 * 60 * 1000) { // 2 minutes buffer
          return token;
        }

        // If token is expired but we have a refresh token, try to refresh
        if (isExpired && token.refresh) {
          console.log('🔄 Access token expired, attempting refresh...');
          return refreshAccessToken(token);
        }

        // If token is expiring soon (within 2 minutes), try to refresh
        if (timeUntilExpiry && timeUntilExpiry <= 2 * 60 * 1000 && token.refresh) {
          console.log('🔄 Access token expiring soon, attempting refresh...');
          return refreshAccessToken(token);
        }
      }

      // If no access token but we have refresh token, try to get new access token
      if (!token.access && token.refresh) {
        console.log('🔄 No access token found, attempting refresh...');
        return refreshAccessToken(token);
      }

      // If we reach here, we have no valid tokens - return token as is
      // Don't automatically sign out, let the client handle it
      console.log('⚠️ No valid tokens available, returning existing token');
      return token;
    },
    
    // eslint-disable-next-line @typescript-eslint/no-explicit-any
    async session({ session, token }: { session: any; token: any }) {
      // If there's a token refresh error, the session should indicate this
      if (token.error === "RefreshAccessTokenError") {
        console.warn("⚠️ Session contains refresh error, user may need to re-authenticate");
      }

      return {
        ...session,
        user: {
          ...session.user,
          access: token.access,
          refresh: token.refresh,
          user: token.user,
        },
        error: token.error, // Pass error to session for client-side handling
      };
    },
  },
  pages: {
    signIn: "/login",
    error: "/login",
  },

  session: {
    strategy: "jwt",
    maxAge: 24 * 60 * 60, // 24 hours
  },

  jwt: {
    maxAge: 24 * 60 * 60, // 24 hours
  },

  secret: process.env.NEXTAUTH_SECRET,

  debug: false, // Disable debug to reduce console noise
});

export const GET = authHandler;
export const POST = authHandler;

    // eslint-disable-next-line @typescript-eslint/no-explicit-any
async function refreshAccessToken(token: { refresh?: string; [key: string]: any }) {
  try {
    console.log("🔄 Attempting to refresh access token...");

    // Debug the refresh token before using it
    if (token.refresh) {
      debugToken(token.refresh, 'refresh');
    } else {
      console.error("❌ No refresh token available");
      throw new Error("No refresh token available");
    }

    const response = await axios.post(`${MAIN_URL}${TOKEN_REFFRESH}`, {
      refresh: token.refresh,
    });

    const refreshedTokens = response.data;
    console.log("✅ Token refresh successful!");
    console.log("🔍 Refresh response keys:", Object.keys(refreshedTokens));
    console.log("🔍 Full refresh response:", refreshedTokens);

    // Debug the new tokens
    if (refreshedTokens.access) {
      debugToken(refreshedTokens.access, 'access');
    } else {
      console.warn("⚠️ No 'access' field in refresh response");
    }

    if (refreshedTokens.refresh) {
      console.log("✅ New refresh token received (token rotation enabled)");
    } else {
      console.log("ℹ️ No new refresh token (using existing one)");
    }

    // Calculate proper expiry time based on the actual token
    let accessTokenExpires = Date.now() + 60 * 60 * 1000; // Default 1 hour
    if (refreshedTokens.access) {
      const timeUntilExpiry = getTimeUntilExpiry(refreshedTokens.access);
      if (timeUntilExpiry && timeUntilExpiry > 0) {
        // Use the actual token expiry minus a small buffer
        accessTokenExpires = Date.now() + timeUntilExpiry - (5 * 60 * 1000); // 5 minutes buffer
      }
    }

    return {
      ...token,
      access: refreshedTokens.access,
      // Update refresh token if provided (some backends rotate refresh tokens)
      refresh: refreshedTokens.refresh || token.refresh,
      accessTokenExpires,
      error: undefined, // Clear any previous errors
    };
  } catch (error) {
    console.error("❌ Token refresh failed:", error);

    if (axios.isAxiosError(error)) {
      console.error("Response status:", error.response?.status);
      console.error("Response data:", error.response?.data);
      console.error("Request URL:", `${MAIN_URL}${TOKEN_REFFRESH}`);

      // If refresh token is invalid (401/403), mark for re-authentication
      if (error.response?.status === 401 || error.response?.status === 403) {
        console.error("🚨 Refresh token is invalid, user needs to re-authenticate");
        return {
          ...token,
          error: "RefreshAccessTokenError",
          access: undefined, // Clear invalid access token
          refresh: undefined, // Clear invalid refresh token
        };
      }
    }

    // For other errors (network, server issues), keep existing tokens
    // This prevents signout due to temporary network issues
    console.warn("⚠️ Temporary refresh error, keeping existing tokens");
    return {
      ...token,
      // Don't set error for temporary issues - this prevents automatic signout
      // The client will retry on next request
    };
  }
}
