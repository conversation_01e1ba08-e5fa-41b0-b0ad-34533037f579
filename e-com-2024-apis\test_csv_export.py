#!/usr/bin/env python
"""
Simple test script to verify CSV export functionality
"""
import os
import sys
import django

# Add the project directory to Python path
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

# Setup Django
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'backend.settings')
django.setup()

from users.privacy_views import DataExportView
from django.contrib.auth import get_user_model
from django.test import RequestFactory
from unittest.mock import Mock

User = get_user_model()

def test_csv_generation():
    """Test the CSV generation functionality"""
    print("Testing CSV export functionality...")
    
    # Create a mock user data structure
    user_data = {
        'personal_info': {
            'id': 1,
            'email': '<EMAIL>',
            'name': 'Test User',
            'phone_number': '+1234567890',
            'date_of_birth': '1990-01-01',
            'is_verified': True
        },
        'addresses': [
            {
                'id': 1,
                'street_address': '123 Test St',
                'city': 'Test City',
                'state': 'Test State',
                'postal_code': '12345',
                'country': 'Test Country',
                'is_default': True
            }
        ],
        'orders': [
            {
                'id': 1,
                'status': 'DELIVERED',
                'total': '99.99',
                'created_at': '2024-01-01T10:00:00Z',
                'tracking_number': 'TRK123456',
                'estimated_delivery_date': '2024-01-05',
                'items': [
                    {
                        'id': 1,
                        'product_name': 'Test Product 1',
                        'variant_name': 'Size M',
                        'quantity': 2,
                        'unit_price': '29.99',
                        'total_price': '59.98'
                    },
                    {
                        'id': 2,
                        'product_name': 'Test Product 2',
                        'variant_name': '',
                        'quantity': 1,
                        'unit_price': '39.99',
                        'total_price': '39.99'
                    }
                ]
            }
        ],
        'wishlist': [
            {
                'id': 1,
                'product': {
                    'name': 'Test Product',
                    'price': '29.99'
                },
                'added_at': '2024-01-01T10:00:00Z'
            }
        ],
        'consents': [
            {
                'type': 'MARKETING',
                'granted': True,
                'granted_at': '2024-01-01T10:00:00Z',
                'withdrawn_at': None,
                'version': '1.0'
            }
        ],
        'export_info': {
            'exported_at': '2024-01-01T10:00:00Z',
            'format': 'CSV'
        }
    }
    
    # Create an instance of DataExportView
    view = DataExportView()
    
    # Test the CSV generation
    try:
        csv_content = view._generate_csv_export(user_data)
        print("✅ CSV generation successful!")
        print("\n--- CSV Content Preview ---")
        print(csv_content[:500] + "..." if len(csv_content) > 500 else csv_content)
        print("\n--- End Preview ---")
        
        # Basic validation
        assert "=== USER DATA EXPORT ===" in csv_content
        assert "=== PERSONAL INFORMATION ===" in csv_content
        assert "=== ADDRESSES ===" in csv_content
        assert "=== ORDERS ===" in csv_content
        assert "=== ORDER ITEMS ===" in csv_content
        assert "=== WISHLIST ===" in csv_content
        assert "=== CONSENT HISTORY ===" in csv_content
        assert "<EMAIL>" in csv_content
        assert "Test Product 1" in csv_content
        assert "Size M" in csv_content
        
        print("✅ All CSV content validations passed!")
        return True
        
    except Exception as e:
        print(f"❌ CSV generation failed: {str(e)}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == "__main__":
    success = test_csv_generation()
    if success:
        print("\n🎉 CSV export functionality is working correctly!")
    else:
        print("\n💥 CSV export functionality has issues!")
        sys.exit(1)
