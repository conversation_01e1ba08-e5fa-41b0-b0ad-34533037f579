"use client";
import React, { Suspense, useEffect } from "react";
import { AtSign, KeyRound } from "lucide-react";
import useApi from "../../../hooks/useApi";
import { MAIN_URL, USER_LOGIN } from "../../../constant/urls";
import { signIn, signOut, useSession } from "next-auth/react";
import { useRouter, useSearchParams } from "next/navigation";
import { Formik, Form, Field, ErrorMessage, FormikHelpers } from "formik";
import * as Yup from "yup";
import { useToast } from "../../../hooks/use-toast";
import Link from "next/link";
import { Toaster } from "../../../components/ui/toaster";
import AuthSpinner from "../../../components/ui/loading/AuthSpinner";
import { motion } from "framer-motion";

// Define TypeScript types for form values and validation schema
interface LoginFormValues {
  email: string;
  password: string;
}

// Define Yup validation schema with TypeScript support
const validationSchema: Yup.Schema<LoginFormValues> = Yup.object({
  email: Yup.string()
    .email("Enter a valid email")
    .required("Email is required"),
  password: Yup.string()
    .min(8, "Password must be at least 8 characters long")
    .required("Password is required"),
});

const LoginContain: React.FC = () => {
  const { status, data: sessionData } = useSession();
  const { create, error, data } = useApi(MAIN_URL);
  const { toast } = useToast();
  const router = useRouter();
  const searchParams = useSearchParams();
  const callbackUrl = searchParams.get("callbackUrl") ?? "/";

  useEffect(() => {
    if (status === "authenticated") {
      // Check if the session has valid access token before redirecting
      // This prevents redirect loops when session is expired but still "authenticated"
      const session = sessionData as any;
      if (session?.user?.access && !session?.user?.error) {
        router.push(callbackUrl ?? "/");
      } else {
        // Session is authenticated but invalid, force sign out
        console.log("Login page: Session authenticated but invalid, signing out");
        signOut({ redirect: false });
      }
    }
  }, [status, sessionData, router, callbackUrl]);

  const handleSubmit = async (
    values: LoginFormValues,
    { setSubmitting }: FormikHelpers<LoginFormValues>
  ) => {
    setSubmitting(true);
    try {
      const res: any = await create(USER_LOGIN, values);

      if (Boolean(res?.id)) {
        toast({
          variant: "success",
          title: "Login successful",
          description: "Redirecting you...",
        });

        // Keep the loading state while redirecting
        setTimeout(() => {
          signIn("credentials", {
            ...res,
            callbackUrl: callbackUrl ?? "/",
          });
        }, 500);
      } else {
        let errorMessage = "Login failed. Please check your credentials.";
        try {
          if (typeof res === 'string') {
            const parsed = JSON.parse(res);
            errorMessage = Object.keys(parsed)
              .map((key) => parsed[key])
              .join(", ");
          }
        } catch (e) {
          console.error("Error parsing login response:", e);
        }

        toast({
          title: "Login Error",
          description: errorMessage,
          variant: "destructive",
        });
        setSubmitting(false);
      }
    } catch (err) {
      console.error("Login error:", err);
      toast({
        title: "Login Error",
        description: "An unexpected error occurred. Please try again.",
        variant: "destructive",
      });
      setSubmitting(false);
    }
  };

  if (status === "loading") {
    return (
      <div className="flex w-full h-screen justify-center items-center">
        <div className="flex flex-col items-center gap-4">
          <AuthSpinner size="lg" color="border-t-blue-600" />
          <p className="text-gray-600 animate-pulse">Loading...</p>
        </div>
      </div>
    );
  }

  return (
    <div className="flex justify-center items-center h-screen w-full bg-gray-50">
      <motion.div
        initial={{ opacity: 0, y: 20 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ duration: 0.5 }}
        className="flex flex-col w-4/5 md:w-[30rem] mx-auto p-4 md:p-8 bg-white rounded-2xl shadow-xl border border-gray-100"
      >
        <motion.div
          className="flex flex-row gap-3 pb-4"
          initial={{ opacity: 0, y: -10 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ delay: 0.2, duration: 0.5 }}
        >
          <div className="p-2 bg-blue-50 rounded-lg">
            <img src="/logotriumph.png" alt="Logo" width="40" className="drop-shadow-sm" />
          </div>
          <h1 className="text-2xl md:text-3xl font-bold text-gray-800 my-auto">
            TRIUMPH ENTERPRISES
          </h1>
        </motion.div>

        <motion.div
          className="text-sm font-light text-gray-600 pb-8"
          initial={{ opacity: 0 }}
          animate={{ opacity: 1 }}
          transition={{ delay: 0.4, duration: 0.5 }}
        >
          Welcome back! Sign in to your account to continue.
        </motion.div>

        <Formik<LoginFormValues>
          initialValues={{ email: "", password: "" }}
          validationSchema={validationSchema}
          onSubmit={handleSubmit}
        >
          {({ isSubmitting }: { isSubmitting: boolean }) => (
            <Form className="flex flex-col">
              <div className="pb-2">
                <label
                  htmlFor="email"
                  className="block mb-2 text-sm font-medium text-[#111827]"
                >
                  Email
                </label>
                <div className="relative text-gray-400">
                  <span className="absolute inset-y-0 left-0 flex items-center p-1 pl-3">
                    <AtSign />
                  </span>
                  <Field
                    type="email"
                    name="email"
                    className="pl-12 mb-2 bg-gray-50 text-gray-600 border border-gray-300 sm:text-sm rounded-lg focus:ring-2 focus:outline-none focus:ring-blue-300 focus:border-blue-500 block w-full p-2.5 py-3 px-4 transition-all duration-300"
                    placeholder="<EMAIL>"
                  />
                  <ErrorMessage
                    name="email"
                    component="p"
                    className="text-red-500 text-xs mt-1"
                  />
                </div>
              </div>

              <div className="pb-6">
                <div className="flex justify-between items-center mb-2">
                  <label
                    htmlFor="password"
                    className="text-sm font-medium text-[#111827]"
                  >
                    Password
                  </label>
                  <motion.div
                    className="inline-block"
                    whileHover={{ scale: 1.05 }}
                  >
                    <Link
                      href="/auth/forgot-password"
                      className="text-xs text-blue-600 hover:text-blue-800 transition-colors duration-300"
                    >
                      Forgot password?
                    </Link>
                  </motion.div>
                </div>
                <div className="relative text-gray-400">
                  <span className="absolute inset-y-0 left-0 flex items-center p-1 pl-3">
                    <KeyRound />
                  </span>
                  <Field
                    type="password"
                    name="password"
                    className="pl-12 mb-2 bg-gray-50 text-gray-600 border border-gray-300 sm:text-sm rounded-lg focus:ring-2 focus:outline-none focus:ring-blue-300 focus:border-blue-500 block w-full p-2.5 py-3 px-4 transition-all duration-300"
                    placeholder="••••••••••"
                  />
                  <ErrorMessage
                    name="password"
                    component="p"
                    className="text-red-500 text-xs mt-1"
                  />
                </div>
              </div>

              <motion.button
                type="submit"
                className="w-full text-white bg-blue-600 hover:bg-blue-700 focus:ring-4 focus:outline-none focus:ring-blue-300 font-medium rounded-lg text-sm px-5 py-3 text-center mb-6 transition-all duration-300 shadow-md hover:shadow-lg flex justify-center items-center gap-2"
                disabled={isSubmitting}
                whileHover={{ scale: 1.02 }}
                whileTap={{ scale: 0.98 }}
              >
                {isSubmitting ? (
                  <>
                    <AuthSpinner size="sm" color="border-t-white" />
                    <span>Signing in...</span>
                  </>
                ) : (
                  "Sign in"
                )}
              </motion.button>
              <div className="text-sm font-light text-gray-600 text-center">
                No account?{" "}
                <motion.div
                  className="inline-block"
                  whileHover={{ scale: 1.05 }}
                >
                  <Link
                    href="/auth/signup"
                    className="font-medium text-blue-600 hover:text-blue-800 transition-colors duration-300"
                  >
                    Sign up now
                  </Link>
                </motion.div>
              </div>
            </Form>
          )}
        </Formik>

        <div className="relative flex py-8 items-center">
          <div className="flex-grow border-t border-[1px] border-gray-200"></div>
          <span className="flex-shrink mx-4 font-medium text-gray-500">OR</span>
          <div className="flex-grow border-t border-[1px] border-gray-200"></div>
        </div>

        <form onSubmit={(e) => e.preventDefault()}>
          <div className="flex flex-row gap-2 justify-center">
            <motion.button
              onClick={() => {
                const button = document.getElementById('google-signin-button');
                if (button) {
                  button.classList.add('animate-pulse');
                }
                signIn("google", { callbackUrl: callbackUrl ?? "/" });
              }}
              id="google-signin-button"
              className="flex flex-row w-full gap-2 bg-white border border-gray-300 p-3 rounded-lg text-gray-700 hover:bg-gray-50 transition-all duration-300 shadow-sm hover:shadow-md"
              whileHover={{ scale: 1.02 }}
              whileTap={{ scale: 0.98 }}
            >
              <img src="/google-icon.svg" alt="Google" className="w-5 h-5" />
              <span className="font-medium mx-auto">Continue with Google</span>
            </motion.button>
          </div>
        </form>
      </motion.div>
      <Toaster />
    </div>
  );
};

const Login = () => {
  return (
    <Suspense fallback={<div>Loading...</div>}>
      <LoginContain />
    </Suspense>
  );
};

export default Login;
