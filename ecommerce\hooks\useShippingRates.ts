/**
 * Custom hook for shipping rate calculation with Rapidshyp integration
 */

import { useState, useCallback, useRef } from 'react';
import useApi from './useApi';
import { MAIN_URL, SHIPPING_CALCULATE_RATES } from '@/constant/urls';
import { handleShippingApiError, handleShippingValidationError } from '@/utils/shippingErrorHandler';
import type {
  CourierRate,
  ShippingRateRequest,
  ShippingRateResponse,
  UseShippingRatesReturn
} from '@/types/shipping';

export const useShippingRates = (): UseShippingRatesReturn => {
  const [rates, setRates] = useState<CourierRate[]>([]);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [source, setSource] = useState<'rapidshyp' | 'fallback' | null>(null);
  const [minimumRate, setMinimumRate] = useState<CourierRate | undefined>();
  
  const { create } = useApi<ShippingRateResponse>(MAIN_URL);
  const abortControllerRef = useRef<AbortController | null>(null);

  const calculateRates = useCallback(async (request: ShippingRateRequest) => {
    // Cancel any ongoing request
    if (abortControllerRef.current) {
      abortControllerRef.current.abort();
    }

    // Create new abort controller
    abortControllerRef.current = new AbortController();

    setLoading(true);
    setError(null);

    try {
      // Validate required fields
      if (!request.delivery_pincode) {
        handleShippingValidationError('pincode', request.delivery_pincode, 'required');
        return;
      }

      if (request.delivery_pincode.length !== 6 || !/^\d{6}$/.test(request.delivery_pincode)) {
        handleShippingValidationError('pincode', request.delivery_pincode, 'format');
        return;
      }

      if (request.weight && (request.weight < 0.1 || request.weight > 50)) {
        handleShippingValidationError('weight', request.weight, request.weight < 0.1 ? 'min' : 'max');
        return;
      }

      // Prepare request data with defaults
      const requestData: ShippingRateRequest = {
        delivery_pincode: request.delivery_pincode,
        pickup_pincode: request.pickup_pincode || undefined,
        weight: request.weight || 1.0,
        cod: request.cod || false,
        total_value: request.total_value || 0
      };

      console.log('Calculating shipping rates:', requestData);

      const response = await create(SHIPPING_CALCULATE_RATES, requestData);

      if (typeof response === 'string') {
        // Error response
        throw new Error(response);
      }

      if (!response || !response.success) {
        throw new Error(response?.message || 'Failed to calculate shipping rates');
      }

      // Combine all rates
      const allRates = [
        ...(response.rapidshyp_rates || []),
        ...(response.existing_methods || [])
      ];

      setRates(allRates);
      setSource(response.source);
      setMinimumRate(response.minimum_rate);

      console.log('Shipping rates calculated:', {
        total: allRates.length,
        rapidshyp: response.rapidshyp_rates?.length || 0,
        existing: response.existing_methods?.length || 0,
        source: response.source,
        minimumRate: response.minimum_rate
      });

    } catch (err) {
      if (err instanceof Error && err.name === 'AbortError') {
        // Request was aborted, don't update state
        return;
      }

      // Use centralized error handling
      const shippingError = handleShippingApiError(err, 'rate_calculation');
      setError(shippingError.message);

      // Clear rates on error
      setRates([]);
      setSource(null);
      setMinimumRate(undefined);
    } finally {
      setLoading(false);
      abortControllerRef.current = null;
    }
  }, [create]);

  const clearRates = useCallback(() => {
    // Cancel any ongoing request
    if (abortControllerRef.current) {
      abortControllerRef.current.abort();
    }

    setRates([]);
    setError(null);
    setSource(null);
    setMinimumRate(undefined);
    setLoading(false);
  }, []);

  return {
    rates,
    loading,
    error,
    source,
    minimumRate,
    calculateRates,
    clearRates
  };
};

export default useShippingRates;
