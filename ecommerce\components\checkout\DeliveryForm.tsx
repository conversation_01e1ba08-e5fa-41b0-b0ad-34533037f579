import { useEffect, useState } from "react";
import { Formik, Form, Field } from "formik";
import * as Yup from "yup";
import { Button } from "../../components/ui/button";
import { Input } from "../../components/ui/input";
import { Label } from "../../components/ui/label";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "../../components/ui/select";
import { useToast } from "../../hooks/use-toast";
import useApi from "../../hooks/useApi";
import { MAIN_URL, USER_ADDRESS } from "../../constant/urls";
import { Card, CardContent } from "../ui/card";
import { useSession } from "next-auth/react";
import { MapPin, Mail, Phone, Home, Building, ArrowRight, Save } from "lucide-react";

// List of Indian states
const STATES = [
  "Andhra Pradesh",
  "Arunachal Pradesh",
  "Assam",
  "Bihar",
  "Chhattisgarh",
  "Goa",
  "Gujarat",
  "Haryana",
  "Himachal Pradesh",
  "Jharkhand",
  "Karnataka",
  "Kerala",
  "Madhya Pradesh",
  "Maharashtra",
  "Manipur",
  "Meghalaya",
  "Mizoram",
  "Nagaland",
  "Odisha",
  "Punjab",
  "Rajasthan",
  "Sikkim",
  "Tamil Nadu",
  "Telangana",
  "Tripura",
  "Uttar Pradesh",
  "Uttarakhand",
  "West Bengal",
];

const ADDRESSTYPE = ["BILLING", "SHIPPING"];

// const initialValues = {
//   street_address: "",
//   apartment: "",
//   city: "",
//   state: "",
//   postal_code: "",
//   is_default: false,
//   order_user_phone: "",
//   order_user_email: "",
// };

const validationSchema = Yup.object().shape({
  street_address: Yup.string()
    .min(5, "Address must be at least 5 characters")
    .required("Address is required"),
  apartment: Yup.string().min(5, "Appartment must be at least 5 characters"),
  city: Yup.string().required("City is required"),
  state: Yup.string().required("State is required"),
  postal_code: Yup.string()
    .matches(/^[1-9][0-9]{5}$/, "Please enter a valid 6-digit pin code")
    .required("Pin code is required"),
  address_type: Yup.string().required("Address Type is required"),
  order_user_phone: Yup.string()
    .matches(/^[6-9]\d{9}$/, "Please enter a valid Indian phone number")
    .required("Phone number is required"),
  order_user_email: Yup.string()
    .email("Please enter a valid email address")
    .required("Email is required"),
});

const DeliveryForm = ({
  handleNext,
  shippingAddress,
  setShippingAddress,
  onClose,
  setAddresses,
  address,
}: any) => {
  const { toast } = useToast();
  const [isSubmitting, setIsSubmitting] = useState(false);
  const { status, data: session }: any = useSession();

  const { create, update, error, loading } = useApi(MAIN_URL);

  const initialValues = {
    street_address: "",
    apartment: "",
    city: "",
    state: "",
    postal_code: "",
    is_default: false,
    order_user_phone: (session?.user?.phone ?? "") as string,
    order_user_email: (session?.user?.email ?? "") as string,
    address_type: "BILLING",
  };

  const {
    data,
    read,
    error: addressError,
    loading: andressLoading,
  } = useApi(MAIN_URL);

  useEffect(() => {
    if (status === "authenticated") {
      if (!onClose) {
        read(USER_ADDRESS);
      }
    }
  }, [status]);

  const handleSubmit = async (values: typeof initialValues) => {
    setIsSubmitting(true);
    try {
      let response: any = null;
      if (address?.id) {
        response = await update(`${USER_ADDRESS}${address.id}/`, values);
      } else {
        response = await create(USER_ADDRESS, values);
      }

      if (response && Boolean(response?.id)) {
        toast({
          title: "Success!",
          description: "Delivery details have been saved successfully.",
        });

        if (onClose) {
          if (address?.id) {
            setAddresses((prev: any) => {
              return prev.map((item: any) =>
                item.id === response.id ? response : item
              );
            });
          } else {
            setAddresses((prev: any) => [...prev, response]);
          }
          onClose();
        } else {
          if (setShippingAddress) setShippingAddress(response);
          if (handleNext) handleNext(0);
        }
      }
    } catch (error) {
      toast({
        variant: "destructive",
        title: "Error",
        description: "Something went wrong. Please try again.",
      });
    } finally {
      setIsSubmitting(false);
    }
  };

  const handleSavedAddressSelect = (address: any) => {
    setShippingAddress(address);
  };

  return (
    <div className="space-y-8">
      <h2 className="text-xl sm:text-2xl font-semibold text-center mb-6 gradient-text flex items-center justify-center gap-2">
        <MapPin className="h-5 w-5 sm:h-6 sm:w-6" />
        Delivery Details
      </h2>

      <Card className="shadow-md border-t-4 border-t-primary">
        <CardContent className="p-6 md:p-8">
          {!onClose && (
            <div className="mb-8">
              <Label className="text-base sm:text-lg font-medium flex items-center gap-2 mb-3">
                <Home className="h-4 w-4 sm:h-5 sm:w-5 text-primary" />
                Saved Addresses
              </Label>
              <Select
                defaultValue={
                  !Boolean(shippingAddress?.id) ? "0" : shippingAddress
                }
                onValueChange={(value) => handleSavedAddressSelect(value)}
              >
                <SelectTrigger className="w-full py-6 text-base">
                  <SelectValue placeholder="Add new address" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value={"0"} className="py-3">
                    <div className="flex items-center gap-2">
                      <Building className="h-4 w-4" />
                      Add new Address
                    </div>
                  </SelectItem>
                  {Array.isArray(data) &&
                    data.map((addr) => (
                      <SelectItem key={addr.id} value={addr} className="py-3">
                        <div className="flex items-center gap-2">
                          <MapPin className="h-4 w-4" />
                          {addr?.street_address} - {addr?.postal_code}
                        </div>
                      </SelectItem>
                    ))}
                </SelectContent>
              </Select>
            </div>
          )}

          {!Boolean(shippingAddress?.id) && (
            <Formik
              initialValues={address ?? initialValues}
              validationSchema={validationSchema}
              onSubmit={handleSubmit}
            >
              {({ errors, touched, setFieldValue, values }) => (
                <Form className="space-y-6">
                  <div className="space-y-2">
                    <Label htmlFor="address">Address</Label>
                    <Field
                      as={Input}
                      id="address"
                      name="street_address"
                      placeholder="Enter your address"
                      className={
                        errors.street_address && touched.street_address
                          ? "border-destructive"
                          : ""
                      }
                    />
                    {errors.street_address && touched.street_address && (
                      <p className="text-sm text-destructive">
                        {errors.street_address}
                      </p>
                    )}
                  </div>
                  <div className="space-y-2">
                    <Label htmlFor="Appartment">Appartment</Label>
                    <Field
                      as={Input}
                      id="Appartment"
                      name="apartment"
                      placeholder="Enter your Appartment"
                      className={
                        errors.apartment && touched.apartment
                          ? "border-destructive"
                          : ""
                      }
                    />
                    {errors.apartment && touched.apartment && (
                      <p className="text-sm text-destructive">
                        {errors.apartment}
                      </p>
                    )}
                  </div>

                  <div className="space-y-2">
                    <Label htmlFor="city">City</Label>
                    <Field
                      as={Input}
                      id="city"
                      name="city"
                      placeholder="Enter your city"
                      className={
                        errors.city && touched.city ? "border-destructive" : ""
                      }
                    />
                    {errors.city && touched.city && (
                      <p className="text-sm text-destructive">{errors.city}</p>
                    )}
                  </div>

                  <div className="space-y-2">
                    <Label htmlFor="state">State</Label>
                    <Select
                      value={values.state}
                      onValueChange={(value) => setFieldValue("state", value)}
                    >
                      <SelectTrigger
                        className={
                          errors.state && touched.state
                            ? "border-destructive"
                            : ""
                        }
                      >
                        <SelectValue placeholder="Select your state" />
                      </SelectTrigger>
                      <SelectContent>
                        {STATES.map((state) => (
                          <SelectItem key={state} value={state}>
                            {state}
                          </SelectItem>
                        ))}
                      </SelectContent>
                    </Select>
                    {errors.state && touched.state && (
                      <p className="text-sm text-destructive">{errors.state}</p>
                    )}
                  </div>

                  <div className="space-y-2">
                    <Label htmlFor="pinCode">Pin Code</Label>
                    <Field
                      as={Input}
                      id="pinCode"
                      name="postal_code"
                      placeholder="Enter 6-digit pin code"
                      maxLength={6}
                      className={
                        errors.postal_code && touched.postal_code
                          ? "border-destructive"
                          : ""
                      }
                    />
                    {errors.postal_code && touched.postal_code && (
                      <p className="text-sm text-destructive">
                        {errors.postal_code}
                      </p>
                    )}
                  </div>

                  <div className="space-y-2">
                    <Label htmlFor="address_type">Address Type</Label>
                    <Select
                      value={values.address_type}
                      onValueChange={(value) =>
                        setFieldValue("address_type", value)
                      }
                    >
                      <SelectTrigger
                        className={
                          errors.address_type && touched.address_type
                            ? "border-destructive"
                            : ""
                        }
                      >
                        <SelectValue placeholder="Select Address Type" />
                      </SelectTrigger>
                      <SelectContent>
                        {ADDRESSTYPE.map((type) => (
                          <SelectItem key={type} value={type}>
                            {type}
                          </SelectItem>
                        ))}
                      </SelectContent>
                    </Select>
                    {errors.address_type && touched.address_type && (
                      <p className="text-sm text-destructive">
                        {errors.address_type}
                      </p>
                    )}
                  </div>

                  <div className="space-y-2">
                    <Label htmlFor="phone">Phone Number</Label>
                    <Field
                      as={Input}
                      id="phone"
                      name="order_user_phone"
                      placeholder="Enter your phone number"
                      maxLength={10}
                      className={
                        errors.order_user_phone && touched.order_user_phone
                          ? "border-destructive"
                          : ""
                      }
                    />
                    {errors.order_user_phone && touched.order_user_phone && (
                      <p className="text-sm text-destructive">
                        {errors.order_user_phone}
                      </p>
                    )}
                  </div>

                  <div className="space-y-2">
                    <Label htmlFor="email">Email</Label>
                    <Field
                      as={Input}
                      id="email"
                      name="order_user_email"
                      type="email"
                      placeholder="Enter your email"
                      className={
                        errors.order_user_email && touched.order_user_email
                          ? "border-destructive"
                          : ""
                      }
                    />
                    {errors.order_user_email && touched.order_user_email && (
                      <p className="text-sm text-destructive">
                        {errors.order_user_email}
                      </p>
                    )}
                  </div>

                  <Button
                    type="submit"
                    className="w-full py-6 mt-4 shadow-md hover:shadow-lg transition-all duration-300 flex items-center justify-center gap-2"
                    disabled={isSubmitting}
                  >
                    {isSubmitting ? (
                      <>
                        <div className="w-5 h-5 border-t-2 border-b-2 border-white rounded-full animate-spin"></div>
                        Saving...
                      </>
                    ) : (
                      <>
                        <Save className="h-5 w-5" />
                        Save Delivery Details
                      </>
                    )}
                  </Button>
                </Form>
              )}
            </Formik>
          )}

          {Boolean(shippingAddress?.id) && (
            <Button
              onClick={() => handleNext(0)}
              className="w-full md:w-1/2 mx-auto flex items-center justify-center gap-2 py-6 mt-4 shadow-md hover:shadow-lg transition-all duration-300"
            >
              Continue to Shipping
              <ArrowRight className="h-5 w-5" />
            </Button>
          )}
        </CardContent>
      </Card>
    </div>
  );
};

export default DeliveryForm;
