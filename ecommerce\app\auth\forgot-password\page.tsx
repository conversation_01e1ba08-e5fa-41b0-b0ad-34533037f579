"use client";

import React, { Suspense } from 'react';
import { motion } from 'framer-motion';
import ForgotPasswordForm from '../../../components/auth/ForgotPasswordForm';
import { Toaster } from '../../../components/ui/toaster';
import AuthSpinner from '../../../components/ui/loading/AuthSpinner';

const ForgotPasswordContent: React.FC = () => {
  return (
    <div className="flex justify-center items-center min-h-screen w-full bg-gray-50 py-8">
      <motion.div
        initial={{ opacity: 0, y: 20 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ duration: 0.5 }}
        className="flex flex-col w-4/5 md:w-[30rem] mx-auto p-4 md:p-8 bg-white rounded-2xl shadow-xl border border-gray-100"
      >
        {/* Header */}
        <motion.div
          className="flex flex-row gap-3 pb-6 border-b border-gray-100"
          initial={{ opacity: 0, y: -10 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ delay: 0.2, duration: 0.5 }}
        >
          <div className="p-2 bg-blue-50 rounded-lg">
            <img 
              src="/logotriumph.png" 
              alt="Triumph Enterprises Logo" 
              width="40" 
              className="drop-shadow-sm" 
            />
          </div>
          <div className="flex flex-col justify-center">
            <h1 className="text-xl md:text-2xl font-bold text-gray-800">
              TRIUMPH ENTERPRISES
            </h1>
            <p className="text-xs text-gray-500">
              Secure Password Recovery
            </p>
          </div>
        </motion.div>

        {/* Form Content */}
        <motion.div
          className="pt-6"
          initial={{ opacity: 0 }}
          animate={{ opacity: 1 }}
          transition={{ delay: 0.4, duration: 0.5 }}
        >
          <ForgotPasswordForm />
        </motion.div>

        {/* Security Notice */}
        <motion.div
          className="mt-8 pt-6 border-t border-gray-100"
          initial={{ opacity: 0 }}
          animate={{ opacity: 1 }}
          transition={{ delay: 0.6, duration: 0.5 }}
        >
          <div className="bg-blue-50 border border-blue-200 rounded-lg p-4">
            <div className="text-xs text-blue-800 space-y-1">
              <p className="font-medium">🔒 Security Information:</p>
              <ul className="space-y-0.5 text-blue-700">
                <li>• Reset links expire in 15 minutes for security</li>
                <li>• Links can only be used once</li>
                <li>• We never store your password in plain text</li>
                <li>• All password reset activities are logged</li>
              </ul>
            </div>
          </div>
        </motion.div>

        {/* Footer */}
        <motion.div
          className="mt-6 text-center"
          initial={{ opacity: 0 }}
          animate={{ opacity: 1 }}
          transition={{ delay: 0.8, duration: 0.5 }}
        >
          <p className="text-xs text-gray-500">
            Having trouble? Contact our support team at{' '}
            <a 
              href="mailto:<EMAIL>" 
              className="text-blue-600 hover:text-blue-800 transition-colors duration-300"
            >
              <EMAIL>
            </a>
          </p>
        </motion.div>
      </motion.div>
      <Toaster />
    </div>
  );
};

const ForgotPasswordPage: React.FC = () => {
  return (
    <Suspense 
      fallback={
        <div className="flex w-full h-screen justify-center items-center">
          <div className="flex flex-col items-center gap-4">
            <AuthSpinner size="lg" color="border-t-blue-600" />
            <p className="text-gray-600 animate-pulse">Loading...</p>
          </div>
        </div>
      }
    >
      <ForgotPasswordContent />
    </Suspense>
  );
};

export default ForgotPasswordPage;
