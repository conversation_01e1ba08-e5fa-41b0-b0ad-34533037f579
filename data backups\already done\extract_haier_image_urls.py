import json
import os
import re
import requests
from bs4 import BeautifulSoup
from urllib.parse import urljoin

# Configuration
JSON_FILE_PATH = 'output/json/haier_kitchen_products.json'
OUTPUT_DIR = 'haier_image_urls'

def create_folder(folder_path):
    """Create folder if it doesn't exist"""
    if not os.path.exists(folder_path):
        os.makedirs(folder_path)
        print(f"Created folder: {folder_path}")

def sanitize_filename(filename):
    """Remove invalid characters from filename"""
    return re.sub(r'[\\/*?:"<>|]', "", filename)

def extract_image_urls(product_url, product_model, product_name):
    """Extract image URLs from a product page."""
    try:
        print(f"Visiting: {product_url}")
        headers = {
            'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36'
        }
        response = requests.get(product_url, headers=headers, timeout=15)
        
        if response.status_code != 200:
            print(f"Failed to access {product_url}, status code: {response.status_code}")
            return []
        
        soup = BeautifulSoup(response.text, 'html.parser')
        
        # Extract all image URLs from the page
        image_urls = []
        for img in soup.find_all('img'):
            src = img.get('src')
            if src:
                # Convert relative URLs to absolute
                full_url = urljoin(product_url, src)
                # Filter out small icons, logos, etc.
                if not any(x in full_url.lower() for x in ['icon', 'logo', 'button']):
                    image_urls.append(full_url)
        
        return image_urls
    
    except Exception as e:
        print(f"Error processing {product_url}: {e}")
        return []

def main():
    # Create base output directory
    create_folder(OUTPUT_DIR)
    
    # Load JSON file
    with open(JSON_FILE_PATH, 'r') as f:
        products = json.load(f)
    
    print(f"Found {len(products)} products in the JSON file")
    
    # Create a file to store all image URLs
    all_urls_file = os.path.join(OUTPUT_DIR, 'all_image_urls.txt')
    
    with open(all_urls_file, 'w') as f:
        f.write("Product Model,Product Name,Image URL\n")
        
        # Process each product
        for i, product in enumerate(products):
            product_name = product.get('name', 'Unknown')
            product_model = product.get('model', 'Unknown')
            
            # Get product URL from images array
            product_urls = product.get('images', [])
            if not product_urls:
                print(f"No URL found for product: {product_name} ({product_model})")
                continue
            
            # Process each URL for the product
            for product_url in product_urls:
                # Skip empty URLs
                if not product_url:
                    continue
                
                # Extract image URLs from the product page
                image_urls = extract_image_urls(product_url, product_model, product_name)
                
                # Write image URLs to the file
                for url in image_urls:
                    f.write(f"{product_model},{product_name},{url}\n")
                
                print(f"Found {len(image_urls)} image URLs for {product_name} ({product_model})")
            
            print(f"Completed {i+1}/{len(products)}: {product_name}")
    
    print(f"All image URLs have been saved to {all_urls_file}")

if __name__ == "__main__":
    main()
