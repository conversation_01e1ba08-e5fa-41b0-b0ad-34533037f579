"use client";

import { useSearchParams } from "next/navigation";
import { Button } from "../../components/ui/button";
import { Separator } from "../../components/ui/separator";
import MainHOF from "../../layout/MainHOF";
import { Download, ShoppingBag, Truck } from "lucide-react";
import Link from "next/link";
import useApi from "@/hooks/useApi";
import { MAIN_URL, ORDERS } from "@/constant/urls";
import { useSession } from "next-auth/react";
import { Suspense, useEffect } from "react";

interface OrderConfirmationProps {
  orderNumber: string;
  items: Array<{
    name: string;
    price: number;
    quantity: number;
    image: string;
  }>;
  shippingMethod: string;
  total: number;
  estimatedDelivery: string;
}

const OrderConfirmationContent = () => {
  const params = useSearchParams();
  const orderId = params.get("order_id");
  const { read, data, loading }: any = useApi(MAIN_URL);
  const { status } = useSession();

  useEffect(() => {
    if (status === "authenticated" && orderId) {
      read(ORDERS + orderId + "/");
    }
  }, [status, orderId]);

  if (loading) {
    return (
      <MainHOF>
        <div className="container mx-auto px-4 py-8">
          <div className="max-w-3xl mx-auto">
            <div className="text-center mb-8">
              <h1 className="text-3xl font-bold mb-4">Loading...</h1>
            </div>
          </div>
        </div>
      </MainHOF>
    );
  }

  return (
    <MainHOF>
      <div className="container mx-auto px-4 py-8">
        <div className="max-w-3xl mx-auto">
          <div className="text-center mb-8">
            <h1 className="text-3xl font-bold mb-4">
              Thank You for Your Order!
            </h1>
            <p className="text-muted-foreground mb-2">Order #{data?.id}</p>
            <p className="text-muted-foreground">
              We'll send you an email confirmation with order details and
              tracking information.
            </p>
          </div>

          <div className="bg-card rounded-lg p-6 shadow-sm mb-6">
            <h2 className="text-xl font-semibold mb-4">Order Details</h2>
            <div className="space-y-4">
              {Array.isArray(data?.items) &&
                data?.items.map((item: any) => (
                  <div key={item.id} className="flex items-center gap-4">
                    <img
                      src={ item.product_image}
                      alt={item.product_name}
                      className="w-20 h-20 object-cover rounded"
                    />
                    <div className="flex-1">
                      <h3 className="font-medium">{item.product_name}</h3>
                      <p className="text-sm text-muted-foreground">
                        Quantity: {item.quantity}
                      </p>
                      <p className="text-sm">
                        ₹
                        {typeof item.total_price === "string" &&
                          Number(item.total_price).toFixed(2)}
                      </p>
                    </div>
                  </div>
                ))}
            </div>

            <Separator className="my-4" />

            <div className="space-y-2">
              <div className="flex justify-between">
                <span>Shipping Method</span>
                <span>{data?.shipping_method?.name}</span>
              </div>
              <div className="flex justify-between">
                <span>Estimated Delivery</span>
                <span>{data?.estimated_delivery_date}</span>
              </div>
              <div className="flex justify-between font-semibold">
                <span>Total</span>
                <span>
                  ₹
                  {typeof data?.total === "string" &&
                    Number(data?.total).toFixed(2)}
                </span>
              </div>
            </div>
          </div>

          <div className="flex flex-col sm:flex-row gap-4 justify-center">
            <Button className="flex items-center gap-2">
              <Truck className="h-4 w-4" />
              Track Order
            </Button>
            <Button variant="outline" className="flex items-center gap-2">
              <Download className="h-4 w-4" />
              Download Receipt
            </Button>
            <Link href="/shop">
              <Button variant="secondary" className="flex items-center gap-2">
                <ShoppingBag className="h-4 w-4" />
                Continue Shopping
              </Button>
            </Link>
          </div>
        </div>
      </div>
    </MainHOF>
  );
};

const OrderConfirmation = () => {
  return (
    <Suspense fallback={<div>Loading...</div>}>
      <OrderConfirmationContent />
    </Suspense>
  );
};
export default OrderConfirmation;
