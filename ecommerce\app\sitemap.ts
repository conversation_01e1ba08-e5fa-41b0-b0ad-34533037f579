import { MetadataRoute } from 'next';
import axios from 'axios';

/**
 * Dynamic sitemap generation for Next.js
 * This function will run at build time in production or during development
 * It follows Google's guidelines for sitemap structure and content
 */
export default async function sitemap(): Promise<MetadataRoute.Sitemap> {
  const baseUrl = process.env.NEXT_PUBLIC_SITE_URL || 'http://localhost:3000';
  const apiUrl = process.env.NEXT_PUBLIC_API_URL || 'http://localhost:8000';

  const currentDate = new Date().toISOString().split('T')[0];

  // Define your static routes here - include all crawlable and indexable pages
  const staticRoutes = [
    // Core pages - highest priority
    {
      url: '/',
      lastModified: currentDate,
      changeFrequency: 'daily',
      priority: 1,
    },
    {
      url: '/shop',
      lastModified: currentDate,
      changeFrequency: 'daily',
      priority: 0.9,
    },
    // Informational pages - medium priority
    {
      url: '/contact-us',
      lastModified: currentDate,
      changeFrequency: 'monthly',
      priority: 0.7,
    },
    {
      url: '/about',
      lastModified: currentDate,
      changeFrequency: 'monthly',
      priority: 0.7,
    },
    // Policy pages - lower priority
    {
      url: '/privacy-policy',
      lastModified: currentDate,
      changeFrequency: 'yearly',
      priority: 0.5,
    },
    {
      url: '/terms-and-conditions',
      lastModified: currentDate,
      changeFrequency: 'yearly',
      priority: 0.5,
    },
    {
      url: '/shipping-policy',
      lastModified: currentDate,
      changeFrequency: 'yearly',
      priority: 0.5,
    },
    {
      url: '/return-policy',
      lastModified: currentDate,
      changeFrequency: 'yearly',
      priority: 0.5,
    },
    {
      url: '/refund-policy',
      lastModified: currentDate,
      changeFrequency: 'yearly',
      priority: 0.5,
    },
  ];

  // Fetch dynamic product routes
  let productRoutes: any[] = [];
  let categoryRoutes: any[] = [];
  // Removed brandRoutes as requested

  try {
    // Fetch products
    console.log('Fetching products for sitemap generation...');
    const productsResponse = await axios.get(`${apiUrl}/api/v1/products/`);
    if (productsResponse.data && productsResponse.data.results) {
      productRoutes = productsResponse.data.results.map((product: any) => ({
        url: `/product/${product.slug}`,
        lastModified: product.updated_at || currentDate,
        changeFrequency: 'weekly',
        priority: 0.8,
      }));
      console.log(`Added ${productRoutes.length} product routes to sitemap`);
    }

    // Fetch categories
    console.log('Fetching categories for sitemap generation...');
    const categoriesResponse = await axios.get(`${apiUrl}/api/v1/products/categories/`);
    if (categoriesResponse.data && categoriesResponse.data.results) {
      categoryRoutes = categoriesResponse.data.results.map((category: any) => ({
        url: `/shop/category/${category.slug}`,
        lastModified: currentDate,
        changeFrequency: 'weekly',
        priority: 0.7,
      }));
      console.log(`Added ${categoryRoutes.length} category routes to sitemap`);
    }

    // Brand routes have been removed as requested
  } catch (error) {
    console.error('Error fetching dynamic routes for sitemap:', error);
    console.log('Continuing with static routes only');
    // Continue with static routes if API fails
  }

  // Combine all routes (without brandRoutes)
  const allRoutes = [...staticRoutes, ...categoryRoutes, ...productRoutes];
  console.log(`Total routes in sitemap: ${allRoutes.length}`);

  // Filter out any routes that should not be indexed (like auth pages, etc.)
  const filteredRoutes = allRoutes.filter(route => {
    const url = route.url.toLowerCase();
    // Exclude routes that shouldn't be indexed
    return !url.includes('/api/') &&
           !url.includes('/admin/') &&
           !url.includes('/auth/') &&
           !url.includes('/checkout/') &&
           !url.includes('/account/') &&
           !url.includes('/cart');
  });

  return filteredRoutes.map((route) => {
    // Ensure the URL is absolute (Google requires absolute URLs)
    const url = route.url.startsWith('http') ? route.url : `${baseUrl}${route.url}`;

    return {
      url: url,
      lastModified: route.lastModified,
      changeFrequency: route.changeFrequency as 'always' | 'hourly' | 'daily' | 'weekly' | 'monthly' | 'yearly' | 'never',
      priority: route.priority,
    };
  });
}
