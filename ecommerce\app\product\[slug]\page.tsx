"use client";
import { useEffect, useState } from "react";
import { ProductInfo } from "../../../components/product/ProductInfo";
import { RelatedProducts } from "../../../components/product/RelatedProducts";
import useApi from "../../../hooks/useApi";
import { MAIN_URL, PRODUCTS } from "../../../constant/urls";
import { useParams } from "next/navigation";
import MainHOF from "../../../layout/MainHOF";
import SingleProductLoading from "@/components/ui/loading/SingleProductLoading";
import ImageCarousel from "@/components/product/ImageCarousel";
import { ProductMetadata } from "./ProductMetadata";

const ProductDetail = () => {
  const { slug } = useParams();
  const [selectedColor, setSelectedColor] = useState("");
  const [selectedSize, setSelectedSize] = useState("");
  const [quantity, setQuantity] = useState(1);
  const { data, loading, read } = useApi(MAIN_URL || "");

  useEffect(() => {
    console.log("Fetching product details for slug:", slug);
    read(PRODUCTS + slug);
  }, [slug]);

  // Log the product data when it changes
  useEffect(() => {
    if (data) {
      console.log("Product data loaded:", data);
    }
  }, [data]);
  // Mock product data - in a real app, this would come from an API
  const product: any = data ?? undefined;

  if (loading) {
    return (
      <MainHOF>
        <div className="container mx-auto px-4 py-8">
          <SingleProductLoading />
        </div>
      </MainHOF>
    );
  }

  return (
    <MainHOF>
      {product && <ProductMetadata product={product} />}
      <div className="container mx-auto px-4 py-4">
        {/* Mobile-first responsive layout */}
        <div className="flex flex-col lg:grid lg:grid-cols-2 gap-4 sm:gap-6 lg:gap-8 lg:items-start">
          {product && (
            <>
              {/* Image carousel - responsive container */}
              <div className="w-full order-1 lg:order-1">
                <div className="w-full p-3 sm:p-4 border rounded-md border-slate-200 flex items-center justify-center lg:sticky lg:top-4">
                  {Array.isArray(product.images) && (
                    <ImageCarousel
                      productImage={product.images}
                      brand={product.brand}
                    />
                  )}
                </div>
              </div>

              {/* Product info - responsive container */}
              <div className="w-full order-2 lg:order-2 mt-4 lg:mt-0">
                <ProductInfo
                  product={product}
                  selectedColor={selectedColor}
                  selectedSize={selectedSize}
                  quantity={quantity}
                  onColorChange={setSelectedColor}
                  onSizeChange={setSelectedSize}
                  onQuantityChange={setQuantity}
                />
              </div>
            </>
          )}
        </div>

        {/* Related products with proper spacing */}
        <div className="mt-8 lg:mt-12">
          <RelatedProducts category_slug={product?.category?.slug || ""} />
        </div>
      </div>
    </MainHOF>
  );
};

export default ProductDetail;
