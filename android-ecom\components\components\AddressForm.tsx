import { View, Text, TextInput, TouchableOpacity, StyleSheet, ScrollView } from 'react-native';
import { useState } from 'react';
import { MaterialIcons } from '@expo/vector-icons';

interface AddressFormProps {
  initialData?: {
    street_address: string;
    apartment: string;
    city: string;
    state: string;
    postal_code: string;
    order_user_phone: string;
    order_user_email: string;
    is_default: boolean;
    address_type: string;
  };
  onSubmit: (data: any) => void;
  onCancel: () => void;
}

export default function AddressForm({ initialData, onSubmit, onCancel }: AddressFormProps) {
  const [formData, setFormData] = useState({
    street_address: initialData?.street_address || '',
    apartment: initialData?.apartment || '',
    city: initialData?.city || '',
    state: initialData?.state || '',
    postal_code: initialData?.postal_code || '',
    order_user_phone: initialData?.order_user_phone || '',
    order_user_email: initialData?.order_user_email || '',
    is_default: initialData?.is_default || false,
    address_type: initialData?.address_type || 'BILLING',
  });

  const [errors, setErrors] = useState<any>({});

  const validate = () => {
    const newErrors: any = {};
    if (!formData.street_address) newErrors.street_address = 'Street address is required';
    if (!formData.apartment) newErrors.apartment = 'Apartment/Suite is required';
    if (!formData.city) newErrors.city = 'City is required';
    if (!formData.state) newErrors.state = 'State is required';
    if (!formData.postal_code) newErrors.postal_code = 'Postal code is required';
    if (!formData.order_user_phone) newErrors.order_user_phone = 'Phone number is required';
    if (!formData.order_user_email) newErrors.order_user_email = 'Email is required';
    
    setErrors(newErrors);
    return Object.keys(newErrors).length === 0;
  };

  const handleSubmit = () => {
    if (validate()) {
      onSubmit(formData);
    }
  };

  const handleAddressTypeChange = (type: string) => {
    setFormData({ ...formData, address_type: type });
  };

  return (
    <ScrollView style={styles.container}>
      <View style={styles.form}>
        <View style={styles.addressTypeContainer}>
          <TouchableOpacity 
            style={[
              styles.addressTypeButton, 
              formData.address_type === 'BILLING' && styles.addressTypeSelected
            ]}
            onPress={() => handleAddressTypeChange('BILLING')}
          >
            <Text style={[
              styles.addressTypeText,
              formData.address_type === 'BILLING' && styles.addressTypeTextSelected
            ]}>Billing</Text>
          </TouchableOpacity>
          <TouchableOpacity 
            style={[
              styles.addressTypeButton, 
              formData.address_type === 'SHIPPING' && styles.addressTypeSelected
            ]}
            onPress={() => handleAddressTypeChange('SHIPPING')}
          >
            <Text style={[
              styles.addressTypeText,
              formData.address_type === 'SHIPPING' && styles.addressTypeTextSelected
            ]}>Shipping</Text>
          </TouchableOpacity>
        </View>

        <View style={styles.inputGroup}>
          <Text style={styles.label}>Street Address</Text>
          <TextInput
            style={[styles.input, errors.street_address && styles.inputError]}
            placeholder="Enter street address"
            value={formData.street_address}
            onChangeText={(text) => setFormData({ ...formData, street_address: text })}
          />
          {errors.street_address && <Text style={styles.errorText}>{errors.street_address}</Text>}
        </View>

        <View style={styles.inputGroup}>
          <Text style={styles.label}>Apartment/Suite</Text>
          <TextInput
            style={[styles.input, errors.apartment && styles.inputError]}
            placeholder="Enter apartment or suite number"
            value={formData.apartment}
            onChangeText={(text) => setFormData({ ...formData, apartment: text })}
          />
          {errors.apartment && <Text style={styles.errorText}>{errors.apartment}</Text>}
        </View>

        <View style={styles.row}>
          <View style={[styles.inputGroup, { flex: 1, marginRight: 8 }]}>
            <Text style={styles.label}>City</Text>
            <TextInput
              style={[styles.input, errors.city && styles.inputError]}
              placeholder="Enter city"
              value={formData.city}
              onChangeText={(text) => setFormData({ ...formData, city: text })}
            />
            {errors.city && <Text style={styles.errorText}>{errors.city}</Text>}
          </View>

          <View style={[styles.inputGroup, { flex: 1, marginLeft: 8 }]}>
            <Text style={styles.label}>State</Text>
            <TextInput
              style={[styles.input, errors.state && styles.inputError]}
              placeholder="Enter state"
              value={formData.state}
              onChangeText={(text) => setFormData({ ...formData, state: text })}
            />
            {errors.state && <Text style={styles.errorText}>{errors.state}</Text>}
          </View>
        </View>

        <View style={styles.inputGroup}>
          <Text style={styles.label}>Postal Code</Text>
          <TextInput
            style={[styles.input, errors.postal_code && styles.inputError]}
            placeholder="Enter postal code"
            value={formData.postal_code}
            onChangeText={(text) => setFormData({ ...formData, postal_code: text })}
            keyboardType="numeric"
          />
          {errors.postal_code && <Text style={styles.errorText}>{errors.postal_code}</Text>}
        </View>

        <View style={styles.inputGroup}>
          <Text style={styles.label}>Phone Number</Text>
          <TextInput
            style={[styles.input, errors.order_user_phone && styles.inputError]}
            placeholder="Enter phone number"
            value={formData.order_user_phone}
            onChangeText={(text) => setFormData({ ...formData, order_user_phone: text })}
            keyboardType="phone-pad"
          />
          {errors.order_user_phone && <Text style={styles.errorText}>{errors.order_user_phone}</Text>}
        </View>

        <View style={styles.inputGroup}>
          <Text style={styles.label}>Email</Text>
          <TextInput
            style={[styles.input, errors.order_user_email && styles.inputError]}
            placeholder="Enter email address"
            value={formData.order_user_email}
            onChangeText={(text) => setFormData({ ...formData, order_user_email: text })}
            keyboardType="email-address"
            autoCapitalize="none"
          />
          {errors.order_user_email && <Text style={styles.errorText}>{errors.order_user_email}</Text>}
        </View>

        <TouchableOpacity
          style={styles.defaultContainer}
          onPress={() => setFormData({ ...formData, is_default: !formData.is_default })}
        >
          <MaterialIcons
            name={formData.is_default ? "check-box" : "check-box-outline-blank"}
            size={24}
            color="#2563EB"
          />
          <Text style={styles.defaultText}>Set as default address</Text>
        </TouchableOpacity>

        <View style={styles.buttonContainer}>
          <TouchableOpacity style={styles.cancelButton} onPress={onCancel}>
            <Text style={styles.cancelButtonText}>Cancel</Text>
          </TouchableOpacity>
          <TouchableOpacity style={styles.submitButton} onPress={handleSubmit}>
            <Text style={styles.submitButtonText}>Save Address</Text>
          </TouchableOpacity>
        </View>
      </View>
    </ScrollView>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#ffffff',
  },
  form: {
    padding: 16,
  },
  addressTypeContainer: {
    flexDirection: 'row',
    marginBottom: 20,
  },
  addressTypeButton: {
    flex: 1,
    padding: 12,
    borderWidth: 1,
    borderColor: '#E5E7EB',
    alignItems: 'center',
  },
  addressTypeSelected: {
    borderColor: '#2563EB',
    backgroundColor: '#EBF4FF',
  },
  addressTypeText: {
    fontSize: 16,
    color: '#374151',
  },
  addressTypeTextSelected: {
    fontWeight: '600',
    color: '#2563EB',
  },
  inputGroup: {
    marginBottom: 16,
  },
  label: {
    fontSize: 14,
    fontWeight: '500',
    marginBottom: 8,
    color: '#374151',
  },
  input: {
    borderWidth: 1,
    borderColor: '#E5E7EB',
    borderRadius: 8,
    padding: 12,
    fontSize: 16,
  },
  inputError: {
    borderColor: '#EF4444',
  },
  errorText: {
    color: '#EF4444',
    fontSize: 12,
    marginTop: 4,
  },
  row: {
    flexDirection: 'row',
    marginBottom: 16,
  },
  defaultContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    marginTop: 8,
    marginBottom: 24,
  },
  defaultText: {
    marginLeft: 8,
    fontSize: 16,
    color: '#374151',
  },
  buttonContainer: {
    flexDirection: 'row',
    justifyContent: 'space-between',
  },
  submitButton: {
    flex: 1,
    backgroundColor: '#2563EB',
    padding: 16,
    borderRadius: 8,
    alignItems: 'center',
    marginLeft: 8,
  },
  submitButtonText: {
    color: 'white',
    fontSize: 16,
    fontWeight: '600',
  },
  cancelButton: {
    flex: 1,
    backgroundColor: '#F3F4F6',
    padding: 16,
    borderRadius: 8,
    alignItems: 'center',
    marginRight: 8,
  },
  cancelButtonText: {
    color: '#374151',
    fontSize: 16,
    fontWeight: '600',
  },
});