"""
Management command to check database replica health and configuration.

This command helps administrators monitor the health of database replicas
and verify that the read/write splitting is working correctly.
"""

from django.core.management.base import BaseCommand, CommandError
from django.conf import settings
from django.db import connections
from backend.db_utils import <PERSON><PERSON>ealth<PERSON>he<PERSON>
from backend.db_router import DatabaseRouter
import time


class Command(BaseCommand):
    help = 'Check database replica health and configuration'

    def add_arguments(self, parser):
        parser.add_argument(
            '--detailed',
            action='store_true',
            help='Show detailed information about each database',
        )
        parser.add_argument(
            '--monitor',
            action='store_true',
            help='Continuously monitor replica health (Ctrl+C to stop)',
        )
        parser.add_argument(
            '--interval',
            type=int,
            default=30,
            help='Monitoring interval in seconds (default: 30)',
        )

    def handle(self, *args, **options):
        self.stdout.write(
            self.style.SUCCESS('Database Replica Health Check')
        )
        self.stdout.write('=' * 50)

        if options['monitor']:
            self.monitor_replicas(options['interval'], options['detailed'])
        else:
            self.check_replica_status(options['detailed'])

    def check_replica_status(self, detailed=False):
        """Check the current status of all configured databases"""
        
        # Check database configuration
        self.stdout.write('\n1. Database Configuration:')
        self.stdout.write('-' * 30)
        
        router = DatabaseRouter()
        
        self.stdout.write(f"Primary database: {router.write_database}")
        self.stdout.write(f"Read databases: {', '.join(router.read_databases)}")
        
        if len(router.read_databases) == 1 and router.read_databases[0] == 'default':
            self.stdout.write(
                self.style.WARNING('No read replicas configured - using primary for all operations')
            )
        else:
            self.stdout.write(
                self.style.SUCCESS(f'Read/write splitting enabled with {len(router.read_databases)} read database(s)')
            )

        # Check database health
        self.stdout.write('\n2. Database Health Status:')
        self.stdout.write('-' * 30)
        
        health_status = DatabaseHealthChecker.check_replica_health()
        
        for db_name, status in health_status.items():
            if status == 'healthy':
                self.stdout.write(
                    f"  {db_name}: {self.style.SUCCESS('✓ Healthy')}"
                )
            else:
                self.stdout.write(
                    f"  {db_name}: {self.style.ERROR('✗ ' + status)}"
                )

        # Show detailed information if requested
        if detailed:
            self.show_detailed_info()

        # Test read/write operations
        self.stdout.write('\n3. Read/Write Operation Test:')
        self.stdout.write('-' * 30)
        
        try:
            self.test_read_write_operations()
            self.stdout.write(
                self.style.SUCCESS('✓ Read/write operations working correctly')
            )
        except Exception as e:
            self.stdout.write(
                self.style.ERROR(f'✗ Read/write operation test failed: {str(e)}')
            )

    def show_detailed_info(self):
        """Show detailed information about each database"""
        self.stdout.write('\n   Detailed Database Information:')
        self.stdout.write('   ' + '-' * 35)
        
        for db_name, db_config in settings.DATABASES.items():
            self.stdout.write(f'\n   Database: {db_name}')
            self.stdout.write(f'     Engine: {db_config.get("ENGINE", "Not specified")}')
            self.stdout.write(f'     Host: {db_config.get("HOST", "Not specified")}')
            self.stdout.write(f'     Port: {db_config.get("PORT", "Not specified")}')
            self.stdout.write(f'     Name: {db_config.get("NAME", "Not specified")}')
            
            # Check connection pool settings if available
            if 'POOL_OPTIONS' in db_config:
                pool_opts = db_config['POOL_OPTIONS']
                self.stdout.write(f'     Pool Size: {pool_opts.get("POOL_SIZE", "Not set")}')
                self.stdout.write(f'     Max Overflow: {pool_opts.get("MAX_OVERFLOW", "Not set")}')

    def test_read_write_operations(self):
        """Test basic read and write operations"""
        from django.contrib.auth import get_user_model
        from backend.db_utils import ReplicaAwareQuerySet
        
        User = get_user_model()
        
        # Test read operation (should use replica if available)
        try:
            user_count = User.objects.count()
            self.stdout.write(f'     Read test: Found {user_count} users')
        except Exception as e:
            raise Exception(f'Read operation failed: {str(e)}')
        
        # Test optimized read operation
        try:
            from products.models import Product
            queryset = ReplicaAwareQuerySet.get_optimized_product_queryset(use_replica=True)
            product_count = queryset.count()
            self.stdout.write(f'     Optimized read test: Found {product_count} products')
        except Exception as e:
            raise Exception(f'Optimized read operation failed: {str(e)}')

    def monitor_replicas(self, interval, detailed):
        """Continuously monitor replica health"""
        self.stdout.write(
            self.style.SUCCESS(f'Starting continuous monitoring (interval: {interval}s)')
        )
        self.stdout.write('Press Ctrl+C to stop monitoring\n')
        
        try:
            while True:
                self.stdout.write(f'\n[{time.strftime("%Y-%m-%d %H:%M:%S")}] Checking replica health...')
                
                health_status = DatabaseHealthChecker.check_replica_health()
                
                all_healthy = True
                for db_name, status in health_status.items():
                    if status == 'healthy':
                        self.stdout.write(f'  {db_name}: ✓')
                    else:
                        self.stdout.write(f'  {db_name}: ✗ {status}')
                        all_healthy = False
                
                if all_healthy:
                    self.stdout.write(self.style.SUCCESS('All databases healthy'))
                else:
                    self.stdout.write(self.style.WARNING('Some databases have issues'))
                
                if detailed:
                    # Show connection counts if available
                    self.show_connection_info()
                
                time.sleep(interval)
                
        except KeyboardInterrupt:
            self.stdout.write('\nMonitoring stopped.')

    def show_connection_info(self):
        """Show connection information for each database"""
        self.stdout.write('  Connection info:')
        
        for db_name in settings.DATABASES.keys():
            try:
                connection = connections[db_name]
                # Try to get connection info (this varies by database backend)
                self.stdout.write(f'    {db_name}: Connected')
            except Exception as e:
                self.stdout.write(f'    {db_name}: Connection error - {str(e)}')

    def style_status(self, status):
        """Apply appropriate styling to status messages"""
        if 'healthy' in status.lower():
            return self.style.SUCCESS(status)
        elif 'warning' in status.lower():
            return self.style.WARNING(status)
        else:
            return self.style.ERROR(status)
