"""
Privacy compliance views for user data rights and consent management.
Implements Right to Access, Right to Erasure, Right to Portability, and Consent Management.
"""

import json
import os
import csv
import io
from datetime import datetime
from django.http import HttpResponse, JsonResponse
from django.shortcuts import get_object_or_404
from django.contrib.auth import get_user_model
from django.utils import timezone
from rest_framework import status, permissions
from rest_framework.views import APIView
from rest_framework.response import Response
from rest_framework.throttling import UserRateThrottle
from .models import UserConsent, DataDeletionRequest
from .serializers import UserSerializer, AddressSerializer, WishlistSerializer
from orders.serializers import OrderSerializer
from backend.security_monitoring import get_client_ip, get_user_agent, log_security_event
from backend.encryption_utils import anonymize_email, anonymize_phone, anonymize_name
from .privacy_performance_monitor import monitor_privacy_performance
from .utils import send_deletion_request_emails, send_deletion_status_update_email
import logging

logger = logging.getLogger(__name__)
User = get_user_model()

# Simple logging function for data processing activities
def log_data_processing(user, activity_type, data_type, description, legal_basis, request):
    """Simple logging function for data processing activities"""
    logger.info(f"Data Processing: {activity_type} - {data_type} - User: {user.email if user else 'Anonymous'} - {description}")

    # Log as security event
    log_security_event('DATA_ACCESS', request, user, {
        'activity_type': activity_type,
        'data_type': data_type,
        'description': description,
        'legal_basis': legal_basis
    })

class ConsentManagementView(APIView):
    """Manage user consent for various data processing activities"""
    
    permission_classes = [permissions.IsAuthenticated]
    throttle_classes = [UserRateThrottle]
    
    @monitor_privacy_performance('consent_fetch')
    def get(self, request):
        """Get current consent status for all consent types"""
        user = request.user
        consents = {}
        
        for consent_type, description in UserConsent.CONSENT_TYPES:
            try:
                consent = UserConsent.objects.get(user=user, consent_type=consent_type)
                consents[consent_type] = {
                    'granted': consent.granted,
                    'granted_at': consent.granted_at,
                    'withdrawn_at': consent.withdrawn_at,
                    'description': description
                }
            except UserConsent.DoesNotExist:
                consents[consent_type] = {
                    'granted': False,
                    'granted_at': None,
                    'withdrawn_at': None,
                    'description': description
                }
        
        # Log data access
        log_data_processing(
            user, 'READ', 'consent_data', 'User accessed consent settings',
            'legitimate_interest', request
        )
        
        return Response(consents)
    
    @monitor_privacy_performance('consent_update')
    def post(self, request):
        """Update consent preferences"""
        user = request.user
        consent_updates = request.data.get('consents', {})
        
        if not consent_updates:
            return Response(
                {'error': 'No consent updates provided'},
                status=status.HTTP_400_BAD_REQUEST
            )
        
        ip_address = get_client_ip(request)
        user_agent = get_user_agent(request)
        
        updated_consents = {}
        
        for consent_type, granted in consent_updates.items():
            if consent_type not in dict(UserConsent.CONSENT_TYPES):
                continue
            
            consent, created = UserConsent.objects.get_or_create(
                user=user,
                consent_type=consent_type,
                defaults={
                    'ip_address': ip_address,
                    'user_agent': user_agent
                }
            )
            
            if granted and not consent.granted:
                consent.grant_consent(ip_address, user_agent)
                action = 'granted'
            elif not granted and consent.granted:
                consent.withdraw_consent(ip_address, user_agent)
                action = 'withdrawn'
            else:
                action = 'unchanged'
            
            updated_consents[consent_type] = {
                'granted': consent.granted,
                'action': action
            }
            
            # Log consent change
            log_data_processing(
                user, 'UPDATE', 'consent_data', 
                f'Consent {action} for {consent_type}',
                'consent', request
            )
        
        return Response({
            'message': 'Consent preferences updated',
            'consents': updated_consents
        })

class DataExportView(APIView):
    """Handle user data export requests (Right to Portability)"""

    permission_classes = [permissions.IsAuthenticated]
    throttle_classes = [UserRateThrottle]

    @monitor_privacy_performance('csv_export')
    def get(self, request):
        """Get user data for export in CSV format"""
        user = request.user

        # Log data access
        log_data_processing(
            user, 'EXPORT', 'all_user_data',
            'User requested data export',
            'consent', request
        )

        # Import Order model to get user's orders
        from orders.models import Order

        # Optimize queries with select_related and prefetch_related for better performance
        orders_queryset = Order.objects.filter(user=user).select_related(
            'shipping_address', 'billing_address', 'shipping_method'
        ).prefetch_related('items__product', 'items__variant')

        addresses_queryset = user.addresses.all()
        wishlist_queryset = user.wishlist.select_related('product').all()

        # Collect all user data with optimized queries
        user_data = {
            'personal_info': UserSerializer(user).data,
            'addresses': AddressSerializer(addresses_queryset, many=True).data,
            'orders': OrderSerializer(orders_queryset, many=True).data,
            'wishlist': WishlistSerializer(wishlist_queryset, many=True).data,
            'consents': self._get_user_consents(user),
            'export_info': {
                'exported_at': timezone.now().isoformat(),
                'format': 'CSV',
                'total_orders': orders_queryset.count(),
                'total_addresses': addresses_queryset.count(),
                'total_wishlist_items': wishlist_queryset.count()
            }
        }

        # Generate CSV content
        csv_content = self._generate_csv_export(user_data)

        # Create HTTP response with CSV content
        response = HttpResponse(csv_content, content_type='text/csv; charset=utf-8')
        timestamp = timezone.now().strftime('%Y%m%d_%H%M%S')
        response['Content-Disposition'] = f'attachment; filename="user_data_export_{user.id}_{timestamp}.csv"'
        response['Content-Length'] = len(csv_content.encode('utf-8'))

        return response

    def _get_user_consents(self, user):
        """Get user's consent history"""
        consents = UserConsent.objects.filter(user=user)
        consent_data = []

        for consent in consents:
            consent_data.append({
                'type': consent.consent_type,
                'granted': consent.granted,
                'granted_at': consent.granted_at,
                'withdrawn_at': consent.withdrawn_at,
                'version': consent.consent_version
            })

        return consent_data

    def _generate_csv_export(self, user_data):
        """Generate CSV content from user data with memory optimization"""
        output = io.StringIO()

        # Export Info Section with statistics
        export_info = user_data['export_info']
        output.write("=== USER DATA EXPORT ===\n")
        output.write(f"Exported At: {export_info['exported_at']}\n")
        output.write(f"Format: {export_info['format']}\n")
        output.write(f"Total Orders: {export_info.get('total_orders', 0)}\n")
        output.write(f"Total Addresses: {export_info.get('total_addresses', 0)}\n")
        output.write(f"Total Wishlist Items: {export_info.get('total_wishlist_items', 0)}\n\n")

        # Personal Information Section
        output.write("=== PERSONAL INFORMATION ===\n")
        personal_info = user_data['personal_info']
        for key, value in personal_info.items():
            output.write(f"{key.replace('_', ' ').title()}: {value or 'N/A'}\n")
        output.write("\n")

        # Addresses Section
        output.write("=== ADDRESSES ===\n")
        if user_data['addresses']:
            # Write CSV header for addresses
            address_fields = ['id', 'street_address', 'city', 'state', 'postal_code', 'country', 'is_default']
            writer = csv.DictWriter(output, fieldnames=address_fields)
            writer.writeheader()

            for address in user_data['addresses']:
                # Filter only the fields we want to export
                filtered_address = {field: address.get(field, '') for field in address_fields}
                writer.writerow(filtered_address)
        else:
            output.write("No addresses found.\n")
        output.write("\n")

        # Wishlist Section
        output.write("=== WISHLIST ===\n")
        if user_data['wishlist']:
            wishlist_fields = ['id', 'product_name', 'product_price', 'added_at']
            writer = csv.DictWriter(output, fieldnames=wishlist_fields)
            writer.writeheader()

            for item in user_data['wishlist']:
                wishlist_row = {
                    'id': item.get('id', ''),
                    'product_name': item.get('product', {}).get('name', '') if item.get('product') else '',
                    'product_price': item.get('product', {}).get('price', '') if item.get('product') else '',
                    'added_at': item.get('added_at', '')
                }
                writer.writerow(wishlist_row)
        else:
            output.write("No wishlist items found.\n")
        output.write("\n")

        # Orders Section
        output.write("=== ORDERS ===\n")
        if user_data['orders']:
            order_fields = ['id', 'status', 'total', 'created_at', 'tracking_number', 'estimated_delivery_date']
            writer = csv.DictWriter(output, fieldnames=order_fields)
            writer.writeheader()

            for order in user_data['orders']:
                filtered_order = {field: order.get(field, '') for field in order_fields}
                writer.writerow(filtered_order)
        else:
            output.write("No orders found.\n")
        output.write("\n")

        # Order Items Section
        output.write("=== ORDER ITEMS ===\n")
        order_items_found = False
        if user_data['orders']:
            for order in user_data['orders']:
                if order.get('items') and len(order['items']) > 0:
                    if not order_items_found:
                        # Write header only once
                        item_fields = ['order_id', 'product_name', 'variant_name', 'quantity', 'unit_price', 'total_price']
                        writer = csv.DictWriter(output, fieldnames=item_fields)
                        writer.writeheader()
                        order_items_found = True

                    for item in order['items']:
                        item_row = {
                            'order_id': order.get('id', ''),
                            'product_name': item.get('product_name', ''),
                            'variant_name': item.get('variant_name', ''),
                            'quantity': item.get('quantity', ''),
                            'unit_price': item.get('unit_price', ''),
                            'total_price': item.get('total_price', '')
                        }
                        writer.writerow(item_row)

        if not order_items_found:
            output.write("No order items found.\n")
        output.write("\n")

        # Consents Section
        output.write("=== CONSENT HISTORY ===\n")
        if user_data['consents']:
            consent_fields = ['type', 'granted', 'granted_at', 'withdrawn_at', 'version']
            writer = csv.DictWriter(output, fieldnames=consent_fields)
            writer.writeheader()

            for consent in user_data['consents']:
                filtered_consent = {field: consent.get(field, '') for field in consent_fields}
                writer.writerow(filtered_consent)
        else:
            output.write("No consent history found.\n")

        return output.getvalue()

class DataDeletionView(APIView):
    """Handle user data deletion requests (Right to Erasure)"""
    
    permission_classes = [permissions.IsAuthenticated]
    throttle_classes = [UserRateThrottle]
    
    def get(self, request):
        """Get user's deletion requests"""
        user = request.user
        deletion_requests = DataDeletionRequest.objects.filter(user=user)
        
        requests_data = []
        for req in deletion_requests:
            requests_data.append({
                'id': req.id,
                'request_date': req.request_date,
                'status': req.status,
                'reason': req.reason,
                'processed_at': req.processed_at,
                'processing_notes': req.processing_notes
            })
        
        return Response({'deletion_requests': requests_data})
    
    @monitor_privacy_performance('data_deletion')
    def post(self, request):
        """Create a data deletion request"""
        user = request.user
        reason = request.data.get('reason', '')
        
        # Check for pending requests
        pending_requests = DataDeletionRequest.objects.filter(
            user=user,
            status__in=['PENDING', 'IN_PROGRESS']
        )
        
        if pending_requests.exists():
            return Response(
                {'error': 'You already have a pending deletion request'},
                status=status.HTTP_400_BAD_REQUEST
            )
        
        # Create deletion request
        deletion_request = DataDeletionRequest.objects.create(
            user=user,
            reason=reason,
            ip_address=get_client_ip(request),
            user_agent=get_user_agent(request)
        )
        
        # Log the request
        log_data_processing(
            user, 'DELETE', 'all_user_data', 
            f'Data deletion requested. Reason: {reason}',
            'consent', request
        )
        
        log_security_event('DATA_ACCESS', request, user, {
            'action': 'deletion_request',
            'reason': reason
        })

        # Send email notifications
        try:
            email_sent = send_deletion_request_emails(deletion_request)
            if email_sent:
                logger.info(f"Deletion request emails sent successfully for user {user.email}")
            else:
                logger.warning(f"Failed to send deletion request emails for user {user.email}")
        except Exception as e:
            logger.error(f"Error sending deletion request emails for user {user.email}: {str(e)}")

        return Response({
            'message': 'Deletion request created successfully',
            'request_id': deletion_request.id,
            'status': deletion_request.status,
            'note': 'Your request will be processed within 30 days as per data protection regulations. You will receive email updates about the status of your request.'
        })
    
    def delete(self, request):
        """Immediately delete user account (for testing/development)"""
        user = request.user
        
        # In production, this should go through approval process
        # For now, we'll anonymize data instead of hard delete
        
        # Anonymize user data
        user.email = anonymize_email(user.email)
        user.first_name = anonymize_name(user.first_name)
        user.last_name = anonymize_name(user.last_name)
        user.phone_number = anonymize_phone(user.phone_number) if hasattr(user, 'phone_number') else None
        user.is_active = False
        user.save()
        
        # Anonymize addresses
        for address in user.addresses.all():
            address.street_address = "Anonymized Address"
            address.city = "Anonymized"
            address.state = "Anonymized"
            address.postal_code = "000000"
            address.save()
        
        # Log the deletion
        log_data_processing(
            user, 'ANONYMIZE', 'all_user_data', 
            'User account anonymized per deletion request',
            'consent', request
        )
        
        return Response({
            'message': 'Account has been anonymized and deactivated'
        })

class PrivacyPolicyView(APIView):
    """Handle privacy policy information"""

    permission_classes = [permissions.AllowAny]

    def get(self, request):
        """Get privacy policy information"""
        return Response({
            'version': '1.0',
            'content': 'Privacy policy content will be available soon.',
            'effective_date': timezone.now().isoformat(),
            'message': 'Privacy policy management is being implemented.'
        })

    def post(self, request):
        """Accept privacy policy"""
        if not request.user.is_authenticated:
            return Response(
                {'error': 'Authentication required'},
                status=status.HTTP_401_UNAUTHORIZED
            )

        user = request.user

        # Log the acceptance
        log_data_processing(
            user, 'CREATE', 'privacy_policy_acceptance',
            'Privacy policy accepted',
            'consent', request
        )

        return Response({
            'message': 'Privacy policy acceptance recorded',
            'version': '1.0',
            'accepted_at': timezone.now().isoformat()
        })
