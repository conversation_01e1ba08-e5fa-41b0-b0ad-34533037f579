/**
 * Error Boundary for Shipping Components
 * Provides graceful fallback when shipping components fail
 */

import React, { Component, ReactNode } from 'react';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Alert, AlertDescription } from '@/components/ui/alert';
import { AlertTriangle, RefreshCw, Truck } from 'lucide-react';

interface Props {
  children: ReactNode;
  fallback?: ReactNode;
  onError?: (error: Error, errorInfo: React.ErrorInfo) => void;
}

interface State {
  hasError: boolean;
  error?: Error;
  errorInfo?: React.ErrorInfo;
}

export class ShippingErrorBoundary extends Component<Props, State> {
  constructor(props: Props) {
    super(props);
    this.state = { hasError: false };
  }

  static getDerivedStateFromError(error: Error): State {
    return { hasError: true, error };
  }

  componentDidCatch(error: Error, errorInfo: React.ErrorInfo) {
    console.error('Shipping component error:', error, errorInfo);
    
    this.setState({
      error,
      errorInfo
    });

    // Call optional error handler
    if (this.props.onError) {
      this.props.onError(error, errorInfo);
    }
  }

  handleRetry = () => {
    this.setState({ hasError: false, error: undefined, errorInfo: undefined });
  };

  render() {
    if (this.state.hasError) {
      // Custom fallback UI
      if (this.props.fallback) {
        return this.props.fallback;
      }

      // Default fallback UI
      return (
        <Card className="w-full border-orange-200 bg-orange-50">
          <CardHeader>
            <CardTitle className="flex items-center text-orange-700">
              <AlertTriangle className="h-5 w-5 mr-2" />
              Shipping Service Temporarily Unavailable
            </CardTitle>
          </CardHeader>
          
          <CardContent className="space-y-4">
            <Alert variant="default" className="border-orange-200 bg-orange-50">
              <AlertTriangle className="h-4 w-4" />
              <AlertDescription>
                We're experiencing technical difficulties with our shipping calculator. 
                You can still proceed with standard shipping options.
              </AlertDescription>
            </Alert>

            <div className="flex items-center justify-between">
              <div className="text-sm text-orange-700">
                <p className="font-medium">Standard shipping options available:</p>
                <ul className="mt-2 space-y-1">
                  <li>• Standard Shipping - ₹50 (5-7 business days)</li>
                  <li>• Express Shipping - ₹159 (2-3 business days)</li>
                </ul>
              </div>
              
              <Button
                onClick={this.handleRetry}
                variant="outline"
                className="border-orange-300 text-orange-700 hover:bg-orange-100"
              >
                <RefreshCw className="h-4 w-4 mr-2" />
                Try Again
              </Button>
            </div>

            {/* Development error details */}
            {process.env.NODE_ENV === 'development' && this.state.error && (
              <details className="mt-4 p-3 bg-red-50 border border-red-200 rounded text-xs">
                <summary className="cursor-pointer font-medium text-red-700">
                  Error Details (Development Only)
                </summary>
                <pre className="mt-2 text-red-600 whitespace-pre-wrap">
                  {this.state.error.toString()}
                  {this.state.errorInfo?.componentStack}
                </pre>
              </details>
            )}
          </CardContent>
        </Card>
      );
    }

    return this.props.children;
  }
}

// HOC for wrapping components with error boundary
export const withShippingErrorBoundary = <P extends object>(
  Component: React.ComponentType<P>,
  fallback?: ReactNode
) => {
  const WrappedComponent = (props: P) => (
    <ShippingErrorBoundary fallback={fallback}>
      <Component {...props} />
    </ShippingErrorBoundary>
  );

  WrappedComponent.displayName = `withShippingErrorBoundary(${Component.displayName || Component.name})`;
  
  return WrappedComponent;
};

// Fallback component for shipping rate calculator
export const ShippingRateCalculatorFallback: React.FC = () => (
  <Card className="w-full">
    <CardHeader>
      <CardTitle className="flex items-center">
        <Truck className="h-5 w-5 mr-2" />
        Standard Shipping Options
      </CardTitle>
    </CardHeader>
    
    <CardContent>
      <Alert>
        <AlertTriangle className="h-4 w-4" />
        <AlertDescription>
          Live shipping rates are temporarily unavailable. Please choose from our standard options below.
        </AlertDescription>
      </Alert>
      
      <div className="mt-4 space-y-3">
        <div className="p-3 border rounded-lg">
          <div className="flex justify-between items-center">
            <div>
              <h4 className="font-medium">Standard Shipping</h4>
              <p className="text-sm text-gray-600">5-7 business days</p>
            </div>
            <span className="font-medium">₹50</span>
          </div>
        </div>
        
        <div className="p-3 border rounded-lg">
          <div className="flex justify-between items-center">
            <div>
              <h4 className="font-medium">Express Shipping</h4>
              <p className="text-sm text-gray-600">2-3 business days</p>
            </div>
            <span className="font-medium">₹159</span>
          </div>
        </div>
      </div>
    </CardContent>
  </Card>
);

export default ShippingErrorBoundary;
