#!/usr/bin/env python3
"""
Update Haier Products JSON with actual high-quality image paths
Maps products to available images and removes products without images
"""

import json
import os
from pathlib import Path
from typing import Dict, List, Set
import logging

# Setup logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

class ProductImageUpdater:
    def __init__(self, json_file: str = "haier_products_updated.json", images_dir: str = "haier_images"):
        self.json_file = Path(json_file)
        self.images_dir = Path(images_dir)
        self.products = []
        self.image_folders = {}
        self.product_mappings = {}
        
    def load_products(self) -> None:
        """Load products from JSON file"""
        try:
            with open(self.json_file, 'r', encoding='utf-8') as f:
                self.products = json.load(f)
            logger.info(f"Loaded {len(self.products)} products from {self.json_file}")
        except Exception as e:
            logger.error(f"Error loading products: {e}")
            self.products = []
    
    def scan_image_folders(self) -> None:
        """Scan the images directory and catalog available images"""
        if not self.images_dir.exists():
            logger.error(f"Images directory {self.images_dir} does not exist")
            return
        
        for folder in self.images_dir.iterdir():
            if folder.is_dir():
                images = []
                for img_file in folder.iterdir():
                    if img_file.is_file() and img_file.suffix.lower() in ['.jpg', '.jpeg', '.png', '.webp']:
                        # Use forward slashes for cross-platform compatibility
                        relative_path = f"haier_images/{folder.name}/{img_file.name}"
                        images.append(relative_path)
                
                if images:
                    self.image_folders[folder.name] = sorted(images)
                    logger.info(f"Found {len(images)} images in {folder.name}")
        
        logger.info(f"Total image folders found: {len(self.image_folders)}")
    
    def create_product_mappings(self) -> None:
        """Create mappings between products and image folders"""
        # Define mapping patterns based on product names and model numbers
        mappings = {
            # Microwave mappings
            "HIM-M28SA1": "built_in_microwave_28_litres_him_m28sa1",
            "HCO-T10U1": "76_litres_smart_oven_hco_t10u1",
            "HIO-M59CF": "built_in_oven_59_litres_hio_m59cf",
            
            # Hob mappings
            "HIC-653FCF-O": "hybrid_hob_3_burner_65_cm_hic_653fcf_o",
            "HIC-754FCF-O": "hybrid_hob_4_burner_75_cm_hic_754fcf_o",
            "HIC-904FCF-O": "hybrid_hob_4_burner_90_cm_hic_904fcf_o",
            "HIC-654FCF-O": "hybrid_hob_4_burner_65_cm_hic_654fcf_o",
            "HIC-905FCF-O": "hybrid_hob_5_burner_90_cm_hic_905fcf_o",
            "HIC-603FCF-O": "hybrid_hob_3_burner_60_cm_hic_603fcf_o",
            "HIC-B3CGAFH": "built_in_smart_hob_3_burner_90_cm_hic_b3cgafh",
            
            # Hood mappings
            "HIH-G90HM-G": "glass_shaped_hood_90_cm_hih_g90hm_g",
            "HIH-G60HM-G": "glass_shaped_hood_60_cm_hih_g60hm_g",
            "HIH-V60HM-C": "vertical_hood_60_cm_hih_v60hm_c",
            "HIH-V75HM-C": "vertical_hood_75_cm_hih_v75hm_c",
            "HIH-V90HM-C": "vertical_hood_90_cm_hih_v90hm_c",
            "HIH-T60HM-V": "t_shaped_hood_60_cm_hih_t60hm_v",
            "HIH-T90HM-V": "t_shaped_hood_90_cm_hih_t90hm_v",
            "HIH-T895": "t_shaped_smart_hood_90_cm_hih_t895",
            
            # Sterilizer mapping
            "HIS-X76SWA1": "65_litres_smart_sterilizer_25_litres_dish_warmer_his_x76swa1"
        }
        
        self.product_mappings = mappings
        logger.info(f"Created {len(mappings)} product mappings")
    
    def extract_model_from_name(self, product_name: str) -> str:
        """Extract model number from product name"""
        # Common patterns to extract model numbers
        import re
        
        # Look for patterns like HIM-M28SA1, HCO-T10U1, etc.
        patterns = [
            r'(HIM-[A-Z0-9]+)',
            r'(HCO-[A-Z0-9]+)',
            r'(HIO-[A-Z0-9]+)',
            r'(HIC-[A-Z0-9-]+)',
            r'(HIH-[A-Z0-9-]+)',
            r'(HIS-[A-Z0-9]+)'
        ]
        
        for pattern in patterns:
            match = re.search(pattern, product_name, re.IGNORECASE)
            if match:
                return match.group(1).upper()
        
        return ""
    
    def update_product_images(self) -> None:
        """Update products with correct image paths"""
        updated_products = []
        removed_count = 0
        updated_count = 0
        
        for product in self.products:
            product_name = product.get('name', '')
            model_number = self.extract_model_from_name(product_name)
            
            # Find corresponding image folder
            image_folder = None
            if model_number and model_number in self.product_mappings:
                folder_name = self.product_mappings[model_number]
                if folder_name in self.image_folders:
                    image_folder = folder_name
            
            # If no direct mapping found, try partial matching
            if not image_folder:
                for folder_name in self.image_folders.keys():
                    if model_number and model_number.lower().replace('-', '_') in folder_name.lower():
                        image_folder = folder_name
                        break
            
            if image_folder and image_folder in self.image_folders:
                # Update product with new image paths
                product['images'] = self.image_folders[image_folder]
                updated_products.append(product)
                updated_count += 1
                logger.info(f"Updated {product_name} with {len(product['images'])} images from {image_folder}")
            else:
                # Remove product if no images found
                removed_count += 1
                logger.warning(f"Removed {product_name} - no images found (model: {model_number})")
        
        self.products = updated_products
        logger.info(f"Updated {updated_count} products, removed {removed_count} products without images")
    
    def save_updated_json(self, output_file: str = None) -> None:
        """Save updated products to JSON file"""
        if output_file is None:
            output_file = self.json_file.stem + "_with_images.json"
        
        try:
            with open(output_file, 'w', encoding='utf-8') as f:
                json.dump(self.products, f, indent=2, ensure_ascii=False)
            logger.info(f"Saved {len(self.products)} products to {output_file}")
        except Exception as e:
            logger.error(f"Error saving updated JSON: {e}")
    
    def generate_summary_report(self) -> None:
        """Generate a summary report of the update process"""
        report = []
        report.append("# Haier Products Image Update Report\n")
        report.append(f"## Summary\n")
        report.append(f"- **Total products after update:** {len(self.products)}")
        report.append(f"- **Total image folders available:** {len(self.image_folders)}")
        report.append(f"- **Products with images:** {len([p for p in self.products if p.get('images')])}\n")
        
        report.append("## Products by Category\n")
        categories = {}
        for product in self.products:
            category = product.get('category', 'Unknown')
            if category not in categories:
                categories[category] = []
            categories[category].append(product)
        
        for category, products in sorted(categories.items()):
            report.append(f"### {category} ({len(products)} products)\n")
            for product in products:
                image_count = len(product.get('images', []))
                report.append(f"- **{product['name']}** - {image_count} images")
            report.append("")
        
        report.append("## Available Image Folders\n")
        for folder_name, images in sorted(self.image_folders.items()):
            report.append(f"- **{folder_name}** - {len(images)} images")
        
        # Save report
        with open("product_image_update_report.md", 'w', encoding='utf-8') as f:
            f.write('\n'.join(report))
        
        logger.info("Generated summary report: product_image_update_report.md")
    
    def run_update(self) -> None:
        """Run the complete update process"""
        logger.info("Starting product image update process...")
        
        # Load existing products
        self.load_products()
        
        # Scan available images
        self.scan_image_folders()
        
        # Create mappings
        self.create_product_mappings()
        
        # Update products
        self.update_product_images()
        
        # Save updated JSON
        self.save_updated_json()
        
        # Generate report
        self.generate_summary_report()
        
        logger.info("Product image update process completed!")

def main():
    """Main function"""
    updater = ProductImageUpdater()
    updater.run_update()

if __name__ == "__main__":
    main()
