#!/usr/bin/env python3
"""
Verification script to confirm that the database routing fix is working correctly.
This script tests category creation and ensures proper routing.
"""

import os
import django

# Setup Django environment
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'backend.settings')
django.setup()

from django.conf import settings
from products.models import Category
from backend.db_router import DatabaseRouter


def clean_test_data():
    """Clean up any existing test data"""
    print("🧹 CLEANING UP TEST DATA")
    print("=" * 40)
    
    test_names = ['TEST_ROUTING_CATEGORY', 'VERIFY_FIX_CATEGORY']
    
    for db_name in settings.DATABASES.keys():
        for test_name in test_names:
            try:
                deleted_count = Category.objects.using(db_name).filter(name=test_name).delete()[0]
                if deleted_count > 0:
                    print(f"  Cleaned {deleted_count} test records from {db_name}")
            except Exception as e:
                print(f"  Error cleaning {db_name}: {e}")


def test_category_creation_flow():
    """Test the complete category creation and sync flow"""
    print("\n🧪 TESTING CATEGORY CREATION FLOW")
    print("=" * 50)
    
    test_category_name = "VERIFY_FIX_CATEGORY"
    
    # Step 1: Create category (should go to PRIMARY)
    print(f"Step 1: Creating category '{test_category_name}'...")
    category = Category.objects.create(name=test_category_name)
    
    print(f"  ✅ Category created with ID: {category.id}")
    print(f"  ✅ Category database state: {category._state.db}")
    
    if category._state.db == 'default':
        print("  🎯 SUCCESS: Category created in PRIMARY database!")
    else:
        print(f"  ❌ ERROR: Category created in {category._state.db} instead of PRIMARY!")
        return False
    
    # Step 2: Check presence in both databases
    print(f"\nStep 2: Checking category presence in all databases...")
    
    primary_count = Category.objects.using('default').filter(name=test_category_name).count()
    print(f"  PRIMARY (default): {primary_count} records")
    
    replica_count = Category.objects.using('read_replica').filter(name=test_category_name).count()
    print(f"  REPLICA (read_replica): {replica_count} records")
    
    # Step 3: Verify sync worked
    if primary_count == 1 and replica_count == 1:
        print("  🎯 SUCCESS: Category synced to replica!")
        
        # Check if IDs match
        primary_cat = Category.objects.using('default').get(name=test_category_name)
        replica_cat = Category.objects.using('read_replica').get(name=test_category_name)
        
        if primary_cat.id == replica_cat.id:
            print(f"  🎯 SUCCESS: IDs match (ID: {primary_cat.id})")
        else:
            print(f"  ⚠️  WARNING: IDs don't match (Primary: {primary_cat.id}, Replica: {replica_cat.id})")
    else:
        print(f"  ❌ ERROR: Sync failed (Primary: {primary_count}, Replica: {replica_count})")
        return False
    
    # Step 4: Test update
    print(f"\nStep 3: Testing category update...")
    category.name = f"{test_category_name}_UPDATED"
    category.save()
    
    # Check if update synced
    updated_primary = Category.objects.using('default').get(id=category.id)
    updated_replica = Category.objects.using('read_replica').get(id=category.id)
    
    if updated_primary.name == updated_replica.name:
        print(f"  🎯 SUCCESS: Update synced! New name: {updated_primary.name}")
    else:
        print(f"  ❌ ERROR: Update not synced (Primary: {updated_primary.name}, Replica: {updated_replica.name})")
        return False
    
    # Step 5: Clean up
    print(f"\nStep 4: Cleaning up test data...")
    category.delete()
    
    # Verify deletion synced
    primary_count_after = Category.objects.using('default').filter(id=category.id).count()
    replica_count_after = Category.objects.using('read_replica').filter(id=category.id).count()
    
    if primary_count_after == 0 and replica_count_after == 0:
        print("  🎯 SUCCESS: Deletion synced to both databases!")
    else:
        print(f"  ❌ ERROR: Deletion not synced (Primary: {primary_count_after}, Replica: {replica_count_after})")
        return False
    
    return True


def test_read_routing():
    """Test that reads go to replica"""
    print("\n📖 TESTING READ ROUTING")
    print("=" * 30)
    
    router = DatabaseRouter()
    
    # Test read routing
    read_db = router.db_for_read(Category)
    write_db = router.db_for_write(Category)
    
    print(f"Read operations route to: {read_db}")
    print(f"Write operations route to: {write_db}")
    
    if read_db == 'read_replica' and write_db == 'default':
        print("🎯 SUCCESS: Routing is working correctly!")
        return True
    else:
        print("❌ ERROR: Routing is not working correctly!")
        return False


def main():
    """Run all verification tests"""
    print("🔧 DATABASE ROUTING FIX VERIFICATION")
    print("=" * 60)
    
    # Clean up any existing test data
    clean_test_data()
    
    # Test routing configuration
    routing_ok = test_read_routing()
    
    # Test category creation flow
    creation_ok = test_category_creation_flow()
    
    # Final summary
    print("\n" + "=" * 60)
    print("📋 VERIFICATION SUMMARY")
    print("=" * 60)
    
    if routing_ok and creation_ok:
        print("🎉 ALL TESTS PASSED!")
        print("\n✅ Database routing is working correctly:")
        print("   • Categories are created in PRIMARY database")
        print("   • Categories are automatically synced to REPLICA")
        print("   • Updates and deletes sync properly")
        print("   • Read operations use REPLICA")
        print("   • Write operations use PRIMARY")
        print("\n🎯 The fix is successful!")
    else:
        print("❌ SOME TESTS FAILED!")
        print("\nIssues found:")
        if not routing_ok:
            print("   • Database routing configuration issues")
        if not creation_ok:
            print("   • Category creation/sync issues")
        print("\n🔧 Please check the configuration and try again.")
    
    return routing_ok and creation_ok


if __name__ == "__main__":
    success = main()
    exit(0 if success else 1)
