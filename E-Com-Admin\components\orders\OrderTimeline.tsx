import { format } from "date-fns";

interface TimelineEvent {
  status: string;
  date: string;
}

interface OrderTimelineProps {
  events: TimelineEvent[];
}

export const OrderTimeline = ({ events }: OrderTimelineProps) => {
  return (
    <div className="space-y-4">
      {events.map((event, index) => (
        <div key={index} className="flex items-center space-x-4">
          <div className="relative flex items-center">
            <div className="h-2 w-2 rounded-full bg-primary" />
            {index !== events.length - 1 && (
              <div className="absolute top-2 left-1 h-full w-[1px] -translate-x-1/2 bg-border" />
            )}
          </div>
          <div className="flex-1">
            <p className="text-sm font-medium">{event.status}</p>
            <p className="text-sm text-muted-foreground">
              {format(new Date(event.date), "PPp")}
            </p>
          </div>
        </div>
      ))}
    </div>
  );
};