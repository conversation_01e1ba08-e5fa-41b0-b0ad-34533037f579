<!DOCTYPE html>
<html>
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>IP Address Blocked - Security Alert</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            line-height: 1.6;
            color: #333;
            max-width: 600px;
            margin: 0 auto;
            padding: 20px;
            background-color: #f5f5f5;
        }
        .container {
            background-color: #ffffff;
            border-radius: 8px;
            padding: 30px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        .header {
            text-align: center;
            margin-bottom: 30px;
            border-bottom: 2px solid #e74c3c;
            padding-bottom: 20px;
        }
        .priority-high {
            background-color: #e74c3c;
            color: white;
            padding: 8px 16px;
            border-radius: 20px;
            font-weight: bold;
            display: inline-block;
            margin-bottom: 15px;
        }
        .alert-icon {
            font-size: 48px;
            color: #e74c3c;
            margin-bottom: 10px;
        }
        h1 {
            color: #2c3e50;
            margin: 0;
            font-size: 24px;
        }
        .blocked-ip {
            background-color: #ffe6e6;
            border: 2px solid #e74c3c;
            border-radius: 8px;
            padding: 20px;
            margin: 20px 0;
            text-align: center;
        }
        .ip-address {
            font-family: monospace;
            font-size: 24px;
            font-weight: bold;
            color: #e74c3c;
            background-color: #fff;
            padding: 10px 20px;
            border-radius: 5px;
            display: inline-block;
            margin: 10px 0;
        }
        .event-details {
            background-color: #f8f9fa;
            border-left: 4px solid #e74c3c;
            padding: 20px;
            margin: 20px 0;
            border-radius: 0 5px 5px 0;
        }
        .detail-row {
            display: flex;
            justify-content: space-between;
            margin-bottom: 10px;
            padding: 8px 0;
            border-bottom: 1px solid #ecf0f1;
        }
        .detail-label {
            font-weight: bold;
            color: #34495e;
            min-width: 120px;
        }
        .detail-value {
            color: #2c3e50;
        }
        .timestamp {
            background-color: #ecf0f1;
            padding: 15px;
            border-radius: 5px;
            text-align: center;
            margin: 20px 0;
            font-family: monospace;
            color: #7f8c8d;
        }
        .action-section {
            background-color: #e8f5e8;
            border: 1px solid #27ae60;
            border-radius: 5px;
            padding: 15px;
            margin: 20px 0;
        }
        .action-title {
            color: #27ae60;
            font-weight: bold;
            margin-bottom: 10px;
        }
        .urgent-notice {
            background-color: #fff3cd;
            border: 1px solid #ffeaa7;
            border-radius: 5px;
            padding: 15px;
            margin: 20px 0;
            color: #856404;
        }
        .footer {
            margin-top: 30px;
            padding-top: 20px;
            border-top: 1px solid #ecf0f1;
            font-size: 12px;
            color: #7f8c8d;
            text-align: center;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <div class="alert-icon">🚫</div>
            <div class="priority-high">HIGH PRIORITY</div>
            <h1>IP Address Blocked</h1>
            <p style="margin: 10px 0 0 0; color: #7f8c8d;">{{ site_name }} Security System</p>
        </div>
        
        <div class="blocked-ip">
            <h3 style="margin-top: 0; color: #e74c3c;">⚠️ BLOCKED IP ADDRESS</h3>
            <div class="ip-address">{{ ip_address }}</div>
            <p style="margin-bottom: 0; color: #666;">This IP has been temporarily blocked due to suspicious activity</p>
        </div>
        
        <div class="event-details">
            <h3 style="margin-top: 0; color: #e74c3c;">Block Details</h3>
            
            <div class="detail-row">
                <span class="detail-label">Blocked IP:</span>
                <span class="detail-value">{{ ip_address }}</span>
            </div>
            
            <div class="detail-row">
                <span class="detail-label">Block Duration:</span>
                <span class="detail-value">{{ event_data.duration_minutes }} minutes</span>
            </div>
            
            <div class="detail-row">
                <span class="detail-label">Reason:</span>
                <span class="detail-value">{{ event_data.reason }}</span>
            </div>
            
            <div class="detail-row">
                <span class="detail-label">Action Taken:</span>
                <span class="detail-value">{{ event_data.action_taken }}</span>
            </div>
        </div>
        
        <div class="timestamp">
            <strong>Block Initiated:</strong> {{ timestamp|date:"F d, Y, g:i A T" }}
        </div>
        
        <div class="action-section">
            <div class="action-title">🛡️ Automatic Protection Activated</div>
            <p>The security system has automatically blocked this IP address to protect your e-commerce platform from potential threats.</p>
        </div>
        
        <div class="urgent-notice">
            <strong>🔍 Recommended Actions:</strong>
            <ul style="margin: 10px 0;">
                <li><strong>Investigate:</strong> Review security logs for this IP address</li>
                <li><strong>Verify:</strong> Check if this is a legitimate user experiencing issues</li>
                <li><strong>Monitor:</strong> Watch for similar patterns from other IP addresses</li>
                <li><strong>Whitelist:</strong> If legitimate, consider adding to whitelist</li>
                <li><strong>Extend Block:</strong> If malicious, consider extending the block duration</li>
            </ul>
        </div>
        
        <div style="background-color: #e3f2fd; border: 1px solid #2196f3; border-radius: 5px; padding: 15px; margin: 20px 0;">
            <h4 style="margin-top: 0; color: #1976d2;">📊 Security Statistics</h4>
            <p style="margin-bottom: 0;">This automated block helps maintain the security and performance of your platform. Regular monitoring ensures legitimate users can access your services while blocking potential threats.</p>
        </div>
        
        <div class="footer">
            <p>This is an automated security alert from {{ site_name }}.</p>
            <p>Block initiated at {{ timestamp|date:"F d, Y, g:i A T" }}</p>
            <p>The block will automatically expire after {{ event_data.duration_minutes }} minutes unless manually extended.</p>
        </div>
    </div>
</body>
</html>
