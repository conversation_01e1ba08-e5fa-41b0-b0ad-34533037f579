export interface ProductImage {
  id: number;
  image: string;
  is_primary: boolean;
  created_at: string;
}

export interface ProductVariant {
  id: number;
  name: string;
  value: string;
  price_adjustment: number;
  stock: number;
}

export interface Category {
  id: number;
  name: string;
  slug: string;
  description?: string;
  is_active: boolean;
}

export interface SubCategory {
  id: number;
  name: string;
  slug: string;
  description?: string;
  category: number;
  is_active: boolean;
}

export interface Brand {
  id: number;
  name: string;
  description?: string;
  is_active: boolean;
}

export interface GST {
  id: number;
  name: string;
  rate: number;
  cgst_rate: number;
  sgst_rate: number;
  igst_rate: number;
  hsn_code?: string;
  description?: string;
  is_active: boolean;
  is_default: boolean;
}

export interface Product {
  id: number;
  name: string;
  slug: string;
  description: string;
  price: number;
  mrp: number;
  base_price: number;
  stock: number;
  is_active: boolean;
  category: Category;
  subcategory?: SubCategory;
  brand?: Brand;
  gst?: GST;
  images: ProductImage[];
  variants: ProductVariant[];
  average_rating?: number;
  total_reviews?: number;
  created_at: string;
  updated_at: string;
  gst_amount?: number;
  gst_rate?: number;
}

export interface ProductFormData {
  name: string;
  description: string;
  price: string;
  category: number;
  subcategory?: number;
  brand?: number;
  gst?: number;
  stock: string;
  is_active: boolean;
  images: string[];
}

export interface ProductsResponse {
  count: number;
  next?: string;
  previous?: string;
  results: Product[];
}

export interface ProductCreateRequest {
  name: string;
  description: string;
  price: number;
  stock: number;
  category: number;
  subcategory?: number;
  brand?: number;
  gst?: number;
  is_active: boolean;
}

export interface ProductUpdateRequest extends Partial<ProductCreateRequest> {
  id: number;
}

export interface ImageUploadResponse {
  id: number;
  image: string;
  is_primary: boolean;
}
