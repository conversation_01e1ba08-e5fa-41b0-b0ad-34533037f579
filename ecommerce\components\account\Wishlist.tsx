import { Button } from "../../components/ui/button";
import { Card, CardContent } from "../../components/ui/card";
import { ShoppingCart, Trash, Trash2, X } from "lucide-react";
import { useToast } from "../../hooks/use-toast";
import useApi from "../../hooks/useApi";
import {
  ADD_TO_CART,
  MAIN_URL,
  REMOVE_FROM_WISHLIST,
} from "../../constant/urls";
import { useState } from "react";

export const Wishlist = ({ wishlist }: any) => {
  const { toast } = useToast();
  const { create, loading, error } = useApi(MAIN_URL);
  const { remove } = useApi(MAIN_URL);
  const [wishlistItems, setWishlistItems] = useState(wishlist);

  const handleRemoveFromWishlist = async (id: number, name: string) => {
    const url = REMOVE_FROM_WISHLIST + `?wishlist_id=${id}`;
    const response: any = await remove(url);
    if (<PERSON><PERSON><PERSON>(response?.status)) {
      setWishlistItems((prev: any) => [
        ...prev.filter((item: any) => item.id !== id),
      ]);
      toast({
        variant: "info",
        title: "Removed from wishlist",
        description: `${name} has been removed from your wishlist.`,
      });
    }
  };

  const handleAddToCart = (id: number, name: string) => {
    create(ADD_TO_CART, {
      product_id: id,
      quantity: 1,
    });
    toast({
      variant: "success",
      title: "Added to cart",
      description: `${name} has been added to your cart.`,
    });
  };

  return (
    <div className="space-y-6 relative">
      <h2 className="text-lg sm:text-xl font-bold text-gray-800 mb-6">Wishlist</h2>

      {(!Array.isArray(wishlistItems) || wishlistItems.length === 0) && (
        <div className="text-center py-12 bg-gray-50 rounded-xl border border-gray-100">
          <svg xmlns="http://www.w3.org/2000/svg" className="h-12 w-12 mx-auto text-gray-400 mb-4" fill="none" viewBox="0 0 24 24" stroke="currentColor">
            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={1.5} d="M4.318 6.318a4.5 4.5 0 000 6.364L12 20.364l7.682-7.682a4.5 4.5 0 00-6.364-6.364L12 7.636l-1.318-1.318a4.5 4.5 0 00-6.364 0z" />
          </svg>
          <p className="text-gray-600 font-medium">Your wishlist is empty</p>
          <p className="text-gray-500 text-sm mt-1">Items you save to your wishlist will appear here</p>
        </div>
      )}

      <div className="grid gap-6 md:grid-cols-2 lg:grid-cols-3">
        {Array.isArray(wishlistItems) &&
          wishlistItems.map((item: any) => (
            <Card key={item.product.id} className="overflow-hidden rounded-xl border border-gray-200 shadow-sm hover:shadow-md transition-all duration-300">
              <div className="relative">
                <img
                  src={item?.product?.image}
                  alt={item?.product?.name}
                  className="w-full h-52 object-cover"
                />
                <Button
                  variant="ghost"
                  size="icon"
                  className="absolute top-2 right-2 bg-white/80 backdrop-blur-sm rounded-full hover:bg-white hover:text-red-600 transition-colors shadow-sm"
                  onClick={() => handleRemoveFromWishlist(item?.id, item?.product?.name)}
                >
                  <Trash2 className="h-4 w-4" />
                </Button>
              </div>

              <CardContent className="p-5">
                <div className="mb-4">
                  <h3 className="font-medium text-gray-800 line-clamp-1 text-sm md:text-base">{item?.product?.name}</h3>
                  <p className="text-base font-bold text-gray-900 mt-1">
                    ₹{typeof item?.product?.price === "string" && Number(item?.product?.price).toFixed(2)}
                  </p>
                </div>

                <Button
                  className="w-full flex items-center justify-center gap-2 bg-blue-600 hover:bg-blue-700 text-white transition-colors rounded-lg py-2"
                  onClick={() => handleAddToCart(item?.product?.id, item?.product?.name)}
                >
                  <ShoppingCart className="h-4 w-4" />
                  Add to Cart
                </Button>
              </CardContent>
            </Card>
          ))}
      </div>
    </div>
  );
};
