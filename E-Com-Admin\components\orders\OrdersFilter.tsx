import { Input } from "@/components/ui/input";
import { But<PERSON> } from "@/components/ui/button";
import { Calendar } from "@/components/ui/calendar";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import {
  Popover,
  PopoverContent,
  PopoverTrigger,
} from "@/components/ui/popover";
import { CalendarIcon, X } from "lucide-react";
import { format } from "date-fns";
import { useState } from "react";
import { ORDER_STATUS_CHOICES } from "@/constant/urls";

interface OrdersFilterProps {
  // eslint-disable-next-line @typescript-eslint/no-explicit-any
  onFilterChange: (filters: any) => void;
  loading?: boolean;
}

export const OrdersFilter = ({ onFilterChange, loading }: OrdersFilterProps) => {
  const [search, setSearch] = useState("");
  const [status, setStatus] = useState("");
  const [date, setDate] = useState<Date>();

  const handleApplyFilters = () => {
    // eslint-disable-next-line @typescript-eslint/no-explicit-any
    const filters: any = {};

    if (search.trim()) {
      filters.search = search.trim();
    }

    if (status) {
      filters.status = status;
    }

    if (date) {
      filters.date_from = format(date, "yyyy-MM-dd");
    }

    onFilterChange(filters);
  };

  const handleClearFilters = () => {
    setSearch("");
    setStatus("");
    setDate(undefined);
    onFilterChange({});
  };

  return (
    <div className="space-y-4">
      <div className="grid gap-4 md:grid-cols-4">
        <div className="flex flex-col space-y-1.5">
          <Input
            placeholder="Search by order ID or customer..."
            value={search}
            onChange={(e) => setSearch(e.target.value)}
            onKeyPress={(e) => e.key === 'Enter' && handleApplyFilters()}
          />
        </div>
        <div className="flex flex-col space-y-1.5">
          <Select value={status} onValueChange={setStatus}>
            <SelectTrigger>
              <SelectValue placeholder="Order Status" />
            </SelectTrigger>
            <SelectContent>
              {ORDER_STATUS_CHOICES.map((status) => (
                <SelectItem key={status.value} value={status.value}>
                  {status.label}
                </SelectItem>
              ))}
            </SelectContent>
          </Select>
        </div>
        <div className="flex flex-col space-y-1.5">
          <Popover>
            <PopoverTrigger asChild>
              <Button variant="outline" className="w-full justify-start text-left font-normal">
                <CalendarIcon className="mr-2 h-4 w-4" />
                {date ? format(date, "PPP") : "Pick a date"}
              </Button>
            </PopoverTrigger>
            <PopoverContent className="w-auto p-0">
              <Calendar
                mode="single"
                selected={date}
                onSelect={setDate}
                initialFocus
              />
            </PopoverContent>
          </Popover>
        </div>
        <div className="flex flex-col space-y-1.5">
          <div className="flex gap-2">
            <Button
              onClick={handleApplyFilters}
              disabled={loading}
              className="flex-1"
            >
              Apply Filters
            </Button>
            <Button
              variant="outline"
              size="icon"
              onClick={handleClearFilters}
              disabled={loading}
              title="Clear filters"
            >
              <X className="h-4 w-4" />
            </Button>
          </div>
        </div>
      </div>

      {/* Active filters display */}
      {(search || status || date) && (
        <div className="flex flex-wrap gap-2">
          <span className="text-sm text-gray-500">Active filters:</span>
          {search && (
            <span className="inline-flex items-center gap-1 px-2 py-1 bg-blue-100 text-blue-800 rounded-md text-sm">
              Search: {search}
              <button onClick={() => setSearch("")} className="hover:text-blue-600">
                <X className="h-3 w-3" />
              </button>
            </span>
          )}
          {status && (
            <span className="inline-flex items-center gap-1 px-2 py-1 bg-green-100 text-green-800 rounded-md text-sm">
              Status: {status}
              <button onClick={() => setStatus("")} className="hover:text-green-600">
                <X className="h-3 w-3" />
              </button>
            </span>
          )}
          {date && (
            <span className="inline-flex items-center gap-1 px-2 py-1 bg-purple-100 text-purple-800 rounded-md text-sm">
              Date: {format(date, "MMM dd, yyyy")}
              <button onClick={() => setDate(undefined)} className="hover:text-purple-600">
                <X className="h-3 w-3" />
              </button>
            </span>
          )}
        </div>
      )}
    </div>
  );
};