"""
Performance tests for ProductListSerializer optimizations
Tests to verify that N+1 query issues have been resolved
"""

import pytest
from django.test import TestCase
from django.test.utils import override_settings
from django.db import connection
from django.contrib.auth import get_user_model
from rest_framework.test import APIClient
from decimal import Decimal

from products.models import (
    Product, Category, Brand, GST, ProductImage, 
    ProductVariant, Review, SubCategorie
)
from products.serializers import ProductListSerializer
from products.views import get_optimized_product_queryset

User = get_user_model()


class ProductListSerializerPerformanceTest(TestCase):
    """Test performance optimizations in ProductListSerializer"""
    
    def setUp(self):
        """Set up test data"""
        # Create test users
        self.user1 = User.objects.create_user(
            email='<EMAIL>',
            password='testpass123'
        )
        self.user2 = User.objects.create_user(
            email='<EMAIL>', 
            password='testpass123'
        )
        
        # Create categories and brands
        self.category = Category.objects.create(name='Electronics')
        self.subcategory = SubCategorie.objects.create(
            name='Smartphones',
            category=self.category
        )
        self.brand = Brand.objects.create(name='Apple')
        
        # Create GST
        self.gst = GST.objects.create(
            name='Standard GST',
            rate=Decimal('18.00')
        )
        
        # Create multiple products
        self.products = []
        for i in range(10):
            product = Product.objects.create(
                name=f'Product {i}',
                description=f'Description for product {i}',
                category=self.category,
                subcategory=self.subcategory,
                brand=self.brand,
                price=Decimal('999.00'),
                gst=self.gst,
                stock=50
            )
            self.products.append(product)
            
            # Add images for each product
            ProductImage.objects.create(
                product=product,
                image=f'test_image_{i}.jpg',
                is_primary=True
            )
            ProductImage.objects.create(
                product=product,
                image=f'test_image_{i}_2.jpg',
                is_primary=False
            )
            
            # Add variants for each product
            ProductVariant.objects.create(
                product=product,
                name=f'Variant {i}',
                sku=f'SKU{i}',
                price_adjustment=Decimal('50.00'),
                stock=25
            )
            
            # Add reviews for each product
            Review.objects.create(
                product=product,
                user=self.user1,
                rating=4,
                comment=f'Good product {i}'
            )
            Review.objects.create(
                product=product,
                user=self.user2,
                rating=5,
                comment=f'Excellent product {i}'
            )

    def test_optimized_queryset_reduces_queries(self):
        """Test that optimized queryset reduces database queries"""
        # Test unoptimized queryset (baseline)
        with self.assertNumQueries(1):  # Only the initial query
            unoptimized_products = list(Product.objects.all())
        
        # Test optimized queryset
        with self.assertNumQueries(1):  # Should still be just 1 query with all optimizations
            optimized_products = list(get_optimized_product_queryset())
            
        # Verify we get the same products
        self.assertEqual(len(unoptimized_products), len(optimized_products))
        
    def test_serializer_with_optimized_queryset(self):
        """Test that serializer uses prefetched data efficiently"""
        # Get optimized queryset
        products = get_optimized_product_queryset()
        
        # Count queries when serializing
        with self.assertNumQueries(1):  # Should only need the initial optimized query
            serializer = ProductListSerializer(products, many=True)
            data = serializer.data
            
        # Verify data is complete
        self.assertEqual(len(data), 10)
        
        # Check that all expected fields are present
        first_product = data[0]
        expected_fields = [
            'id', 'name', 'price', 'mrp', 'base_price', 'gst_amount', 
            'gst_rate', 'category', 'brand', 'image', 'average_rating', 
            'review_count'
        ]
        
        for field in expected_fields:
            self.assertIn(field, first_product)
            
        # Verify aggregated data is correct
        self.assertEqual(first_product['review_count'], 2)
        self.assertEqual(first_product['average_rating'], 4.5)  # (4+5)/2
        
    def test_api_endpoint_performance(self):
        """Test API endpoint performance with optimized queries"""
        client = APIClient()
        
        # Test product list endpoint
        with self.assertNumQueries(1):  # Should be optimized to 1 query
            response = client.get('/api/products/')
            
        self.assertEqual(response.status_code, 200)
        data = response.json()
        
        # Verify pagination structure
        self.assertIn('results', data)
        self.assertIn('count', data)
        
        # Verify product data
        products = data['results']
        self.assertTrue(len(products) > 0)
        
        # Check first product has all required fields
        first_product = products[0]
        self.assertIn('average_rating', first_product)
        self.assertIn('review_count', first_product)
        self.assertIn('brand', first_product)
        self.assertIn('image', first_product)
        
    def test_gst_calculations_use_prefetched_data(self):
        """Test that GST calculations use prefetched GST data"""
        products = get_optimized_product_queryset()
        
        # This should not trigger additional queries since GST is prefetched
        with self.assertNumQueries(0):
            for product in products:
                # These should use prefetched GST data
                gst_amount = product.calculate_gst_from_mrp()
                gst_rate = product.get_gst_rate()
                
                self.assertIsNotNone(gst_amount)
                self.assertIsNotNone(gst_rate)
                
    def test_image_retrieval_uses_prefetched_data(self):
        """Test that image retrieval uses prefetched data"""
        products = get_optimized_product_queryset()
        
        # This should not trigger additional queries since images are prefetched
        with self.assertNumQueries(0):
            for product in products:
                # Access prefetched images
                images = list(product.images.all())
                self.assertTrue(len(images) > 0)
                
                # Find primary image
                primary_image = next((img for img in images if img.is_primary), None)
                self.assertIsNotNone(primary_image)

    @override_settings(DEBUG=True)
    def test_query_count_comparison(self):
        """Compare query counts between optimized and unoptimized approaches"""
        # Reset query log
        connection.queries_log.clear()
        
        # Test optimized approach
        products = get_optimized_product_queryset()
        serializer = ProductListSerializer(products, many=True)
        optimized_data = serializer.data
        optimized_query_count = len(connection.queries)
        
        # Reset query log
        connection.queries_log.clear()
        
        # Test unoptimized approach (simulate old behavior)
        unoptimized_products = Product.objects.all()
        # This would trigger many queries in the old implementation
        unoptimized_serializer = ProductListSerializer(unoptimized_products, many=True)
        unoptimized_data = unoptimized_serializer.data
        unoptimized_query_count = len(connection.queries)
        
        # Optimized should use significantly fewer queries
        print(f"Optimized queries: {optimized_query_count}")
        print(f"Unoptimized queries: {unoptimized_query_count}")
        
        # The optimized version should use much fewer queries
        self.assertLess(optimized_query_count, unoptimized_query_count)
        
        # Data should be the same
        self.assertEqual(len(optimized_data), len(unoptimized_data))
