
# Create your views here.
from rest_framework.views import APIView
from rest_framework import permissions
from rest_framework.response import Response
from django.db.models import Sum, Count, F, Q, When, Value, Case, CharField
from datetime import datetime, timedelta
from orders.models import Order, OrderItem  # Assuming you have a Product model
from users.models import Customer
from django.contrib.auth import get_user_model
from django.utils.timezone import now

import calendar
User = get_user_model()

from rest_framework.response import Response
from rest_framework.views import APIView
from .serializers import ProductPerformanceSerializer, SalesChartSerializer, CustomerDemographicSerializer




class AnalyticsAPIView(APIView):
    def get(self, request):
        date_from = request.query_params.get('date_from')
        date_to = request.query_params.get('date_to')

        # Default date range: last 30 days if not specified
        if not date_from:
            date_from = now() - timedelta(days=30)
        if not date_to:
            date_to = now()

        # Filter orders by date range
        orders = Order.objects.filter(created_at__range=[date_from, date_to])

        # Product Performance Data
        product_performance = (
            OrderItem.objects
            .filter(order__in=orders)
            .values('product', 'product__name')
            .annotate(
                sales=Sum('quantity'),
                revenue=Sum(F('quantity') * F('unit_price')),
                # Assume some growth calculation here, for simplicity shown as a static example
                growth=F('sales') * 0.1  # Replace with actual growth calculation logic
            )
            .values('product', 'product__name', 'sales', 'revenue', 'growth')
        )
        product_performance_data = [
            {
                'id': item['product'],
                'name': item['product__name'],
                'sales': item['sales'],
                'revenue': item['revenue'],
                'growth': f"{item['growth']}%",
            } for item in product_performance
        ]

        # Sales Chart Data
        sales_chart = (
            orders
            .annotate(month=F('created_at__month'))
            .values('month')
            .annotate(sales=Sum('total'))
            .values('month', 'sales')
        )
        sales_chart_data = [
            {
                'name': month,
                'sales': month_data['sales'],
            } for month, month_data in enumerate(sales_chart, start=1)
        ]

        # Customer Demographic Data
        customer_age_groups = [
            {'name': '18-24', 'value': Customer.objects.filter(Q(date_of_birth__year__range=[18, 24])).count()},
            {'name': '25-34', 'value': Customer.objects.filter(Q(date_of_birth__year__range=[25, 34])).count()},
            {'name': '35-44', 'value': Customer.objects.filter(Q(date_of_birth__year__range=[35, 44])).count()},
            {'name': '45+', 'value': Customer.objects.filter(Q(date_of_birth__year__gte=45)).count()},
        ]

        # Prepare the response data
        response_data = {
            'product_performance': ProductPerformanceSerializer(product_performance_data, many=True).data,
            'sales_chart': SalesChartSerializer(sales_chart_data, many=True).data,
            'customer_demographics': CustomerDemographicSerializer(customer_age_groups, many=True).data,
        }

        return Response(response_data)
