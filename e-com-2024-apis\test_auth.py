#!/usr/bin/env python
"""
Quick authentication test script to debug 401 issues
Run this from the Django project root: python test_auth.py
"""

import os
import sys
import django
from django.conf import settings

# Add the project root to Python path
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

# Setup Django
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'backend.settings')
django.setup()

from django.contrib.auth import get_user_model
from rest_framework_simplejwt.tokens import RefreshToken
from backend.security_monitoring import is_ip_blocked, get_client_ip
from django.core.cache import cache
import requests

User = get_user_model()

def test_ip_blocking():
    """Test if localhost IPs are blocked"""
    test_ips = ['127.0.0.1', 'localhost', '::1']
    
    print("=== IP BLOCKING TEST ===")
    for ip in test_ips:
        blocked = is_ip_blocked(ip)
        print(f"IP {ip}: {'BLOCKED' if blocked else 'OK'}")
    
    return any(is_ip_blocked(ip) for ip in test_ips)

def clear_ip_blocks():
    """Clear IP blocks for common localhost IPs"""
    test_ips = ['127.0.0.1', 'localhost', '::1']
    
    print("\n=== CLEARING IP BLOCKS ===")
    for ip in test_ips:
        cache_keys = [
            f"blocked_ip_{ip}",
            f"failed_attempts_ip_{ip}",
            f"failed_attempts_user_",  # This might have various user patterns
        ]
        
        for key in cache_keys:
            cache.delete(key)
            print(f"Cleared: {key}")

def test_jwt_generation():
    """Test JWT token generation for a user"""
    print("\n=== JWT TOKEN TEST ===")
    
    try:
        # Get the first user or create a test user
        user = User.objects.first()
        if not user:
            print("No users found in database")
            return None
            
        print(f"Testing with user: {user.email} (ID: {user.id})")
        
        # Generate tokens
        refresh = RefreshToken.for_user(user)
        access_token = refresh.access_token
        
        print(f"Access Token: {str(access_token)[:50]}...")
        print(f"Refresh Token: {str(refresh)[:50]}...")
        
        return str(access_token)
        
    except Exception as e:
        print(f"Error generating JWT: {e}")
        return None

def test_api_call(access_token):
    """Test the actual API call"""
    print("\n=== API CALL TEST ===")
    
    if not access_token:
        print("No access token available")
        return
    
    url = "http://localhost:8000/api/v1/users/detail/"
    headers = {
        "Authorization": f"Bearer {access_token}",
        "Content-Type": "application/json"
    }
    
    try:
        print(f"Making request to: {url}")
        print(f"Headers: {headers}")
        
        response = requests.get(url, headers=headers, timeout=10)
        
        print(f"Status Code: {response.status_code}")
        print(f"Response Headers: {dict(response.headers)}")
        
        if response.status_code == 200:
            print("✅ SUCCESS: API call worked!")
            print(f"Response: {response.json()}")
        else:
            print(f"❌ FAILED: {response.status_code}")
            print(f"Response: {response.text}")
            
    except requests.exceptions.ConnectionError:
        print("❌ CONNECTION ERROR: Django server is not running on localhost:8000")
    except Exception as e:
        print(f"❌ ERROR: {e}")

def main():
    print("🔍 AUTHENTICATION DEBUG SCRIPT")
    print("=" * 50)
    
    # Test 1: Check IP blocking
    ips_blocked = test_ip_blocking()
    
    if ips_blocked:
        print("\n⚠️  Some IPs are blocked. Clearing blocks...")
        clear_ip_blocks()
        print("✅ IP blocks cleared. Please try again.")
    
    # Test 2: Generate JWT token
    access_token = test_jwt_generation()
    
    # Test 3: Test API call
    test_api_call(access_token)
    
    print("\n" + "=" * 50)
    print("🔧 TROUBLESHOOTING TIPS:")
    print("1. Make sure Django server is running: python manage.py runserver")
    print("2. Check Django logs for any error messages")
    print("3. Verify your NextAuth session contains a valid access token")
    print("4. Check browser network tab for the actual request being made")

if __name__ == "__main__":
    main()
