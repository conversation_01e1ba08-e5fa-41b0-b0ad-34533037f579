#!/usr/bin/env python3
"""
Dynamic Image Downloader Script

Usage: python download.py <filename.txt>
Example: python download.py "Altrix 1CK Milano Bronze Mortise Locks Mortise Locks.txt"

This script reads a text file containing a product name and image URLs,
then downloads all images to a folder named after the product.
"""

import os
import sys
import requests
import re
import time
import csv
from urllib.parse import urlparse, urljoin
from pathlib import Path


def sanitize_filename(filename):
    """Remove invalid characters from filename for folder creation"""
    # Remove file extension if present
    name = os.path.splitext(filename)[0]
    # Replace invalid characters with underscores
    sanitized = re.sub(r'[\\/*?:"<>|]', "_", name)
    # Remove multiple consecutive underscores
    sanitized = re.sub(r'_+', '_', sanitized)
    # Remove leading/trailing underscores
    sanitized = sanitized.strip('_')
    return sanitized


def is_csv_file(filepath):
    """Check if the file is a CSV file based on extension"""
    return filepath.lower().endswith('.csv')


def extract_urls_from_csv(filepath):
    """Extract URLs from CSV file"""
    products_data = []

    try:
        with open(filepath, 'r', encoding='utf-8') as f:
            # Try to detect if it has headers
            sample = f.read(1024)
            f.seek(0)

            # Use csv.Sniffer to detect delimiter
            sniffer = csv.Sniffer()
            delimiter = sniffer.sniff(sample).delimiter

            reader = csv.reader(f, delimiter=delimiter)

            # Skip header row if it exists
            first_row = next(reader, None)
            if first_row and ('product' in first_row[0].lower() or 'name' in first_row[0].lower()):
                # This looks like a header row, skip it
                pass
            else:
                # This is data, process it
                f.seek(0)
                reader = csv.reader(f, delimiter=delimiter)

            for row_num, row in enumerate(reader, 1):
                if len(row) >= 2:
                    product_name = row[0].strip()
                    urls_string = row[1].strip()

                    # Skip empty rows
                    if not product_name or not urls_string:
                        continue

                    # Split URLs by semicolon and clean them
                    urls = []
                    for url in urls_string.split(';'):
                        url = url.strip()
                        if url:
                            # Handle protocol-relative URLs
                            if url.startswith('//'):
                                url = 'https:' + url
                            if url.startswith('http'):
                                urls.append(url)

                    if urls:
                        products_data.append((product_name, urls))

    except FileNotFoundError:
        print(f"Error: File '{filepath}' not found.")
        return []
    except Exception as e:
        print(f"Error reading CSV file '{filepath}': {e}")
        return []

    return products_data


def extract_urls_from_txt(filepath):
    """Extract URLs from text file"""
    urls = []
    product_name = ""

    try:
        with open(filepath, 'r', encoding='utf-8') as f:
            lines = f.readlines()

        # First non-empty line is usually the product name
        for line in lines:
            line = line.strip()
            if line and not product_name:
                product_name = line
                continue

            # Look for URLs (http/https or protocol-relative URLs)
            if line and (line.startswith('http') or line.startswith('//')):
                # Handle protocol-relative URLs
                if line.startswith('//'):
                    line = 'https:' + line
                urls.append(line)

    except FileNotFoundError:
        print(f"Error: File '{filepath}' not found.")
        return None, []
    except Exception as e:
        print(f"Error reading file '{filepath}': {e}")
        return None, []

    return product_name, urls


def extract_urls_from_file(filepath):
    """Extract URLs from file (supports both TXT and CSV formats)"""
    if is_csv_file(filepath):
        return extract_urls_from_csv(filepath)
    else:
        product_name, urls = extract_urls_from_txt(filepath)
        if product_name and urls:
            return [(product_name, urls)]
        else:
            return []


def get_file_extension(url, content_type=None):
    """Determine file extension from URL or content type"""
    # Try to get extension from URL first
    parsed_url = urlparse(url)
    path = parsed_url.path
    ext = os.path.splitext(path)[1].lower()
    
    # If we got a valid image extension from URL, use it
    if ext in ['.jpg', '.jpeg', '.png', '.gif', '.bmp', '.webp']:
        return ext
    
    # Try to determine from content type
    if content_type:
        if 'jpeg' in content_type or 'jpg' in content_type:
            return '.jpg'
        elif 'png' in content_type:
            return '.png'
        elif 'gif' in content_type:
            return '.gif'
        elif 'webp' in content_type:
            return '.webp'
        elif 'bmp' in content_type:
            return '.bmp'
    
    # Default to .jpg
    return '.jpg'


def download_image(url, save_path, timeout=15):
    """Download image from URL and save to specified path"""
    headers = {
        'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36',
        'Accept': 'image/webp,image/apng,image/*,*/*;q=0.8',
        'Accept-Language': 'en-US,en;q=0.9',
        'Accept-Encoding': 'gzip, deflate, br',
        'DNT': '1',
        'Connection': 'keep-alive',
        'Upgrade-Insecure-Requests': '1',
    }
    
    try:
        print(f"Downloading: {url}")
        response = requests.get(url, headers=headers, stream=True, timeout=timeout)
        
        if response.status_code == 200:
            # Create directory if it doesn't exist
            os.makedirs(os.path.dirname(save_path), exist_ok=True)
            
            # Write image data
            with open(save_path, 'wb') as f:
                for chunk in response.iter_content(chunk_size=1024):
                    if chunk:
                        f.write(chunk)
            
            print(f"✓ Successfully downloaded: {save_path}")
            return True
        else:
            print(f"✗ Failed to download {url} - Status code: {response.status_code}")
            return False
            
    except requests.exceptions.Timeout:
        print(f"✗ Timeout downloading {url}")
        return False
    except requests.exceptions.RequestException as e:
        print(f"✗ Request error downloading {url}: {e}")
        return False
    except Exception as e:
        print(f"✗ Error downloading {url}: {e}")
        return False


def download_product_images(product_name, urls, base_output_dir="downloaded_images"):
    """Download images for a single product"""
    print(f"\nProcessing Product: {product_name}")
    print(f"Found {len(urls)} URLs to download")
    print("-" * 50)

    # Create output directory based on product name
    folder_name = sanitize_filename(product_name)
    output_dir = os.path.join(base_output_dir, folder_name)

    print(f"Output directory: {output_dir}")

    # Download images
    successful_downloads = 0
    failed_downloads = 0

    for i, url in enumerate(urls, 1):
        print(f"\n[{i}/{len(urls)}] Processing URL...")

        # Make a test request to get content type
        try:
            head_response = requests.head(url, timeout=10)
            content_type = head_response.headers.get('content-type', '')
        except:
            content_type = ''

        # Determine file extension
        file_ext = get_file_extension(url, content_type)

        # Create filename
        filename = f"image_{i}{file_ext}"
        save_path = os.path.join(output_dir, filename)

        # Download the image
        if download_image(url, save_path):
            successful_downloads += 1
        else:
            failed_downloads += 1

        # Add small delay to be respectful to servers
        time.sleep(0.5)

    return successful_downloads, failed_downloads, output_dir


def main():
    """Main function to handle command line arguments and orchestrate the download"""
    if len(sys.argv) != 2:
        print("Usage: python download.py <filename.txt|filename.csv>")
        print("Examples:")
        print("  python download.py \"Altrix 1CK Milano Bronze Mortise Locks Mortise Locks.txt\"")
        print("  python download.py \"urls.csv\"")
        sys.exit(1)

    input_file = sys.argv[1]

    # Check if file exists
    if not os.path.exists(input_file):
        print(f"Error: File '{input_file}' does not exist.")
        sys.exit(1)

    print(f"Processing file: {input_file}")
    print("=" * 70)

    # Extract products data
    products_data = extract_urls_from_file(input_file)

    if not products_data:
        print("Error: No products or URLs found in the file.")
        sys.exit(1)

    print(f"Found {len(products_data)} product(s) to process")
    print("=" * 70)

    # Process each product
    total_successful = 0
    total_failed = 0
    processed_products = []

    for product_name, urls in products_data:
        successful, failed, output_dir = download_product_images(product_name, urls)
        total_successful += successful
        total_failed += failed
        processed_products.append({
            'name': product_name,
            'successful': successful,
            'failed': failed,
            'output_dir': output_dir
        })

    # Print final summary
    print("\n" + "=" * 70)
    print("FINAL DOWNLOAD SUMMARY")
    print("=" * 70)
    print(f"Total products processed: {len(products_data)}")
    print(f"Total successful downloads: {total_successful}")
    print(f"Total failed downloads: {total_failed}")
    print("\nPer-product breakdown:")

    for product in processed_products:
        print(f"\n• {product['name']}")
        print(f"  ✓ Successful: {product['successful']}")
        print(f"  ✗ Failed: {product['failed']}")
        if product['successful'] > 0:
            print(f"  📁 Folder: {os.path.abspath(product['output_dir'])}")

    if total_successful > 0:
        print(f"\n✓ All images saved to: {os.path.abspath('downloaded_images')}")
    else:
        print("\n✗ No images were successfully downloaded.")


if __name__ == "__main__":
    main()
