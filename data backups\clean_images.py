#!/usr/bin/env python3
"""
Simple Image Cleaner Script

This script removes low quality and duplicate images from any folder.
It's a simplified version of the comprehensive image quality filter.

Usage: python clean_images.py [folder_path]
If no folder is provided, it will ask you to enter one.

Features:
- Removes duplicate images
- Removes images that are too small (< 200x200 pixels)
- Removes very small files (< 5KB)
- Removes corrupted images
- Creates backup of removed images
"""

import os
import sys
import shutil
import hashlib
from pathlib import Path
from PIL import Image
import logging

# Configure logging
logging.basicConfig(level=logging.INFO, format='%(levelname)s: %(message)s')
logger = logging.getLogger(__name__)


class SimpleImageCleaner:
    def __init__(self, folder_path: str):
        self.folder = Path(folder_path)
        self.backup_folder = self.folder.parent / f"{self.folder.name}_removed"
        self.backup_folder.mkdir(exist_ok=True)
        
        # Simple quality thresholds
        self.min_width = 200
        self.min_height = 200
        self.min_file_size = 5 * 1024  # 5KB
        
        # Statistics
        self.stats = {
            'total': 0,
            'removed': 0,
            'kept': 0,
            'duplicates': 0
        }
        
        # Supported extensions
        self.image_extensions = {'.jpg', '.jpeg', '.png', '.gif', '.bmp', '.webp'}

    def get_image_hash(self, image_path: Path) -> str:
        """Calculate simple hash for duplicate detection"""
        try:
            with Image.open(image_path) as img:
                # Convert to grayscale and resize for consistent hashing
                img = img.convert('L').resize((8, 8))
                pixels = list(img.getdata())
                hash_string = ''.join([str(1 if pixel > 128 else 0) for pixel in pixels])
                return hashlib.md5(hash_string.encode()).hexdigest()[:16]
        except:
            return ""

    def is_low_quality(self, image_path: Path) -> tuple:
        """Check if image is low quality"""
        reasons = []
        
        try:
            # Check file size
            file_size = image_path.stat().st_size
            if file_size < self.min_file_size:
                reasons.append(f"file too small ({file_size//1024}KB)")
            
            # Check image dimensions
            with Image.open(image_path) as img:
                width, height = img.size
                if width < self.min_width:
                    reasons.append(f"width too small ({width}px)")
                if height < self.min_height:
                    reasons.append(f"height too small ({height}px)")
                    
        except Exception as e:
            reasons.append("corrupted or unreadable")
        
        return len(reasons) > 0, reasons

    def move_to_backup(self, image_path: Path, reason: str) -> bool:
        """Move image to backup folder"""
        try:
            backup_path = self.backup_folder / image_path.name
            
            # Handle name conflicts
            counter = 1
            while backup_path.exists():
                name_parts = image_path.stem, counter, image_path.suffix
                backup_path = self.backup_folder / f"{name_parts[0]}_{name_parts[1]}{name_parts[2]}"
                counter += 1
            
            shutil.move(str(image_path), str(backup_path))
            logger.info(f"Removed: {image_path.name} ({reason})")
            return True
            
        except Exception as e:
            logger.error(f"Failed to remove {image_path.name}: {e}")
            return False

    def clean_folder(self, dry_run: bool = False):
        """Clean the folder of low quality images"""
        if not self.folder.exists():
            logger.error(f"Folder does not exist: {self.folder}")
            return
        
        # Get all image files
        image_files = []
        for file_path in self.folder.iterdir():
            if file_path.is_file() and file_path.suffix.lower() in self.image_extensions:
                image_files.append(file_path)
        
        if not image_files:
            logger.info("No images found in the folder")
            return
        
        logger.info(f"Found {len(image_files)} images")
        self.stats['total'] = len(image_files)
        
        # Find duplicates
        hash_groups = {}
        for image_path in image_files:
            img_hash = self.get_image_hash(image_path)
            if img_hash:
                if img_hash not in hash_groups:
                    hash_groups[img_hash] = []
                hash_groups[img_hash].append(image_path)
        
        # Remove duplicates (keep largest file)
        duplicates_found = 0
        for hash_val, duplicate_paths in hash_groups.items():
            if len(duplicate_paths) > 1:
                # Keep the largest file
                largest_file = max(duplicate_paths, key=lambda p: p.stat().st_size)
                for dup_path in duplicate_paths:
                    if dup_path != largest_file:
                        if not dry_run:
                            if self.move_to_backup(dup_path, "duplicate"):
                                self.stats['duplicates'] += 1
                                image_files.remove(dup_path)
                        else:
                            logger.info(f"[DRY RUN] Would remove duplicate: {dup_path.name}")
                            duplicates_found += 1
        
        if dry_run:
            self.stats['duplicates'] = duplicates_found
        
        # Check remaining images for quality
        for image_path in image_files:
            is_low_qual, reasons = self.is_low_quality(image_path)
            
            if is_low_qual:
                reason_text = ", ".join(reasons)
                if not dry_run:
                    if self.move_to_backup(image_path, reason_text):
                        self.stats['removed'] += 1
                    else:
                        self.stats['kept'] += 1
                else:
                    logger.info(f"[DRY RUN] Would remove: {image_path.name} ({reason_text})")
                    self.stats['removed'] += 1
            else:
                self.stats['kept'] += 1

    def print_summary(self):
        """Print cleanup summary"""
        print("\n" + "="*50)
        print("IMAGE CLEANUP SUMMARY")
        print("="*50)
        print(f"Total images: {self.stats['total']}")
        print(f"Duplicates removed: {self.stats['duplicates']}")
        print(f"Low quality removed: {self.stats['removed']}")
        print(f"Images kept: {self.stats['kept']}")
        
        if self.stats['duplicates'] > 0 or self.stats['removed'] > 0:
            print(f"\nBackup folder: {self.backup_folder}")
        print("="*50)


def main():
    """Main function"""
    # Get folder path
    if len(sys.argv) > 1:
        folder_path = sys.argv[1]
    else:
        folder_path = input("Enter the folder path to clean: ").strip().strip('"')
    
    if not os.path.exists(folder_path):
        print(f"Error: Folder '{folder_path}' does not exist.")
        return
    
    print(f"\n🧹 Simple Image Cleaner")
    print(f"Folder: {folder_path}")
    print("="*60)
    print("This will remove:")
    print("• Duplicate images")
    print("• Images smaller than 200x200 pixels")
    print("• Files smaller than 5KB")
    print("• Corrupted images")
    print("\nRemoved images will be moved to a backup folder.")
    
    # Ask for dry run
    dry_run_response = input("\nRun dry-run first to see what would be removed? (Y/n): ").strip().lower()
    dry_run = dry_run_response != 'n'
    
    # Initialize cleaner and run
    cleaner = SimpleImageCleaner(folder_path)
    cleaner.clean_folder(dry_run=dry_run)
    cleaner.print_summary()
    
    if dry_run:
        proceed = input("\nProceed with actual cleanup? (y/N): ").strip().lower()
        if proceed == 'y':
            print("\nRunning actual cleanup...")
            cleaner = SimpleImageCleaner(folder_path)  # Reset stats
            cleaner.clean_folder(dry_run=False)
            cleaner.print_summary()
        else:
            print("Cleanup cancelled.")
    
    print("\n✅ Done!")


if __name__ == "__main__":
    main()
