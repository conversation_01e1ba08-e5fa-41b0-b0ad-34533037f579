# Haier Product Image Scraper

This script extracts high-quality images from Haier product pages using Playwright for reliable web scraping.

## Features

- **High-Quality Image Detection**: Automatically filters out thumbnails, icons, and low-resolution images
- **Organized Output**: Creates separate folders for each product based on URL structure
- **Concurrent Downloads**: Downloads multiple images simultaneously for faster processing
- **Robust Error Handling**: Continues processing even if individual pages fail
- **Duplicate Prevention**: Avoids downloading the same image multiple times
- **Comprehensive Logging**: Detailed logs for monitoring progress and debugging

## Setup

### Option 1: Automatic Setup
```bash
python setup_scraper.py
```

### Option 2: Manual Setup
```bash
# Install dependencies
pip install playwright aiohttp aiofiles

# Install Playwright browser
playwright install chromium
```

## Usage

1. Ensure your URLs are in `urls_haier.txt` (one URL per line)
2. Run the scraper:
```bash
python haier_image_scraper.py
```

## Output Structure

```
haier_images/
├── t_shaped_smart_hood_90_cm_hih_t895/
│   ├── image_001.jpg
│   ├── image_002.jpg
│   └── ...
├── vertical_hood_90_cm_hih_v90hm_c/
│   ├── image_001.jpg
│   └── ...
└── ...
```

## Image Quality Filtering

The scraper automatically filters images to ensure high quality:

### Excluded Images:
- Thumbnails (containing 'thumb', 'small', 'mini')
- Icons and logos
- Images with small dimensions (w=50, h=100, etc.)
- Banner images

### Included Images:
- Product gallery images
- High-resolution product photos
- Kitchen appliance images
- Images from product carousels/sliders

## Configuration

You can modify the script behavior by editing these variables in `haier_image_scraper.py`:

- `output_dir`: Change the output directory name
- `image_selectors`: Add more CSS selectors for finding images
- `skip_patterns`: Modify patterns for filtering out low-quality images

## Logging

The script creates detailed logs in:
- `haier_scraper.log`: File log with all details
- Console output: Real-time progress updates

## Error Handling

- Individual page failures don't stop the entire process
- Network timeouts are handled gracefully
- Invalid URLs are skipped with warnings
- Duplicate images are automatically avoided

## Performance

- Uses async/await for concurrent processing
- Implements proper delays between requests to be respectful to the server
- Efficient memory usage with streaming downloads

## Troubleshooting

### Common Issues:

1. **Playwright not installed**: Run `playwright install chromium`
2. **Network timeouts**: Check internet connection and try again
3. **No images found**: The website structure may have changed; check the CSS selectors
4. **Permission errors**: Ensure write permissions in the output directory

### Debug Mode:
To enable more detailed logging, modify the logging level in the script:
```python
logging.basicConfig(level=logging.DEBUG, ...)
```

## Requirements

- Python 3.7+
- playwright>=1.40.0
- aiohttp>=3.9.0
- aiofiles>=23.0.0

## Notes

- The script is designed to be respectful to the Haier website with appropriate delays
- Images are saved with sequential numbering for easy organization
- The script handles both absolute and relative image URLs
- Supports common image formats: JPG, PNG, WebP
