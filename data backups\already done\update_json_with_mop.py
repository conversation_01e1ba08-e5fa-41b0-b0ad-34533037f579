import csv
import json
import re

def clean_price(price_str):
    """Clean price string by removing commas and converting to float."""
    if not price_str or price_str.strip() == '':
        return None
    # Remove commas and convert to float
    return float(price_str.replace(',', '').replace('"', ''))

def update_json_with_mop():
    # Load the JSON data
    with open('haier_products_new_format.json', 'r', encoding='utf-8') as json_file:
        products = json.load(json_file)

    # Create a dictionary to map model numbers to MOP values from CSV
    model_to_mop = {}

    # Read the CSV file
    with open('Haier KA.csv', 'r', encoding='utf-8') as csv_file:
        csv_reader = csv.reader(csv_file)
        # Skip the first two rows (header rows)
        next(csv_reader)  # Skip the first row (Haier)
        next(csv_reader)  # Skip the second row (column headers)

        # Process each row in the CSV
        for row in csv_reader:
            if len(row) >= 6:  # Ensure the row has enough columns
                classification = row[0]
                sap_code = row[1]
                model = row[2]
                mrp = row[3]
                dp = row[4]
                mop = row[5]

                # Store the model and its MOP value
                if model and mop:
                    model_to_mop[model] = mop
                    print(f"Found in CSV: Model={model}, MOP={mop}")

    # Update the JSON data with MOP values
    updated_count = 0
    for i, product in enumerate(products):
        model = product.get('model')
        if model and model in model_to_mop:
            # Update the price with the MOP value
            mop_value = model_to_mop[model]
            clean_mop = clean_price(mop_value)
            # Directly update the price in the product dictionary
            products[i]['price'] = clean_mop
            updated_count += 1
            print(f"Updated product: {model} with price: {clean_mop}")

    # Save the updated JSON data
    with open('haier_products_new_format.json', 'w', encoding='utf-8') as json_file:
        json.dump(products, json_file, indent=2)

    print(f"Updated {updated_count} products with MOP values.")

    # Verify the updates
    with open('haier_products_new_format.json', 'r', encoding='utf-8') as json_file:
        updated_products = json.load(json_file)

    print("\nVerification of updated products:")
    for product in updated_products:
        model = product.get('model')
        price = product.get('price')
        if model in model_to_mop:
            print(f"Model: {model}, Price: {price}")

if __name__ == "__main__":
    update_json_with_mop()
