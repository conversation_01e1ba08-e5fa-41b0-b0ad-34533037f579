import { <PERSON>, CardContent, <PERSON>Header, CardTitle } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { Button } from "@/components/ui/button";
import { Separator } from "@/components/ui/separator";
import { format } from "date-fns";
import { Package, Truck, Eye, MapPin } from "lucide-react";
import { CustomerOrder } from "@/hooks/useCustomerOrders";
import { useRouter } from "next/navigation";
import { formatCurrency } from "@/utils/formatters";

interface OrderHistoryCardProps {
  order: CustomerOrder;
}

const getStatusColor = (status: string) => {
  switch (status.toUpperCase()) {
    case 'PENDING':
      return 'bg-yellow-100 text-yellow-800 border-yellow-200';
    case 'PROCESSING':
      return 'bg-blue-100 text-blue-800 border-blue-200';
    case 'PAID':
      return 'bg-green-100 text-green-800 border-green-200';
    case 'SHIPPED':
      return 'bg-purple-100 text-purple-800 border-purple-200';
    case 'DELIVERED':
      return 'bg-emerald-100 text-emerald-800 border-emerald-200';
    case 'CANCELLED':
      return 'bg-red-100 text-red-800 border-red-200';
    case 'REFUNDED':
      return 'bg-gray-100 text-gray-800 border-gray-200';
    default:
      return 'bg-gray-100 text-gray-800 border-gray-200';
  }
};

const formatDate = (dateString: string): string => {
  try {
    return format(new Date(dateString), "MMM d, yyyy 'at' h:mm a");
  } catch {
    return "Invalid date";
  }
};

export const OrderHistoryCard = ({ order }: OrderHistoryCardProps) => {
  const router = useRouter();

  const handleViewOrder = () => {
    router.push(`/orders/${order.id}`);
  };

  return (
    <Card className="mb-4 hover:shadow-md transition-shadow">
      <CardHeader className="pb-3">
        <div className="flex items-center justify-between">
          <div className="flex items-center space-x-3">
            <Package className="h-5 w-5 text-muted-foreground" />
            <div>
              <CardTitle className="text-lg">Order #{order.id.slice(-8)}</CardTitle>
              <p className="text-sm text-muted-foreground">
                Placed on {formatDate(order.created_at)}
              </p>
            </div>
          </div>
          <div className="flex items-center space-x-2">
            <Badge className={getStatusColor(order.status)}>
              {order.status}
            </Badge>
            <Button
              variant="outline"
              size="sm"
              onClick={handleViewOrder}
              className="flex items-center space-x-1"
            >
              <Eye className="h-4 w-4" />
              <span>View</span>
            </Button>
          </div>
        </div>
      </CardHeader>
      
      <CardContent className="space-y-4">
        {/* Order Items */}
        <div className="space-y-2">
          <h4 className="font-medium text-sm text-muted-foreground">Items ({order.items?.length || 0})</h4>
          <div className="space-y-2">
            {order.items?.slice(0, 3).map((item, index) => (
              <div key={index} className="flex items-center justify-between text-sm">
                <div className="flex-1">
                  <span className="font-medium">{item.product_name}</span>
                  {item.variant_name && (
                    <span className="text-muted-foreground"> - {item.variant_name}</span>
                  )}
                  <span className="text-muted-foreground"> × {item.quantity}</span>
                </div>
                <span className="font-medium">${formatCurrency(item.total_price)}</span>
              </div>
            ))}
            {order.items && order.items.length > 3 && (
              <p className="text-sm text-muted-foreground">
                +{order.items.length - 3} more items
              </p>
            )}
          </div>
        </div>

        <Separator />

        {/* Order Summary */}
        <div className="grid grid-cols-2 gap-4 text-sm">
          <div className="space-y-1">
            <div className="flex justify-between">
              <span className="text-muted-foreground">Subtotal:</span>
              <span>${formatCurrency(order.subtotal)}</span>
            </div>
            <div className="flex justify-between">
              <span className="text-muted-foreground">Shipping:</span>
              <span>${formatCurrency(order.shipping_cost)}</span>
            </div>
            {(Number(order.gst_amount) || 0) > 0 && (
              <div className="flex justify-between">
                <span className="text-muted-foreground">GST:</span>
                <span>${formatCurrency(order.gst_amount)}</span>
              </div>
            )}
          </div>
          <div className="space-y-1">
            <div className="flex justify-between font-medium">
              <span>Total:</span>
              <span>${formatCurrency(order.total)}</span>
            </div>
            {order.tracking_number && (
              <div className="flex items-center space-x-1 text-muted-foreground">
                <Truck className="h-3 w-3" />
                <span className="text-xs">Tracking: {order.tracking_number}</span>
              </div>
            )}
          </div>
        </div>

        {/* Shipping Address */}
        {order.shipping_address && (
          <>
            <Separator />
            <div className="flex items-start space-x-2 text-sm">
              <MapPin className="h-4 w-4 text-muted-foreground mt-0.5" />
              <div>
                <p className="font-medium">Shipping Address</p>
                <p className="text-muted-foreground">
                  {order.shipping_address.street_address}, {order.shipping_address.city}, {order.shipping_address.state} {order.shipping_address.postal_code}
                </p>
              </div>
            </div>
          </>
        )}

        {/* Estimated Delivery */}
        {order.estimated_delivery_date && (
          <div className="text-sm">
            <span className="text-muted-foreground">Estimated Delivery: </span>
            <span className="font-medium">
              {formatDate(order.estimated_delivery_date)}
            </span>
          </div>
        )}
      </CardContent>
    </Card>
  );
};
