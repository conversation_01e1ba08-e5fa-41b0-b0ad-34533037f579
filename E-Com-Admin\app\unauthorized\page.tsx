"use client";

import { useRouter } from "next/navigation";
import { <PERSON>, <PERSON><PERSON>onte<PERSON>, Card<PERSON><PERSON>er, CardTitle } from "@/components/ui/card";
import { But<PERSON> } from "@/components/ui/button";
import { 
  Shield, 
  ArrowLeft, 
  LogIn,
  AlertTriangle 
} from "lucide-react";

const UnauthorizedPage = () => {
  const router = useRouter();

  return (
    <div className="min-h-screen bg-gradient-to-br from-red-50 to-orange-100 flex items-center justify-center p-4">
      <div className="w-full max-w-md">
        {/* Header */}
        <div className="text-center mb-8">
          <div className="inline-flex items-center justify-center w-16 h-16 bg-red-600 rounded-full mb-4">
            <AlertTriangle className="h-8 w-8 text-white" />
          </div>
          <h1 className="text-3xl font-bold text-gray-900 mb-2">Access Denied</h1>
          <p className="text-gray-600">You dont have permission to access this resource</p>
        </div>

        {/* Error Card */}
        <Card className="border-0 shadow-xl bg-white/80 backdrop-blur-sm">
          <CardHeader className="space-y-1 pb-6">
            <CardTitle className="text-xl font-semibold text-center text-gray-900">
              Unauthorized Access
            </CardTitle>
          </CardHeader>
          <CardContent className="space-y-4">
            <div className="text-center space-y-4">
              <div className="p-4 bg-red-50 border border-red-200 rounded-lg">
                <div className="flex items-center gap-2 text-red-700">
                  <Shield className="h-4 w-4" />
                  <p className="text-sm font-medium">
                    You need proper authentication to access this page
                  </p>
                </div>
              </div>

              <div className="space-y-3">
                <p className="text-sm text-gray-600">
                  This page requires admin privileges. Please sign in with an authorized account.
                </p>
                
                <div className="flex flex-col gap-2">
                  <Button
                    onClick={() => router.push('/login')}
                    className="w-full bg-blue-600 hover:bg-blue-700 text-white"
                  >
                    <LogIn className="mr-2 h-4 w-4" />
                    Sign In
                  </Button>
                  
                  <Button
                    variant="outline"
                    onClick={() => router.push('/')}
                    className="w-full"
                  >
                    <ArrowLeft className="mr-2 h-4 w-4" />
                    Back to Dashboard
                  </Button>
                </div>
              </div>
            </div>

            {/* Help Section */}
            <div className="mt-6 p-4 bg-gray-50 rounded-lg border">
              <h3 className="text-sm font-medium text-gray-700 mb-2">Need Help?</h3>
              <div className="text-xs text-gray-600 space-y-1">
                <p>• Contact your administrator for access</p>
                <p>• Ensure you are using the correct credentials</p>
                <p>• Try refreshing your session</p>
              </div>
            </div>
          </CardContent>
        </Card>

        {/* Footer */}
        <div className="mt-6 text-center">
          <p className="text-sm text-gray-600">
            Having trouble?{" "}
            <button 
              onClick={() => router.push('/login')}
              className="text-blue-600 hover:text-blue-700 font-medium"
            >
              Try signing in again
            </button>
          </p>
        </div>
      </div>
    </div>
  );
};

export default UnauthorizedPage;
