
"use client"
import { DualRangeSlider } from "@/components/ui/DualSlider";
import Footer from "../../components/utils/Footer";
import NavBar from "../../components/utils/Navbar";
import React, { useState } from "react";

const page = () => {
  const [values, setValues] = useState([0, 100]);
  return (
    <div>
      <NavBar />
      <DualRangeSlider
        label={() => <>₹</>}
        lableContenPos={"left"}
        value={values}
        onValueChange={setValues}
        min={0}
        max={100}
        step={1}
      />
      <Footer />
    </div>
  );
};

export default page;
