import { View, Text, Image, TouchableOpacity, StyleSheet } from "react-native";
import { MaterialIcons } from "@expo/vector-icons";
import { BaseURL } from "@/constants/ApiEndpoint";

interface ProductCardProps {
  id: string;
  name: string;
  price: number;
  image: string;
  average_rating: number | string | null;
  onAddToCart?: () => void;
  onPress?: () => void;
}

export default function ProductCard({
  name,
  price,
  image,
  average_rating,
  id,
  slug,
  onAddToCart,
  onPress,
}: ProductCardProps) {
  return (
    <TouchableOpacity style={styles.card} onPress={onPress} activeOpacity={0.9}>
      <Image
        source={{
          uri: BaseURL + image,
        }}
        style={styles.image}
      />
      <View style={styles.content}>
        <Text style={styles.name} numberOfLines={2}>
          {name}
        </Text>
        {average_rating && (
          <View style={styles.ratingContainer}>
            <MaterialIcons name="star" size={16} color="#FFD700" />
            <Text style={styles.rating}>
              {Number(average_rating).toFixed(1)}
            </Text>
          </View>
        )}
        <View style={styles.bottomRow}>
          <Text style={styles.price}>₹{Number(price).toFixed(2)}</Text>
          <TouchableOpacity style={styles.addButton} onPress={onAddToCart}>
            <MaterialIcons name="add-shopping-cart" size={20} color="white" />
          </TouchableOpacity>
        </View>
      </View>
    </TouchableOpacity>
  );
}

const styles = StyleSheet.create({
  card: {
    backgroundColor: "white",
    borderRadius: 12,
    margin: 8,
    flex: 1,
    minWidth: 160,
    maxWidth: 250,
    shadowColor: "#000",
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 8,
    elevation: 3,
    overflow: "hidden",
  },
  image: {
    width: "100%",
    height: 150,
    resizeMode: "cover",
  },
  content: {
    padding: 12,
  },
  name: {
    fontSize: 14,
    fontWeight: "600",
    marginBottom: 4,
    color: "#1a1a1a",
  },
  ratingContainer: {
    flexDirection: "row",
    alignItems: "center",
    marginBottom: 8,
  },
  rating: {
    marginLeft: 4,
    fontSize: 12,
    color: "#666",
  },
  bottomRow: {
    flexDirection: "row",
    justifyContent: "space-between",
    alignItems: "center",
  },
  price: {
    fontSize: 16,
    fontWeight: "bold",
    color: "#1a1a1a",
  },
  addButton: {
    backgroundColor: "#2563EB",
    padding: 8,
    borderRadius: 8,
  },
});
