// Types definitions based on the provided JSON structure
interface User {
  id: number;
  email: string;
  name: string;
  phone_number: string | null;
  date_of_birth: string | null;
  is_verified: boolean;
  display_image_url: string | null;
}

interface ProductImage {
  id: number;
  image: string;
  is_primary: boolean;
}

interface Brand {
  id: number;
  name: string;
}

interface Subcategory {
  id: number;
  name: string;
  slug: string;
}

interface Category {
  id: number;
  name: string;
  slug: string;
  subcategories: Subcategory[];
}

interface Product {
  id: number;
  name: string;
  slug: string;
  description: string;
  price: string;
  category: Category;
  stock: number;
  is_active: boolean;
  images: ProductImage[];
  variants: any[]; // Add more specific type if needed
  average_rating: number | null;
  review_count: number;
  created_at: string;
  updated_at: string;
  brand: Brand;
  image: string | null;
  reviews: any[]; // Add more specific type if needed
}

interface WishlistItem {
  id: number;
  product: Product;
  added_at: string;
}

interface OrderItem {
  id: number;
  product_name: string;
  variant_name: string;
  quantity: number;
  unit_price: string;
  total_price: string;
  product_image: string | null;
}

interface Address {
  id: number;
  address_type: string;
  street_address: string;
  apartment: string;
  city: string;
  state: string;
  country: string;
  postal_code: string;
  order_user_phone: string;
  order_user_email: string;
  is_default: boolean;
  user: number;
}

interface ShippingMethod {
  id: number;
  name: string;
  description: string;
  price: string;
  estimated_days: number;
  is_active: boolean;
}

interface Order {
  id: string;
  status: string;
  items: OrderItem[];
  shipping_address: Address;
  billing_address: Address;
  shipping_method: ShippingMethod;
  subtotal: string;
  shipping_cost: string;
  total: string;
  tracking_number: string;
  estimated_delivery_date: string;
  notes: string;
  created_at: string;
  updated_at: string;
  user: User;
  payments: any[]; // Add more specific type if needed
}

interface UserData {
  user: User;
  orders: Order[];
  shipping_address: Address[];
  payment_methods: any[]; // Add more specific type if needed
  wishlist: WishlistItem[];
}

import { useState, useEffect, useCallback } from "react";
import axios from "axios";
import { BaseURL } from "../constants/ApiEndpoint";
import { useAuth } from "@/context/AuthContext";

// Main hook to fetch all user data
export const useUserData = () => {
  const [userData, setUserData] = useState<UserData | null>(null);
  const [loading, setLoading] = useState<boolean>(true);
  const [error, setError] = useState<string | null>(null);
  const { accessToken, isAuthenticated } = useAuth(); // Assuming you have a way to get the access token from your auth context or state

  const fetchUserData = useCallback(async () => {
    if (!isAuthenticated || !accessToken) {
      setError("Not authenticated or missing token");
      return;
    }
    try {
      setLoading(true);
      const response = await axios.get(`${BaseURL}/api/v1/users/detail/`, {
        headers: {
          Authorization: `Bearer ${accessToken}`,
        },
      });
      setUserData(response.data);
      setError(null);
    } catch (err) {
      setError("Failed to fetch user data");
      console.error("Error fetching user data:", err);
    } finally {
      setLoading(false);
    }
  }, [accessToken, isAuthenticated]);

  useEffect(() => {
    if (isAuthenticated) {
      fetchUserData();
    }
  }, [fetchUserData, isAuthenticated]);

  return { userData, loading, error, refetchUserData: fetchUserData };
};

// Specific hook for user profile information
export const useUserProfile = () => {
  const { userData, loading, error } = useUserData();

  return {
    profile: userData?.user || null,
    loading,
    error,
  };
};

// Specific hook for user's wishlist
export const useWishlist = () => {
  const { userData, loading, error, refetchUserData } = useUserData();

  return {
    wishlist: userData?.wishlist || [],
    loading,
    error,
    refetchWishlist: refetchUserData,
  };
};

// Specific hook for user's orders
export const useOrders = () => {
  const { userData, loading, error } = useUserData();

  return {
    orders: userData?.orders || [],
    loading,
    error,
  };
};

// Specific hook for user's shipping addresses
export const useShippingAddresses = () => {
  const { userData, loading, error } = useUserData();
  const { accessToken, isAuthenticated } = useAuth();

  const addAddress = async (formData: any) => {
    if (!isAuthenticated) return;
    try {
      const response = await axios.post(
        `${BaseURL}/api/v1/users/addresses/`,
        formData,
        {
          headers: {
            Authorization: `Bearer ${accessToken}`,
          },
        }
      );
      if (response) {
        console.log(response.data);
      }
    } catch (error) {
      console.error("Error adding address:", error);
    }
  };
  const updateAddress = async (editingAddressid: any, formData: any) => {
    console.log("editingAddressid", editingAddressid);
    if (!isAuthenticated) return;
    try {
      const response = await axios.put(
        `${BaseURL}/api/v1/users/addresses/${editingAddressid}/`,
        formData,
        {
          headers: {
            Authorization: `Bearer ${accessToken}`,
          },
        }
      );
      if (response) {
        console.log(response.data);
      }
    } catch (error) {
      console.error("Error adding address:", error);
    }
  };

  return {
    addresses: userData?.shipping_address || [],
    loading,
    error,
    addAddress,
    updateAddress,
  };
};

// Specific hook for user's payment methods
export const usePaymentMethods = () => {
  const { userData, loading, error } = useUserData();

  return {
    paymentMethods: userData?.payment_methods || [],
    loading,
    error,
  };
};

// Hook for getting an order by ID
export const useOrderById = (orderId: string) => {
  const [loading, setLoading] = useState<boolean>(true);
  const [order, setOrder] = useState<Order | null>();
  const { accessToken, isAuthenticated } = useAuth();
  const [error, setError] = useState<string | null>(null);

  useEffect(() => {
    const fetchOrder = async () => {
      try {
        const response = await axios.get(
          `${BaseURL}/api/v1/orders/${orderId}/`,
          {
            headers: {
              Authorization: `Bearer ${accessToken}`,
            },
          }
        );
        if (isAuthenticated && response.data) {
          setOrder(response.data);
          setLoading(false);
        }
      } catch (error) {
        console.error("Error fetching order:", error);
        setError("Failed to fetch order details" + error);
        setLoading(false);
      } finally {
        setLoading(false);
      }
    };
    fetchOrder();
  }, [isAuthenticated]);

  return {
    order,
    loading,
    error: error,
  };
};

// Hook to get recent orders (e.g., last 3)
export const useRecentOrders = (count: number = 3) => {
  const { userData, loading, error } = useUserData();
  const [recentOrders, setRecentOrders] = useState<Order[]>([]);

  useEffect(() => {
    if (userData && userData.orders) {
      // Sort orders by creation date (newest first)
      const sortedOrders = [...userData.orders].sort(
        (a, b) =>
          new Date(b.created_at).getTime() - new Date(a.created_at).getTime()
      );
      setRecentOrders(sortedOrders.slice(0, count));
    }
  }, [userData, count]);

  return {
    recentOrders,
    loading,
    error,
  };
};
