#!/usr/bin/env python3
"""
Test script to verify database isolation for tests.
This script checks that tests use SQLite3 and are isolated from production database.
"""

import os
import sys
import django
from django.conf import settings
from django.core.management import execute_from_command_line

def test_database_isolation():
    """Test that the test configuration uses SQLite and is isolated."""
    
    # Set test settings
    os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'tests.test_settings')
    
    # Setup Django
    django.setup()
    
    print("=== Database Isolation Test ===")
    print(f"Settings module: {settings.SETTINGS_MODULE}")
    print(f"Database engine: {settings.DATABASES['default']['ENGINE']}")
    print(f"Database name: {settings.DATABASES['default']['NAME']}")
    
    # Check if using SQLite
    if 'sqlite3' in settings.DATABASES['default']['ENGINE']:
        print("✅ Using SQLite3 for tests - GOOD!")
    else:
        print("❌ NOT using SQLite3 for tests - BAD!")
        return False
    
    # Check if using in-memory database
    if settings.DATABASES['default']['NAME'] == ':memory:':
        print("✅ Using in-memory database - GOOD!")
    else:
        print(f"⚠️  Using file database: {settings.DATABASES['default']['NAME']}")
    
    # Check other test-specific settings
    print(f"DEBUG: {settings.DEBUG}")
    print(f"APPEND_SLASH: {getattr(settings, 'APPEND_SLASH', True)}")
    print(f"ALLOWED_HOSTS: {settings.ALLOWED_HOSTS}")
    print(f"EMAIL_BACKEND: {settings.EMAIL_BACKEND}")
    
    # Check if production database settings are overridden
    if hasattr(settings, 'DB_NAME'):
        print(f"DB_NAME (should be test): {settings.DB_NAME}")
    
    print("\n=== Test Settings Verification ===")
    
    # Verify test-specific settings
    test_settings_ok = True
    
    if not settings.EMAIL_BACKEND == 'django.core.mail.backends.console.EmailBackend':
        print("❌ Email backend not set to console")
        test_settings_ok = False
    else:
        print("✅ Email backend set to console")
    
    if getattr(settings, 'APPEND_SLASH', True):
        print("❌ APPEND_SLASH should be False for tests")
        test_settings_ok = False
    else:
        print("✅ APPEND_SLASH disabled for tests")
    
    if 'testserver' not in settings.ALLOWED_HOSTS:
        print("❌ testserver not in ALLOWED_HOSTS")
        test_settings_ok = False
    else:
        print("✅ testserver in ALLOWED_HOSTS")
    
    return test_settings_ok

def run_quick_test():
    """Run a quick test to verify database setup."""
    print("\n=== Running Quick Database Test ===")
    
    try:
        from django.contrib.auth import get_user_model
        from backend.security_monitoring import FailedLoginAttempt, SecurityEvent

        User = get_user_model()

        # Try to create tables (this should work with migrations)
        print("Testing model imports...")
        print(f"User model: {User}")
        print(f"FailedLoginAttempt model: {FailedLoginAttempt}")
        print(f"SecurityEvent model: {SecurityEvent}")
        print("✅ All models imported successfully")

        # Test that we can access the database
        print("Testing database access...")
        user_count = User.objects.count()
        print(f"User count: {user_count}")
        print("✅ Database access working")

        return True

    except Exception as e:
        print(f"❌ Error testing models: {e}")
        return False

if __name__ == '__main__':
    print("Testing database isolation for e-commerce API tests...")
    
    # Test database isolation
    isolation_ok = test_database_isolation()
    
    # Test model imports
    models_ok = run_quick_test()
    
    print("\n=== Summary ===")
    if isolation_ok and models_ok:
        print("✅ All tests passed! Database isolation is working correctly.")
        sys.exit(0)
    else:
        print("❌ Some tests failed. Check the output above.")
        sys.exit(1)
