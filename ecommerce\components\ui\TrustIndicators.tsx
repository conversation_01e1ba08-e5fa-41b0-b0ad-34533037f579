import { Shield, Truck, Refresh<PERSON><PERSON>, Clock } from "lucide-react";
import { motion } from "framer-motion";

const TrustIndicators = () => {
  const items = [
    {
      icon: <Truck className="w-5 h-5 xs:w-6 xs:h-6" />,
      title: "Free Shipping",
      description: "On all orders over ₹5000",
      gradient: "from-theme-accent-primary to-theme-accent-hover",
      delay: 0,
    },
    {
      icon: <RefreshCw className="w-5 h-5 xs:w-6 xs:h-6" />,
      title: "Easy Returns",
      description: "7-day return policy",
      gradient: "from-theme-accent-secondary to-theme-accent-primary",
      delay: 0.1,
    },
    {
      icon: <Shield className="w-5 h-5 xs:w-6 xs:h-6" />,
      title: "Secure Checkout",
      description: "100% protected payments",
      gradient: "from-theme-accent-primary to-theme-accent-secondary",
      delay: 0.2,
    },
    {
      icon: <Clock className="w-5 h-5 xs:w-6 xs:h-6" />,
      title: "24/7 Support",
      description: "Always here to help you",
      gradient: "from-theme-accent-secondary to-theme-accent-hover",
      delay: 0.3,
    },
  ];

  const containerVariants = {
    hidden: { opacity: 0 },
    visible: {
      opacity: 1,
      transition: {
        staggerChildren: 0.15,
        delayChildren: 0.1,
      },
    },
  };

  const itemVariants = {
    hidden: { opacity: 0, y: 20 },
    visible: {
      opacity: 1,
      y: 0,
      transition: {
        duration: 0.5,
        ease: "easeOut",
      },
    },
  };

  return (
    <div className="container mx-auto px-4 py-6 sm:py-8">
      <motion.div
        className="grid grid-cols-2 sm:grid-cols-2 lg:grid-cols-4 gap-3 xs:gap-4 sm:gap-6 max-w-6xl mx-auto"
        variants={containerVariants}
        initial="hidden"
        animate="visible"
        viewport={{ once: true, amount: 0.3 }}
      >
        {items.map((item, index) => (
          <motion.div
            key={index}
            variants={itemVariants}
            custom={index}
            className="group flex flex-col items-center text-center p-3 xs:p-4 sm:p-6 rounded-xl bg-white/90 backdrop-blur-sm shadow-md border border-theme-accent-primary/10
              hover:shadow-lg transition-all duration-300 hover:-translate-y-1 hover:bg-white/95"
            style={{
              boxShadow: "0 4px 20px rgba(0, 0, 0, 0.05)",
            }}
          >
            <div className={`p-2 xs:p-3 rounded-full bg-gradient-to-r ${item.gradient} text-white mb-2 xs:mb-4 shadow-md
              transition-all duration-300 hover:scale-110 hover:shadow-lg`}>
              {item.icon}
            </div>
            <h3 className="font-semibold text-base xs:text-lg text-theme-text-primary mb-1 xs:mb-2 transition-all duration-300 group-hover:text-theme-accent-primary">
              {item.title}
            </h3>
            <p className="text-xs xs:text-sm text-gray-600 transition-all duration-300 group-hover:text-theme-text-primary">
              {item.description}
            </p>
          </motion.div>
        ))}
      </motion.div>
    </div>
  );
};

export default TrustIndicators;