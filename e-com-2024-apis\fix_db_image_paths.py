#!/usr/bin/env python
"""
Script to fix image paths in the database by removing duplicate 'media/' prefixes.
This script will:
1. Find all ProductImage and Brand records with incorrect paths
2. Update the paths to remove the duplicate 'media/' prefix
3. Save the changes to the database
"""

import os
import sys
import argparse
import logging
import django
from django.db.models import Q

# Set up logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    handlers=[logging.StreamHandler()]
)
logger = logging.getLogger('db-path-fix')

# Set up Django environment
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'backend.settings')
django.setup()

# Import models after Django setup
from products.models import ProductImage, Brand

def fix_product_image_paths(dry_run=True):
    """
    Fix ProductImage paths in the database by removing duplicate 'media/' prefixes
    and ensuring correct path format

    Args:
        dry_run: If True, only show what would be done without making changes
    """
    logger.info(f"Mode: {'DRY RUN' if dry_run else 'LIVE'}")

    # Get all ProductImage records
    all_product_images = ProductImage.objects.all()
    logger.info(f"Total ProductImage records: {all_product_images.count()}")

    # Process all ProductImage records
    fixed_count = 0
    error_count = 0
    skipped_count = 0

    for img in all_product_images:
        old_path = img.image.name
        new_path = old_path
        needs_fix = False

        # Check for duplicate 'media/' prefix
        if old_path.startswith('media/media/'):
            new_path = old_path[6:]  # Remove the first 'media/' prefix
            needs_fix = True
        # Check for single 'media/' prefix that should be removed
        elif old_path.startswith('media/'):
            # If the path is like 'media/products/...' we want to make it 'products/...'
            if old_path.startswith('media/products/'):
                new_path = old_path[6:]  # Remove the 'media/' prefix
                needs_fix = True
            # If the path is like 'media/brands/...' we want to make it 'brands/...'
            elif old_path.startswith('media/brands/'):
                new_path = old_path[6:]  # Remove the 'media/' prefix
                needs_fix = True

        if not needs_fix:
            skipped_count += 1
            continue

        logger.info(f"Processing ProductImage {img.id}: {old_path} -> {new_path}")

        if dry_run:
            logger.info(f"[DRY RUN] Would update ProductImage {img.id} path to {new_path}")
            fixed_count += 1
            continue

        try:
            # Update the path directly in the database
            # This avoids triggering any file operations
            ProductImage.objects.filter(id=img.id).update(image=new_path)
            logger.info(f"Successfully updated ProductImage {img.id} path to {new_path}")
            fixed_count += 1
        except Exception as e:
            logger.error(f"Error updating ProductImage {img.id}: {e}")
            error_count += 1

    # Log summary for ProductImage
    logger.info(f"ProductImage fix summary: {all_product_images.count()} total records, {fixed_count} fixed, {skipped_count} skipped, {error_count} errors")

    return fixed_count, error_count

def fix_brand_image_paths(dry_run=True):
    """
    Fix Brand image paths in the database by removing duplicate 'media/' prefixes
    and ensuring correct path format

    Args:
        dry_run: If True, only show what would be done without making changes
    """
    # Get all Brand records with images
    all_brands = Brand.objects.filter(
        ~Q(image=''),  # Exclude empty images
        ~Q(image__isnull=True)  # Exclude null images
    )

    logger.info(f"Total Brand records with images: {all_brands.count()}")

    # Process Brand records
    fixed_count = 0
    error_count = 0
    skipped_count = 0

    for brand in all_brands:
        old_path = brand.image.name
        new_path = old_path
        needs_fix = False

        # Check for duplicate 'media/' prefix
        if old_path.startswith('media/media/'):
            new_path = old_path[6:]  # Remove the first 'media/' prefix
            needs_fix = True
        # Check for single 'media/' prefix that should be removed
        elif old_path.startswith('media/'):
            # If the path is like 'media/brands/...' we want to make it 'brands/...'
            if old_path.startswith('media/brands/'):
                new_path = old_path[6:]  # Remove the 'media/' prefix
                needs_fix = True

        if not needs_fix:
            skipped_count += 1
            continue

        logger.info(f"Processing Brand {brand.id}: {old_path} -> {new_path}")

        if dry_run:
            logger.info(f"[DRY RUN] Would update Brand {brand.id} path to {new_path}")
            fixed_count += 1
            continue

        try:
            # Update the path directly in the database
            # This avoids triggering any file operations
            Brand.objects.filter(id=brand.id).update(image=new_path)
            logger.info(f"Successfully updated Brand {brand.id} path to {new_path}")
            fixed_count += 1
        except Exception as e:
            logger.error(f"Error updating Brand {brand.id}: {e}")
            error_count += 1

    # Log summary for Brand
    logger.info(f"Brand fix summary: {all_brands.count()} total records, {fixed_count} fixed, {skipped_count} skipped, {error_count} errors")

    return fixed_count, error_count

def fix_all_image_paths(dry_run=True):
    """
    Fix all image paths in the database

    Args:
        dry_run: If True, only show what would be done without making changes
    """
    # Fix ProductImage paths
    product_fixed, product_errors = fix_product_image_paths(dry_run)

    # Fix Brand image paths
    brand_fixed, brand_errors = fix_brand_image_paths(dry_run)

    # Log overall summary
    total_fixed = product_fixed + brand_fixed
    total_errors = product_errors + brand_errors

    logger.info(f"Overall fix summary: {total_fixed} records fixed, {total_errors} errors")
    if dry_run:
        logger.info("This was a dry run. No changes were made. Run with --live to apply changes.")

if __name__ == '__main__':
    parser = argparse.ArgumentParser(description='Fix image paths in the database by removing duplicate media/ prefixes')
    parser.add_argument('--live', action='store_true', help='Apply changes (default is dry run)')

    args = parser.parse_args()

    try:
        logger.info("Starting database image path fix")
        fix_all_image_paths(dry_run=not args.live)
        logger.info("Database image path fix completed")
    except Exception as e:
        logger.error(f"Error during database image path fix: {str(e)}")
        sys.exit(1)
