from django.contrib import admin
from django.utils.html import format_html
from django.urls import reverse
from django.utils.safestring import mark_safe
from .models import (
    RapidshypConfiguration, 
    RapidshypShipment, 
    ShippingRateCache, 
    TrackingEvent,
    RapidshypAPILog
)


@admin.register(RapidshypConfiguration)
class RapidshypConfigurationAdmin(admin.ModelAdmin):
    list_display = ['store_name', 'pickup_address_name', 'default_pickup_pincode', 
                   'contact_name', 'is_active', 'created_at']
    list_filter = ['is_active', 'created_at']
    search_fields = ['store_name', 'pickup_address_name', 'contact_name', 'contact_email']
    readonly_fields = ['created_at', 'updated_at']
    
    fieldsets = (
        ('Store Information', {
            'fields': ('store_name', 'pickup_address_name', 'is_active')
        }),
        ('Contact Details', {
            'fields': ('contact_name', 'contact_phone', 'contact_email')
        }),
        ('Address Information', {
            'fields': ('default_pickup_pincode', 'address_line_1', 'address_line_2')
        }),
        ('Timestamps', {
            'fields': ('created_at', 'updated_at'),
            'classes': ('collapse',)
        }),
    )


class TrackingEventInline(admin.TabularInline):
    model = TrackingEvent
    extra = 0
    readonly_fields = ['status', 'status_description', 'location', 'event_timestamp', 'created_at']
    can_delete = False
    
    def has_add_permission(self, request, obj=None):
        return False


@admin.register(RapidshypShipment)
class RapidshypShipmentAdmin(admin.ModelAdmin):
    list_display = ['rapidshyp_order_id', 'order_link', 'courier_name', 'current_status', 
                   'awb_number', 'total_freight', 'created_at']
    list_filter = ['current_status', 'courier_name', 'pickup_scheduled', 'created_at']
    search_fields = ['rapidshyp_order_id', 'awb_number', 'order__id', 'courier_name']
    readonly_fields = ['id', 'order_link', 'created_at', 'updated_at', 'tracking_url_link']
    inlines = [TrackingEventInline]
    
    fieldsets = (
        ('Order Information', {
            'fields': ('order_link', 'rapidshyp_order_id', 'shipment_id')
        }),
        ('Courier Details', {
            'fields': ('courier_code', 'courier_name', 'parent_courier_name', 'awb_number')
        }),
        ('Status & Tracking', {
            'fields': ('current_status', 'status_description', 'tracking_url_link')
        }),
        ('Shipping Details', {
            'fields': ('total_freight', 'freight_mode', 'cutoff_time', 'min_weight', 'max_weight')
        }),
        ('Pickup & Delivery', {
            'fields': ('pickup_scheduled', 'pickup_date', 'expected_delivery_date', 'actual_delivery_date')
        }),
        ('Documents', {
            'fields': ('label_url', 'invoice_url', 'manifest_url'),
            'classes': ('collapse',)
        }),
        ('Raw Data', {
            'fields': ('rapidshyp_response_data',),
            'classes': ('collapse',)
        }),
        ('Timestamps', {
            'fields': ('id', 'created_at', 'updated_at'),
            'classes': ('collapse',)
        }),
    )
    
    def order_link(self, obj):
        """Create a link to the related order"""
        if obj.order:
            url = reverse('admin:orders_order_change', args=[obj.order.id])
            return format_html('<a href="{}">{}</a>', url, obj.order.id)
        return '-'
    order_link.short_description = 'Order'
    
    def tracking_url_link(self, obj):
        """Create a clickable tracking URL"""
        if obj.tracking_url:
            return format_html('<a href="{}" target="_blank">Track Shipment</a>', obj.tracking_url)
        return '-'
    tracking_url_link.short_description = 'Tracking URL'
    
    actions = ['sync_tracking_status']
    
    def sync_tracking_status(self, request, queryset):
        """Admin action to sync tracking status for selected shipments"""
        updated_count = 0
        for shipment in queryset:
            try:
                # Import here to avoid circular imports
                from .services.shipping_service import ShippingService
                service = ShippingService()
                service.sync_tracking_status(shipment)
                updated_count += 1
            except Exception as e:
                self.message_user(request, f"Error syncing {shipment.rapidshyp_order_id}: {e}", 
                                level='ERROR')
        
        if updated_count:
            self.message_user(request, f"Successfully synced {updated_count} shipments.")
    
    sync_tracking_status.short_description = "Sync tracking status for selected shipments"


@admin.register(ShippingRateCache)
class ShippingRateCacheAdmin(admin.ModelAdmin):
    list_display = ['pickup_pincode', 'delivery_pincode', 'weight', 'cod', 
                   'is_serviceable', 'cached_at', 'expires_at', 'is_expired_display']
    list_filter = ['is_serviceable', 'cod', 'cached_at']
    search_fields = ['pickup_pincode', 'delivery_pincode']
    readonly_fields = ['cached_at', 'is_expired_display']
    
    def is_expired_display(self, obj):
        """Display if cache entry is expired"""
        if obj.is_expired:
            return format_html('<span style="color: red;">Expired</span>')
        return format_html('<span style="color: green;">Valid</span>')
    is_expired_display.short_description = 'Status'
    
    actions = ['clear_expired_cache']
    
    def clear_expired_cache(self, request, queryset):
        """Admin action to clear expired cache entries"""
        expired_entries = queryset.filter(expires_at__lt=timezone.now())
        count = expired_entries.count()
        expired_entries.delete()
        self.message_user(request, f"Cleared {count} expired cache entries.")
    
    clear_expired_cache.short_description = "Clear expired cache entries"


@admin.register(TrackingEvent)
class TrackingEventAdmin(admin.ModelAdmin):
    list_display = ['shipment_order_id', 'status', 'status_description', 
                   'location', 'event_timestamp', 'created_at']
    list_filter = ['status', 'event_timestamp', 'created_at']
    search_fields = ['shipment__rapidshyp_order_id', 'shipment__order__id', 
                    'status', 'location']
    readonly_fields = ['created_at']
    
    def shipment_order_id(self, obj):
        """Display shipment order ID"""
        return obj.shipment.rapidshyp_order_id
    shipment_order_id.short_description = 'Shipment ID'


@admin.register(RapidshypAPILog)
class RapidshypAPILogAdmin(admin.ModelAdmin):
    list_display = ['method', 'endpoint', 'is_success', 'response_status_code', 
                   'response_time_ms', 'created_at']
    list_filter = ['method', 'is_success', 'response_status_code', 'created_at']
    search_fields = ['method', 'endpoint', 'error_message']
    readonly_fields = ['created_at']
    
    fieldsets = (
        ('Request Information', {
            'fields': ('method', 'endpoint', 'request_data')
        }),
        ('Response Information', {
            'fields': ('response_data', 'response_status_code', 'response_time_ms', 'is_success')
        }),
        ('Error Information', {
            'fields': ('error_message',),
            'classes': ('collapse',)
        }),
        ('Timestamp', {
            'fields': ('created_at',)
        }),
    )
    
    def has_add_permission(self, request):
        """Prevent manual addition of API logs"""
        return False
    
    def has_change_permission(self, request, obj=None):
        """Make API logs read-only"""
        return False
    
    actions = ['clear_old_logs']
    
    def clear_old_logs(self, request, queryset):
        """Admin action to clear logs older than 30 days"""
        from django.utils import timezone
        from datetime import timedelta
        
        thirty_days_ago = timezone.now() - timedelta(days=30)
        old_logs = RapidshypAPILog.objects.filter(created_at__lt=thirty_days_ago)
        count = old_logs.count()
        old_logs.delete()
        self.message_user(request, f"Cleared {count} old API logs.")
    
    clear_old_logs.short_description = "Clear logs older than 30 days"
