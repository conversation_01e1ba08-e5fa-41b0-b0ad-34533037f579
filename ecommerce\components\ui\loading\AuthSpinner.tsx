import React from 'react';

interface AuthSpinnerProps {
  size?: 'sm' | 'md' | 'lg';
  color?: string;
}

const AuthSpinner = ({ 
  size = 'md', 
  color = 'border-t-blue-600' 
}: AuthSpinnerProps) => {
  const sizeClasses = {
    sm: 'w-4 h-4',
    md: 'w-6 h-6',
    lg: 'w-8 h-8'
  };

  return (
    <div className={`${sizeClasses[size]} rounded-full border-2 border-gray-200 ${color} animate-spin`}></div>
  );
};

export default AuthSpinner;
