#!/bin/bash
# Rollback script for encryption deployment
# Use this script if encryption deployment needs to be reverted

set -e  # Exit on any error

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Logging function
log() {
    echo -e "${BLUE}[$(date +'%Y-%m-%d %H:%M:%S')]${NC} $1"
}

success() {
    echo -e "${GREEN}[$(date +'%Y-%m-%d %H:%M:%S')] ✅ $1${NC}"
}

warning() {
    echo -e "${YELLOW}[$(date +'%Y-%m-%d %H:%M:%S')] ⚠️  $1${NC}"
}

error() {
    echo -e "${RED}[$(date +'%Y-%m-%d %H:%M:%S')] ❌ $1${NC}"
}

# Configuration
BACKUP_DIR="./backups"

warning "⚠️  ENCRYPTION ROLLBACK PROCEDURE"
warning "This will revert all encryption changes and restore from backup"

# Check if running in production
if [[ "${ENVIRONMENT}" == "production" ]]; then
    error "🚨 PRODUCTION ROLLBACK DETECTED"
    warning "This action will:"
    warning "  - Restore database from backup"
    warning "  - Revert all encryption migrations"
    warning "  - Remove encrypted field changes"
    echo ""
    read -p "Are you absolutely sure you want to proceed? Type 'ROLLBACK' to confirm: " confirm
    if [[ $confirm != "ROLLBACK" ]]; then
        error "Rollback cancelled by user"
        exit 1
    fi
fi

log "🔄 Starting encryption rollback procedure..."

# Step 1: Find the most recent backup
log "🔍 Looking for database backup..."

if [[ ! -d "${BACKUP_DIR}" ]]; then
    error "Backup directory not found: ${BACKUP_DIR}"
    exit 1
fi

# Find the most recent backup file
BACKUP_FILE=$(ls -t ${BACKUP_DIR}/backup_before_encryption_*.sql 2>/dev/null | head -1)

if [[ -z "${BACKUP_FILE}" ]]; then
    error "No encryption backup file found in ${BACKUP_DIR}"
    error "Expected format: backup_before_encryption_YYYYMMDD_HHMMSS.sql"
    exit 1
fi

log "📁 Found backup file: ${BACKUP_FILE}"

# Step 2: Confirm backup file
read -p "Use this backup file? (yes/no): " use_backup
if [[ $use_backup != "yes" ]]; then
    log "Please specify the backup file path:"
    read -p "Backup file path: " BACKUP_FILE
    
    if [[ ! -f "${BACKUP_FILE}" ]]; then
        error "Backup file not found: ${BACKUP_FILE}"
        exit 1
    fi
fi

# Step 3: Create a pre-rollback backup
log "💾 Creating pre-rollback backup..."
TIMESTAMP=$(date +%Y%m%d_%H%M%S)
PRE_ROLLBACK_BACKUP="${BACKUP_DIR}/pre_rollback_backup_${TIMESTAMP}.sql"

if [[ "${DB_ENGINE}" == *"postgresql"* ]]; then
    pg_dump "${DATABASE_URL}" > "${PRE_ROLLBACK_BACKUP}"
    if [[ $? -eq 0 ]]; then
        success "Pre-rollback backup created: ${PRE_ROLLBACK_BACKUP}"
    else
        error "Pre-rollback backup failed"
        exit 1
    fi
fi

# Step 4: Stop application services (if applicable)
log "🛑 Stopping application services..."
# Add your service stop commands here
# systemctl stop your-app-service
warning "Manual step: Stop your application services if needed"

# Step 5: Restore database from backup
log "🔄 Restoring database from backup..."
warning "This will overwrite all current data!"

read -p "Proceed with database restore? (yes/no): " proceed_restore
if [[ $proceed_restore != "yes" ]]; then
    error "Database restore cancelled"
    exit 1
fi

if [[ "${DB_ENGINE}" == *"postgresql"* ]]; then
    # Drop and recreate database (be very careful!)
    log "⚠️  Dropping current database..."
    dropdb "${DB_NAME}" --if-exists
    
    log "🆕 Creating fresh database..."
    createdb "${DB_NAME}"
    
    log "📥 Restoring from backup..."
    psql "${DATABASE_URL}" < "${BACKUP_FILE}"
    
    if [[ $? -eq 0 ]]; then
        success "Database restored successfully"
    else
        error "Database restore failed"
        exit 1
    fi
else
    error "Non-PostgreSQL database detected. Manual restore required."
    error "Please restore your database manually from: ${BACKUP_FILE}"
    exit 1
fi

# Step 6: Reset Django migrations
log "🔄 Resetting Django migrations..."

# Find the last migration before encryption
LAST_MIGRATION=$(python manage.py showmigrations users --plan | grep -B10 "add_encrypted_fields" | tail -2 | head -1 | awk '{print $2}')

if [[ -n "${LAST_MIGRATION}" ]]; then
    log "📝 Rolling back to migration: ${LAST_MIGRATION}"
    python manage.py migrate users ${LAST_MIGRATION}
    
    if [[ $? -eq 0 ]]; then
        success "Migration rollback completed"
    else
        error "Migration rollback failed"
        exit 1
    fi
else
    warning "Could not determine last migration before encryption"
    warning "You may need to manually specify the migration to rollback to"
fi

# Step 7: Remove encryption migration files
log "🗑️  Removing encryption migration files..."
find . -name "*add_encrypted_fields*" -type f -delete
find . -name "*encryption*" -path "*/migrations/*" -type f -delete

success "Encryption migration files removed"

# Step 8: Validate rollback
log "✅ Validating rollback..."

# Check database connectivity
python manage.py check --database default
if [[ $? -ne 0 ]]; then
    error "Database connectivity check failed after rollback"
    exit 1
fi

# Run Django checks
python manage.py check
if [[ $? -ne 0 ]]; then
    error "Django checks failed after rollback"
    exit 1
fi

success "Rollback validation passed"

# Step 9: Start application services (if applicable)
log "🚀 Starting application services..."
# Add your service start commands here
# systemctl start your-app-service
warning "Manual step: Start your application services"

success "🎉 Encryption rollback completed successfully!"

log "📊 Rollback Summary:"
log "   - Database restored from: ${BACKUP_FILE}"
log "   - Pre-rollback backup saved: ${PRE_ROLLBACK_BACKUP}"
log "   - Migrations rolled back"
log "   - Encryption files removed"
log "   - System validation passed"

warning "📝 Post-rollback tasks:"
warning "   1. Test application functionality"
warning "   2. Monitor application logs"
warning "   3. Update team on rollback completion"
warning "   4. Review rollback reasons for future improvements"

log "🔓 Database encryption has been successfully removed"
