/*
  Product Card styles have been moved to Tailwind classes in the ProductCard component.
  This file is kept for backward compatibility but is no longer used for styling product cards.
*/

/* Product Grid Layout */
.product-grid {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(250px, 1fr));
  gap: 1.5rem;
}

@media (max-width: 640px) {
  .product-grid {
    grid-template-columns: repeat(auto-fill, minmax(150px, 1fr));
    gap: 1rem;
  }
}

/* Category Tabs */
.category-tabs {
  display: flex;
  flex-wrap: nowrap;
  overflow-x: auto;
  gap: 0.5rem;
  padding-bottom: 0.5rem;
  scrollbar-width: none; /* Firefox */
}

.category-tabs::-webkit-scrollbar {
  display: none; /* Chrome, Safari, Edge */
}

.category-tab {
  white-space: nowrap;
  padding: 0.5rem 1rem;
  border-radius: 9999px;
  font-weight: 500;
  transition: all 0.3s ease-in-out;
}

/* Decorative elements */
.decorative-circle {
  position: absolute;
  border-radius: 50%;
  opacity: 0.5;
  filter: blur(30px);
  z-index: 0;
}

/* Animations */
@keyframes spin {
  from {
    transform: rotate(0deg);
  }
  to {
    transform: rotate(360deg);
  }
}

@keyframes fadeIn {
  from {
    opacity: 0;
  }
  to {
    opacity: 1;
  }
}

@keyframes slideUp {
  from {
    opacity: 0;
    transform: translateY(20px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

/* Utility classes for scrollbar hiding */
.scrollbar-hide {
  -ms-overflow-style: none;  /* IE and Edge */
  scrollbar-width: none;  /* Firefox */
}

.scrollbar-hide::-webkit-scrollbar {
  display: none; /* Chrome, Safari, Opera */
}

/* Responsive adjustments have been moved to Tailwind classes in the ProductCard component */

/* Smart Home specific styles */
.smart-home-hero {
  position: relative;
  overflow: hidden;
}

.smart-home-product-badge {
  position: absolute;
  background: white;
  padding: 0.5rem;
  border-radius: 0.5rem;
  box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06);
  z-index: 10;
}

.smart-home-product-badge.top-right {
  top: 25%;
  right: -1rem;
  transform: rotate(6deg);
}

.smart-home-product-badge.bottom-left {
  bottom: 25%;
  left: -1rem;
  transform: rotate(-6deg);
}
