"use client";

import { <PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON> } from "@/components/ui/card";
import { Skeleton } from "@/components/ui/skeleton";
import { Badge } from "@/components/ui/badge";
import {
  <PERSON>Axis,
  YAxis,
  CartesianGrid,
  Tooltip,
  ResponsiveContainer,
  BarChart,
  Bar,
  AreaChart,
  Area,
} from "recharts";
import { TrendingUp, BarChart3 } from "lucide-react";

interface AnalyticsData {
  product_performance: Array<{
    id: number;
    name: string;
    sales: number;
    revenue: number;
    growth: string;
  }>;
  sales_chart: Array<{
    name: string;
    sales: number;
  }>;
  customer_demographics: Array<{
    name: string;
    value: number;
  }>;
}

interface GraphData {
  salesData: Array<{
    name: string;
    value: number;
  }>;
  productData: Array<{
    name: string;
    sales: number;
  }>;
}

interface AnalyticsChartsProps {
  analyticsData: AnalyticsData | null;
  graphData: GraphData | null;
  loading: boolean;
  error: string | null;
}


// eslint-disable-next-line @typescript-eslint/no-explicit-any
const CustomTooltip = ({ active, payload, label }: any) => {
  if (active && payload && payload.length) {
    return (
      <div className="bg-white p-3 border border-gray-200 rounded-lg shadow-lg">
        <p className="font-medium text-gray-900">{label}</p>
        {/* eslint-disable-next-line @typescript-eslint/no-explicit-any */}
        {payload.map((entry: any, index: number) => (
          <p key={index} className="text-sm" style={{ color: entry.color }}>
            {entry.name}: {typeof entry.value === 'number' ?
              entry.name.toLowerCase().includes('revenue') || entry.name.toLowerCase().includes('sales') ?
                `Qty-${entry.value.toLocaleString('en-IN')}` :
                entry.value.toLocaleString('en-IN')
              : entry.value}
          </p>
        ))}
      </div>
    );
  }
  return null;
};

const ChartSkeleton = () => (
  <div className="h-[300px] flex items-center justify-center">
    <div className="space-y-3 w-full">
      <Skeleton className="h-4 w-3/4" />
      <Skeleton className="h-4 w-1/2" />
      <Skeleton className="h-4 w-2/3" />
      <Skeleton className="h-4 w-1/3" />
    </div>
  </div>
);

export const AnalyticsCharts: React.FC<AnalyticsChartsProps> = ({
  analyticsData,
  graphData,
  loading,
  error
}) => {
  // Debug: Log the data to see what we're getting
  console.log('AnalyticsCharts - graphData:', graphData);
  console.log('AnalyticsCharts - salesData:', graphData?.salesData);
  console.log('AnalyticsCharts - loading:', loading);
  console.log('AnalyticsCharts - error:', error);

  // Fallback data for demonstration
  const fallbackSalesData = [
    { name: "Jan", value: 3150000 },
    { name: "Feb", value: 3640000 },
    { name: "Mar", value: 3360000 },
    { name: "Apr", value: 4270000 },
    { name: "May", value: 3850000 },
    { name: "Jun", value: 4690000 },
  ];

  // Use actual data if available, otherwise use fallback
  const salesData = graphData?.salesData && graphData.salesData.length > 0
    ? graphData.salesData
    : fallbackSalesData;

  if (error) {
    return (
      <Card className="border-red-200 bg-red-50/50">
        <CardContent className="pt-6">
          <div className="text-center text-red-600">
            <p className="text-sm">Failed to load analytics data</p>
            <p className="text-xs mt-1">{error}</p>
          </div>
        </CardContent>
      </Card>
    );
  }

  return (
    <div className="space-y-6">
      {/* Revenue Over Time - Full width */}
      <Card className="border-0 shadow-lg bg-gradient-to-br from-white to-blue-50/30">
        <CardHeader className="flex flex-col sm:flex-row sm:items-center sm:justify-between space-y-2 sm:space-y-0">
          <div className="space-y-1">
            <CardTitle className="flex items-center gap-2 text-gray-900 text-lg sm:text-xl">
              <TrendingUp className="h-4 w-4 sm:h-5 sm:w-5 text-blue-500" />
              <span className="truncate">Revenue Over Time</span>
            </CardTitle>
            <p className="text-xs sm:text-sm text-muted-foreground">
              Monthly revenue trends and growth patterns
            </p>
          </div>
          <div className="flex items-center gap-2">
            <Badge variant="secondary" className="bg-blue-100 text-blue-700 text-xs sm:text-sm">
              Last 6 Months
            </Badge>
            <Badge variant="outline" className="text-xs">
              {salesData.length} data points
            </Badge>
            {!graphData?.salesData && (
              <Badge variant="outline" className="text-xs text-orange-600">
                Demo Data
              </Badge>
            )}
          </div>
        </CardHeader>
        <CardContent className="px-3 sm:px-6">
          {loading ? (
            <ChartSkeleton />
          ) : (
            <div className="h-[300px] sm:h-[400px] w-full">
              <ResponsiveContainer width="100%" height="100%">
                <AreaChart
                  data={salesData}
                  margin={{ top: 10, right: 30, left: 20, bottom: 5 }}
                >
                  <defs>
                    <linearGradient id="colorRevenue" x1="0" y1="0" x2="0" y2="1">
                      <stop offset="5%" stopColor="#4F46E5" stopOpacity={0.3}/>
                      <stop offset="95%" stopColor="#4F46E5" stopOpacity={0}/>
                    </linearGradient>
                  </defs>
                  <CartesianGrid strokeDasharray="3 3" stroke="#E5E7EB" />
                  <XAxis
                    dataKey="name"
                    stroke="#6B7280"
                    fontSize={12}
                    tickLine={false}
                    axisLine={false}
                  />
                  <YAxis
                    stroke="#6B7280"
                    fontSize={12}
                    tickLine={false}
                    axisLine={false}
                    tickFormatter={(value) => `₹${(value/1000).toFixed(0)}k`}
                    width={80}
                  />
                  <Tooltip content={<CustomTooltip />} />
                  <Area
                    type="monotone"
                    dataKey="value"
                    stroke="#4F46E5"
                    strokeWidth={3}
                    fill="url(#colorRevenue)"
                  />
                </AreaChart>
              </ResponsiveContainer>
            </div>
          )}
        </CardContent>
      </Card>

      {/* Top Products Performance - Full width */}
      <Card className="border-0 shadow-lg bg-gradient-to-br from-white to-green-50/30">
        <CardHeader className="pb-3">
          <CardTitle className="flex items-center gap-2 text-gray-900 text-lg sm:text-xl">
            <BarChart3 className="h-4 w-4 sm:h-5 sm:w-5 text-green-500" />
            <span className="truncate">Top Products Performance</span>
          </CardTitle>
          <p className="text-xs sm:text-sm text-muted-foreground">
            Best performing products by sales and revenue
          </p>
        </CardHeader>
        <CardContent className="px-3 sm:px-6">
          {loading ? (
            <ChartSkeleton />
          ) : (
            <div className="h-[300px] sm:h-[350px] w-full">
              <ResponsiveContainer width="100%" height="100%">
                <BarChart
                  data={analyticsData?.product_performance?.slice(0, 6) || []}
                  margin={{ top: 5, right: 5, left: 5, bottom: 60 }}
                >
                  <CartesianGrid strokeDasharray="3 3" stroke="#E5E7EB" />
                  <XAxis
                    dataKey="name"
                    stroke="#6B7280"
                    fontSize={10}
                    tickLine={false}
                    axisLine={false}
                    angle={-45}
                    textAnchor="end"
                    height={60}
                    interval={0}
                  />
                  <YAxis
                    stroke="#6B7280"
                    fontSize={10}
                    tickLine={false}
                    axisLine={false}
                    width={60}
                  />
                  <Tooltip content={<CustomTooltip />} />
                  <Bar
                    dataKey="sales"
                    fill="#10B981"
                    radius={[2, 2, 0, 0]}
                    name="Sales"
                  />
                  <Bar
                    dataKey="revenue"
                    fill="#3B82F6"
                    radius={[2, 2, 0, 0]}
                    name="Revenue"
                  />
                </BarChart>
              </ResponsiveContainer>
            </div>
          )}
        </CardContent>
      </Card>
    </div>
  );
};
