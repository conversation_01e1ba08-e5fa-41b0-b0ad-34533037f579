"""
Management command to backup primary database to JSON format.

This command creates a complete backup of the primary database in JSON format,
preserving all IDs and relationships for exact replica restoration.
"""

import os
import json
from datetime import datetime
from django.core.management.base import BaseCommand, CommandError
from django.core import serializers
from django.apps import apps
from django.conf import settings
from django.db import transaction
from products.models import Product, Category, Brand, SubCategorie, GST, ProductImage, ProductVariant
from users.models import Customer, Address
from orders.models import Order, OrderItem, Cart, CartItem, ShippingMethod
from promotions.models import Promotion, PromotionUsage


class Command(BaseCommand):
    help = 'Backup primary database to JSON format with exact ID preservation'

    def add_arguments(self, parser):
        parser.add_argument(
            '--output',
            type=str,
            help='Output file path (default: backup_YYYYMMDD_HHMMSS.json)',
        )
        parser.add_argument(
            '--models',
            type=str,
            help='Comma-separated list of models to backup (default: all)',
        )
        parser.add_argument(
            '--exclude-users',
            action='store_true',
            help='Exclude user data from backup (for privacy)',
        )
        parser.add_argument(
            '--exclude-orders',
            action='store_true',
            help='Exclude order data from backup (for privacy)',
        )
        parser.add_argument(
            '--compress',
            action='store_true',
            help='Compress the backup file',
        )

    def handle(self, *args, **options):
        self.stdout.write(
            self.style.SUCCESS('Primary Database Backup Tool')
        )
        self.stdout.write('=' * 50)

        # Generate output filename if not provided
        if not options['output']:
            timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
            options['output'] = f'backup_{timestamp}.json'

        # Determine models to backup
        models_to_backup = self.get_models_to_backup(options)

        # Create backup
        backup_data = self.create_backup(models_to_backup, options)

        # Save backup to file
        self.save_backup(backup_data, options['output'], options['compress'])

        self.stdout.write(
            self.style.SUCCESS(f'Backup completed: {options["output"]}')
        )

    def get_models_to_backup(self, options):
        """Determine which models to backup"""
        
        # Default models for e-commerce data
        default_models = [
            # Product catalog
            (Category, 'products.Category'),
            (SubCategorie, 'products.SubCategorie'),
            (Brand, 'products.Brand'),
            (GST, 'products.GST'),
            (Product, 'products.Product'),
            (ProductImage, 'products.ProductImage'),
            (ProductVariant, 'products.ProductVariant'),
            
            # Promotions
            (Promotion, 'promotions.Promotion'),
            (PromotionUsage, 'promotions.PromotionUsage'),
            
            # Shipping
            (ShippingMethod, 'orders.ShippingMethod'),
        ]

        # Add user data if not excluded
        if not options['exclude_users']:
            default_models.extend([
                (Customer, 'users.Customer'),
                (Address, 'orders.Address'),
            ])

        # Add order data if not excluded
        if not options['exclude_orders']:
            default_models.extend([
                (Cart, 'orders.Cart'),
                (CartItem, 'orders.CartItem'),
                (Order, 'orders.Order'),
                (OrderItem, 'orders.OrderItem'),
            ])
        else:
            # If orders are excluded, also exclude PromotionUsage which depends on orders
            self.stdout.write('   ⚠️  Excluding PromotionUsage due to order dependency')
            # Remove PromotionUsage from the list
            default_models = [(model, name) for model, name in default_models
                            if name != 'promotions.PromotionUsage']

        # Filter by specific models if requested
        if options['models']:
            requested_models = [m.strip().lower() for m in options['models'].split(',')]
            filtered_models = []
            
            for model_class, model_name in default_models:
                if model_class.__name__.lower() in requested_models:
                    filtered_models.append((model_class, model_name))
            
            return filtered_models

        return default_models

    def create_backup(self, models_to_backup, options):
        """Create backup data preserving IDs and relationships"""
        self.stdout.write('\n📦 Creating backup...')
        
        backup_data = {
            'metadata': {
                'created_at': datetime.now().isoformat(),
                'django_version': getattr(settings, 'DJANGO_VERSION', 'unknown'),
                'database_engine': settings.DATABASES['default']['ENGINE'],
                'backup_version': '1.0',
                'models_included': [],
            },
            'data': []
        }

        total_records = 0

        for model_class, model_name in models_to_backup:
            self.stdout.write(f'  📋 Backing up {model_name}...')
            
            try:
                # Get all objects from primary database
                queryset = model_class.objects.all().order_by('pk')
                count = queryset.count()
                
                if count > 0:
                    # Serialize objects preserving IDs
                    serialized_data = serializers.serialize(
                        'json', 
                        queryset,
                        use_natural_foreign_keys=False,
                        use_natural_primary_keys=False
                    )
                    
                    # Parse and add to backup
                    model_data = json.loads(serialized_data)
                    
                    backup_data['data'].extend(model_data)
                    backup_data['metadata']['models_included'].append({
                        'model': model_name,
                        'count': count
                    })
                    
                    total_records += count
                    self.stdout.write(f'    ✅ {count} records backed up')
                else:
                    self.stdout.write(f'    ⚠️  No records found')
                    
            except Exception as e:
                self.stdout.write(
                    self.style.ERROR(f'    ❌ Error backing up {model_name}: {e}')
                )

        backup_data['metadata']['total_records'] = total_records
        
        self.stdout.write(f'\n📊 Backup Summary:')
        self.stdout.write(f'   Total records: {total_records}')
        self.stdout.write(f'   Models included: {len(backup_data["metadata"]["models_included"])}')
        
        return backup_data

    def save_backup(self, backup_data, output_file, compress):
        """Save backup data to file"""
        self.stdout.write(f'\n💾 Saving backup to {output_file}...')
        
        try:
            if compress:
                import gzip
                
                with gzip.open(output_file + '.gz', 'wt', encoding='utf-8') as f:
                    json.dump(backup_data, f, indent=2, ensure_ascii=False)
                
                output_file = output_file + '.gz'
                self.stdout.write(f'   ✅ Compressed backup saved: {output_file}')
            else:
                with open(output_file, 'w', encoding='utf-8') as f:
                    json.dump(backup_data, f, indent=2, ensure_ascii=False)
                
                self.stdout.write(f'   ✅ Backup saved: {output_file}')
            
            # Show file size
            file_size = os.path.getsize(output_file)
            size_mb = file_size / (1024 * 1024)
            self.stdout.write(f'   📏 File size: {size_mb:.2f} MB')
            
        except Exception as e:
            raise CommandError(f'Failed to save backup: {e}')

    def show_backup_info(self, backup_file):
        """Show information about a backup file"""
        self.stdout.write(f'\n📋 Backup File Information: {backup_file}')
        self.stdout.write('-' * 50)
        
        try:
            # Handle compressed files
            if backup_file.endswith('.gz'):
                import gzip
                with gzip.open(backup_file, 'rt', encoding='utf-8') as f:
                    backup_data = json.load(f)
            else:
                with open(backup_file, 'r', encoding='utf-8') as f:
                    backup_data = json.load(f)
            
            metadata = backup_data.get('metadata', {})
            
            self.stdout.write(f'Created: {metadata.get("created_at", "Unknown")}')
            self.stdout.write(f'Total records: {metadata.get("total_records", 0)}')
            self.stdout.write(f'Django version: {metadata.get("django_version", "Unknown")}')
            
            self.stdout.write('\nModels included:')
            for model_info in metadata.get('models_included', []):
                self.stdout.write(f'  {model_info["model"]}: {model_info["count"]} records')
                
        except Exception as e:
            self.stdout.write(self.style.ERROR(f'Error reading backup file: {e}'))
