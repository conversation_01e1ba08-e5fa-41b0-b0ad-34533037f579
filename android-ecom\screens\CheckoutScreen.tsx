// import { View, Text, StyleSheet, ScrollView, TouchableOpacity, TextInput } from 'react-native';
// import { SafeAreaView } from 'react-native-safe-area-context';
// import { MaterialIcons } from '@expo/vector-icons';

// export default function CheckoutScreen({ navigation }) {
//   return (
//     <SafeAreaView style={styles.container}>
//       <View style={styles.header}>
//         <TouchableOpacity onPress={() => navigation.goBack()} style={styles.backButton}>
//           <MaterialIcons name="arrow-back" size={24} color="#333" />
//         </TouchableOpacity>
//         <Text style={styles.title}>Checkout</Text>
//         <View style={{ width: 24 }} />
//       </View>

//       <ScrollView style={styles.content}>
//         <View style={styles.section}>
//           <Text style={styles.sectionTitle}>Shipping Address</Text>
//           <View style={styles.card}>
//             <TextInput
//               style={styles.input}
//               placeholder="Full Name"
//               placeholderTextColor="#666"
//             />
//             <TextInput
//               style={styles.input}
//               placeholder="Address Line 1"
//               placeholderTextColor="#666"
//             />
//             <TextInput
//               style={styles.input}
//               placeholder="Address Line 2"
//               placeholderTextColor="#666"
//             />
//             <View style={styles.row}>
//               <TextInput
//                 style={[styles.input, styles.halfInput]}
//                 placeholder="City"
//                 placeholderTextColor="#666"
//               />
//               <TextInput
//                 style={[styles.input, styles.halfInput]}
//                 placeholder="ZIP Code"
//                 placeholderTextColor="#666"
//               />
//             </View>
//           </View>
//         </View>

//         <View style={styles.section}>
//           <Text style={styles.sectionTitle}>Payment Method</Text>
//           <View style={styles.card}>
//             <TouchableOpacity style={styles.paymentOption}>
//               <MaterialIcons name="credit-card" size={24} color="#2563EB" />
//               <Text style={styles.paymentText}>Credit/Debit Card</Text>
//               <MaterialIcons name="chevron-right" size={24} color="#666" />
//             </TouchableOpacity>

//             <TouchableOpacity style={styles.paymentOption}>
//               <MaterialIcons name="account-balance" size={24} color="#2563EB" />
//               <Text style={styles.paymentText}>Bank Transfer</Text>
//               <MaterialIcons name="chevron-right" size={24} color="#666" />
//             </TouchableOpacity>
//           </View>
//         </View>

//         <View style={styles.section}>
//           <Text style={styles.sectionTitle}>Order Summary</Text>
//           <View style={styles.card}>
//             <View style={styles.summaryRow}>
//               <Text style={styles.summaryLabel}>Subtotal</Text>
//               <Text style={styles.summaryValue}>$699.98</Text>
//             </View>
//             <View style={styles.summaryRow}>
//               <Text style={styles.summaryLabel}>Shipping</Text>
//               <Text style={styles.summaryValue}>$10.00</Text>
//             </View>
//             <View style={styles.summaryRow}>
//               <Text style={styles.summaryLabel}>Tax</Text>
//               <Text style={styles.summaryValue}>$70.00</Text>
//             </View>
//             <View style={[styles.summaryRow, styles.totalRow]}>
//               <Text style={styles.totalLabel}>Total</Text>
//               <Text style={styles.totalValue}>$779.98</Text>
//             </View>
//           </View>
//         </View>
//       </ScrollView>

//       <View style={styles.bottomBar}>
//         <TouchableOpacity style={styles.placeOrderButton}>
//           <Text style={styles.placeOrderText}>Place Order</Text>
//         </TouchableOpacity>
//       </View>
//     </SafeAreaView>
//   );
// }

// const styles = StyleSheet.create({
//   container: {
//     flex: 1,
//     backgroundColor: '#f3f4f6',
//   },
//   header: {
//     flexDirection: 'row',
//     alignItems: 'center',
//     justifyContent: 'space-between',
//     padding: 16,
//     backgroundColor: 'white',
//   },
//   backButton: {
//     padding: 8,
//   },
//   title: {
//     fontSize: 20,
//     fontWeight: 'bold',
//   },
//   content: {
//     flex: 1,
//   },
//   section: {
//     marginTop: 16,
//     paddingHorizontal: 16,
//   },
//   sectionTitle: {
//     fontSize: 18,
//     fontWeight: '600',
//     marginBottom: 8,
//   },
//   card: {
//     backgroundColor: 'white',
//     borderRadius: 12,
//     padding: 16,
//     shadowColor: '#000',
//     shadowOffset: { width: 0, height: 2 },
//     shadowOpacity: 0.1,
//     shadowRadius: 8,
//     elevation: 3,
//   },
//   input: {
//     borderWidth: 1,
//     borderColor: '#e5e7eb',
//     borderRadius: 8,
//     padding: 12,
//     marginBottom: 12,
//     fontSize: 16,
//   },
//   row: {
//     flexDirection: 'row',
//     justifyContent: 'space-between',
//   },
//   halfInput: {
//     width: '48%',
//   },
//   paymentOption: {
//     flexDirection: 'row',
//     alignItems: 'center',
//     paddingVertical: 12,
//     borderBottomWidth: 1,
//     borderBottomColor: '#f3f4f6',
//   },
//   paymentText: {
//     flex: 1,
//     marginLeft: 12,
//     fontSize: 16,
//   },
//   summaryRow: {
//     flexDirection: 'row',
//     justifyContent: 'space-between',
//     marginBottom: 8,
//   },
//   summaryLabel: {
//     fontSize: 16,
//     color: '#666',
//   },
//   summaryValue: {
//     fontSize: 16,
//     fontWeight: '600',
//   },
//   totalRow: {
//     marginTop: 8,
//     paddingTop: 8,
//     borderTopWidth: 1,
//     borderTopColor: '#f3f4f6',
//   },
//   totalLabel: {
//     fontSize: 18,
//     fontWeight: 'bold',
//   },
//   totalValue: {
//     fontSize: 18,
//     fontWeight: 'bold',
//     color: '#2563EB',
//   },
//   bottomBar: {
//     padding: 16,
//     backgroundColor: 'white',
//     borderTopWidth: 1,
//     borderTopColor: '#f3f4f6',
//   },
//   placeOrderButton: {
//     backgroundColor: '#2563EB',
//     padding: 16,
//     borderRadius: 12,
//     alignItems: 'center',
//   },

// text: {
//     color: 'white',
//     fontSize: 16,
//     fontWeight: '600',
//   },
// });
import React, { useState, useEffect } from "react";
import {
  View,
  Text,
  StyleSheet,
  ScrollView,
  TouchableOpacity,
  TextInput,
  Alert,
  ActivityIndicator,
} from "react-native";
import { SafeAreaView } from "react-native-safe-area-context";
import { MaterialIcons } from "@expo/vector-icons";
import axios from "axios";
import { useAuth } from "@/context/AuthContext";
import { BaseURL } from "@/constants/ApiEndpoint";

export default function CheckoutScreen({ navigation }) {
  const [cartData, setCartData] = useState(null);
  const [addresses, setAddresses] = useState([]);
  const [selectedAddress, setSelectedAddress] = useState(null);
  const [showAddressForm, setShowAddressForm] = useState(false);
  const [isLoading, setIsLoading] = useState(false);
  const { accessToken } = useAuth();
  const [addressForm, setAddressForm] = useState({
    address_type: "BILLING",
    street_address: "",
    apartment: "",
    city: "",
    state: "",
    postal_code: "",
    order_user_phone: "",
    order_user_email: "",
    is_default: false,
  });

  useEffect(() => {
    // Fetch cart data
    fetchCartData();
    // Fetch addresses
    fetchAddresses();
  }, []);

  const fetchCartData = async () => {
    try {
      const response = await axios.get(BaseURL + "/api/v1/orders/cart/", {
        headers: {
          Authorization: `Bearer ${accessToken}`,
        },
      });
      setCartData(response.data);
    } catch (error) {
      console.error("Error fetching cart data:", error);
      Alert.alert("Error", "Failed to load cart data");
    }
  };

  const fetchAddresses = async () => {
    try {
      const response = await axios.get(BaseURL + "/api/v1/users/addresses/", {
        headers: {
          Authorization: `Bearer ${accessToken}`,
        },
      });
      setAddresses(response.data);
      if (response.data.length > 0) {
        // Set default address if available
        const defaultAddress = response.data.find((addr) => addr.is_default);
        setSelectedAddress(defaultAddress || response.data[0]);
      }
    } catch (error) {
      console.error("Error fetching addresses:", error);
    }
  };

  const handleAddressChange = (field, value) => {
    setAddressForm((prev) => ({
      ...prev,
      [field]: value,
    }));
  };

  const addAddress = async () => {
    try {
      const response = await axios.post(
        BaseURL + "/api/v1/users/addresses/",
        addressForm,
        {
          headers: {
            Authorization: `Bearer ${accessToken}`,
          },
        }
      );
      setAddresses([...addresses, response.data]);
      setSelectedAddress(response.data);
      setShowAddressForm(false);
      Alert.alert("Success", "Address added successfully");
    } catch (error) {
      console.error("Error adding address:", error);
      Alert.alert("Error", "Failed to add address");
    }
  };

  // const handlePlaceOrder = async () => {
  //   if (!selectedAddress) {
  //     Alert.alert("Error", "Please select or add an address");
  //     return;
  //   }

  //   try {
  //     setIsLoading(true);
      
  //     // Prepare order data
  //     const orderData = {
  //       shipping_address_id: selectedAddress.id,
  //       billing_address_id: selectedAddress.id,
  //       shipping_method_id: 1, // Default shipping method
  //       notes: "",
  //     };

  //     // Make API call to create order
  //     const response = await axios.post(
  //       BaseURL + "/api/v1/orders/",
  //       orderData,
  //       {
  //         headers: {
  //           Authorization: `Bearer ${accessToken}`,
  //         },
  //       }
  //     );

  //     setIsLoading(false);
  //     Alert.alert(
  //       "Success", 
  //       "Order placed successfully!",
  //       [
  //         {
  //           text: "OK",
  //           onPress: () => navigation.navigate("OrderConfirmation", { orderData: response.data })
  //         }
  //       ]
  //     );
  //   } catch (error) {
  //     setIsLoading(false);
  //     console.error("Error placing order:", error);
  //     Alert.alert(
  //       "Error", 
  //       error.response?.data?.message || "Failed to place order. Please try again."
  //     );
  //   }
  // };
  const handlePlaceOrder = async () => {
    if (!selectedAddress) {
      Alert.alert("Error", "Please select or add an address");
      return;
    }
  
    try {
      setIsLoading(true);
      
      // Prepare order data
      const orderData = {
        shipping_address_id: selectedAddress.id,
        billing_address_id: selectedAddress.id,
        shipping_method_id: 1, // Default shipping method
        notes: "",
      };
  
      // Make API call to create order
      const response = await axios.post(
        BaseURL + "/api/v1/orders/",
        orderData,
        {
          headers: {
            Authorization: `Bearer ${accessToken}`,
          },
        }
      );
  
      setIsLoading(false);
      
      // Extract order ID from the response
      const orderId = response.data.id;
      
      // Navigate to OrderConfirmation with orderId
      navigation.navigate("OrderConfirmation", { 
        orderId: orderId,
        orderData: response.data 
      });
    } catch (error:any) {
      setIsLoading(false);
      console.error("Error placing order:", error);
      Alert.alert(
        "Error", 
        error?.response?.data?.message || "Failed to place order. Please try again."
      );
    }
  };
  if (!cartData) {
    return (
      <SafeAreaView style={styles.container}>
        <View style={styles.header}>
          <TouchableOpacity
            onPress={() => navigation.goBack()}
            style={styles.backButton}
          >
            <MaterialIcons name="arrow-back" size={24} color="#333" />
          </TouchableOpacity>
          <Text style={styles.title}>Checkout</Text>
          <View style={{ width: 24 }} />
        </View>
        <View style={styles.loadingContainer}>
          <ActivityIndicator size="large" color="#2563EB" />
          <Text style={{ marginTop: 16 }}>Loading...</Text>
        </View>
      </SafeAreaView>
    );
  }

  return (
    <SafeAreaView style={styles.container}>
      <View style={styles.header}>
        <TouchableOpacity
          onPress={() => navigation.goBack()}
          style={styles.backButton}
        >
          <MaterialIcons name="arrow-back" size={24} color="#333" />
        </TouchableOpacity>
        <Text style={styles.title}>Checkout</Text>
        <View style={{ width: 24 }} />
      </View>

      <ScrollView style={styles.content}>
        {/* Cart Items Section */}
        <View style={styles.section}>
          <Text style={styles.sectionTitle}>
            Cart Items ({cartData.total_items})
          </Text>
          <View style={styles.card}>
            {cartData.items.map((item) => (
              <View key={item.id} style={styles.cartItem}>
                <Text style={styles.itemName} numberOfLines={1}>
                  {item.product.name}
                </Text>
                <View style={styles.itemDetails}>
                  <Text>Qty: {item.quantity}</Text>
                  <Text style={styles.itemPrice}>₹{item.line_total}</Text>
                </View>
              </View>
            ))}
          </View>
        </View>

        {/* Shipping Address Section */}
        <View style={styles.section}>
          <Text style={styles.sectionTitle}>Shipping Address</Text>

          {addresses.length > 0 && !showAddressForm && (
            <View style={styles.card}>
              {addresses.map((address) => (
                <TouchableOpacity
                  key={address.id}
                  style={[
                    styles.addressItem,
                    selectedAddress?.id === address.id &&
                      styles.selectedAddress,
                  ]}
                  onPress={() => setSelectedAddress(address)}
                >
                  <MaterialIcons
                    name={
                      selectedAddress?.id === address.id
                        ? "radio-button-checked"
                        : "radio-button-unchecked"
                    }
                    size={24}
                    color="#2563EB"
                  />
                  <View style={styles.addressDetails}>
                    <Text style={styles.addressText}>
                      {address.street_address}, {address.apartment}
                    </Text>
                    <Text style={styles.addressText}>
                      {address.city}, {address.state} {address.postal_code}
                    </Text>
                    <Text style={styles.addressContact}>
                      {address.order_user_phone} • {address.order_user_email}
                    </Text>
                  </View>
                </TouchableOpacity>
              ))}

              <TouchableOpacity
                style={styles.newAddressButton}
                onPress={() => setShowAddressForm(true)}
              >
                <MaterialIcons name="add" size={20} color="#2563EB" />
                <Text style={styles.newAddressText}>Add New Address</Text>
              </TouchableOpacity>
            </View>
          )}

          {(showAddressForm || addresses.length === 0) && (
            <View style={styles.card}>
              <TextInput
                style={styles.input}
                placeholder="Email"
                value={addressForm.order_user_email}
                onChangeText={(text) =>
                  handleAddressChange("order_user_email", text)
                }
                keyboardType="email-address"
                placeholderTextColor="#666"
              />
              <TextInput
                style={styles.input}
                placeholder="Phone Number"
                value={addressForm.order_user_phone}
                onChangeText={(text) =>
                  handleAddressChange("order_user_phone", text)
                }
                keyboardType="phone-pad"
                placeholderTextColor="#666"
              />
              <TextInput
                style={styles.input}
                placeholder="Street Address"
                value={addressForm.street_address}
                onChangeText={(text) =>
                  handleAddressChange("street_address", text)
                }
                placeholderTextColor="#666"
              />
              <TextInput
                style={styles.input}
                placeholder="Apartment, Suite, etc."
                value={addressForm.apartment}
                onChangeText={(text) => handleAddressChange("apartment", text)}
                placeholderTextColor="#666"
              />
              <View style={styles.row}>
                <TextInput
                  style={[styles.input, styles.halfInput]}
                  placeholder="City"
                  value={addressForm.city}
                  onChangeText={(text) => handleAddressChange("city", text)}
                  placeholderTextColor="#666"
                />
                <TextInput
                  style={[styles.input, styles.halfInput]}
                  placeholder="State"
                  value={addressForm.state}
                  onChangeText={(text) => handleAddressChange("state", text)}
                  placeholderTextColor="#666"
                />
              </View>
              <TextInput
                style={styles.input}
                placeholder="Postal Code"
                value={addressForm.postal_code}
                onChangeText={(text) =>
                  handleAddressChange("postal_code", text)
                }
                keyboardType="numeric"
                placeholderTextColor="#666"
              />

              <View style={styles.formButtons}>
                {showAddressForm && (
                  <TouchableOpacity
                    style={styles.cancelButton}
                    onPress={() => setShowAddressForm(false)}
                  >
                    <Text style={styles.cancelButtonText}>Cancel</Text>
                  </TouchableOpacity>
                )}
                <TouchableOpacity
                  style={styles.saveButton}
                  onPress={addAddress}
                >
                  <Text style={styles.saveButtonText}>Save Address</Text>
                </TouchableOpacity>
              </View>
            </View>
          )}
        </View>

        {/* Payment Method Section */}
        <View style={styles.section}>
          <Text style={styles.sectionTitle}>Payment Method</Text>
          <View style={styles.card}>
            <TouchableOpacity style={styles.paymentOption}>
              <MaterialIcons name="credit-card" size={24} color="#2563EB" />
              <Text style={styles.paymentText}>Credit/Debit Card</Text>
              <MaterialIcons name="chevron-right" size={24} color="#666" />
            </TouchableOpacity>

            <TouchableOpacity
              style={[styles.paymentOption, { borderBottomWidth: 0 }]}
            >
              <MaterialIcons name="account-balance" size={24} color="#2563EB" />
              <Text style={styles.paymentText}>Bank Transfer</Text>
              <MaterialIcons name="chevron-right" size={24} color="#666" />
            </TouchableOpacity>
          </View>
        </View>

        {/* Order Summary Section */}
        <View style={styles.section}>
          <Text style={styles.sectionTitle}>Order Summary</Text>
          <View style={styles.card}>
            <View style={styles.summaryRow}>
              <Text style={styles.summaryLabel}>Subtotal</Text>
              <Text style={styles.summaryValue}>₹{cartData.subtotal}</Text>
            </View>
            <View style={styles.summaryRow}>
              <Text style={styles.summaryLabel}>Shipping</Text>
              <Text style={styles.summaryValue}>₹0.00</Text>
            </View>
            <View style={styles.summaryRow}>
              <Text style={styles.summaryLabel}>Tax</Text>
              <Text style={styles.summaryValue}>₹0.00</Text>
            </View>
            <View style={[styles.summaryRow, styles.totalRow]}>
              <Text style={styles.totalLabel}>Total</Text>
              <Text style={styles.totalValue}>₹{cartData.subtotal}</Text>
            </View>
          </View>
        </View>
      </ScrollView>

      <View style={styles.bottomBar}>
        <TouchableOpacity
          style={[
            styles.placeOrderButton,
            isLoading && styles.disabledButton
          ]}
          onPress={handlePlaceOrder}
          disabled={isLoading}
        >
          {isLoading ? (
            <ActivityIndicator color="white" size="small" />
          ) : (
            <Text style={styles.placeOrderText}>Place Order</Text>
          )}
        </TouchableOpacity>
      </View>
    </SafeAreaView>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: "#f3f4f6",
  },
  header: {
    flexDirection: "row",
    alignItems: "center",
    justifyContent: "space-between",
    padding: 16,
    backgroundColor: "white",
  },
  backButton: {
    padding: 8,
  },
  title: {
    fontSize: 20,
    fontWeight: "bold",
  },
  content: {
    flex: 1,
  },
  loadingContainer: {
    flex: 1,
    justifyContent: "center",
    alignItems: "center",
  },
  section: {
    marginTop: 16,
    paddingHorizontal: 16,
  },
  sectionTitle: {
    fontSize: 18,
    fontWeight: "600",
    marginBottom: 8,
  },
  card: {
    backgroundColor: "white",
    borderRadius: 12,
    padding: 16,
    shadowColor: "#000",
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 8,
    elevation: 3,
  },
  cartItem: {
    flexDirection: "column",
    paddingVertical: 12,
    borderBottomWidth: 1,
    borderBottomColor: "#f3f4f6",
  },
  itemName: {
    fontSize: 16,
    fontWeight: "500",
  },
  itemDetails: {
    flexDirection: "row",
    justifyContent: "space-between",
    marginTop: 4,
  },
  itemPrice: {
    fontWeight: "600",
    color: "#2563EB",
  },
  addressItem: {
    flexDirection: "row",
    alignItems: "flex-start",
    paddingVertical: 12,
    borderBottomWidth: 1,
    borderBottomColor: "#f3f4f6",
  },
  selectedAddress: {
    backgroundColor: "#f0f7ff",
  },
  addressDetails: {
    flex: 1,
    marginLeft: 12,
  },
  addressText: {
    fontSize: 16,
    marginBottom: 2,
  },
  addressContact: {
    fontSize: 14,
    color: "#666",
    marginTop: 4,
  },
  newAddressButton: {
    flexDirection: "row",
    alignItems: "center",
    justifyContent: "center",
    marginTop: 12,
    paddingVertical: 8,
  },
  newAddressText: {
    color: "#2563EB",
    fontSize: 16,
    fontWeight: "500",
    marginLeft: 8,
  },
  input: {
    borderWidth: 1,
    borderColor: "#e5e7eb",
    borderRadius: 8,
    padding: 12,
    marginBottom: 12,
    fontSize: 16,
  },
  row: {
    flexDirection: "row",
    justifyContent: "space-between",
  },
  halfInput: {
    width: "48%",
  },
  formButtons: {
    flexDirection: "row",
    justifyContent: "flex-end",
    marginTop: 8,
  },
  cancelButton: {
    paddingVertical: 12,
    paddingHorizontal: 16,
    marginRight: 12,
  },
  cancelButtonText: {
    color: "#666",
    fontSize: 16,
    fontWeight: "500",
  },
  saveButton: {
    backgroundColor: "#2563EB",
    paddingVertical: 12,
    paddingHorizontal: 16,
    borderRadius: 8,
  },
  saveButtonText: {
    color: "white",
    fontSize: 16,
    fontWeight: "500",
  },
  paymentOption: {
    flexDirection: "row",
    alignItems: "center",
    paddingVertical: 12,
    borderBottomWidth: 1,
    borderBottomColor: "#f3f4f6",
  },
  paymentText: {
    flex: 1,
    marginLeft: 12,
    fontSize: 16,
  },
  summaryRow: {
    flexDirection: "row",
    justifyContent: "space-between",
    marginBottom: 8,
  },
  summaryLabel: {
    fontSize: 16,
    color: "#666",
  },
  summaryValue: {
    fontSize: 16,
    fontWeight: "600",
  },
  totalRow: {
    marginTop: 8,
    paddingTop: 8,
    borderTopWidth: 1,
    borderTopColor: "#f3f4f6",
  },
  totalLabel: {
    fontSize: 18,
    fontWeight: "bold",
  },
  totalValue: {
    fontSize: 18,
    fontWeight: "bold",
    color: "#2563EB",
  },
  bottomBar: {
    padding: 16,
    backgroundColor: "white",
    borderTopWidth: 1,
    borderTopColor: "#f3f4f6",
  },
  placeOrderButton: {
    backgroundColor: "#2563EB",
    padding: 16,
    borderRadius: 12,
    alignItems: "center",
  },
  placeOrderText: {
    color: "white",
    fontSize: 16,
    fontWeight: "600",
  },
  disabledButton: {
    backgroundColor: "#93c5fd",
  },
});