"use client";
import { useState, useEffect } from "react";
import { CATEGORIZE_PRODUCTS, MAIN_URL } from "../../constant/urls";
import Product from "./Product";
import axios from "axios";

export const RelatedProducts = ({
  category_slug,
}: {
  category_slug: string;
}) => {
  const [products, setProducts] = useState<any[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  useEffect(() => {
    const fetchRelatedProducts = async () => {
      if (!category_slug || category_slug.trim() === "") {
        setLoading(false);
        return;
      }

      try {
        setLoading(true);
        console.log("Fetching related products for category:", category_slug);

        // Use the CATEGORIZE_PRODUCTS function to get the correct endpoint
        const endpoint = CATEGORIZE_PRODUCTS(category_slug);
        const url = `${MAIN_URL}${endpoint}`;
        console.log("Fetching from URL:", url);

        const response = await axios.get(url);
        const data = response.data;
        console.log("Related products data:", data);

        // The API returns a paginated response with products in the results array
        if (data && data.results && data.results.products) {
          setProducts(data.results.products);
        } else {
          setProducts([]);
        }
      } catch (err) {
        console.error("Error fetching related products:", err);
        if (axios.isAxiosError(err)) {
          setError(err.response?.data?.message || err.message);
        } else {
          setError(err instanceof Error ? err.message : "Unknown error");
        }
      } finally {
        setLoading(false);
      }
    };

    fetchRelatedProducts();
  }, [category_slug]);

  // Don't render anything if there's no category slug
  if (!category_slug || category_slug.trim() === "") {
    return null;
  }

  return (
    <div className="mt-16">
      <h2 className="text-2xl font-bold mb-6">Related Products</h2>
      {loading ? (
        <div className="flex justify-center">
          <div className="animate-spin rounded-full h-12 w-12 border-t-2 border-b-2 border-gray-900"></div>
        </div>
      ) : error ? (
        <div className="text-center text-red-500">
          Error loading related products: {error}
        </div>
      ) : (
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-6">
          {Array.isArray(products) && products.length > 0 ? (
            products.map((product) => (
              <Product key={product.id} {...product} />
            ))
          ) : (
            <div className="col-span-full text-center text-gray-500">
              No related products found for this category
            </div>
          )}
        </div>
      )}
    </div>
  );
};
