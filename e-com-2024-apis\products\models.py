from django.db import models
from django.core.validators import MinValueValidator, MaxValueValidator
from django.contrib.auth import get_user_model
from django.db.models import Avg
from django.utils.text import slugify
import os

User = get_user_model()


class GST(models.Model):
    """GST rate model for dynamic tax calculation"""
    name = models.CharField(max_length=100, help_text="GST category name (e.g., 'Standard Rate', 'Electronics')")
    rate = models.DecimalField(max_digits=5, decimal_places=2, default=18.00,
                              validators=[MinValueValidator(0), MaxValueValidator(100)],
                              help_text="GST rate in percentage")
    cgst_rate = models.DecimalField(max_digits=5, decimal_places=2, default=9.00,
                                   validators=[MinValueValidator(0), MaxValueValidator(50)],
                                   help_text="CGST rate in percentage")
    sgst_rate = models.DecimalField(max_digits=5, decimal_places=2, default=9.00,
                                   validators=[MinValueValidator(0), MaxValueValidator(50)],
                                   help_text="SGST rate in percentage")
    igst_rate = models.DecimalField(max_digits=5, decimal_places=2, default=18.00,
                                   validators=[MinValueValidator(0), MaxValueValidator(100)],
                                   help_text="IGST rate in percentage (for inter-state)")
    hsn_code = models.CharField(max_length=20, blank=True, help_text="HSN/SAC code for this GST category")
    description = models.TextField(blank=True, help_text="Description of when this GST rate applies")
    is_active = models.BooleanField(default=True)
    is_default = models.BooleanField(default=False, help_text="Default GST rate for new products")
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)

    class Meta:
        verbose_name = "GST Rate"
        verbose_name_plural = "GST Rates"
        ordering = ['-is_default', 'rate']

    def __str__(self):
        return f"{self.name} - {self.rate}%"

    def save(self, *args, **kwargs):
        # Ensure only one default GST rate exists
        if self.is_default:
            GST.objects.filter(is_default=True).update(is_default=False)
        super().save(*args, **kwargs)

    def clean(self):
        from django.core.exceptions import ValidationError
        # Ensure CGST + SGST equals total GST rate
        if self.cgst_rate + self.sgst_rate != self.rate:
            raise ValidationError("CGST + SGST should equal the total GST rate")

    @classmethod
    def get_default_gst(cls):
        """Get the default GST rate"""
        default_gst = cls.objects.filter(is_default=True, is_active=True).first()
        if not default_gst:
            # Create default 18% GST if none exists
            default_gst = cls.objects.create(
                name="Standard Rate",
                rate=18.00,
                cgst_rate=9.00,
                sgst_rate=9.00,
                igst_rate=18.00,
                hsn_code="8471",
                description="Standard GST rate for most products",
                is_default=True,
                is_active=True
            )
        return default_gst

    def calculate_gst_amount(self, base_amount):
        """Calculate GST amount for given base amount"""
        from decimal import Decimal
        return (Decimal(str(base_amount)) * Decimal(str(self.rate))) / Decimal('100')

    def calculate_cgst_amount(self, base_amount):
        """Calculate CGST amount for given base amount"""
        from decimal import Decimal
        return (Decimal(str(base_amount)) * Decimal(str(self.cgst_rate))) / Decimal('100')

    def calculate_sgst_amount(self, base_amount):
        """Calculate SGST amount for given base amount"""
        from decimal import Decimal
        return (Decimal(str(base_amount)) * Decimal(str(self.sgst_rate))) / Decimal('100')

    def calculate_igst_amount(self, base_amount):
        """Calculate IGST amount for given base amount"""
        from decimal import Decimal
        return (Decimal(str(base_amount)) * Decimal(str(self.igst_rate))) / Decimal('100')

    def calculate_base_price_from_inclusive(self, inclusive_price):
        """Calculate base price from GST inclusive price"""
        from decimal import Decimal
        inclusive_price = Decimal(str(inclusive_price))
        gst_rate = Decimal(str(self.rate))
        # Formula: base_price = inclusive_price / (1 + gst_rate/100)
        return inclusive_price / (Decimal('1') + (gst_rate / Decimal('100')))

    def calculate_gst_from_inclusive(self, inclusive_price):
        """Calculate GST amount from GST inclusive price"""
        base_price = self.calculate_base_price_from_inclusive(inclusive_price)
        return self.calculate_gst_amount(base_price)

    def calculate_cgst_from_inclusive(self, inclusive_price):
        """Calculate CGST amount from GST inclusive price"""
        base_price = self.calculate_base_price_from_inclusive(inclusive_price)
        return self.calculate_cgst_amount(base_price)

    def calculate_sgst_from_inclusive(self, inclusive_price):
        """Calculate SGST amount from GST inclusive price"""
        base_price = self.calculate_base_price_from_inclusive(inclusive_price)
        return self.calculate_sgst_amount(base_price)

    def calculate_igst_from_inclusive(self, inclusive_price):
        """Calculate IGST amount from GST inclusive price"""
        base_price = self.calculate_base_price_from_inclusive(inclusive_price)
        return self.calculate_igst_amount(base_price)

    def calculate_gst_breakdown(self, base_amount, is_inter_state=False):
        """Calculate complete GST breakdown for given base amount"""
        if is_inter_state:
            igst_amount = self.calculate_igst_amount(base_amount)
            return {
                'igst_amount': round(igst_amount, 2),
                'cgst_amount': 0,
                'sgst_amount': 0,
                'total_gst': round(igst_amount, 2)
            }
        else:
            cgst_amount = self.calculate_cgst_amount(base_amount)
            sgst_amount = self.calculate_sgst_amount(base_amount)
            return {
                'igst_amount': 0,
                'cgst_amount': round(cgst_amount, 2),
                'sgst_amount': round(sgst_amount, 2),
                'total_gst': round(cgst_amount + sgst_amount, 2)
            }

    def calculate_gst_breakdown_from_inclusive(self, inclusive_price, is_inter_state=False):
        """Calculate complete GST breakdown from GST inclusive price"""
        from decimal import Decimal
        base_price = self.calculate_base_price_from_inclusive(inclusive_price)

        if is_inter_state:
            igst_amount = self.calculate_igst_from_inclusive(inclusive_price)
            return {
                'base_price': round(base_price, 2),
                'igst_amount': round(igst_amount, 2),
                'cgst_amount': 0,
                'sgst_amount': 0,
                'total_gst': round(igst_amount, 2),
                'mrp': round(Decimal(str(inclusive_price)), 2)
            }
        else:
            cgst_amount = self.calculate_cgst_from_inclusive(inclusive_price)
            sgst_amount = self.calculate_sgst_from_inclusive(inclusive_price)
            total_gst = cgst_amount + sgst_amount
            return {
                'base_price': round(base_price, 2),
                'igst_amount': 0,
                'cgst_amount': round(cgst_amount, 2),
                'sgst_amount': round(sgst_amount, 2),
                'total_gst': round(total_gst, 2),
                'mrp': round(Decimal(str(inclusive_price)), 2)
            }


def limit_name_length(name, max_length=30):
    """Limit name length for directory names"""
    if len(name) <= max_length:
        return name
    return name[:max_length]


def product_image_upload_path(instance, filename):
    """
    Generate upload path for product images
    Format: products/{product_name}/{filename}
    """
    product_name = limit_name_length(instance.product.name)
    # Clean the name to be safe for directory usage
    safe_name = slugify(product_name)
    # Return the path with forward slashes for consistent URL handling
    return f'products/{safe_name}/{filename}'


def brand_image_upload_path(instance, filename):
    """
    Generate upload path for brand images
    Format: brands/{brand_name}/{filename}
    """
    brand_name = limit_name_length(instance.name)
    # Clean the name to be safe for directory usage
    safe_name = slugify(brand_name)
    # Return the path with forward slashes for consistent URL handling
    return f'brands/{safe_name}/{filename}'


def category_image_upload_path(instance, filename):
    """
    Generate upload path for category images
    Format: categories/{category_name}/{filename}
    """
    category_name = limit_name_length(instance.name)
    # Clean the name to be safe for directory usage
    safe_name = slugify(category_name)
    # Return the path with forward slashes for consistent URL handling
    return f'categories/{safe_name}/{filename}'


def subcategory_image_upload_path(instance, filename):
    """
    Generate upload path for subcategory images
    Format: subcategories/{subcategory_name}/{filename}
    """
    subcategory_name = limit_name_length(instance.name)
    # Clean the name to be safe for directory usage
    safe_name = slugify(subcategory_name)
    # Return the path with forward slashes for consistent URL handling
    return f'subcategories/{safe_name}/{filename}'


class SubCategorie(models.Model):
    name = models.CharField(max_length=100)
    slug = models.SlugField(unique=True, blank=True, null=True)
    description = models.TextField(blank=True)
    category = models.ForeignKey(
        "Category", on_delete=models.CASCADE, related_name="subcategories"
    )
    image = models.ImageField(upload_to=subcategory_image_upload_path, null=True, blank=True, max_length=255)
    is_active = models.BooleanField(default=True)
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)

    def save(self, *args, **kwargs):
        if self.id:
            super().save(*args, **kwargs)
        else:
            self.slug = slugify(self.name)
            super().save(*args, **kwargs)

    def __str__(self):
        return self.name


class Category(models.Model):
    name = models.CharField(max_length=100)
    slug = models.SlugField(unique=True, blank=True, null=True)
    description = models.TextField(blank=True)
    image = models.ImageField(upload_to=category_image_upload_path, null=True, blank=True, max_length=255)
    is_active = models.BooleanField(default=True)
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)

    class Meta:
        verbose_name_plural = "categories"

    def save(self, *args, **kwargs):
        if self.id:
            super().save(*args, **kwargs)
        else:
            self.slug = slugify(self.name)
            super().save(*args, **kwargs)

    def __str__(self):
        return self.name


class Brand(models.Model):
    name = models.CharField(max_length=100)
    description = models.TextField(blank=True)
    image = models.ImageField(upload_to=brand_image_upload_path, null=True, blank=True, max_length=255)
    is_active = models.BooleanField(default=True)
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)

    def __str__(self):
        return self.name


class Product(models.Model):
    name = models.CharField(max_length=1024)
    slug = models.SlugField(unique=True, null=True, blank=True)
    description = models.TextField(null=True, blank=True)
    category = models.ForeignKey(
        Category,
        null=True,
        blank=True,
        on_delete=models.SET_NULL,
        related_name="products",
    )
    subcategory = models.ForeignKey(
        SubCategorie,
        null=True,
        blank=True,
        on_delete=models.SET_NULL,
        related_name="products"
    )
    brand = models.ForeignKey(
        Brand, null=True, blank=True, on_delete=models.CASCADE, related_name="products"
    )
    price = models.DecimalField(max_digits=10, decimal_places=2)
    gst = models.ForeignKey(
        GST,
        null=True,
        blank=True,
        on_delete=models.SET_NULL,
        related_name="products",
        help_text="GST rate for this product. If not set, default 18% will be used."
    )
    stock = models.PositiveIntegerField(default=0)
    is_active = models.BooleanField(default=True)
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)

    class Meta:
        ordering = ["-created_at"]
        indexes = [
            models.Index(fields=['category', 'is_active']),
            models.Index(fields=['brand', 'is_active']),
            models.Index(fields=['price']),
            models.Index(fields=['stock']),
            models.Index(fields=['created_at']),
        ]

    @property
    def average_rating(self):
        return self.reviews.aggregate(Avg("rating"))["rating__avg"] or 0.0

    def save(self, *args, **kwargs):
        if self.id:
            super().save(*args, **kwargs)
        else:
            self.slug = slugify(self.name)
            super().save(*args, **kwargs)

    @property
    def total_reviews(self):
        return self.reviews.count()

    def get_gst_rate(self):
        """Get GST rate for this product. Returns default 18% if no GST is set."""
        if self.gst:
            return self.gst
        # Return default GST rate
        return GST.get_default_gst()

    def calculate_gst_amount(self):
        """Calculate GST amount for this product's price (DEPRECATED - use calculate_gst_from_mrp)"""
        gst_rate = self.get_gst_rate()
        return gst_rate.calculate_gst_amount(self.price)

    def calculate_cgst_amount(self):
        """Calculate CGST amount for this product's price (DEPRECATED - use calculate_cgst_from_mrp)"""
        gst_rate = self.get_gst_rate()
        return gst_rate.calculate_cgst_amount(self.price)

    def calculate_sgst_amount(self):
        """Calculate SGST amount for this product's price (DEPRECATED - use calculate_sgst_from_mrp)"""
        gst_rate = self.get_gst_rate()
        return gst_rate.calculate_sgst_amount(self.price)

    def calculate_igst_amount(self):
        """Calculate IGST amount for this product's price (DEPRECATED - use calculate_igst_from_mrp)"""
        gst_rate = self.get_gst_rate()
        return gst_rate.calculate_igst_amount(self.price)

    @property
    def price_with_gst(self):
        """Get price including GST (DEPRECATED - price field now stores MRP inclusive of GST)"""
        return self.price + self.calculate_gst_amount()

    # New methods for GST inclusive pricing
    @property
    def mrp(self):
        """Get MRP (Maximum Retail Price) - price field stores GST inclusive price"""
        return self.price

    @property
    def base_price(self):
        """Get base price (excluding GST) calculated from MRP"""
        gst_rate = self.get_gst_rate()
        return gst_rate.calculate_base_price_from_inclusive(self.price)

    def calculate_gst_from_mrp(self):
        """Calculate GST amount from MRP (GST inclusive price)"""
        gst_rate = self.get_gst_rate()
        return gst_rate.calculate_gst_from_inclusive(self.price)

    def calculate_cgst_from_mrp(self):
        """Calculate CGST amount from MRP (GST inclusive price)"""
        gst_rate = self.get_gst_rate()
        return gst_rate.calculate_cgst_from_inclusive(self.price)

    def calculate_sgst_from_mrp(self):
        """Calculate SGST amount from MRP (GST inclusive price)"""
        gst_rate = self.get_gst_rate()
        return gst_rate.calculate_sgst_from_inclusive(self.price)

    def calculate_igst_from_mrp(self):
        """Calculate IGST amount from MRP (GST inclusive price)"""
        gst_rate = self.get_gst_rate()
        return gst_rate.calculate_igst_from_inclusive(self.price)

    def calculate_gst_breakdown(self, quantity=1, is_inter_state=False):
        """Calculate complete GST breakdown for this product (DEPRECATED - use calculate_gst_breakdown_from_mrp)"""
        gst_rate = self.get_gst_rate()
        base_amount = self.price * quantity
        return gst_rate.calculate_gst_breakdown(base_amount, is_inter_state)

    def calculate_gst_breakdown_from_mrp(self, quantity=1, is_inter_state=False):
        """Calculate complete GST breakdown from MRP (GST inclusive price)"""
        gst_rate = self.get_gst_rate()
        total_mrp = self.price * quantity
        breakdown = gst_rate.calculate_gst_breakdown_from_inclusive(total_mrp, is_inter_state)
        breakdown['quantity'] = quantity
        breakdown['unit_mrp'] = round(self.price, 2)
        breakdown['unit_base_price'] = round(self.base_price, 2)
        breakdown['gst_rate'] = float(gst_rate.rate)  # Add GST rate to breakdown
        return breakdown

    def __str__(self):
        return self.name


class Review(models.Model):
    product = models.ForeignKey(
        Product, on_delete=models.CASCADE, related_name="reviews"
    )
    user = models.ForeignKey(User, on_delete=models.CASCADE, related_name="reviews")
    rating = models.PositiveSmallIntegerField(
        validators=[MinValueValidator(1), MaxValueValidator(5)]
    )
    title = models.CharField(max_length=100)
    comment = models.TextField()
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)
    is_approved = models.BooleanField(default=True)

    class Meta:
        unique_together = ("product", "user")
        ordering = ["-created_at"]

    def __str__(self):
        return f"{self.user.email}'s review of {self.product.name}"


class ProductImage(models.Model):
    product = models.ForeignKey(
        Product, on_delete=models.CASCADE, related_name="images"
    )
    image = models.ImageField(upload_to=product_image_upload_path, max_length=255)
    is_primary = models.BooleanField(default=False)
    created_at = models.DateTimeField(auto_now_add=True)

    class Meta:
        ordering = ["-is_primary", "-created_at"]

    def save(self, *args, **kwargs):
        if self.is_primary:
            ProductImage.objects.filter(product=self.product, is_primary=True).update(
                is_primary=False
            )
        super().save(*args, **kwargs)

    def __str__(self):
        return f"Image for {self.product.name}"


class ProductVariant(models.Model):
    product = models.ForeignKey(
        Product, on_delete=models.CASCADE, related_name="variants"
    )
    name = models.CharField(max_length=100)
    sku = models.CharField(max_length=100, unique=True)
    price_adjustment = models.DecimalField(max_digits=10, decimal_places=2, default=0)
    stock = models.IntegerField(default=0, validators=[MinValueValidator(0)])

    class Meta:
        ordering = ["name"]

    def __str__(self):
        return f"{self.product.name} - {self.name}"
