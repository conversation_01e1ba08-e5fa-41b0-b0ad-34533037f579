#!/usr/bin/env python
"""
Django Migration Cleaner Script
Safely removes all migration files without affecting the database.
"""

import os
import sys
import glob
import shutil
from pathlib import Path

def clean_migrations():
    """Remove all migration files except __init__.py"""
    
    # List of Django apps in your project
    apps = [
        'users',
        'products', 
        'orders',
        'promotions',
        'dashboard',
        'payment_gateway',
        'backend',
        'contact'  # if it exists
    ]
    
    print("🧹 Starting migration cleanup...")
    print("=" * 50)
    
    total_removed = 0
    
    for app in apps:
        migrations_dir = f"{app}/migrations"
        
        if not os.path.exists(migrations_dir):
            print(f"⚠️  {app}: migrations directory not found, skipping...")
            continue
            
        print(f"📁 Processing {app}/migrations/")
        
        # Find all .py files except __init__.py
        migration_files = glob.glob(f"{migrations_dir}/*.py")
        migration_files = [f for f in migration_files if not f.endswith('__init__.py')]
        
        if not migration_files:
            print(f"   ✅ No migration files to remove")
            continue
            
        # Remove migration files
        removed_count = 0
        for migration_file in migration_files:
            try:
                os.remove(migration_file)
                filename = os.path.basename(migration_file)
                print(f"   🗑️  Removed: {filename}")
                removed_count += 1
            except Exception as e:
                print(f"   ❌ Error removing {migration_file}: {e}")
        
        # Remove __pycache__ directory if it exists
        pycache_dir = f"{migrations_dir}/__pycache__"
        if os.path.exists(pycache_dir):
            try:
                shutil.rmtree(pycache_dir)
                print(f"   🗑️  Removed: __pycache__ directory")
            except Exception as e:
                print(f"   ❌ Error removing __pycache__: {e}")
        
        total_removed += removed_count
        print(f"   ✅ Removed {removed_count} migration files from {app}")
        print()
    
    print("=" * 50)
    print(f"🎉 Migration cleanup completed!")
    print(f"📊 Total files removed: {total_removed}")
    print()
    print("📝 Next steps:")
    print("1. Run: python manage.py makemigrations")
    print("2. Run: python manage.py migrate --fake-initial")
    print("   OR: python manage.py migrate (if database already exists)")
    print()
    print("⚠️  Note: This only removes files, your database is unchanged!")
    print("✅ Your Django installation has been fixed and migrations are working!")

if __name__ == "__main__":
    # Confirm before proceeding
    response = input("⚠️  This will remove all migration files. Continue? (y/N): ")
    if response.lower() in ['y', 'yes']:
        clean_migrations()
    else:
        print("❌ Operation cancelled.")
