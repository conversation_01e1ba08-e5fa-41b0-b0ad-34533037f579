"use client";

import { use<PERSON>tate, use<PERSON><PERSON><PERSON>, use<PERSON><PERSON>back } from "react";
import { <PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, Card<PERSON><PERSON><PERSON> } from "@/components/ui/card";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import { Skeleton } from "@/components/ui/skeleton";
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from "@/components/ui/table";
import useApi from "@/hooks/useApi";
import { MAIN_URL, PAYMENT_ANALYTICS, PAYMENT_LIST } from "@/constant/urls";
import {
  PAYMENT_CONFIG,
  getPaymentStatusColor,
  isSuccessfulPayment,
  isPendingPayment,
  isFailedPayment,
} from "@/constant/paymentConfig";
import {
  CreditCard,
  TrendingUp,
  AlertCircle,
  CheckCircle,
  Clock,
  RefreshCw,
  Download,
  Eye,
  Filter,
  IndianRupee
} from "lucide-react";
import {
  <PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON>,
  <PERSON>,
  <PERSON>,
  <PERSON>,
  <PERSON>
} from "recharts";



interface PaymentData {
  id: string;
  order_id: string;
  amount: number;
  status: string;
  payment_method: string;
  created_at: string;
  customer_name: string;
  customer_email: string;
  transaction_id?: string;
  payment_status_display: string;
  payment_method_display: string;
  order_total: number;
}

interface PaymentAnalytics {
  total_revenue: number;
  total_transactions: number;
  success_rate: number;
  status_distribution: Array<{
    status: string;
    count: number;
    total_amount: number;
  }>;
  method_distribution: Array<{
    payment_method: string;
    count: number;
    total_amount: number;
  }>;
  recent_transactions: Array<{
    day: string;
    total_amount: number;
    transaction_count: number;
  }>;
}

const PaymentManagement = () => {
  const [payments, setPayments] = useState<PaymentData[]>([]);
  const [analytics, setAnalytics] = useState<PaymentAnalytics | null>(null);
  const [loading, setLoading] = useState(true);
  const [analyticsLoading, setAnalyticsLoading] = useState(true);
  const [, setError] = useState<string | null>(null);
  const [filter, setFilter] = useState('all');

  const { read } = useApi(MAIN_URL);

  const fetchPayments = useCallback(async () => {
    try {
      setLoading(true);
      setError(null);

      // Build query parameters
      const params = new URLSearchParams({
        page_size: PAYMENT_CONFIG.DEFAULT_PAGE_SIZE.toString(),
        ordering: '-created_at'
      });

      if (filter !== 'all') {
        params.append('status', filter);
      }

      // Fetch payments from dedicated payment API
      const result = await read(`${PAYMENT_LIST}?${params.toString()}`);

      if (typeof result === 'string') {
        throw new Error(result);
      }

      // eslint-disable-next-line @typescript-eslint/no-explicit-any
      const paymentsData = result as { results?: any[]; [key: string]: any };
      const paymentsArray = paymentsData.results || paymentsData;

      // Use the payment data directly from the API
      const paymentData: PaymentData[] = Array.isArray(paymentsArray) ? paymentsArray : [];

      setPayments(paymentData);
    } catch (err) {
      setError(err instanceof Error ? err.message : 'Failed to fetch payments');
    } finally {
      setLoading(false);
    }
  }, [read, filter]);

  const fetchAnalytics = useCallback(async () => {
    try {
      setAnalyticsLoading(true);

      // Fetch payment analytics
      const result = await read(PAYMENT_ANALYTICS);

      if (typeof result === 'string') {
        throw new Error(result);
      }

      setAnalytics(result as PaymentAnalytics);
    } catch (err) {
      console.error('Failed to fetch payment analytics:', err);
    } finally {
      setAnalyticsLoading(false);
    }
  }, [read]);

  useEffect(() => {
    fetchPayments();
    fetchAnalytics();
  }, [fetchPayments, fetchAnalytics]);

  const getStatusColor = (status: string) => {
    return getPaymentStatusColor(status);
  };

  const getStatusIcon = (status: string) => {
    switch (status.toLowerCase()) {
      case 'paid':
      case 'completed':
      case 'success':
        return <CheckCircle className="h-4 w-4" />;
      case 'pending':
        return <Clock className="h-4 w-4" />;
      case 'failed':
      case 'cancelled':
        return <AlertCircle className="h-4 w-4" />;
      default:
        return <Clock className="h-4 w-4" />;
    }
  };

  const filteredPayments = payments.filter(payment => {
    if (filter === 'all') return true;
    return payment.status.toLowerCase() === filter;
  });

  // Use analytics data if available, otherwise calculate from payments
  const totalAmount = analytics?.total_revenue || payments.reduce((sum, payment) => sum + Number(payment.amount), 0);
  const totalTransactions = analytics?.total_transactions || payments.length;
  const successRate = analytics?.success_rate || 0;

  // Calculate status distribution from analytics or payments
  const successfulPayments = payments.filter(p => isSuccessfulPayment(p.status));
  const pendingPayments = payments.filter(p => isPendingPayment(p.status));
  const failedPayments = payments.filter(p => isFailedPayment(p.status));

  const paymentStatusData = analytics?.status_distribution?.map(item => ({
    name: item.status,
    value: item.count,
    color: item.status.toLowerCase().includes('success') || item.status.toLowerCase().includes('paid') || item.status.toLowerCase().includes('completed') ? '#10B981' :
           item.status.toLowerCase().includes('pending') ? '#F59E0B' : '#EF4444'
  })) || [
    { name: 'Successful', value: successfulPayments.length, color: '#10B981' },
    { name: 'Pending', value: pendingPayments.length, color: '#F59E0B' },
    { name: 'Failed', value: failedPayments.length, color: '#EF4444' },
  ];

  // Use analytics recent transactions or fallback to payments
  const recentPaymentsData = analytics?.recent_transactions?.map((transaction) => ({
    name: new Date(transaction.day).toLocaleDateString('en-US', { month: 'short', day: 'numeric' }),
    amount: transaction.total_amount
  })) || payments.slice(0, 7).map((payment, index) => ({
    name: `Day ${7 - index}`,
    amount: payment.amount
  }));

  return (
    <div className="min-h-screen bg-gradient-to-br from-gray-50 to-white">
      <div className="space-y-8 p-6 animate-fadeIn">
        {/* Header */}
        <div className="flex flex-col lg:flex-row lg:items-center lg:justify-between gap-4">
          <div>
            <h1 className="text-4xl font-bold bg-gradient-to-r from-gray-900 to-gray-600 bg-clip-text text-transparent">
              Payment Management
            </h1>
            <p className="text-gray-600 mt-2">
              Monitor transactions, payment status, and financial metrics
            </p>
          </div>
          
          <div className="flex items-center gap-3">
            <Button variant="outline" size="sm" className="gap-2">
              <Filter className="h-4 w-4" />
              Filter
            </Button>
            <Button variant="outline" size="sm" className="gap-2">
              <Download className="h-4 w-4" />
              Export
            </Button>
            <Button
              variant="outline"
              size="sm"
              onClick={() => {
                fetchPayments();
                fetchAnalytics();
              }}
              className="gap-2"
            >
              <RefreshCw className="h-4 w-4" />
              Refresh
            </Button>
          </div>
        </div>

        {/* Statistics Cards */}
        <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-4">
          <Card className="border-0 shadow-lg bg-gradient-to-br from-white to-green-50/30">
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium text-gray-600">Total Revenue</CardTitle>
              <IndianRupee className="h-5 w-5 text-green-500" />
            </CardHeader>
            <CardContent>
              {loading || analyticsLoading ? (
                <Skeleton className="h-8 w-24" />
              ) : (
                <>
                  <div className="text-2xl font-bold text-gray-900">
                    ₹{Number(totalAmount).toFixed(2)}
                  </div>
                  <p className="text-xs text-green-600 flex items-center gap-1 mt-1">
                    <TrendingUp className="h-3 w-3" />
                    From {totalTransactions} transactions
                  </p>
                </>
              )}
            </CardContent>
          </Card>

          <Card className="border-0 shadow-lg bg-gradient-to-br from-white to-blue-50/30">
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium text-gray-600">Successful Payments</CardTitle>
              <CheckCircle className="h-5 w-5 text-blue-500" />
            </CardHeader>
            <CardContent>
              {loading || analyticsLoading ? (
                <Skeleton className="h-8 w-24" />
              ) : (
                <>
                  <div className="text-2xl font-bold text-gray-900">
                    {successfulPayments.length}
                  </div>
                  <p className="text-xs text-blue-600 flex items-center gap-1 mt-1">
                    <CheckCircle className="h-3 w-3" />
                    {successRate.toFixed(1)}% success rate
                  </p>
                </>
              )}
            </CardContent>
          </Card>

          <Card className="border-0 shadow-lg bg-gradient-to-br from-white to-yellow-50/30">
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium text-gray-600">Pending Payments</CardTitle>
              <Clock className="h-5 w-5 text-yellow-500" />
            </CardHeader>
            <CardContent>
              {loading ? (
                <Skeleton className="h-8 w-24" />
              ) : (
                <>
                  <div className="text-2xl font-bold text-gray-900">
                    {pendingPayments.length}
                  </div>
                  <p className="text-xs text-yellow-600 flex items-center gap-1 mt-1">
                    <Clock className="h-3 w-3" />
                    Awaiting processing
                  </p>
                </>
              )}
            </CardContent>
          </Card>

          <Card className="border-0 shadow-lg bg-gradient-to-br from-white to-red-50/30">
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium text-gray-600">Failed Payments</CardTitle>
              <AlertCircle className="h-5 w-5 text-red-500" />
            </CardHeader>
            <CardContent>
              {loading ? (
                <Skeleton className="h-8 w-24" />
              ) : (
                <>
                  <div className="text-2xl font-bold text-gray-900">
                    {failedPayments.length}
                  </div>
                  <p className="text-xs text-red-600 flex items-center gap-1 mt-1">
                    <AlertCircle className="h-3 w-3" />
                    Need attention
                  </p>
                </>
              )}
            </CardContent>
          </Card>
        </div>

        {/* Charts */}
        <div className="grid gap-6 lg:grid-cols-2">
          {/* Payment Status Distribution */}
          <Card className="border-0 shadow-lg bg-gradient-to-br from-white to-blue-50/30">
            <CardHeader>
              <CardTitle className="flex items-center gap-2 text-gray-900">
                <CreditCard className="h-5 w-5 text-blue-500" />
                Payment Status Distribution
              </CardTitle>
            </CardHeader>
            <CardContent>
              {loading ? (
                <div className="h-[300px] flex items-center justify-center">
                  <Skeleton className="h-48 w-48 rounded-full" />
                </div>
              ) : (
                <div className="h-[300px]">
                  <ResponsiveContainer width="100%" height="100%">
                    <PieChart>
                      <Pie
                        data={paymentStatusData}
                        cx="50%"
                        cy="50%"
                        innerRadius={60}
                        outerRadius={100}
                        paddingAngle={5}
                        dataKey="value"
                      >
                        {paymentStatusData.map((entry, index) => (
                          <Cell key={`cell-${index}`} fill={entry.color} />
                        ))}
                      </Pie>
                      <Tooltip />
                    </PieChart>
                  </ResponsiveContainer>
                </div>
              )}
            </CardContent>
          </Card>

          {/* Recent Transactions */}
          <Card className="border-0 shadow-lg bg-gradient-to-br from-white to-green-50/30">
            <CardHeader>
              <CardTitle className="flex items-center gap-2 text-gray-900">
                <TrendingUp className="h-5 w-5 text-green-500" />
                Recent Transaction Amounts
              </CardTitle>
            </CardHeader>
            <CardContent>
              {loading ? (
                <div className="h-[300px] flex items-center justify-center">
                  <Skeleton className="h-4 w-full" />
                </div>
              ) : (
                <div className="h-[300px]">
                  <ResponsiveContainer width="100%" height="100%">
                    <BarChart data={recentPaymentsData}>
                      <CartesianGrid strokeDasharray="3 3" stroke="#E5E7EB" />
                      <XAxis dataKey="name" stroke="#6B7280" fontSize={12} />
                      <YAxis stroke="#6B7280" fontSize={12} />
                      <Tooltip />
                      <Bar dataKey="amount" fill="#10B981" radius={[4, 4, 0, 0]} />
                    </BarChart>
                  </ResponsiveContainer>
                </div>
              )}
            </CardContent>
          </Card>
        </div>

        {/* Payments Table */}
        <Card className="border-0 shadow-lg bg-gradient-to-br from-white to-gray-50/30">
          <CardHeader>
            <CardTitle className="flex items-center gap-2 text-gray-900">
              <CreditCard className="h-5 w-5 text-gray-700" />
              Recent Transactions
            </CardTitle>
            <div className="flex gap-2 mt-4">
              {['all', 'completed', 'pending', 'failed'].map((status) => (
                <Button
                  key={status}
                  variant={filter === status ? "default" : "outline"}
                  size="sm"
                  onClick={() => setFilter(status)}
                  className="capitalize"
                >
                  {status}
                </Button>
              ))}
            </div>
          </CardHeader>
          <CardContent>
            {loading ? (
              <div className="space-y-3">
                {[1, 2, 3, 4, 5].map((i) => (
                  <Skeleton key={i} className="h-12 w-full" />
                ))}
              </div>
            ) : (
              <Table>
                <TableHeader>
                  <TableRow>
                    <TableHead>Order ID</TableHead>
                    <TableHead>Customer</TableHead>
                    <TableHead>Amount</TableHead>
                    <TableHead>Status</TableHead>
                    <TableHead>Payment Method</TableHead>
                    <TableHead>Date</TableHead>
                    <TableHead>Actions</TableHead>
                  </TableRow>
                </TableHeader>
                <TableBody>
                  {filteredPayments.slice(0, 10).map((payment) => (
                    <TableRow key={payment.id}>
                      <TableCell className="font-medium">
                        #{payment.order_id.slice(-6)}
                      </TableCell>
                      <TableCell>{payment.customer_name}</TableCell>
                      <TableCell className="font-semibold">
                        ₹{Number(payment?.amount).toFixed(2)}
                      </TableCell>
                      <TableCell>
                        <Badge className={`${getStatusColor(payment.status)} flex items-center gap-1 w-fit`}>
                          {getStatusIcon(payment.status)}
                          {payment.payment_status_display || payment.status}
                        </Badge>
                      </TableCell>
                      <TableCell>{payment.payment_method_display || payment.payment_method}</TableCell>
                      <TableCell>
                        {new Date(payment.created_at).toLocaleDateString()}
                      </TableCell>
                      <TableCell>
                        <Button variant="ghost" size="sm">
                          <Eye className="h-4 w-4" />
                        </Button>
                      </TableCell>
                    </TableRow>
                  ))}
                </TableBody>
              </Table>
            )}
          </CardContent>
        </Card>
      </div>
    </div>
  );
};

export default PaymentManagement;
