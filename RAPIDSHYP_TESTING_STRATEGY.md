# Rapidshyp Integration - Testing Strategy

## Testing Overview

This document outlines the comprehensive testing strategy to ensure the Rapidshyp integration maintains 100% backward compatibility while adding new functionality seamlessly.

## Testing Principles

### 1. Backward Compatibility First
- **Zero Breaking Changes**: All existing functionality must work exactly as before
- **Existing API Contracts**: All current API endpoints must return expected responses
- **Database Integrity**: Existing data must remain intact and accessible
- **User Experience**: Current user flows must work without modification

### 2. Additive Testing Approach
- **New Features Only**: Test new Rapidshyp features independently
- **Fallback Mechanisms**: Verify graceful degradation when Rapidshyp is unavailable
- **Integration Points**: Test where new and existing systems interact
- **Performance Impact**: Ensure no degradation in existing performance

## Test Categories

### Category 1: Backward Compatibility Tests (Critical)

#### 1.1 Existing Order Flow Tests
```python
# tests/test_backward_compatibility.py
class BackwardCompatibilityTests(TestCase):
    def test_existing_order_creation_unchanged(self):
        """Verify existing order creation works exactly as before"""
        # Test data that worked before integration
        order_data = {
            'shipping_address': self.shipping_address.id,
            'billing_address': self.billing_address.id,
            'shipping_method': self.shipping_method.id,
            'notes': 'Test order'
        }
        
        response = self.client.post('/api/v1/orders/', order_data)
        
        # Should work exactly as before
        self.assertEqual(response.status_code, 201)
        self.assertIn('id', response.data)
        self.assertEqual(response.data['status'], 'PENDING')
        
        # New fields should be present but not affect existing behavior
        order = Order.objects.get(id=response.data['id'])
        self.assertFalse(order.rapidshyp_enabled)  # Default False
        self.assertEqual(order.pickup_pincode, '')  # Default empty
    
    def test_existing_shipping_methods_unchanged(self):
        """Verify existing shipping methods API works unchanged"""
        response = self.client.get('/api/v1/orders/shipping-methods/')
        
        self.assertEqual(response.status_code, 200)
        self.assertGreater(len(response.data), 0)
        
        # Verify existing shipping method structure
        method = response.data[0]
        required_fields = ['id', 'name', 'price', 'estimated_days', 'is_active']
        for field in required_fields:
            self.assertIn(field, method)
    
    def test_existing_order_serialization_unchanged(self):
        """Verify existing order serialization includes all original fields"""
        order = self.create_test_order()
        serializer = OrderSerializer(order)
        
        # All original fields must be present
        original_fields = [
            'id', 'status', 'shipping_address', 'billing_address',
            'shipping_method', 'subtotal', 'gst_amount', 'total',
            'tracking_number', 'created_at', 'updated_at'
        ]
        
        for field in original_fields:
            self.assertIn(field, serializer.data)
```

#### 1.2 Database Migration Tests
```python
# tests/test_database_migrations.py
class DatabaseMigrationTests(TestCase):
    def test_new_fields_are_nullable(self):
        """Verify new fields don't break existing data"""
        # Create order before migration (simulate existing data)
        order = Order.objects.create(
            user=self.user,
            shipping_address=self.shipping_address,
            billing_address=self.billing_address,
            shipping_method=self.shipping_method,
            subtotal=100.00,
            total=150.00
        )
        
        # Verify new fields have safe defaults
        self.assertFalse(order.rapidshyp_enabled)
        self.assertEqual(order.pickup_pincode, '')
        self.assertEqual(order.delivery_pincode, '')
        self.assertIsNone(order.package_weight)
        self.assertIsNone(order.rapidshyp_rate_data)
    
    def test_existing_queries_still_work(self):
        """Verify existing database queries work unchanged"""
        # Test existing query patterns
        orders = Order.objects.filter(user=self.user)
        self.assertIsNotNone(orders)
        
        # Test existing aggregations
        total_orders = Order.objects.count()
        self.assertGreaterEqual(total_orders, 0)
        
        # Test existing joins
        orders_with_shipping = Order.objects.select_related('shipping_method')
        self.assertIsNotNone(orders_with_shipping)
```

### Category 2: Rapidshyp Integration Tests

#### 2.1 Service Layer Tests
```python
# shipping/tests/test_rapidshyp_service.py
class RapidshypServiceTests(TestCase):
    def setUp(self):
        self.service = ShippingService()
        self.mock_rapidshyp_response = {
            'status': True,
            'serviceable_courier_list': [
                {
                    'courier_code': '6001',
                    'courier_name': 'BlueDart Express',
                    'total_freight': 85.50,
                    'estimated_days': 2
                }
            ]
        }
    
    @patch('shipping.services.rapidshyp_client.RapidshypClient.check_serviceability')
    def test_successful_rate_calculation(self, mock_check):
        """Test successful Rapidshyp rate calculation"""
        mock_check.return_value = self.mock_rapidshyp_response
        
        rates = self.service.get_shipping_rates(
            pickup_pincode='110001',
            delivery_pincode='400001',
            weight=1.0,
            cod=False,
            total_value=1000.0
        )
        
        self.assertTrue(rates['success'])
        self.assertEqual(rates['source'], 'rapidshyp')
        self.assertGreater(len(rates['rapidshyp_rates']), 0)
    
    @patch('shipping.services.rapidshyp_client.RapidshypClient.check_serviceability')
    def test_fallback_on_rapidshyp_failure(self, mock_check):
        """Test fallback to existing methods when Rapidshyp fails"""
        mock_check.side_effect = Exception("API Error")
        
        rates = self.service.get_shipping_rates(
            pickup_pincode='110001',
            delivery_pincode='400001',
            weight=1.0,
            cod=False,
            total_value=1000.0
        )
        
        self.assertTrue(rates['success'])
        self.assertEqual(rates['source'], 'fallback')
        self.assertGreater(len(rates['existing_methods']), 0)
        self.assertEqual(len(rates['rapidshyp_rates']), 0)
```

#### 2.2 API Endpoint Tests
```python
# shipping/tests/test_api_endpoints.py
class ShippingAPITests(APITestCase):
    def setUp(self):
        self.user = User.objects.create_user(
            email='<EMAIL>',
            password='testpass123'
        )
        self.client.force_authenticate(user=self.user)
    
    def test_calculate_rates_endpoint(self):
        """Test shipping rate calculation endpoint"""
        data = {
            'delivery_pincode': '400001',
            'weight': 1.0,
            'cod': False,
            'total_value': 1000.0
        }
        
        response = self.client.post('/api/v1/shipping/calculate-rates/', data)
        
        self.assertEqual(response.status_code, 200)
        self.assertIn('success', response.data)
        self.assertIn('rapidshyp_rates', response.data)
        self.assertIn('existing_methods', response.data)
    
    def test_calculate_rates_with_invalid_pincode(self):
        """Test rate calculation with invalid pincode"""
        data = {
            'delivery_pincode': '12345',  # Invalid pincode
            'weight': 1.0,
            'cod': False,
            'total_value': 1000.0
        }
        
        response = self.client.post('/api/v1/shipping/calculate-rates/', data)
        
        # Should still succeed with fallback methods
        self.assertEqual(response.status_code, 200)
        self.assertTrue(response.data['success'])
        self.assertEqual(response.data['source'], 'fallback')
```

### Category 3: Frontend Integration Tests

#### 3.1 Component Tests
```typescript
// components/shipping/__tests__/RapidshypRateCalculator.test.tsx
import { render, screen, waitFor } from '@testing-library/react';
import userEvent from '@testing-library/user-event';
import { RapidshypRateCalculator } from '../RapidshypRateCalculator';

// Mock the useApi hook
jest.mock('@/hooks/useApi', () => ({
  useApi: () => ({
    create: jest.fn().mockResolvedValue({
      success: true,
      source: 'rapidshyp',
      rapidshyp_rates: [
        {
          courier_code: '6001',
          courier_name: 'BlueDart Express',
          total_freight: 85.50,
          estimated_days: 2
        }
      ],
      existing_methods: []
    })
  })
}));

describe('RapidshypRateCalculator', () => {
  const mockProps = {
    deliveryPincode: '400001',
    orderWeight: 1.0,
    isCOD: false,
    orderTotal: 1000,
    onRateSelect: jest.fn(),
  };

  test('displays shipping rates correctly', async () => {
    render(<RapidshypRateCalculator {...mockProps} />);
    
    await waitFor(() => {
      expect(screen.getByText('BlueDart Express')).toBeInTheDocument();
      expect(screen.getByText('₹85.50')).toBeInTheDocument();
      expect(screen.getByText('2 business days')).toBeInTheDocument();
    });
  });

  test('handles rate selection', async () => {
    const onRateSelect = jest.fn();
    render(<RapidshypRateCalculator {...mockProps} onRateSelect={onRateSelect} />);
    
    await waitFor(() => {
      const rateOption = screen.getByText('BlueDart Express').closest('div');
      userEvent.click(rateOption);
    });
    
    expect(onRateSelect).toHaveBeenCalledWith(
      expect.objectContaining({
        courier_code: '6001',
        courier_name: 'BlueDart Express'
      })
    );
  });

  test('shows fallback message when Rapidshyp fails', async () => {
    // Mock API failure
    const mockCreate = jest.fn().mockResolvedValue({
      success: true,
      source: 'fallback',
      rapidshyp_rates: [],
      existing_methods: [
        {
          courier_code: 'existing_1',
          courier_name: 'Standard Shipping',
          total_freight: 50.00
        }
      ]
    });

    jest.mocked(useApi).mockReturnValue({ create: mockCreate });
    
    render(<RapidshypRateCalculator {...mockProps} />);
    
    await waitFor(() => {
      expect(screen.getByText(/standard shipping rates/i)).toBeInTheDocument();
      expect(screen.getByText('Standard Shipping')).toBeInTheDocument();
    });
  });
});
```

#### 3.2 Integration Tests
```typescript
// tests/integration/checkout-flow.test.tsx
import { render, screen, waitFor } from '@testing-library/react';
import userEvent from '@testing-library/user-event';
import CheckoutPage from '@/app/checkout/page';

describe('Checkout Flow Integration', () => {
  test('existing checkout flow works unchanged', async () => {
    render(<CheckoutPage />);
    
    // Verify existing shipping options are still available
    expect(screen.getByText(/standard shipping/i)).toBeInTheDocument();
    
    // Verify existing checkout steps work
    const nextButton = screen.getByText(/next/i);
    expect(nextButton).toBeInTheDocument();
    
    // Test existing shipping method selection
    const standardShipping = screen.getByText(/standard shipping/i);
    userEvent.click(standardShipping);
    
    // Should proceed to next step as before
    userEvent.click(nextButton);
    
    await waitFor(() => {
      expect(screen.getByText(/payment/i)).toBeInTheDocument();
    });
  });

  test('enhanced shipping options appear when pincode is entered', async () => {
    render(<CheckoutPage />);
    
    // Enter delivery pincode
    const pincodeInput = screen.getByPlaceholderText(/enter.*pincode/i);
    userEvent.type(pincodeInput, '400001');
    
    await waitFor(() => {
      expect(screen.getByText(/live shipping rates/i)).toBeInTheDocument();
    });
  });

  test('fallback works when Rapidshyp is unavailable', async () => {
    // Mock API failure
    jest.mocked(fetch).mockRejectedValueOnce(new Error('API Error'));
    
    render(<CheckoutPage />);
    
    const pincodeInput = screen.getByPlaceholderText(/enter.*pincode/i);
    userEvent.type(pincodeInput, '400001');
    
    await waitFor(() => {
      expect(screen.getByText(/standard shipping rates/i)).toBeInTheDocument();
    });
  });
});
```

### Category 4: Performance Tests

#### 4.1 Load Testing
```python
# tests/test_performance.py
class PerformanceTests(TestCase):
    def test_shipping_rate_calculation_performance(self):
        """Verify shipping rate calculation doesn't slow down checkout"""
        import time
        
        start_time = time.time()
        
        # Simulate multiple concurrent rate calculations
        for _ in range(10):
            response = self.client.post('/api/v1/shipping/calculate-rates/', {
                'delivery_pincode': '400001',
                'weight': 1.0,
                'cod': False,
                'total_value': 1000.0
            })
            self.assertEqual(response.status_code, 200)
        
        end_time = time.time()
        avg_time = (end_time - start_time) / 10
        
        # Should complete within 2 seconds per request
        self.assertLess(avg_time, 2.0)
    
    def test_existing_order_creation_performance_unchanged(self):
        """Verify order creation performance is not degraded"""
        import time
        
        start_time = time.time()
        
        # Create orders using existing flow
        for i in range(5):
            response = self.client.post('/api/v1/orders/', {
                'shipping_address': self.shipping_address.id,
                'billing_address': self.billing_address.id,
                'shipping_method': self.shipping_method.id,
                'notes': f'Test order {i}'
            })
            self.assertEqual(response.status_code, 201)
        
        end_time = time.time()
        avg_time = (end_time - start_time) / 5
        
        # Should maintain existing performance (< 1 second per order)
        self.assertLess(avg_time, 1.0)
```

## Test Execution Strategy

### Phase 1: Pre-Integration Testing
1. **Baseline Tests**: Run all existing tests to establish baseline
2. **Performance Baseline**: Measure current performance metrics
3. **Data Backup**: Create test data snapshots

### Phase 2: Integration Testing
1. **Backward Compatibility**: Run all compatibility tests first
2. **New Feature Testing**: Test Rapidshyp features independently
3. **Integration Points**: Test interaction between old and new systems

### Phase 3: End-to-End Testing
1. **User Journey Testing**: Complete checkout flows
2. **Error Scenario Testing**: API failures, network issues
3. **Performance Validation**: Ensure no degradation

### Phase 4: Production Readiness
1. **Load Testing**: Simulate production traffic
2. **Monitoring Setup**: Implement performance monitoring
3. **Rollback Testing**: Verify rollback procedures work

## Test Data Management

### Test Fixtures
```python
# fixtures/test_data.py
class TestDataFixtures:
    @classmethod
    def create_test_shipping_methods(cls):
        """Create standard shipping methods for testing"""
        return [
            ShippingMethod.objects.create(
                name='Standard Shipping',
                price=50.00,
                estimated_days=5,
                is_active=True
            ),
            ShippingMethod.objects.create(
                name='Express Shipping',
                price=150.00,
                estimated_days=2,
                is_active=True
            )
        ]
    
    @classmethod
    def create_test_addresses(cls, user):
        """Create test addresses"""
        return Address.objects.create(
            user=user,
            street_address='123 Test Street',
            city='Test City',
            state='Test State',
            postal_code='400001',
            address_type='SHIPPING'
        )
```

## Continuous Integration Setup

### GitHub Actions Workflow
```yaml
# .github/workflows/rapidshyp-tests.yml
name: Rapidshyp Integration Tests

on:
  pull_request:
    paths:
      - 'shipping/**'
      - 'orders/**'
      - 'ecommerce/components/shipping/**'

jobs:
  backward-compatibility:
    runs-on: ubuntu-latest
    steps:
      - uses: actions/checkout@v2
      - name: Run Backward Compatibility Tests
        run: |
          python manage.py test tests.test_backward_compatibility
          
  rapidshyp-integration:
    runs-on: ubuntu-latest
    needs: backward-compatibility
    steps:
      - uses: actions/checkout@v2
      - name: Run Rapidshyp Tests
        run: |
          python manage.py test shipping.tests
          
  frontend-tests:
    runs-on: ubuntu-latest
    steps:
      - uses: actions/checkout@v2
      - name: Run Frontend Tests
        run: |
          cd ecommerce
          npm test -- --coverage
```

This comprehensive testing strategy ensures that the Rapidshyp integration maintains complete backward compatibility while thoroughly validating all new functionality.
