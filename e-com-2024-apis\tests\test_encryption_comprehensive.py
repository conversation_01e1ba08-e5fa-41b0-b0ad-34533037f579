"""
Comprehensive test suite for database encryption implementation.
Tests encryption/decryption, data integrity, performance, and API compatibility.
"""

import time
import json
from django.test import TestCase, TransactionTestCase
from django.contrib.auth import get_user_model
from django.db import connection
from rest_framework.test import APITestCase, APIClient
from rest_framework import status
from rest_framework_simplejwt.tokens import RefreshToken

from users.models import Customer, Address, ContactMessage
from backend.encryption_utils import encryption_manager, EncryptedCharField, EncryptedEmailField
from orders.models import Order

User = get_user_model()


class EncryptionBasicTestCase(TestCase):
    """Test basic encryption/decryption functionality"""
    
    def test_encryption_manager_basic(self):
        """Test basic encryption and decryption"""
        test_data = "test_phone_9876543210"
        
        # Test encryption
        encrypted = encryption_manager.encrypt(test_data)
        self.assertIsNotNone(encrypted)
        self.assertNotEqual(encrypted, test_data)
        
        # Test decryption
        decrypted = encryption_manager.decrypt(encrypted)
        self.assertEqual(decrypted, test_data)
    
    def test_encryption_edge_cases(self):
        """Test encryption with edge cases"""
        test_cases = [
            "",  # Empty string
            None,  # None value
            "a",  # Single character
            "Special chars: !@#$%^&*()",  # Special characters
            "Unicode: 测试数据 🔐",  # Unicode characters
            "Very long string " * 100,  # Long string
        ]
        
        for test_data in test_cases:
            with self.subTest(test_data=test_data):
                encrypted = encryption_manager.encrypt(test_data)
                decrypted = encryption_manager.decrypt(encrypted)
                self.assertEqual(decrypted, test_data)
    
    def test_encrypted_field_behavior(self):
        """Test EncryptedCharField behavior"""
        field = EncryptedCharField(max_length=100)
        
        # Test field properties
        self.assertEqual(field.original_max_length, 100)
        self.assertEqual(field.max_length, 300)  # Should be expanded for encryption
        
        # Test validation
        test_value = "test_value"
        field.validate(test_value, None)  # Should not raise exception
        
        # Test long value validation
        long_value = "x" * 150
        with self.assertRaises(Exception):
            field.validate(long_value, None)


class EncryptionModelTestCase(TestCase):
    """Test encryption with Django models"""
    
    def setUp(self):
        self.user_data = {
            'email': '<EMAIL>',
            'name': 'Test User',
            'phone_number': '9876543210',
            'password': 'testpass123'
        }
    
    def test_customer_phone_encryption(self):
        """Test Customer phone number encryption"""
        customer = Customer.objects.create(**self.user_data)
        
        # Verify data is accessible normally
        self.assertEqual(customer.phone_number, '9876543210')
        
        # Verify data is encrypted in database
        with connection.cursor() as cursor:
            cursor.execute(
                "SELECT phone_number FROM users_customer WHERE id = %s",
                [customer.id]
            )
            raw_phone = cursor.fetchone()[0]
            
            # Raw data should be different from original
            self.assertNotEqual(raw_phone, '9876543210')
            # Raw data should look encrypted (base64-like)
            self.assertGreater(len(raw_phone), 20)
    
    def test_address_encryption(self):
        """Test Address field encryption"""
        customer = Customer.objects.create(**self.user_data)
        
        address_data = {
            'user': customer,
            'address_type': 'BILLING',
            'street_address': '123 Test Street',
            'apartment': 'Apt 4B',
            'city': 'Test City',
            'state': 'Test State',
            'postal_code': '123456',
            'order_user_phone': '9876543210',
            'order_user_email': '<EMAIL>'
        }
        
        address = Address.objects.create(**address_data)
        
        # Verify decrypted access works
        self.assertEqual(address.street_address, '123 Test Street')
        self.assertEqual(address.apartment, 'Apt 4B')
        self.assertEqual(address.city, 'Test City')
        self.assertEqual(address.state, 'Test State')
        self.assertEqual(address.postal_code, '123456')
        self.assertEqual(address.order_user_phone, '9876543210')
        self.assertEqual(address.order_user_email, '<EMAIL>')
        
        # Verify data is encrypted in database
        with connection.cursor() as cursor:
            cursor.execute(
                "SELECT street_address, city, postal_code FROM users_address WHERE id = %s",
                [address.id]
            )
            raw_data = cursor.fetchone()
            
            # All should be encrypted (different from original)
            self.assertNotEqual(raw_data[0], '123 Test Street')
            self.assertNotEqual(raw_data[1], 'Test City')
            self.assertNotEqual(raw_data[2], '123456')
    
    def test_contact_message_encryption(self):
        """Test ContactMessage encryption"""
        contact_data = {
            'name': 'John Doe',
            'email': '<EMAIL>',
            'subject': 'Test Subject',
            'message': 'Test message content'
        }
        
        contact = ContactMessage.objects.create(**contact_data)
        
        # Verify decrypted access works
        self.assertEqual(contact.name, 'John Doe')
        self.assertEqual(contact.email, '<EMAIL>')
        self.assertEqual(contact.subject, 'Test Subject')  # Not encrypted
        self.assertEqual(contact.message, 'Test message content')  # Not encrypted
        
        # Verify encrypted fields are encrypted in database
        with connection.cursor() as cursor:
            cursor.execute(
                "SELECT name, email FROM users_contactmessage WHERE id = %s",
                [contact.id]
            )
            raw_data = cursor.fetchone()
            
            # Name and email should be encrypted
            self.assertNotEqual(raw_data[0], 'John Doe')
            self.assertNotEqual(raw_data[1], '<EMAIL>')


class EncryptionPerformanceTestCase(TestCase):
    """Test encryption performance"""
    
    def test_encryption_performance(self):
        """Test encryption/decryption performance"""
        test_data = "performance_test_phone_9876543210"
        iterations = 100
        
        # Test encryption performance
        start_time = time.time()
        for _ in range(iterations):
            encryption_manager.encrypt(test_data)
        encrypt_time = time.time() - start_time
        
        # Test decryption performance
        encrypted_data = encryption_manager.encrypt(test_data)
        start_time = time.time()
        for _ in range(iterations):
            encryption_manager.decrypt(encrypted_data)
        decrypt_time = time.time() - start_time
        
        avg_encrypt_time = (encrypt_time / iterations) * 1000  # Convert to ms
        avg_decrypt_time = (decrypt_time / iterations) * 1000  # Convert to ms
        
        # Performance assertions (adjust thresholds as needed)
        self.assertLess(avg_encrypt_time, 5.0, "Encryption too slow")
        self.assertLess(avg_decrypt_time, 5.0, "Decryption too slow")
    
    def test_bulk_operations_performance(self):
        """Test performance with bulk operations"""
        # Create multiple customers
        customers_data = [
            {
                'email': f'user{i}@example.com',
                'name': f'User {i}',
                'phone_number': f'987654321{i % 10}',
                'password': 'testpass123'
            }
            for i in range(50)
        ]
        
        start_time = time.time()
        customers = [Customer(**data) for data in customers_data]
        Customer.objects.bulk_create(customers)
        bulk_create_time = time.time() - start_time
        
        # Bulk create should complete in reasonable time
        self.assertLess(bulk_create_time, 10.0, "Bulk create too slow")
        
        # Test bulk retrieval
        start_time = time.time()
        retrieved_customers = list(Customer.objects.all())
        bulk_retrieve_time = time.time() - start_time
        
        # Verify all data is properly decrypted
        for customer in retrieved_customers:
            self.assertTrue(customer.phone_number.startswith('987654321'))
        
        self.assertLess(bulk_retrieve_time, 5.0, "Bulk retrieve too slow")


class EncryptionAPITestCase(APITestCase):
    """Test API compatibility with encryption"""
    
    def setUp(self):
        self.client = APIClient()
        self.user = Customer.objects.create_user(
            email='<EMAIL>',
            name='Test User',
            phone_number='9876543210',
            password='testpass123'
        )
        
        # Get JWT token
        refresh = RefreshToken.for_user(self.user)
        self.client.credentials(HTTP_AUTHORIZATION=f'Bearer {refresh.access_token}')
    
    def test_user_profile_api(self):
        """Test user profile API returns decrypted data"""
        response = self.client.get('/api/v1/users/profile/')
        
        self.assertEqual(response.status_code, status.HTTP_200_OK)
        data = response.json()
        
        # Data should be in plain text (decrypted by backend)
        self.assertEqual(data['phone_number'], '9876543210')
        self.assertEqual(data['email'], '<EMAIL>')
    
    def test_address_api_crud(self):
        """Test address API with encrypted fields"""
        address_data = {
            'address_type': 'BILLING',
            'street_address': '123 Test Street',
            'apartment': 'Apt 4B',
            'city': 'Test City',
            'state': 'Test State',
            'postal_code': '123456',
            'order_user_phone': '9876543210',
            'order_user_email': '<EMAIL>'
        }
        
        # Test CREATE
        response = self.client.post('/api/v1/users/addresses/', address_data)
        self.assertEqual(response.status_code, status.HTTP_201_CREATED)
        
        address_id = response.json()['id']
        
        # Test READ
        response = self.client.get(f'/api/v1/users/addresses/{address_id}/')
        self.assertEqual(response.status_code, status.HTTP_200_OK)
        
        data = response.json()
        self.assertEqual(data['street_address'], '123 Test Street')
        self.assertEqual(data['city'], 'Test City')
        self.assertEqual(data['order_user_phone'], '9876543210')
        
        # Test UPDATE
        update_data = {'city': 'Updated City'}
        response = self.client.patch(f'/api/v1/users/addresses/{address_id}/', update_data)
        self.assertEqual(response.status_code, status.HTTP_200_OK)
        
        # Verify update
        response = self.client.get(f'/api/v1/users/addresses/{address_id}/')
        data = response.json()
        self.assertEqual(data['city'], 'Updated City')
    
    def test_contact_form_api(self):
        """Test contact form API with encrypted fields"""
        contact_data = {
            'name': 'John Doe',
            'email': '<EMAIL>',
            'subject': 'Test Subject',
            'message': 'Test message content'
        }

        response = self.client.post('/api/v1/users/contact/', contact_data)
        self.assertEqual(response.status_code, status.HTTP_201_CREATED)

        # Get the contact ID from the response
        response_data = response.json()
        contact_id = response_data['data']['id']

        # Verify data was saved and encrypted
        contact = ContactMessage.objects.get(id=contact_id)
        self.assertEqual(contact.name, 'John Doe')
        self.assertEqual(contact.email, '<EMAIL>')

        # Verify that encrypted fields are actually encrypted in the database
        with connection.cursor() as cursor:
            cursor.execute(
                "SELECT name, email FROM users_contactmessage WHERE id = %s",
                [contact.id]
            )
            raw_data = cursor.fetchone()

            # Name and email should be encrypted (different from original)
            self.assertNotEqual(raw_data[0], 'John Doe')
            self.assertNotEqual(raw_data[1], '<EMAIL>')
    
    def test_search_functionality(self):
        """Test search functionality with encrypted fields"""
        # This test requires admin permissions
        admin_user = Customer.objects.create_user(
            email='<EMAIL>',
            name='Admin User',
            password='adminpass123',
            is_staff=True,
            is_superuser=True
        )
        
        refresh = RefreshToken.for_user(admin_user)
        self.client.credentials(HTTP_AUTHORIZATION=f'Bearer {refresh.access_token}')
        
        # Test search by phone number (exact match)
        response = self.client.get('/api/v1/users/customers/?search=9876543210')
        self.assertEqual(response.status_code, status.HTTP_200_OK)
        
        # Test search by email (exact match)
        response = self.client.get('/api/v1/users/customers/?search=<EMAIL>')
        self.assertEqual(response.status_code, status.HTTP_200_OK)
        
        # Test search by name (partial match)
        response = self.client.get('/api/v1/users/customers/?search=Test')
        self.assertEqual(response.status_code, status.HTTP_200_OK)


class EncryptionDataIntegrityTestCase(TransactionTestCase):
    """Test data integrity during encryption migration"""
    
    def test_migration_data_integrity(self):
        """Test that data migration preserves all data"""
        # Create test data before "migration"
        original_customers = []
        for i in range(10):
            customer = Customer.objects.create(
                email=f'user{i}@example.com',
                name=f'User {i}',
                phone_number=f'987654321{i}',
                password='testpass123'
            )
            original_customers.append({
                'id': customer.id,
                'email': customer.email,
                'name': customer.name,
                'phone_number': customer.phone_number
            })
        
        # Verify all data is preserved after encryption
        for original in original_customers:
            customer = Customer.objects.get(id=original['id'])
            self.assertEqual(customer.email, original['email'])
            self.assertEqual(customer.name, original['name'])
            self.assertEqual(customer.phone_number, original['phone_number'])
    
    def test_backward_compatibility(self):
        """Test that the system handles both encrypted and unencrypted data"""
        # This test simulates the transition period where some data might be encrypted
        # and some might not be (though in practice, migration should be atomic)
        
        customer = Customer.objects.create(
            email='<EMAIL>',
            name='Test User',
            phone_number='9876543210',
            password='testpass123'
        )
        
        # Data should be accessible regardless of encryption state
        self.assertEqual(customer.phone_number, '9876543210')
        
        # Refresh from database
        customer.refresh_from_db()
        self.assertEqual(customer.phone_number, '9876543210')
