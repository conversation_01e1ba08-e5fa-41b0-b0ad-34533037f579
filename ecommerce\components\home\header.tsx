import Link from 'next/link'
import { Search, User, Heart, ShoppingCart } from 'lucide-react'

export default function Header() {
  return (
    <header className="container mx-auto px-4 py-4">
      <div className="flex items-center justify-between">
        <button className="p-2">
          <Search className="h-5 w-5" />
        </button>
        
        <div className="flex items-center gap-4">
          <Link href="/account">
            <User className="h-5 w-5" />
          </Link>
          <Link href="/wishlist">
            <div className="relative">
              <Heart className="h-5 w-5" />
              <span className="absolute -top-1 -right-1 h-4 w-4 rounded-full bg-red-500 text-[10px] text-white flex items-center justify-center">
                0
              </span>
            </div>
          </Link>
          <Link href="/cart">
            <div className="relative">
              <ShoppingCart className="h-5 w-5" />
              <span className="absolute -top-1 -right-1 h-4 w-4 rounded-full bg-red-500 text-[10px] text-white flex items-center justify-center">
                0
              </span>
            </div>
          </Link>
        </div>
      </div>
    </header>
  )
}

