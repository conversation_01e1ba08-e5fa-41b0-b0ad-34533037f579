"""
Database utilities for replica-aware operations.

This module provides utilities to optimize database operations
with read replica support while maintaining existing functionality.
"""

from django.db.models import Prefetch, Avg, Count
from django.conf import settings
from .db_router import ReplicaAwareManager


class ReplicaAwareQuerySet:
    """
    Utility class to create replica-aware querysets that maintain
    all existing optimizations while leveraging read replicas.
    """
    
    @staticmethod
    def get_optimized_product_queryset(base_queryset=None, use_replica=True):
        """
        Enhanced version of the existing get_optimized_product_queryset
        with replica support.
        
        Args:
            base_queryset: Base queryset to optimize (defaults to Product.objects.all())
            use_replica: Whether to use read replica for this query
        
        Returns:
            Optimized queryset with proper select_related, prefetch_related,
            and annotations to avoid N+1 queries.
        """
        from products.models import Product, Review
        
        if base_queryset is None:
            base_queryset = Product.objects.all()
        
        # Apply replica routing if requested and available
        if use_replica:
            base_queryset = ReplicaAwareManager.using_read_replica(base_queryset)
        
        return base_queryset.select_related(
            'category',
            'brand', 
            'gst'
        ).prefetch_related(
            'images',
            'variants',
            Prefetch('reviews', queryset=Review.objects.select_related('user'))
        ).annotate(
            avg_rating=Avg('reviews__rating'),
            review_count=Count('reviews', distinct=True)
        )
    
    @staticmethod
    def get_optimized_order_queryset(base_queryset=None, use_replica=True):
        """
        Optimized order queryset with replica support.
        
        Args:
            base_queryset: Base queryset to optimize
            use_replica: Whether to use read replica for this query
        """
        from orders.models import Order
        
        if base_queryset is None:
            base_queryset = Order.objects.all()
        
        # Apply replica routing if requested and available
        if use_replica:
            base_queryset = ReplicaAwareManager.using_read_replica(base_queryset)
        
        return base_queryset.select_related(
            'user',
            'shipping_address',
            'billing_address',
            'shipping_method'
        ).prefetch_related(
            'items__product',
            'items__variant'
        )
    
    @staticmethod
    def get_optimized_user_queryset(base_queryset=None, use_replica=True):
        """
        Optimized user queryset with replica support.

        Args:
            base_queryset: Base queryset to optimize
            use_replica: Whether to use read replica for this query
        """
        from django.contrib.auth import get_user_model

        User = get_user_model()

        if base_queryset is None:
            base_queryset = User.objects.all()

        # Apply replica routing if requested and available
        if use_replica:
            base_queryset = ReplicaAwareManager.using_read_replica(base_queryset)

        return base_queryset.prefetch_related(
            'addresses',
            'order_set'
            # Note: wishlist_set removed as it may not exist in all configurations
        )


class DatabaseHealthChecker:
    """
    Utility to check database health and automatically fallback
    to primary database if replicas are unavailable.
    """
    
    @staticmethod
    def check_replica_health():
        """
        Check if read replicas are healthy and accessible.
        
        Returns:
            dict: Status of each configured database
        """
        from django.db import connections
        from django.core.exceptions import ImproperlyConfigured
        
        status = {}
        
        for db_name in settings.DATABASES.keys():
            try:
                connection = connections[db_name]
                with connection.cursor() as cursor:
                    cursor.execute("SELECT 1")
                    cursor.fetchone()
                status[db_name] = 'healthy'
            except Exception as e:
                status[db_name] = f'unhealthy: {str(e)}'
        
        return status
    
    @staticmethod
    def get_healthy_read_database():
        """
        Get a healthy read database, falling back to primary if needed.

        Returns:
            str: Database name to use for read operations
        """
        from .db_router import DatabaseRouter
        router = DatabaseRouter()

        # Check replica health
        for db_name in router.read_databases:
            if db_name == 'default':
                continue

            try:
                from django.db import connections
                connection = connections[db_name]
                with connection.cursor() as cursor:
                    cursor.execute("SELECT 1")
                    cursor.fetchone()
                return db_name
            except Exception:
                continue

        # Fallback to primary database
        return 'default'


class ReplicaAwareCache:
    """
    Cache utilities that work well with database replicas.
    """
    
    @staticmethod
    def cache_key_for_model(model_class, obj_id, suffix=''):
        """
        Generate a consistent cache key for model instances.
        
        Args:
            model_class: The model class
            obj_id: Object ID
            suffix: Optional suffix for the cache key
        
        Returns:
            str: Cache key
        """
        app_label = model_class._meta.app_label
        model_name = model_class._meta.model_name
        key = f"{app_label}:{model_name}:{obj_id}"
        if suffix:
            key += f":{suffix}"
        return key
    
    @staticmethod
    def invalidate_model_cache(model_class, obj_id):
        """
        Invalidate cache for a specific model instance.
        
        Args:
            model_class: The model class
            obj_id: Object ID to invalidate
        """
        from django.core.cache import cache
        
        # Common cache patterns to invalidate
        patterns = [
            ReplicaAwareCache.cache_key_for_model(model_class, obj_id),
            ReplicaAwareCache.cache_key_for_model(model_class, obj_id, 'detail'),
            ReplicaAwareCache.cache_key_for_model(model_class, obj_id, 'list'),
        ]
        
        for pattern in patterns:
            cache.delete(pattern)


def ensure_primary_db(queryset):
    """
    Utility function to ensure a queryset uses the primary database.

    This is useful for operations that need read-after-write consistency.

    Args:
        queryset: Django queryset

    Returns:
        Queryset using primary database
    """
    return ReplicaAwareManager.using_primary(queryset)


def ensure_read_after_write_consistency(model_class, **lookup_kwargs):
    """
    Ensure read-after-write consistency for critical operations.

    Use this when you need to read data immediately after writing it.

    Args:
        model_class: The model class to query
        **lookup_kwargs: Lookup parameters for the query

    Returns:
        QuerySet using primary database for consistency

    Example:
        # After creating a user, ensure we can read it immediately
        user = User.objects.create(email="<EMAIL>")
        user_check = ensure_read_after_write_consistency(User, email="<EMAIL>").first()
    """
    queryset = model_class.objects.filter(**lookup_kwargs)
    return ReplicaAwareManager.using_primary(queryset)


def ensure_read_replica(queryset):
    """
    Utility function to ensure a queryset uses a read replica.
    
    Args:
        queryset: Django queryset
    
    Returns:
        Queryset using read replica (or primary if no replica available)
    """
    return ReplicaAwareManager.using_read_replica(queryset)
