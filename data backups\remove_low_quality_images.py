#!/usr/bin/env python3
"""
Low Quality Image Remover Script

This script removes low quality images from a specified folder based on various quality criteria:
- Image dimensions (too small)
- File size (too small or too large)
- Aspect ratio (too wide or too tall)
- Blur detection
- Duplicate detection
- Corrupted images

Usage: python remove_low_quality_images.py <folder_path>
Example: python remove_low_quality_images.py "downloaded_images/Qubo WiFi Door Bell Pro by HERO GROUP 3MP 1296p Instant Phone Visitor Video Door Phone (Wireless Single Way)"
"""

import os
import sys
import shutil
import hashlib
from pathlib import Path
from PIL import Image, ImageStat
import logging
from typing import List, Tuple, Dict

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler('image_quality_cleanup.log'),
        logging.StreamHandler()
    ]
)
logger = logging.getLogger(__name__)


class ImageQualityFilter:
    def __init__(self, target_folder: str):
        self.target_folder = Path(target_folder)
        self.backup_folder = self.target_folder.parent / f"{self.target_folder.name}_removed_images"
        self.backup_folder.mkdir(exist_ok=True)
        
        # Quality thresholds - optimized for product images
        self.min_width = 200
        self.min_height = 200
        self.min_file_size = 5 * 1024  # 5KB
        self.max_file_size = 20 * 1024 * 1024  # 20MB
        self.min_aspect_ratio = 0.2  # Very wide or very tall images
        self.max_aspect_ratio = 5.0
        self.min_variance = 50  # Image variance (to detect blank/solid color images)
        self.blur_threshold = 100  # Blur detection threshold
        
        # Statistics
        self.stats = {
            'total_images': 0,
            'removed_images': 0,
            'kept_images': 0,
            'duplicates_removed': 0,
            'reasons': {}
        }
        
        # Supported image extensions
        self.image_extensions = {'.jpg', '.jpeg', '.png', '.gif', '.bmp', '.webp'}

    def calculate_image_hash(self, image_path: Path) -> str:
        """Calculate perceptual hash of image for duplicate detection"""
        try:
            with Image.open(image_path) as img:
                # Convert to grayscale and resize for consistent hashing
                img = img.convert('L').resize((8, 8), Image.Resampling.LANCZOS)
                pixels = list(img.getdata())
                # Create a simple hash based on pixel values
                hash_string = ''.join([str(1 if pixel > 128 else 0) for pixel in pixels])
                return hashlib.md5(hash_string.encode()).hexdigest()[:16]
        except Exception as e:
            logger.warning(f"Could not calculate hash for {image_path}: {e}")
            return ""

    def calculate_image_variance(self, image_path: Path) -> float:
        """Calculate image variance to detect blank or solid color images"""
        try:
            with Image.open(image_path) as img:
                # Convert to grayscale for variance calculation
                gray_img = img.convert('L')
                stat = ImageStat.Stat(gray_img)
                return stat.var[0]
        except Exception as e:
            logger.warning(f"Could not calculate variance for {image_path}: {e}")
            return 0

    def is_image_blurry(self, img) -> bool:
        """Basic blur detection using image variance"""
        try:
            # Convert to grayscale
            gray = img.convert('L')
            
            # Resize for faster processing
            gray = gray.resize((100, 100))
            
            # Calculate variance of pixel values (low variance = blurry)
            pixels = list(gray.getdata())
            mean = sum(pixels) / len(pixels)
            variance = sum((p - mean) ** 2 for p in pixels) / len(pixels)
            
            return variance < self.blur_threshold
            
        except Exception:
            return False  # If we can't determine, assume it's not blurry

    def is_low_quality(self, image_path: Path) -> Tuple[bool, List[str]]:
        """Analyze image and determine if it's low quality"""
        reasons = []
        
        try:
            # Check file size
            file_size = image_path.stat().st_size
            if file_size < self.min_file_size:
                reasons.append(f"file_too_small_{file_size//1024}KB")
            elif file_size > self.max_file_size:
                reasons.append(f"file_too_large_{file_size//1024//1024}MB")
            
            # Check image properties
            with Image.open(image_path) as img:
                width, height = img.size
                
                # Check minimum dimensions
                if width < self.min_width:
                    reasons.append(f"width_too_small_{width}px")
                if height < self.min_height:
                    reasons.append(f"height_too_small_{height}px")
                
                # Check aspect ratio
                aspect_ratio = width / height
                if aspect_ratio < self.min_aspect_ratio:
                    reasons.append(f"aspect_ratio_too_narrow_{aspect_ratio:.2f}")
                elif aspect_ratio > self.max_aspect_ratio:
                    reasons.append(f"aspect_ratio_too_wide_{aspect_ratio:.2f}")
                
                # Check image variance (blank/solid color detection)
                variance = self.calculate_image_variance(image_path)
                if variance < self.min_variance:
                    reasons.append(f"low_variance_{variance:.1f}")
                
                # Check if image is blurry
                if self.is_image_blurry(img):
                    reasons.append("blurry_image")
                
                # Check if image is mostly one color
                try:
                    colors = img.getcolors(maxcolors=256*256*256)
                    if colors and len(colors) < 5:  # Very few colors
                        reasons.append("too_few_colors")
                except:
                    pass
                
        except Exception as e:
            reasons.append(f"corrupted_or_unreadable")
            logger.warning(f"Error analyzing {image_path}: {e}")
        
        return len(reasons) > 0, reasons

    def find_duplicates(self, image_paths: List[Path]) -> Dict[str, List[Path]]:
        """Find duplicate images using perceptual hashing"""
        hash_groups = {}
        
        logger.info("Calculating image hashes for duplicate detection...")
        for image_path in image_paths:
            img_hash = self.calculate_image_hash(image_path)
            if img_hash:
                if img_hash not in hash_groups:
                    hash_groups[img_hash] = []
                hash_groups[img_hash].append(image_path)
        
        # Return only groups with duplicates
        duplicates = {h: paths for h, paths in hash_groups.items() if len(paths) > 1}
        return duplicates

    def remove_image(self, image_path: Path, reasons: List[str]) -> bool:
        """Move image to backup folder"""
        try:
            # Create backup path
            backup_path = self.backup_folder / image_path.name
            
            # Handle name conflicts
            counter = 1
            while backup_path.exists():
                name_parts = image_path.stem, counter, image_path.suffix
                backup_path = self.backup_folder / f"{name_parts[0]}_{name_parts[1]}{name_parts[2]}"
                counter += 1
            
            # Move file
            shutil.move(str(image_path), str(backup_path))
            
            # Update statistics
            for reason in reasons:
                self.stats['reasons'][reason] = self.stats['reasons'].get(reason, 0) + 1
            
            logger.info(f"Removed: {image_path.name} - Reasons: {', '.join(reasons)}")
            return True
            
        except Exception as e:
            logger.error(f"Failed to remove {image_path}: {e}")
            return False

    def get_image_files(self) -> List[Path]:
        """Get all image files in the target folder"""
        image_files = []
        for file_path in self.target_folder.iterdir():
            if file_path.is_file() and file_path.suffix.lower() in self.image_extensions:
                image_files.append(file_path)
        return sorted(image_files)

    def process_folder(self, dry_run: bool = False) -> None:
        """Process the target folder and remove low quality images"""
        if not self.target_folder.exists():
            logger.error(f"Target folder does not exist: {self.target_folder}")
            return
        
        logger.info(f"Processing folder: {self.target_folder}")
        logger.info(f"Backup folder: {self.backup_folder}")
        
        # Get all image files
        image_files = self.get_image_files()
        logger.info(f"Found {len(image_files)} images")
        
        if not image_files:
            logger.info("No images found in the folder")
            return
        
        # Find and handle duplicates first
        duplicates = self.find_duplicates(image_files)
        if duplicates:
            logger.info(f"Found {len(duplicates)} groups of duplicate images")
            for hash_val, duplicate_paths in duplicates.items():
                # Keep the largest file, remove others
                largest_file = max(duplicate_paths, key=lambda p: p.stat().st_size)
                for dup_path in duplicate_paths:
                    if dup_path != largest_file:
                        if not dry_run:
                            if self.remove_image(dup_path, ["duplicate"]):
                                self.stats['duplicates_removed'] += 1
                                image_files.remove(dup_path)
                        else:
                            logger.info(f"[DRY RUN] Would remove duplicate: {dup_path.name}")
                            self.stats['duplicates_removed'] += 1
        
        # Analyze remaining images for quality
        for image_path in image_files:
            self.stats['total_images'] += 1
            
            is_low_qual, reasons = self.is_low_quality(image_path)
            
            if is_low_qual:
                if not dry_run:
                    if self.remove_image(image_path, reasons):
                        self.stats['removed_images'] += 1
                    else:
                        self.stats['kept_images'] += 1
                else:
                    logger.info(f"[DRY RUN] Would remove: {image_path.name} - Reasons: {', '.join(reasons)}")
                    self.stats['removed_images'] += 1
            else:
                self.stats['kept_images'] += 1
                logger.debug(f"Kept: {image_path.name}")

    def print_summary(self) -> None:
        """Print processing summary"""
        print("\n" + "="*60)
        print("IMAGE QUALITY CLEANUP SUMMARY")
        print("="*60)
        print(f"Total images processed: {self.stats['total_images']}")
        print(f"Images removed: {self.stats['removed_images']}")
        print(f"Images kept: {self.stats['kept_images']}")
        print(f"Duplicates removed: {self.stats['duplicates_removed']}")
        
        if self.stats['reasons']:
            print(f"\nRemoval reasons breakdown:")
            for reason, count in sorted(self.stats['reasons'].items()):
                print(f"  {reason}: {count}")
        
        print(f"\nBackup location: {self.backup_folder}")
        print("="*60)


def main():
    """Main function"""
    if len(sys.argv) != 2:
        print("Usage: python remove_low_quality_images.py <folder_path>")
        print('Example: python remove_low_quality_images.py "downloaded_images/Product Name"')
        sys.exit(1)
    
    folder_path = sys.argv[1]
    
    if not os.path.exists(folder_path):
        print(f"Error: Folder '{folder_path}' does not exist.")
        sys.exit(1)
    
    print(f"🔍 Image Quality Filter for: {folder_path}")
    print("="*70)
    print("This will analyze and remove low-quality images based on:")
    print("• Minimum dimensions: 200x200 pixels")
    print("• Minimum file size: 5KB")
    print("• Aspect ratio limits: 0.2 to 5.0")
    print("• Blur detection")
    print("• Duplicate detection")
    print("• Blank/solid color detection")
    print("• Corruption detection")
    print(f"\nLow-quality images will be moved to backup folder (not deleted)")
    
    # Ask for dry run first
    dry_run_response = input("\nRun in dry-run mode first to see what would be removed? (Y/n): ").strip().lower()
    dry_run = dry_run_response != 'n'
    
    # Initialize and run filter
    filter_tool = ImageQualityFilter(folder_path)
    filter_tool.process_folder(dry_run=dry_run)
    filter_tool.print_summary()
    
    if dry_run:
        proceed = input("\nProceed with actual cleanup? (y/N): ").strip().lower()
        if proceed == 'y':
            print("\nRunning actual cleanup...")
            filter_tool = ImageQualityFilter(folder_path)  # Reset stats
            filter_tool.process_folder(dry_run=False)
            filter_tool.print_summary()
        else:
            print("Cleanup cancelled.")
    
    print(f"\n✅ Processing completed!")
    print(f"📋 Check 'image_quality_cleanup.log' for detailed results.")


if __name__ == "__main__":
    main()
