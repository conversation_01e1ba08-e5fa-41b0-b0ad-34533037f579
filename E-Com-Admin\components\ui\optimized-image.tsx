"use client";

import Image from "next/image";
import { useState } from "react";
import { cn } from "@/lib/utils";

interface OptimizedImageProps {
  src: string;
  alt: string;
  width?: number;
  height?: number;
  className?: string;
  fallbackSrc?: string;
  priority?: boolean;
  fill?: boolean;
  sizes?: string;
  quality?: number;
}

export const OptimizedImage: React.FC<OptimizedImageProps> = ({
  src,
  alt,
  width,
  height,
  className,
  fallbackSrc = "/assets/product_icon.jpg",
  priority = false,
  fill = false,
  sizes,
  quality = 75,
  ...props
}) => {
  const [imgSrc, setImgSrc] = useState(src);
  const [isLoading, setIsLoading] = useState(true);

  const handleError = () => {
    if (imgSrc !== fallbackSrc) {
      setImgSrc(fallbackSrc);
    }
  };

  const handleLoad = () => {
    setIsLoading(false);
  };

  // Check if the image is from MinIO or external source
  const isExternalImage = imgSrc?.includes('minio-triumph.trio.net.in') || 
                         imgSrc?.includes('http') || 
                         imgSrc?.includes('https');

  const imageProps = {
    src: imgSrc || fallbackSrc,
    alt: alt || "Image",
    onError: handleError,
    onLoad: handleLoad,
    className: cn(
      "transition-opacity duration-300",
      isLoading && "opacity-0",
      !isLoading && "opacity-100",
      className
    ),
    quality,
    priority,
    // Use unoptimized for external images to avoid Next.js optimization issues
    unoptimized: isExternalImage,
    ...props
  };

  if (fill) {
    return (
      <div className="relative overflow-hidden">
        <Image
          {...imageProps}
          alt={alt || ""}
          fill
          sizes={sizes || "(max-width: 768px) 100vw, (max-width: 1200px) 50vw, 33vw"}
        />
        {isLoading && (
          <div className="absolute inset-0 bg-gray-200 animate-pulse" />
        )}
      </div>
    );
  }

  return (
    <div className="relative">
      <Image
        {...imageProps}
        alt={alt || ""}
        width={width || 300}
        height={height || 300}
      />
      {isLoading && (
        <div 
          className="absolute inset-0 bg-gray-200 animate-pulse rounded"
          style={{ width: width || 300, height: height || 300 }}
        />
      )}
    </div>
  );
};

// Utility function to get optimized image URL
export const getOptimizedImageUrl = (
  originalUrl: string, 
): string => {
  if (!originalUrl) return "/assets/product_icon.jpg";
  
  // If it's already a local asset, return as is
  if (originalUrl.startsWith('/')) return originalUrl;
  
  // For MinIO or external URLs, return as is since we'll use unoptimized
  if (originalUrl.includes('minio-triumph.trio.net.in') || 
      originalUrl.includes('http')) {
    return originalUrl;
  }
  
  return originalUrl;
};

// Product Image Component specifically for product listings
export const ProductImage: React.FC<{
  src: string;
  alt: string;
  size?: 'sm' | 'md' | 'lg';
  className?: string;
}> = ({ src, alt, size = 'md', className }) => {
  const sizeMap = {
    sm: { width: 40, height: 40 },
    md: { width: 60, height: 60 },
    lg: { width: 120, height: 120 }
  };

  const { width, height } = sizeMap[size];

  return (
    <div className={cn(
      "relative rounded-md overflow-hidden bg-gray-100 flex items-center justify-center",
      className
    )} style={{ width, height }}>
      <OptimizedImage
        src={src}
        alt={alt}
        width={width}
        height={height}
        className="object-cover w-full h-full"
        fallbackSrc="/assets/product_icon.jpg"
      />
    </div>
  );
};
