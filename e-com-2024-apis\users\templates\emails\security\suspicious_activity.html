<!DOCTYPE html>
<html>
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Suspicious Activity Detected - Security Alert</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            line-height: 1.6;
            color: #333;
            max-width: 600px;
            margin: 0 auto;
            padding: 20px;
            background-color: #f5f5f5;
        }
        .container {
            background-color: #ffffff;
            border-radius: 8px;
            padding: 30px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        .header {
            text-align: center;
            margin-bottom: 30px;
            border-bottom: 2px solid #e74c3c;
            padding-bottom: 20px;
        }
        .priority-high {
            background-color: #e74c3c;
            color: white;
            padding: 8px 16px;
            border-radius: 20px;
            font-weight: bold;
            display: inline-block;
            margin-bottom: 15px;
        }
        .alert-icon {
            font-size: 48px;
            color: #e74c3c;
            margin-bottom: 10px;
        }
        h1 {
            color: #2c3e50;
            margin: 0;
            font-size: 24px;
        }
        .suspicious-activity {
            background-color: #ffebee;
            border: 2px solid #e74c3c;
            border-radius: 8px;
            padding: 20px;
            margin: 20px 0;
            text-align: center;
        }
        .activity-type {
            font-size: 20px;
            font-weight: bold;
            color: #e74c3c;
            margin: 10px 0;
            padding: 10px;
            background-color: #fff;
            border-radius: 5px;
        }
        .event-details {
            background-color: #f8f9fa;
            border-left: 4px solid #e74c3c;
            padding: 20px;
            margin: 20px 0;
            border-radius: 0 5px 5px 0;
        }
        .detail-row {
            display: flex;
            justify-content: space-between;
            margin-bottom: 10px;
            padding: 8px 0;
            border-bottom: 1px solid #ecf0f1;
        }
        .detail-label {
            font-weight: bold;
            color: #34495e;
            min-width: 120px;
        }
        .detail-value {
            color: #2c3e50;
            word-break: break-all;
        }
        .timestamp {
            background-color: #ecf0f1;
            padding: 15px;
            border-radius: 5px;
            text-align: center;
            margin: 20px 0;
            font-family: monospace;
            color: #7f8c8d;
        }
        .action-section {
            background-color: #e8f5e8;
            border: 1px solid #27ae60;
            border-radius: 5px;
            padding: 15px;
            margin: 20px 0;
        }
        .action-title {
            color: #27ae60;
            font-weight: bold;
            margin-bottom: 10px;
        }
        .urgent-notice {
            background-color: #fff3cd;
            border: 1px solid #ffeaa7;
            border-radius: 5px;
            padding: 15px;
            margin: 20px 0;
            color: #856404;
        }
        .footer {
            margin-top: 30px;
            padding-top: 20px;
            border-top: 1px solid #ecf0f1;
            font-size: 12px;
            color: #7f8c8d;
            text-align: center;
        }
        .details-box {
            background-color: #f8f9fa;
            border: 1px solid #dee2e6;
            border-radius: 5px;
            padding: 15px;
            margin: 10px 0;
            font-family: monospace;
            font-size: 14px;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <div class="alert-icon">🚨</div>
            <div class="priority-high">HIGH PRIORITY</div>
            <h1>Suspicious Activity Detected</h1>
            <p style="margin: 10px 0 0 0; color: #7f8c8d;">{{ site_name }} Security Monitoring</p>
        </div>
        
        <div class="suspicious-activity">
            <h3 style="margin-top: 0; color: #e74c3c;">⚠️ SECURITY THREAT DETECTED</h3>
            <div class="activity-type">{{ event_data.activity_type|default:"Unknown Activity" }}</div>
            <p style="margin-bottom: 0; color: #666;">Unusual behavior pattern identified and flagged for review</p>
        </div>
        
        <div class="event-details">
            <h3 style="margin-top: 0; color: #e74c3c;">Activity Details</h3>
            
            <div class="detail-row">
                <span class="detail-label">Activity Type:</span>
                <span class="detail-value">{{ event_data.activity_type|default:"Unspecified" }}</span>
            </div>
            
            <div class="detail-row">
                <span class="detail-label">Source IP:</span>
                <span class="detail-value">{{ ip_address }}</span>
            </div>
            
            <div class="detail-row">
                <span class="detail-label">User:</span>
                <span class="detail-value">{{ user_email }}</span>
            </div>
            
            <div class="detail-row">
                <span class="detail-label">User Agent:</span>
                <span class="detail-value">{{ event_data.user_agent|default:"Unknown" }}</span>
            </div>
            
            {% if event_data.details %}
            <div class="detail-row">
                <span class="detail-label">Additional Details:</span>
                <span class="detail-value">
                    <div class="details-box">
                        {% for key, value in event_data.details.items %}
                            <strong>{{ key|title }}:</strong> {{ value }}<br>
                        {% endfor %}
                    </div>
                </span>
            </div>
            {% endif %}
        </div>
        
        <div class="timestamp">
            <strong>Detection Time:</strong> {{ timestamp|date:"F d, Y, g:i A T" }}
        </div>
        
        <div class="action-section">
            <div class="action-title">🛡️ Security Response</div>
            <p>{{ event_data.action_taken|default:"Activity has been logged and is being monitored for patterns." }}</p>
        </div>
        
        <div class="urgent-notice">
            <strong>🔍 Investigation Required:</strong>
            <ul style="margin: 10px 0;">
                <li><strong>Immediate Review:</strong> Examine the suspicious activity details above</li>
                <li><strong>Pattern Analysis:</strong> Check for similar activities from this IP or user</li>
                <li><strong>User Verification:</strong> If user is known, verify if activity is legitimate</li>
                <li><strong>Security Measures:</strong> Consider implementing additional security controls</li>
                <li><strong>Incident Response:</strong> Follow your security incident response procedures</li>
            </ul>
        </div>
        
        <div style="background-color: #e3f2fd; border: 1px solid #2196f3; border-radius: 5px; padding: 15px; margin: 20px 0;">
            <h4 style="margin-top: 0; color: #1976d2;">🔍 Security Analysis</h4>
            <p><strong>Risk Assessment:</strong> High - Suspicious activity patterns detected</p>
            <p><strong>Source:</strong> IP {{ ip_address }} {% if user_email != "Anonymous" %}(User: {{ user_email }}){% endif %}</p>
            <p style="margin-bottom: 0;"><strong>Recommendation:</strong> Immediate investigation and potential security measures required</p>
        </div>
        
        <div class="footer">
            <p>This is an automated security alert from {{ site_name }}.</p>
            <p>Alert generated at {{ timestamp|date:"F d, Y, g:i A T" }}</p>
            <p>Please investigate this activity promptly to ensure platform security.</p>
        </div>
    </div>
</body>
</html>
