# SEO Setup Guide

This document explains how to properly configure the SEO settings for your e-commerce application.

## Environment Variables

For proper SEO functionality, you should set the following environment variable in your deployment environment:

```
NEXT_PUBLIC_SITE_URL=https://your-actual-domain.com
```

Replace `https://your-actual-domain.com` with your actual production domain.

## How to Set Environment Variables

### Local Development

Create a `.env.local` file in the root of your project with:

```
NEXT_PUBLIC_SITE_URL=http://localhost:3000
```

### Production Deployment

Set the environment variable in your hosting platform:

#### Vercel

1. Go to your project settings in Vercel
2. Navigate to the "Environment Variables" section
3. Add `NEXT_PUBLIC_SITE_URL` with your production URL

#### Other Hosting Providers

Follow your hosting provider's documentation for setting environment variables.

## SEO Features

The application includes the following SEO optimizations:

1. **Dynamic Metadata**: Title, description, and keywords optimized for search engines
2. **Open Graph Tags**: For better social media sharing
3. **Twitter Card Tags**: For Twitter sharing
4. **JSON-LD Structured Data**: For rich search results
5. **Dynamic Sitemap**: Generated at build time
6. **Robots.txt**: Properly configured for search engine crawling
7. **Manifest.json**: For Progressive Web App support

## Logo Display

The logo is configured to display properly in:

1. The website header
2. Search engine results
3. Social media shares
4. Browser tabs (favicon)

## Testing SEO

You can test your SEO configuration using:

1. [Google's Rich Results Test](https://search.google.com/test/rich-results)
2. [Facebook Sharing Debugger](https://developers.facebook.com/tools/debug/)
3. [Twitter Card Validator](https://cards-dev.twitter.com/validator)
4. [LinkedIn Post Inspector](https://www.linkedin.com/post-inspector/)

## Troubleshooting

If your SEO features aren't working correctly:

1. Verify the `NEXT_PUBLIC_SITE_URL` is set correctly
2. Make sure your site is accessible to search engines
3. Check that your logo files exist at the correct paths
4. Validate your structured data using Google's testing tools
