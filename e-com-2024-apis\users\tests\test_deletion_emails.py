"""
Test cases for data deletion request email notifications.
"""

from django.test import TestCase
from django.contrib.auth import get_user_model
from django.core import mail
from django.urls import reverse
from rest_framework.test import APIClient
from rest_framework import status
from users.models import DataDeletionRequest
from users.utils import (
    send_deletion_request_emails,
    send_deletion_confirmation_email,
    send_deletion_admin_notification,
    send_deletion_status_update_email
)

User = get_user_model()


class DeletionEmailTestCase(TestCase):
    """Test email notifications for data deletion requests"""
    
    def setUp(self):
        self.user = User.objects.create_user(
            email='<EMAIL>',
            name='Test User',
            password='testpass123'
        )
        self.client = APIClient()
        self.client.force_authenticate(user=self.user)
    
    def test_deletion_request_sends_emails(self):
        """Test that creating a deletion request sends emails"""
        # Clear any existing emails
        mail.outbox = []
        
        url = reverse('data-deletion')
        deletion_data = {
            'reason': 'I want to delete my account'
        }
        
        response = self.client.post(url, deletion_data, format='json')
        self.assertEqual(response.status_code, status.HTTP_201_CREATED)
        
        # Check that emails were sent
        self.assertEqual(len(mail.outbox), 2)  # User confirmation + Admin notification
        
        # Check user confirmation email
        user_email = mail.outbox[0]
        self.assertIn('<EMAIL>', user_email.to)
        self.assertIn('Data Deletion Request Received', user_email.subject)
        
        # Check admin notification email
        admin_email = mail.outbox[1]
        self.assertIn('<EMAIL>', admin_email.to)
        self.assertIn('New Data Deletion Request', admin_email.subject)
    
    def test_deletion_confirmation_email_content(self):
        """Test the content of deletion confirmation email"""
        deletion_request = DataDeletionRequest.objects.create(
            user=self.user,
            reason='Test reason',
            ip_address='127.0.0.1'
        )
        
        # Clear any existing emails
        mail.outbox = []
        
        send_deletion_confirmation_email(deletion_request)
        
        self.assertEqual(len(mail.outbox), 1)
        email = mail.outbox[0]
        
        self.assertEqual(email.to, ['<EMAIL>'])
        self.assertIn('Data Deletion Request Received', email.subject)
        self.assertIn('Test User', email.body)
        self.assertIn('Test reason', email.body)
    
    def test_deletion_admin_notification_content(self):
        """Test the content of admin notification email"""
        deletion_request = DataDeletionRequest.objects.create(
            user=self.user,
            reason='Test reason',
            ip_address='127.0.0.1'
        )
        
        # Clear any existing emails
        mail.outbox = []
        
        send_deletion_admin_notification(deletion_request)
        
        self.assertEqual(len(mail.outbox), 1)
        email = mail.outbox[0]
        
        self.assertIn('<EMAIL>', email.to)
        self.assertIn('<EMAIL>', email.to)
        self.assertIn('New Data Deletion Request', email.subject)
        self.assertIn('<EMAIL>', email.body)
    
    def test_deletion_status_update_email(self):
        """Test status update email when request is completed"""
        deletion_request = DataDeletionRequest.objects.create(
            user=self.user,
            reason='Test reason',
            ip_address='127.0.0.1'
        )
        
        # Clear any existing emails
        mail.outbox = []
        
        # Complete the deletion request
        deletion_request.complete_deletion(
            processed_by=None,
            notes='Request processed successfully'
        )
        
        # Check that status update email was sent
        self.assertEqual(len(mail.outbox), 1)
        email = mail.outbox[0]
        
        self.assertEqual(email.to, ['<EMAIL>'])
        self.assertIn('Data Deletion Request Update', email.subject)
        self.assertIn('COMPLETED', email.body)
    
    def test_deletion_status_update_rejected(self):
        """Test status update email when request is rejected"""
        deletion_request = DataDeletionRequest.objects.create(
            user=self.user,
            reason='Test reason',
            ip_address='127.0.0.1'
        )
        
        # Clear any existing emails
        mail.outbox = []
        
        # Reject the deletion request
        deletion_request.reject_deletion(
            processed_by=None,
            notes='Request rejected due to pending orders'
        )
        
        # Check that status update email was sent
        self.assertEqual(len(mail.outbox), 1)
        email = mail.outbox[0]
        
        self.assertEqual(email.to, ['<EMAIL>'])
        self.assertIn('Data Deletion Request Update', email.subject)
        self.assertIn('REJECTED', email.body)
        self.assertIn('pending orders', email.body)
    
    def test_email_error_handling(self):
        """Test that email errors don't break the deletion request creation"""
        # Mock email sending to fail
        from unittest.mock import patch
        
        url = reverse('data-deletion')
        deletion_data = {
            'reason': 'I want to delete my account'
        }
        
        with patch('users.utils.send_deletion_request_emails', side_effect=Exception('Email error')):
            response = self.client.post(url, deletion_data, format='json')
            
            # Request should still succeed even if email fails
            self.assertEqual(response.status_code, status.HTTP_201_CREATED)
            
            # Deletion request should still be created
            self.assertTrue(DataDeletionRequest.objects.filter(user=self.user).exists())
