"""
Comprehensive tests for user views and API endpoints
Tests authentication, registration, profile management, addresses, and privacy features
"""

import pytest
import json
from decimal import Decimal
from unittest.mock import patch, MagicMock
from django.test import TestCase
from django.urls import reverse
from django.contrib.auth import get_user_model
from rest_framework.test import APITestCase, APIClient
from rest_framework import status
from rest_framework_simplejwt.tokens import RefreshToken

from users.models import Address, PaymentMethod, Wishlist, ContactMessage, ContactMessage, UserConsent
from products.models import Product, Category, Brand

User = get_user_model()

class TestUserRegistrationView(APITestCase):
    """Test user registration API"""
    
    def setUp(self):
        """Set up test data"""
        self.registration_url = reverse('user-list-create')
        self.valid_data = {
            'email': '<EMAIL>',
            'password': 'testpass123',
            'name': 'Test User',
            'phone_number': '+1234567890'
        }
    
    def test_user_registration_success(self):
        """Test successful user registration"""
        response = self.client.post(self.registration_url, self.valid_data)
        
        self.assertEqual(response.status_code, status.HTTP_201_CREATED)
        self.assertTrue(User.objects.filter(email='<EMAIL>').exists())
        
        # Check response data
        self.assertIn('email', response.data)
        self.assertIn('name', response.data)
        self.assertNotIn('password', response.data)  # Password should not be returned
    
    def test_user_registration_duplicate_email(self):
        """Test registration with duplicate email"""
        # Create user first
        User.objects.create_user(**self.valid_data)
        
        # Try to register with same email
        response = self.client.post(self.registration_url, self.valid_data)
        
        self.assertEqual(response.status_code, status.HTTP_400_BAD_REQUEST)
        self.assertIn('email', response.data)
    
    def test_user_registration_invalid_email(self):
        """Test registration with invalid email"""
        invalid_data = self.valid_data.copy()
        invalid_data['email'] = 'invalid-email'
        
        response = self.client.post(self.registration_url, invalid_data)
        
        self.assertEqual(response.status_code, status.HTTP_400_BAD_REQUEST)
        self.assertIn('email', response.data)
    
    def test_user_registration_weak_password(self):
        """Test registration with weak password"""
        invalid_data = self.valid_data.copy()
        invalid_data['password'] = '123'
        
        response = self.client.post(self.registration_url, invalid_data)
        
        self.assertEqual(response.status_code, status.HTTP_400_BAD_REQUEST)
        self.assertIn('password', response.data)
    
    def test_user_registration_missing_required_fields(self):
        """Test registration with missing required fields"""
        incomplete_data = {
            'email': '<EMAIL>'
            # Missing password and name
        }
        
        response = self.client.post(self.registration_url, incomplete_data)
        
        self.assertEqual(response.status_code, status.HTTP_400_BAD_REQUEST)
        self.assertIn('password', response.data)
        self.assertIn('name', response.data)

class TestLoginView(APITestCase):
    """Test user login API"""
    
    def setUp(self):
        """Set up test data"""
        self.login_url = reverse('login')
        self.user = User.objects.create_user(
            email='<EMAIL>',
            password='testpass123',
            name='Test User'
        )
    
    def test_login_success(self):
        """Test successful login"""
        login_data = {
            'email': '<EMAIL>',
            'password': 'testpass123'
        }
        
        response = self.client.post(self.login_url, login_data)
        
        self.assertEqual(response.status_code, status.HTTP_200_OK)
        self.assertIn('accessToken', response.data)
        self.assertIn('refreshToken', response.data)
        self.assertIn('email', response.data)  # User data is flattened in response
    
    def test_login_invalid_credentials(self):
        """Test login with invalid credentials"""
        login_data = {
            'email': '<EMAIL>',
            'password': 'wrongpassword'
        }
        
        response = self.client.post(self.login_url, login_data)
        
        self.assertEqual(response.status_code, status.HTTP_400_BAD_REQUEST)
    
    def test_login_nonexistent_user(self):
        """Test login with nonexistent user"""
        login_data = {
            'email': '<EMAIL>',
            'password': 'testpass123'
        }
        
        response = self.client.post(self.login_url, login_data)
        
        self.assertEqual(response.status_code, status.HTTP_400_BAD_REQUEST)
    
    def test_login_missing_fields(self):
        """Test login with missing fields"""
        login_data = {
            'email': '<EMAIL>'
            # Missing password
        }
        
        response = self.client.post(self.login_url, login_data)
        
        self.assertEqual(response.status_code, status.HTTP_400_BAD_REQUEST)

class TestUserProfileView(APITestCase):
    """Test user profile management"""
    
    def setUp(self):
        """Set up test data"""
        self.user = User.objects.create_user(
            email='<EMAIL>',
            password='testpass123',
            name='Test User'
        )
        self.profile_url = reverse('user-profile')
        
        # Authenticate user
        refresh = RefreshToken.for_user(self.user)
        self.client.credentials(HTTP_AUTHORIZATION=f'Bearer {refresh.access_token}')
    
    def test_get_user_profile(self):
        """Test getting user profile"""
        response = self.client.get(self.profile_url)
        
        self.assertEqual(response.status_code, status.HTTP_200_OK)
        self.assertEqual(response.data['email'], self.user.email)
        self.assertEqual(response.data['name'], self.user.name)
    
    def test_update_user_profile(self):
        """Test updating user profile"""
        update_data = {
            'name': 'Updated Name',
            'phone_number': '+9876543210'
        }

        profile_update_url = reverse('profile-update')
        response = self.client.patch(profile_update_url, update_data)
        
        self.assertEqual(response.status_code, status.HTTP_200_OK)
        
        # Verify update
        self.user.refresh_from_db()
        self.assertEqual(self.user.name, 'Updated Name')
        self.assertEqual(self.user.phone_number, '+9876543210')
    
    def test_profile_unauthenticated(self):
        """Test profile access without authentication"""
        self.client.credentials()  # Remove authentication
        
        response = self.client.get(self.profile_url)
        
        self.assertEqual(response.status_code, status.HTTP_401_UNAUTHORIZED)

class TestChangePasswordView(APITestCase):
    """Test password change functionality"""
    
    def setUp(self):
        """Set up test data"""
        self.user = User.objects.create_user(
            email='<EMAIL>',
            password='oldpassword123',
            name='Test User'
        )
        self.change_password_url = reverse('user-change-password')
        
        # Authenticate user
        refresh = RefreshToken.for_user(self.user)
        self.client.credentials(HTTP_AUTHORIZATION=f'Bearer {refresh.access_token}')
    
    def test_change_password_success(self):
        """Test successful password change"""
        change_data = {
            'old_password': 'oldpassword123',
            'new_password': 'newpassword123'
        }
        
        response = self.client.put(self.change_password_url, change_data)
        
        self.assertEqual(response.status_code, status.HTTP_200_OK)
        
        # Verify password changed
        self.user.refresh_from_db()
        self.assertTrue(self.user.check_password('newpassword123'))
    
    def test_change_password_wrong_old_password(self):
        """Test password change with wrong old password"""
        change_data = {
            'old_password': 'wrongpassword',
            'new_password': 'newpassword123'
        }
        
        response = self.client.put(self.change_password_url, change_data)
        
        self.assertEqual(response.status_code, status.HTTP_400_BAD_REQUEST)
        self.assertIn('old_password', response.data)
    
    def test_change_password_weak_new_password(self):
        """Test password change with weak new password"""
        change_data = {
            'old_password': 'oldpassword123',
            'new_password': '123'
        }
        
        response = self.client.put(self.change_password_url, change_data)
        
        self.assertEqual(response.status_code, status.HTTP_400_BAD_REQUEST)
        self.assertIn('new_password', response.data)

class TestAddressViews(APITestCase):
    """Test address management views"""
    
    def setUp(self):
        """Set up test data"""
        self.user = User.objects.create_user(
            email='<EMAIL>',
            password='testpass123',
            name='Test User'
        )
        self.address_list_url = reverse('address-list-create')
        
        # Authenticate user
        refresh = RefreshToken.for_user(self.user)
        self.client.credentials(HTTP_AUTHORIZATION=f'Bearer {refresh.access_token}')
        
        self.valid_address_data = {
            'street_address': '123 Main St',
            'apartment': 'Apt 4B',
            'city': 'Test City',
            'state': 'Test State',
            'postal_code': '12345',
            'country': 'Test Country',
            'is_default': True,
            'address_type': 'BILLING'  # Add required address_type field
        }
    
    def test_create_address(self):
        """Test creating a new address"""
        response = self.client.post(self.address_list_url, self.valid_address_data)

        self.assertEqual(response.status_code, status.HTTP_201_CREATED)
        self.assertTrue(Address.objects.filter(user=self.user).exists())
    
    def test_list_addresses(self):
        """Test listing user addresses"""
        # Create test address
        Address.objects.create(user=self.user, **self.valid_address_data)
        
        response = self.client.get(self.address_list_url)
        
        self.assertEqual(response.status_code, status.HTTP_200_OK)
        self.assertEqual(len(response.data), 1)
        self.assertEqual(response.data[0]['street_address'], '123 Main St')
    
    def test_update_address(self):
        """Test updating an address"""
        address = Address.objects.create(user=self.user, **self.valid_address_data)
        address_detail_url = reverse('address-detail', kwargs={'pk': address.pk})
        
        update_data = {'city': 'Updated City'}
        response = self.client.patch(address_detail_url, update_data)
        
        self.assertEqual(response.status_code, status.HTTP_200_OK)
        
        # Verify update
        address.refresh_from_db()
        self.assertEqual(address.city, 'Updated City')
    
    def test_delete_address(self):
        """Test deleting an address"""
        address = Address.objects.create(user=self.user, **self.valid_address_data)
        address_detail_url = reverse('address-detail', kwargs={'pk': address.pk})
        
        response = self.client.delete(address_detail_url)
        
        self.assertEqual(response.status_code, status.HTTP_200_OK)
        self.assertFalse(Address.objects.filter(pk=address.pk).exists())

class TestWishlistViews(APITestCase):
    """Test wishlist management views"""
    
    def setUp(self):
        """Set up test data"""
        self.user = User.objects.create_user(
            email='<EMAIL>',
            password='testpass123',
            name='Test User'
        )
        self.wishlist_url = reverse('wishlist-list-create')
        
        # Authenticate user
        refresh = RefreshToken.for_user(self.user)
        self.client.credentials(HTTP_AUTHORIZATION=f'Bearer {refresh.access_token}')
        
        # Create test product
        category = Category.objects.create(name='Electronics')
        brand = Brand.objects.create(name='Apple')
        self.product = Product.objects.create(
            name='iPhone 13',
            category=category,
            brand=brand,
            price=Decimal('999.00'),
            stock=50
        )
    
    def test_add_to_wishlist(self):
        """Test adding product to wishlist"""
        wishlist_data = {'product_id': self.product.id}
        
        response = self.client.post(self.wishlist_url, wishlist_data)
        
        self.assertEqual(response.status_code, status.HTTP_201_CREATED)
        self.assertTrue(Wishlist.objects.filter(user=self.user, product=self.product).exists())
    
    def test_list_wishlist(self):
        """Test listing wishlist items"""
        # Add product to wishlist
        Wishlist.objects.create(user=self.user, product=self.product)
        
        response = self.client.get(self.wishlist_url)
        
        self.assertEqual(response.status_code, status.HTTP_200_OK)
        self.assertEqual(len(response.data), 1)
        self.assertEqual(response.data[0]['product']['name'], 'iPhone 13')
    
    def test_remove_from_wishlist(self):
        """Test removing product from wishlist"""
        wishlist_item = Wishlist.objects.create(user=self.user, product=self.product)
        remove_url = reverse('remove-wishlist') + f'?wishlist_id={wishlist_item.id}'
        
        response = self.client.delete(remove_url)
        
        self.assertEqual(response.status_code, status.HTTP_200_OK)
        self.assertFalse(Wishlist.objects.filter(pk=wishlist_item.pk).exists())
    
    def test_toggle_wishlist(self):
        """Test toggling product in wishlist"""
        toggle_url = reverse('wishlist-toggle', kwargs={'pk': self.product.id})
        toggle_data = {}
        
        # Add to wishlist
        response = self.client.post(toggle_url, toggle_data)
        self.assertEqual(response.status_code, status.HTTP_201_CREATED)
        self.assertTrue(Wishlist.objects.filter(user=self.user, product=self.product).exists())

        # Remove from wishlist
        response = self.client.post(toggle_url, toggle_data)
        self.assertEqual(response.status_code, status.HTTP_200_OK)
        self.assertFalse(Wishlist.objects.filter(user=self.user, product=self.product).exists())

class TestContactFormView(APITestCase):
    """Test contact form submission"""
    
    def setUp(self):
        """Set up test data"""
        self.contact_url = reverse('contact-form')
        self.valid_contact_data = {
            'name': 'John Doe',
            'email': '<EMAIL>',
            'subject': 'Test Subject',
            'message': 'This is a test message.'
        }
    
    @patch('users.views.send_contact_emails')
    def test_submit_contact_form(self, mock_send_emails):
        """Test successful contact form submission"""
        mock_send_emails.return_value = True

        response = self.client.post(self.contact_url, self.valid_contact_data)

        self.assertEqual(response.status_code, status.HTTP_201_CREATED)

        # Check that a ContactMessage was created (can't filter by encrypted email)
        self.assertEqual(ContactMessage.objects.count(), 1)

        # Verify the created message has the correct data
        message = ContactMessage.objects.first()
        self.assertEqual(message.name, 'John Doe')
        self.assertEqual(message.email, '<EMAIL>')  # Decryption happens automatically
        self.assertEqual(message.subject, 'Test Subject')
    
    def test_contact_form_invalid_email(self):
        """Test contact form with invalid email"""
        invalid_data = self.valid_contact_data.copy()
        invalid_data['email'] = 'invalid-email'
        
        response = self.client.post(self.contact_url, invalid_data)
        
        self.assertEqual(response.status_code, status.HTTP_400_BAD_REQUEST)
        self.assertIn('email', response.data)
    
    def test_contact_form_missing_fields(self):
        """Test contact form with missing required fields"""
        incomplete_data = {
            'name': 'John Doe'
            # Missing email, subject, message
        }
        
        response = self.client.post(self.contact_url, incomplete_data)
        
        self.assertEqual(response.status_code, status.HTTP_400_BAD_REQUEST)

class TestForgotPasswordView(APITestCase):
    """Test forgot password functionality"""
    
    def setUp(self):
        """Set up test data"""
        self.user = User.objects.create_user(
            email='<EMAIL>',
            password='testpass123',
            name='Test User'
        )
        self.forgot_password_url = reverse('forgot-password')
    
    @patch('users.utils.send_password_reset_email')
    def test_forgot_password_success(self, mock_send_email):
        """Test successful forgot password request"""
        forgot_data = {'email': '<EMAIL>'}

        response = self.client.post(self.forgot_password_url, forgot_data)

        self.assertEqual(response.status_code, status.HTTP_200_OK)
        mock_send_email.assert_called_once()
    
    def test_forgot_password_nonexistent_email(self):
        """Test forgot password with nonexistent email"""
        forgot_data = {'email': '<EMAIL>'}
        
        response = self.client.post(self.forgot_password_url, forgot_data)
        
        # Should still return 200 for security reasons
        self.assertEqual(response.status_code, status.HTTP_200_OK)
    
    def test_forgot_password_invalid_email(self):
        """Test forgot password with invalid email format"""
        forgot_data = {'email': 'invalid-email'}
        
        response = self.client.post(self.forgot_password_url, forgot_data)
        
        self.assertEqual(response.status_code, status.HTTP_400_BAD_REQUEST)

class TestPrivacyViews(APITestCase):
    """Test privacy-related views"""
    
    def setUp(self):
        """Set up test data"""
        self.user = User.objects.create_user(
            email='<EMAIL>',
            password='testpass123',
            name='Test User'
        )
        
        # Authenticate user
        refresh = RefreshToken.for_user(self.user)
        self.client.credentials(HTTP_AUTHORIZATION=f'Bearer {refresh.access_token}')
    
    def test_consent_management_view(self):
        """Test consent management view"""
        consent_url = reverse('consent-management')
        
        response = self.client.get(consent_url)
        
        self.assertEqual(response.status_code, status.HTTP_200_OK)
    
    def test_data_export_view(self):
        """Test data export view"""
        export_url = reverse('data-export')
        
        response = self.client.get(export_url)
        
        self.assertEqual(response.status_code, status.HTTP_200_OK)
        self.assertEqual(response['Content-Type'], 'text/csv; charset=utf-8')
    
    def test_data_deletion_request(self):
        """Test data deletion request"""
        deletion_url = reverse('data-deletion')
        deletion_data = {
            'reason': 'No longer need account'
        }
        
        response = self.client.post(deletion_url, deletion_data)
        
        self.assertEqual(response.status_code, status.HTTP_200_OK)
