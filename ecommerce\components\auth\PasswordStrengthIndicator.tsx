"use client";

import React from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import { Check, X, AlertCircle } from 'lucide-react';
import { 
  validatePasswordStrength, 
  getPasswordStrengthLevel, 
  getPasswordStrengthColor,
  type PasswordValidationResult 
} from '../../utils/passwordValidation';

interface PasswordStrengthIndicatorProps {
  password: string;
  showRequirements?: boolean;
  className?: string;
}

const PasswordStrengthIndicator: React.FC<PasswordStrengthIndicatorProps> = ({
  password,
  showRequirements = true,
  className = ""
}) => {
  const validation: PasswordValidationResult = validatePasswordStrength(password);
  const strengthLevel = getPasswordStrengthLevel(validation.score);
  const strengthColor = getPasswordStrengthColor(validation.score);

  if (!password) {
    return null;
  }

  return (
    <div className={`space-y-3 ${className}`}>
      {/* Password Strength Bar */}
      <div className="space-y-2">
        <div className="flex justify-between items-center">
          <span className="text-sm font-medium text-gray-700">
            Password Strength
          </span>
          <span className={`text-sm font-medium ${strengthLevel.color}`}>
            {strengthLevel.level.charAt(0).toUpperCase() + strengthLevel.level.slice(1)}
          </span>
        </div>
        
        <div className="w-full bg-gray-200 rounded-full h-2">
          <motion.div
            className={`h-2 rounded-full ${strengthColor}`}
            initial={{ width: 0 }}
            animate={{ width: `${validation.score}%` }}
            transition={{ duration: 0.3, ease: "easeOut" }}
          />
        </div>
        
        <p className={`text-xs ${strengthLevel.color}`}>
          {strengthLevel.description}
        </p>
      </div>

      {/* Requirements List */}
      {showRequirements && (
        <AnimatePresence>
          <motion.div
            initial={{ opacity: 0, height: 0 }}
            animate={{ opacity: 1, height: "auto" }}
            exit={{ opacity: 0, height: 0 }}
            transition={{ duration: 0.3 }}
            className="space-y-2"
          >
            <h4 className="text-sm font-medium text-gray-700 flex items-center gap-2">
              <AlertCircle className="w-4 h-4" />
              Password Requirements
            </h4>
            
            <div className="space-y-1">
              {validation.requirements.map((requirement) => (
                <motion.div
                  key={requirement.id}
                  initial={{ opacity: 0, x: -10 }}
                  animate={{ opacity: 1, x: 0 }}
                  transition={{ duration: 0.2 }}
                  className={`flex items-center gap-2 text-xs ${
                    requirement.isValid ? 'text-green-600' : 'text-gray-500'
                  }`}
                >
                  {requirement.isValid ? (
                    <Check className="w-3 h-3 text-green-500" />
                  ) : (
                    <X className="w-3 h-3 text-gray-400" />
                  )}
                  <span>{requirement.label}</span>
                </motion.div>
              ))}
            </div>

            {/* Error Messages */}
            {validation.errors.length > 0 && (
              <div className="space-y-1 mt-2">
                {validation.errors.map((error, index) => (
                  <motion.div
                    key={index}
                    initial={{ opacity: 0, x: -10 }}
                    animate={{ opacity: 1, x: 0 }}
                    transition={{ duration: 0.2, delay: index * 0.1 }}
                    className="flex items-center gap-2 text-xs text-red-600"
                  >
                    <X className="w-3 h-3 text-red-500" />
                    <span>{error}</span>
                  </motion.div>
                ))}
              </div>
            )}
          </motion.div>
        </AnimatePresence>
      )}
    </div>
  );
};

export default PasswordStrengthIndicator;
