"""
Management command to test the security notification system.
This command allows administrators to test email notifications without triggering real security events.
"""

from django.core.management.base import BaseCommand, CommandError
from django.utils import timezone
from backend.security_notifications import (
    send_ip_blocked_alert,
    send_multiple_failed_logins_alert,
    send_suspicious_activity_alert,
    send_admin_action_alert,
    security_notifier
)


class Command(BaseCommand):
    help = 'Test the security notification system by sending sample alerts'
    
    def add_arguments(self, parser):
        parser.add_argument(
            '--type',
            type=str,
            choices=['ip_blocked', 'failed_logins', 'suspicious', 'admin_action', 'all'],
            default='all',
            help='Type of security notification to test'
        )
        
        parser.add_argument(
            '--email',
            type=str,
            help='Override recipient email address for testing'
        )
        
        parser.add_argument(
            '--dry-run',
            action='store_true',
            help='Show what would be sent without actually sending emails'
        )
    
    def handle(self, *args, **options):
        notification_type = options['type']
        dry_run = options['dry_run']
        test_email = options.get('email')
        
        if dry_run:
            self.stdout.write(
                self.style.WARNING('DRY RUN MODE: No emails will be sent')
            )
        
        # Override recipients if test email provided
        if test_email:
            original_recipients = security_notifier.recipients
            security_notifier.recipients = [test_email]
            self.stdout.write(f'Overriding recipients to: {test_email}')
        
        try:
            if notification_type == 'all':
                self._test_all_notifications(dry_run)
            elif notification_type == 'ip_blocked':
                self._test_ip_blocked(dry_run)
            elif notification_type == 'failed_logins':
                self._test_failed_logins(dry_run)
            elif notification_type == 'suspicious':
                self._test_suspicious_activity(dry_run)
            elif notification_type == 'admin_action':
                self._test_admin_action(dry_run)
                
        except Exception as e:
            raise CommandError(f'Error testing security notifications: {str(e)}')
        
        finally:
            # Restore original recipients if they were overridden
            if test_email:
                security_notifier.recipients = original_recipients
        
        if not dry_run:
            self.stdout.write(
                self.style.SUCCESS('Security notification tests completed successfully!')
            )
    
    def _test_all_notifications(self, dry_run):
        """Test all notification types."""
        self.stdout.write('Testing all security notification types...\n')
        
        self._test_ip_blocked(dry_run)
        self._test_failed_logins(dry_run)
        self._test_suspicious_activity(dry_run)
        self._test_admin_action(dry_run)
    
    def _test_ip_blocked(self, dry_run):
        """Test IP blocked notification."""
        self.stdout.write('Testing IP Blocked notification...')
        
        if dry_run:
            self.stdout.write('  Would send IP blocked alert for *************')
            return
        
        result = send_ip_blocked_alert(
            ip_address='*************',
            duration_minutes=30,
            reason='Security notification system test'
        )
        
        if result:
            self.stdout.write(
                self.style.SUCCESS('  ✓ IP blocked notification sent successfully')
            )
        else:
            self.stdout.write(
                self.style.ERROR('  ✗ Failed to send IP blocked notification')
            )
    
    def _test_failed_logins(self, dry_run):
        """Test multiple failed logins notification."""
        self.stdout.write('Testing Multiple Failed Logins notification...')
        
        if dry_run:
            self.stdout.write('  Would send failed logins <NAME_EMAIL>')
            return
        
        result = send_multiple_failed_logins_alert(
            ip_address='*************',
            username='<EMAIL>',
            attempt_count=5
        )
        
        if result:
            self.stdout.write(
                self.style.SUCCESS('  ✓ Failed logins notification sent successfully')
            )
        else:
            self.stdout.write(
                self.style.ERROR('  ✗ Failed to send failed logins notification')
            )
    
    def _test_suspicious_activity(self, dry_run):
        """Test suspicious activity notification."""
        self.stdout.write('Testing Suspicious Activity notification...')
        
        if dry_run:
            self.stdout.write('  Would send suspicious activity alert')
            return
        
        result = send_suspicious_activity_alert(
            ip_address='*************',
            activity_type='Unusual API access pattern',
            details={
                'requests_per_minute': 150,
                'endpoints_accessed': 25,
                'user_agent': 'Automated Bot/1.0',
                'pattern': 'Rapid sequential API calls'
            }
        )
        
        if result:
            self.stdout.write(
                self.style.SUCCESS('  ✓ Suspicious activity notification sent successfully')
            )
        else:
            self.stdout.write(
                self.style.ERROR('  ✗ Failed to send suspicious activity notification')
            )
    
    def _test_admin_action(self, dry_run):
        """Test admin action notification."""
        self.stdout.write('Testing Admin Action notification...')
        
        if dry_run:
            self.stdout.write('  Would send admin action alert')
            return
        
        result = send_admin_action_alert(
            admin_user='<EMAIL>',
            action='User account modification',
            target='user_id_12345',
            ip_address='*************'
        )
        
        if result:
            self.stdout.write(
                self.style.SUCCESS('  ✓ Admin action notification sent successfully')
            )
        else:
            self.stdout.write(
                self.style.ERROR('  ✗ Failed to send admin action notification')
            )
    
    def _show_configuration(self):
        """Show current security notification configuration."""
        self.stdout.write('\nCurrent Security Notification Configuration:')
        self.stdout.write(f'  Enabled: {security_notifier.enabled}')
        self.stdout.write(f'  Recipients: {security_notifier.recipients}')
        self.stdout.write(f'  Throttle Minutes: {security_notifier.throttle_minutes}')
        self.stdout.write(f'  High Priority Events: {security_notifier.high_priority_events}')
        self.stdout.write(f'  Medium Priority Events: {security_notifier.medium_priority_events}')
        
        self.stdout.write('\nUsage Examples:')
        self.stdout.write('  python manage.py test_security_notifications --type=ip_blocked')
        self.stdout.write('  python manage.py test_security_notifications --type=all --email=<EMAIL>')
        self.stdout.write('  python manage.py test_security_notifications --dry-run')
