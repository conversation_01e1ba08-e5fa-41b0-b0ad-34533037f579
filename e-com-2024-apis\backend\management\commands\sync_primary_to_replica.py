"""
Management command to sync primary database to replica with exact ID preservation.

This command combines backup and restore operations to ensure replica databases
have exactly the same data and IDs as the primary database.
"""

import os
import tempfile
from django.core.management.base import BaseCommand, CommandError
from django.core.management import call_command
from django.conf import settings
from backend.db_sync import DatabaseSynchronizer


class Command(BaseCommand):
    help = 'Sync primary database to replica with exact ID preservation'

    def add_arguments(self, parser):
        parser.add_argument(
            '--target-db',
            type=str,
            help='Target replica database name (default: all replicas)',
        )
        parser.add_argument(
            '--backup-file',
            type=str,
            help='Use existing backup file instead of creating new one',
        )
        parser.add_argument(
            '--keep-backup',
            action='store_true',
            help='Keep the backup file after restore',
        )
        parser.add_argument(
            '--exclude-users',
            action='store_true',
            help='Exclude user data from sync',
        )
        parser.add_argument(
            '--exclude-orders',
            action='store_true',
            help='Exclude order data from sync',
        )
        parser.add_argument(
            '--dry-run',
            action='store_true',
            help='Show what would be synced without actually doing it',
        )
        parser.add_argument(
            '--force',
            action='store_true',
            help='Force sync without confirmation',
        )

    def handle(self, *args, **options):
        self.stdout.write(
            self.style.SUCCESS('Primary to Replica Sync Tool (ID Preserving)')
        )
        self.stdout.write('=' * 70)

        # Check replica configuration
        replica_dbs = self.get_target_databases(options['target_db'])
        
        # Show sync plan
        self.show_sync_plan(replica_dbs, options)
        
        if options['dry_run']:
            self.stdout.write(
                self.style.WARNING('DRY RUN MODE - No changes will be made')
            )
            return

        # Confirm sync
        if not options['force']:
            confirm = input('\nProceed with sync? (yes/no): ')
            if confirm.lower() != 'yes':
                self.stdout.write('Sync cancelled.')
                return

        # Perform sync
        self.perform_sync(replica_dbs, options)

        self.stdout.write(
            self.style.SUCCESS('Primary to replica sync completed successfully!')
        )

    def get_target_databases(self, target_db_name):
        """Get list of target replica databases"""
        replica_dbs = DatabaseSynchronizer.get_replica_databases()
        
        if not replica_dbs:
            raise CommandError('No replica databases configured')
        
        if target_db_name:
            if target_db_name not in replica_dbs:
                raise CommandError(f'Replica database "{target_db_name}" not found')
            return [target_db_name]
        
        return replica_dbs

    def show_sync_plan(self, replica_dbs, options):
        """Show what will be synced"""
        self.stdout.write(f'\n📋 Sync Plan')
        self.stdout.write('-' * 30)
        
        self.stdout.write(f'Source: PRIMARY database')
        self.stdout.write(f'Target(s): {", ".join(replica_dbs)}')
        
        # Show what will be included/excluded
        inclusions = []
        exclusions = []
        
        if not options['exclude_users']:
            inclusions.append('User data')
        else:
            exclusions.append('User data')
            
        if not options['exclude_orders']:
            inclusions.append('Order data')
        else:
            exclusions.append('Order data')
        
        inclusions.extend(['Product catalog', 'Categories', 'Brands', 'Promotions'])
        
        if inclusions:
            self.stdout.write(f'Included: {", ".join(inclusions)}')
        if exclusions:
            self.stdout.write(f'Excluded: {", ".join(exclusions)}')
        
        # Show current record counts
        self.show_current_counts()

    def show_current_counts(self):
        """Show current record counts in primary and replicas"""
        self.stdout.write(f'\n📊 Current Record Counts')
        self.stdout.write('-' * 30)
        
        from products.models import Product, Category, Brand
        from users.models import Customer
        from orders.models import Order
        
        models_to_check = [
            (Product, 'Products'),
            (Category, 'Categories'),
            (Brand, 'Brands'),
            (Customer, 'Users'),
            (Order, 'Orders'),
        ]
        
        replica_dbs = DatabaseSynchronizer.get_replica_databases()
        
        for model_class, model_name in models_to_check:
            primary_count = model_class.objects.count()
            self.stdout.write(f'\n{model_name}:')
            self.stdout.write(f'  PRIMARY: {primary_count} records')
            
            for db_name in replica_dbs:
                try:
                    replica_count = model_class.objects.using(db_name).count()
                    if replica_count == primary_count:
                        status = '✅ SYNCED'
                    else:
                        status = f'❌ OUT OF SYNC (diff: {abs(primary_count - replica_count)})'
                    
                    self.stdout.write(f'  {db_name.upper()}: {replica_count} records {status}')
                except Exception as e:
                    self.stdout.write(f'  {db_name.upper()}: ❌ ERROR - {e}')

    def perform_sync(self, replica_dbs, options):
        """Perform the complete sync operation"""
        backup_file = options.get('backup_file')
        temp_backup = False
        
        try:
            # Step 1: Create backup if not provided
            if not backup_file:
                backup_file = self.create_backup(options)
                temp_backup = True
            
            # Step 2: Restore to each replica
            for db_name in replica_dbs:
                self.restore_to_replica(backup_file, db_name, options)
            
        finally:
            # Clean up temporary backup if created
            if temp_backup and not options['keep_backup'] and backup_file:
                try:
                    os.remove(backup_file)
                    self.stdout.write(f'🧹 Cleaned up temporary backup: {backup_file}')
                except Exception as e:
                    self.stdout.write(f'⚠️  Could not remove backup file: {e}')

    def create_backup(self, options):
        """Create backup of primary database"""
        self.stdout.write(f'\n📦 Step 1: Creating backup of primary database...')
        
        # Create temporary backup file
        temp_dir = tempfile.gettempdir()
        backup_file = os.path.join(temp_dir, 'primary_sync_backup.json')
        
        # Prepare backup command arguments
        backup_args = ['--output', backup_file]
        
        if options['exclude_users']:
            backup_args.append('--exclude-users')
        
        if options['exclude_orders']:
            backup_args.append('--exclude-orders')
        
        # Create backup
        call_command('backup_primary_db', *backup_args)
        
        self.stdout.write(f'   ✅ Backup created: {backup_file}')
        return backup_file

    def restore_to_replica(self, backup_file, target_db, options):
        """Restore backup to specific replica database"""
        self.stdout.write(f'\n📥 Step 2: Restoring to {target_db} database...')
        
        # Prepare restore command arguments
        restore_args = [backup_file, '--target-db', target_db, '--force', '--skip-sequences']
        
        exclude_models = []

        if options['exclude_users']:
            exclude_models.extend(['Customer', 'Address'])

        if options['exclude_orders']:
            exclude_models.extend(['Order', 'OrderItem', 'Cart', 'CartItem'])

        if exclude_models:
            restore_args.extend(['--exclude-models', ','.join(exclude_models)])
        
        # Restore backup
        call_command('restore_to_replica', *restore_args)
        
        self.stdout.write(f'   ✅ Restore to {target_db} completed')

    def verify_sync(self, replica_dbs):
        """Verify that sync was successful"""
        self.stdout.write(f'\n🔍 Step 3: Verifying sync results...')
        
        from products.models import Product, Category, Brand
        
        models_to_verify = [Product, Category, Brand]
        all_synced = True
        
        for model_class in models_to_verify:
            primary_count = model_class.objects.count()
            model_name = model_class.__name__
            
            for db_name in replica_dbs:
                try:
                    replica_count = model_class.objects.using(db_name).count()
                    if replica_count == primary_count:
                        self.stdout.write(f'   ✅ {model_name} in {db_name}: {replica_count} records (SYNCED)')
                    else:
                        self.stdout.write(f'   ❌ {model_name} in {db_name}: {replica_count} records (OUT OF SYNC)')
                        all_synced = False
                except Exception as e:
                    self.stdout.write(f'   ❌ {model_name} in {db_name}: ERROR - {e}')
                    all_synced = False
        
        if all_synced:
            self.stdout.write(f'\n🎯 Verification: All replicas are perfectly synced!')
        else:
            self.stdout.write(f'\n⚠️  Verification: Some replicas may not be fully synced')
        
        return all_synced
