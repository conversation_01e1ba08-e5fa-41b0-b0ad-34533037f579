"""
Comprehensive tests for encryption utilities
Tests encryption/decryption, field encryption, and data anonymization
"""

import pytest
from unittest.mock import patch, MagicMock
from django.test import TestCase, override_settings
from django.core.exceptions import ValidationError
from django.contrib.auth import get_user_model

User = get_user_model()

class TestEncryptionManager(TestCase):
    """Test the encryption manager functionality"""
    
    def setUp(self):
        """Set up test data"""
        from backend.encryption_utils import EncryptionManager
        self.encryption_manager = EncryptionManager()
    
    def test_encryption_manager_initialization(self):
        """Test encryption manager can be initialized"""
        self.assertIsNotNone(self.encryption_manager)
        self.assertIsNotNone(self.encryption_manager._cipher)
    
    def test_encrypt_decrypt_string(self):
        """Test basic string encryption and decryption"""
        test_data = "sensitive information"
        
        # Encrypt the data
        encrypted = self.encryption_manager.encrypt(test_data)
        self.assertIsNotNone(encrypted)
        self.assertNotEqual(encrypted, test_data)
        
        # Decrypt the data
        decrypted = self.encryption_manager.decrypt(encrypted)
        self.assertEqual(decrypted, test_data)
    
    def test_encrypt_decrypt_none(self):
        """Test encryption/decryption of None values"""
        self.assertIsNone(self.encryption_manager.encrypt(None))
        self.assertIsNone(self.encryption_manager.decrypt(None))
    
    def test_encrypt_decrypt_empty_string(self):
        """Test encryption/decryption of empty strings"""
        encrypted = self.encryption_manager.encrypt("")
        decrypted = self.encryption_manager.decrypt(encrypted)
        self.assertEqual(decrypted, "")
    
    def test_encrypt_decrypt_unicode(self):
        """Test encryption/decryption of unicode strings"""
        test_data = "测试数据 🔒"
        
        encrypted = self.encryption_manager.encrypt(test_data)
        decrypted = self.encryption_manager.decrypt(encrypted)
        self.assertEqual(decrypted, test_data)
    
    def test_encrypt_decrypt_long_string(self):
        """Test encryption/decryption of long strings"""
        test_data = "A" * 1000  # 1000 character string
        
        encrypted = self.encryption_manager.encrypt(test_data)
        decrypted = self.encryption_manager.decrypt(encrypted)
        self.assertEqual(decrypted, test_data)
    
    def test_encryption_consistency(self):
        """Test that encryption produces different results each time"""
        test_data = "consistent test"
        
        encrypted1 = self.encryption_manager.encrypt(test_data)
        encrypted2 = self.encryption_manager.encrypt(test_data)
        
        # Encrypted values should be different (due to random IV)
        self.assertNotEqual(encrypted1, encrypted2)
        
        # But both should decrypt to the same value
        self.assertEqual(self.encryption_manager.decrypt(encrypted1), test_data)
        self.assertEqual(self.encryption_manager.decrypt(encrypted2), test_data)
    
    def test_decrypt_invalid_data(self):
        """Test decryption of invalid data"""
        with self.assertRaises(Exception):
            self.encryption_manager.decrypt("invalid_encrypted_data")
    
    @patch('backend.encryption_utils.logger')
    def test_encryption_error_handling(self, mock_logger):
        """Test encryption error handling"""
        with patch.object(self.encryption_manager, '_cipher', None):
            result = self.encryption_manager.encrypt("test")
            self.assertIsNone(result)
            mock_logger.error.assert_called()

class TestEncryptedFields(TestCase):
    """Test encrypted database fields"""
    
    def test_encrypted_char_field_import(self):
        """Test that encrypted char field can be imported"""
        from backend.encryption_utils import EncryptedCharField
        field = EncryptedCharField(max_length=100)
        self.assertIsNotNone(field)
    
    def test_encrypted_email_field_import(self):
        """Test that encrypted email field can be imported"""
        from backend.encryption_utils import EncryptedEmailField
        field = EncryptedEmailField()
        self.assertIsNotNone(field)
    
    def test_encrypted_char_field_max_length(self):
        """Test encrypted char field max length adjustment"""
        from backend.encryption_utils import EncryptedCharField
        field = EncryptedCharField(max_length=50)
        # Max length should be increased to accommodate encryption
        self.assertGreater(field.max_length, 50)
    
    def test_encrypted_email_field_max_length(self):
        """Test encrypted email field max length adjustment"""
        from backend.encryption_utils import EncryptedEmailField
        field = EncryptedEmailField()
        # Max length should be increased to accommodate encryption
        self.assertGreater(field.max_length, 254)

class TestUtilityFunctions(TestCase):
    """Test encryption utility functions"""
    
    def test_mask_sensitive_data(self):
        """Test sensitive data masking"""
        from backend.encryption_utils import mask_sensitive_data
        
        # Test normal string
        result = mask_sensitive_data("1234567890", visible_chars=4)
        self.assertEqual(result, "12****90")
        
        # Test short string
        result = mask_sensitive_data("123", visible_chars=4)
        self.assertEqual(result, "***")
        
        # Test empty string
        result = mask_sensitive_data("", visible_chars=4)
        self.assertEqual(result, "")
        
        # Test None
        result = mask_sensitive_data(None, visible_chars=4)
        self.assertEqual(result, "")
    
    def test_hash_for_lookup(self):
        """Test data hashing for lookup"""
        from backend.encryption_utils import hash_for_lookup
        
        # Test normal string
        result = hash_for_lookup("<EMAIL>")
        self.assertIsNotNone(result)
        self.assertEqual(len(result), 64)  # SHA256 hash length
        
        # Test consistency
        result2 = hash_for_lookup("<EMAIL>")
        self.assertEqual(result, result2)
        
        # Test different strings produce different hashes
        result3 = hash_for_lookup("<EMAIL>")
        self.assertNotEqual(result, result3)
        
        # Test None
        result = hash_for_lookup(None)
        self.assertIsNone(result)
        
        # Test empty string
        result = hash_for_lookup("")
        self.assertIsNone(result)
    
    def test_encrypt_phone_number(self):
        """Test phone number encryption utility"""
        from backend.encryption_utils import encrypt_phone_number, decrypt_phone_number
        
        phone = "+1234567890"
        encrypted = encrypt_phone_number(phone)
        self.assertIsNotNone(encrypted)
        self.assertNotEqual(encrypted, phone)
        
        decrypted = decrypt_phone_number(encrypted)
        self.assertEqual(decrypted, phone)
        
        # Test None
        self.assertIsNone(encrypt_phone_number(None))
        self.assertIsNone(decrypt_phone_number(None))
    
    def test_encrypt_address(self):
        """Test address encryption utility"""
        from backend.encryption_utils import encrypt_address, decrypt_address
        
        address = "123 Main St, City, State 12345"
        encrypted = encrypt_address(address)
        self.assertIsNotNone(encrypted)
        self.assertNotEqual(encrypted, address)
        
        decrypted = decrypt_address(encrypted)
        self.assertEqual(decrypted, address)
        
        # Test None
        self.assertIsNone(encrypt_address(None))
        self.assertIsNone(decrypt_address(None))
    
    def test_anonymize_email(self):
        """Test email anonymization"""
        from backend.encryption_utils import anonymize_email
        
        # Test normal email
        result = anonymize_email("<EMAIL>")
        self.assertIn("@anonymized.com", result)
        self.assertIn("user_", result)
        
        # Test invalid email
        result = anonymize_email("invalid_email")
        self.assertEqual(result, "<EMAIL>")
        
        # Test None
        result = anonymize_email(None)
        self.assertEqual(result, "<EMAIL>")
        
        # Test empty string
        result = anonymize_email("")
        self.assertEqual(result, "<EMAIL>")
