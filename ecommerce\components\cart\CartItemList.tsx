import { MAIN_URL } from "@/constant/urls";
import AddOrRemoveBtn from "../utils/AddOrRemoveBtn";

interface CartItem {
  id: number;
  name: string;
  price: number;
  color: string;
  size: string;
  quantity: number;
  image: string;
}

interface CartItemListProps {
  items: any;
  handleAddOrRemoveToCart: any;
}

export const CartItemList = ({
  items,
  handleAddOrRemoveToCart,
}: CartItemListProps) => {
  return (
    <div className="space-y-6">
      <h2 className="text-xl font-semibold mb-4">Your Items</h2>
      {items.map((item: any) => (
        <div
          key={item?.id}
          className="flex flex-col sm:flex-row items-start sm:items-center gap-6 p-6 border rounded-lg shadow-sm hover:shadow-md transition-all duration-300 bg-white"
        >
          <div className="relative">
            <img
              src={item?.product?.images[0]?.image_url ?? "/assets/products/product-placeholder.svg"}
              alt={item?.product?.name}
              className="w-28 h-28 object-cover rounded-md"
            />
            <span className="absolute -top-2 -right-2 bg-primary text-white text-xs font-bold rounded-full w-6 h-6 flex items-center justify-center">
              {item?.quantity}
            </span>
          </div>
          <div className="flex-1 space-y-2">
            <h3 className="font-semibold text-lg">{item?.product?.name}</h3>
            <div className="flex flex-wrap gap-2">
              {item?.product?.category && typeof item?.product?.category === 'string' ? (
                <span className="text-xs bg-gray-100 px-2 py-1 rounded-full">
                  {item?.product?.category}
                </span>
              ) : item?.product?.category?.name ? (
                <span className="text-xs bg-gray-100 px-2 py-1 rounded-full">
                  {item?.product?.category?.name}
                </span>
              ) : null}
            </div>
            <div className="flex items-center gap-2">
              <p className="font-medium text-primary text-lg">₹{item?.product?.price}</p>
              <span className="text-xs text-gray-500">(incl. of all taxes)</span>
            </div>
          </div>
          <div className="flex flex-col items-center gap-2">
            <p className="text-sm text-gray-500">Quantity</p>
            <AddOrRemoveBtn
              handleAddOrRemoveToCart={handleAddOrRemoveToCart}
              quantity={item?.quantity}
              itemId={item?.id}
              data={item}
            />
            <p className="text-sm font-medium mt-2">
              Total: ₹{(item?.product?.price * item?.quantity).toFixed(2)}
            </p>
          </div>
        </div>
      ))}
    </div>
  );
};
