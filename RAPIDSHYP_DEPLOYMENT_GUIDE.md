# Rapidshyp Integration - Deployment & Rollback Guide

## Deployment Strategy Overview

This guide ensures a safe, zero-downtime deployment of Rapidshyp integration with comprehensive rollback capabilities. The deployment follows a **feature-flag approach** to enable gradual rollout and instant rollback if needed.

## Pre-Deployment Checklist

### 1. Environment Preparation
- [ ] Rapidshyp API credentials obtained and tested
- [ ] Environment variables configured in all environments
- [ ] Database backup completed
- [ ] All tests passing (backward compatibility + new features)
- [ ] Performance benchmarks established
- [ ] Monitoring and alerting configured

### 2. Code Review Checklist
- [ ] No modifications to existing core files (only additions)
- [ ] All new database fields are nullable with safe defaults
- [ ] Fallback mechanisms implemented and tested
- [ ] Error handling covers all API failure scenarios
- [ ] Logging implemented for debugging and monitoring

### 3. Infrastructure Readiness
- [ ] Redis cache available for rate caching
- [ ] API rate limits configured for Rapidshyp calls
- [ ] Network connectivity to Rapidshyp APIs verified
- [ ] SSL certificates valid for webhook endpoints

## Deployment Phases

### Phase 1: Infrastructure Deployment (Zero Risk)

#### Step 1.1: Deploy Backend Code (Feature Disabled)
```bash
# Deploy with Rapidshyp disabled
export RAPIDSHYP_ENABLED=false
export RAPIDSHYP_API_KEY=""  # Empty initially

# Deploy backend
cd /path/to/e-com-2024-apis
git pull origin main
pip install -r requirements.txt

# Run database migrations (safe - only adds nullable fields)
python manage.py migrate

# Restart application
sudo systemctl restart gunicorn
sudo systemctl restart nginx
```

#### Step 1.2: Deploy Frontend Code
```bash
# Deploy frontend with feature flags disabled
cd /path/to/ecommerce
git pull origin main
npm install
npm run build

# Deploy to production
npm run start
```

#### Step 1.3: Verify Existing Functionality
```bash
# Run health checks
curl -X GET "https://your-api-domain.com/api/v1/orders/shipping-methods/"
curl -X POST "https://your-api-domain.com/api/v1/orders/" \
  -H "Authorization: Bearer YOUR_TOKEN" \
  -H "Content-Type: application/json" \
  -d '{"shipping_address": 1, "billing_address": 1, "shipping_method": 1}'

# Verify frontend checkout flow
# Manual testing: Complete an order using existing flow
```

### Phase 2: Gradual Feature Activation

#### Step 2.1: Enable Rapidshyp for Testing (Internal Only)
```bash
# Update environment variables
export RAPIDSHYP_ENABLED=true
export RAPIDSHYP_API_KEY="your_test_api_key"
export RAPIDSHYP_STORE_NAME="DEFAULT"
export RAPIDSHYP_DEFAULT_PICKUP_PINCODE="110001"

# Restart application to pick up new environment
sudo systemctl restart gunicorn
```

#### Step 2.2: Internal Testing Phase
```bash
# Test new shipping rate calculation endpoint
curl -X POST "https://your-api-domain.com/api/v1/shipping/calculate-rates/" \
  -H "Authorization: Bearer YOUR_TOKEN" \
  -H "Content-Type: application/json" \
  -d '{
    "delivery_pincode": "400001",
    "weight": 1.0,
    "cod": false,
    "total_value": 1000.0
  }'

# Expected response should include both Rapidshyp and existing methods
```

#### Step 2.3: Limited User Rollout (10% of users)
```python
# Add feature flag logic to views (if needed)
# shipping/services/shipping_service.py

def should_use_rapidshyp(user):
    """Determine if user should see Rapidshyp features"""
    if not settings.RAPIDSHYP_ENABLED:
        return False
    
    # Enable for 10% of users initially
    import hashlib
    user_hash = int(hashlib.md5(str(user.id).encode()).hexdigest(), 16)
    return (user_hash % 100) < 10  # 10% rollout
```

### Phase 3: Full Production Deployment

#### Step 3.1: Production API Key Configuration
```bash
# Switch to production API key
export RAPIDSHYP_API_KEY="your_production_api_key"
export RAPIDSHYP_SANDBOX_MODE=false

# Restart application
sudo systemctl restart gunicorn
```

#### Step 3.2: Enable for All Users
```python
# Remove feature flag restrictions
def should_use_rapidshyp(user):
    return settings.RAPIDSHYP_ENABLED
```

#### Step 3.3: Monitor and Validate
```bash
# Monitor application logs
tail -f /var/log/gunicorn/error.log | grep -i rapidshyp

# Monitor performance metrics
# Check response times for shipping rate calculations
# Verify order creation success rates
# Monitor API error rates
```

## Environment Configuration

### Development Environment
```bash
# .env.development
RAPIDSHYP_ENABLED=true
RAPIDSHYP_API_KEY=your_dev_api_key
RAPIDSHYP_STORE_NAME=DEFAULT
RAPIDSHYP_DEFAULT_PICKUP_PINCODE=110001
RAPIDSHYP_SANDBOX_MODE=true
RAPIDSHYP_WEBHOOK_SECRET=dev_webhook_secret
```

### Staging Environment
```bash
# .env.staging
RAPIDSHYP_ENABLED=true
RAPIDSHYP_API_KEY=your_staging_api_key
RAPIDSHYP_STORE_NAME=DEFAULT
RAPIDSHYP_DEFAULT_PICKUP_PINCODE=110001
RAPIDSHYP_SANDBOX_MODE=true
RAPIDSHYP_WEBHOOK_SECRET=staging_webhook_secret
```

### Production Environment
```bash
# .env.production
RAPIDSHYP_ENABLED=true
RAPIDSHYP_API_KEY=your_production_api_key
RAPIDSHYP_STORE_NAME=DEFAULT
RAPIDSHYP_DEFAULT_PICKUP_PINCODE=110001
RAPIDSHYP_SANDBOX_MODE=false
RAPIDSHYP_WEBHOOK_SECRET=production_webhook_secret
```

## Monitoring and Alerting

### Key Metrics to Monitor
```python
# monitoring/rapidshyp_metrics.py
RAPIDSHYP_METRICS = {
    'api_response_time': 'Average response time for Rapidshyp API calls',
    'api_success_rate': 'Percentage of successful Rapidshyp API calls',
    'fallback_usage_rate': 'Percentage of orders using fallback shipping',
    'order_creation_success_rate': 'Overall order creation success rate',
    'shipping_rate_calculation_time': 'Time to calculate shipping rates',
    'cache_hit_rate': 'Percentage of shipping rates served from cache'
}
```

### Alert Thresholds
```yaml
# alerts.yml
rapidshyp_alerts:
  api_response_time:
    warning: 2000ms
    critical: 5000ms
  
  api_success_rate:
    warning: 95%
    critical: 90%
  
  fallback_usage_rate:
    warning: 20%
    critical: 50%
  
  order_creation_success_rate:
    warning: 98%
    critical: 95%
```

### Health Check Endpoints
```python
# shipping/views.py
@api_view(['GET'])
def rapidshyp_health_check(request):
    """Health check endpoint for Rapidshyp integration"""
    try:
        # Test basic API connectivity
        client = RapidshypClient()
        test_response = client.check_serviceability(
            pickup_pincode='110001',
            delivery_pincode='400001',
            cod=False,
            total_value=100,
            weight=1.0
        )
        
        return Response({
            'status': 'healthy',
            'rapidshyp_api': 'connected',
            'response_time': test_response.get('response_time', 'N/A'),
            'timestamp': timezone.now().isoformat()
        })
    except Exception as e:
        return Response({
            'status': 'unhealthy',
            'rapidshyp_api': 'disconnected',
            'error': str(e),
            'timestamp': timezone.now().isoformat()
        }, status=500)
```

## Rollback Procedures

### Level 1: Instant Rollback (Feature Disable)
**Use Case**: Rapidshyp API issues, performance problems
**Downtime**: 0 seconds
**Risk**: None

```bash
# Instantly disable Rapidshyp without code changes
export RAPIDSHYP_ENABLED=false

# Restart application
sudo systemctl restart gunicorn

# Verify fallback is working
curl -X POST "https://your-api-domain.com/api/v1/shipping/calculate-rates/" \
  -H "Authorization: Bearer YOUR_TOKEN" \
  -H "Content-Type: application/json" \
  -d '{"delivery_pincode": "400001", "weight": 1.0}'

# Should return only existing shipping methods
```

### Level 2: Partial Rollback (Code Rollback)
**Use Case**: Critical bugs in new code
**Downtime**: 2-5 minutes
**Risk**: Low (new code is isolated)

```bash
# Rollback to previous git commit
cd /path/to/e-com-2024-apis
git log --oneline -10  # Find previous commit
git checkout PREVIOUS_COMMIT_HASH

# Reinstall dependencies (if changed)
pip install -r requirements.txt

# Restart application
sudo systemctl restart gunicorn

# Verify existing functionality
curl -X GET "https://your-api-domain.com/api/v1/orders/shipping-methods/"
```

### Level 3: Database Rollback (Emergency Only)
**Use Case**: Database corruption or critical data issues
**Downtime**: 10-30 minutes
**Risk**: Medium (data loss possible)

```bash
# Stop application
sudo systemctl stop gunicorn

# Rollback database migrations
cd /path/to/e-com-2024-apis
python manage.py migrate orders PREVIOUS_MIGRATION_NUMBER
python manage.py migrate shipping zero  # Remove shipping app migrations

# Restore from backup if needed
pg_restore -d your_database backup_file.sql

# Remove shipping app from INSTALLED_APPS temporarily
# Edit settings.py and comment out 'shipping'

# Restart application
sudo systemctl start gunicorn
```

## Rollback Testing

### Pre-Deployment Rollback Tests
```bash
# Test rollback procedures in staging
# 1. Deploy Rapidshyp integration
# 2. Create test orders with Rapidshyp
# 3. Disable Rapidshyp (Level 1 rollback)
# 4. Verify existing functionality works
# 5. Test code rollback (Level 2)
# 6. Test database rollback (Level 3)
```

### Rollback Validation Checklist
- [ ] Existing order creation works
- [ ] Existing shipping methods available
- [ ] No API errors in logs
- [ ] Frontend checkout flow functional
- [ ] Performance metrics normal
- [ ] No data corruption

## Post-Deployment Validation

### Immediate Validation (First 30 minutes)
```bash
# Check application health
curl -X GET "https://your-api-domain.com/health/"

# Test existing functionality
curl -X GET "https://your-api-domain.com/api/v1/orders/shipping-methods/"

# Test new functionality
curl -X POST "https://your-api-domain.com/api/v1/shipping/calculate-rates/" \
  -H "Authorization: Bearer YOUR_TOKEN" \
  -H "Content-Type: application/json" \
  -d '{"delivery_pincode": "400001", "weight": 1.0}'

# Monitor error logs
tail -f /var/log/gunicorn/error.log
```

### Extended Validation (First 24 hours)
- Monitor order creation success rates
- Track shipping rate calculation performance
- Verify fallback mechanisms activate when needed
- Check customer support tickets for shipping issues
- Monitor API usage and costs

### Success Criteria
- ✅ Zero increase in order creation failures
- ✅ Shipping rate calculations complete within 2 seconds
- ✅ Fallback mechanisms work seamlessly
- ✅ No critical errors in application logs
- ✅ Customer experience improved or unchanged

## Emergency Contacts

### Deployment Team
- **Lead Developer**: [Contact Info]
- **DevOps Engineer**: [Contact Info]
- **Database Administrator**: [Contact Info]

### Rapidshyp Support
- **Technical Support**: [Rapidshyp Contact]
- **Account Manager**: [Rapidshyp Contact]
- **Emergency Hotline**: [Rapidshyp Emergency Contact]

### Escalation Procedures
1. **Level 1**: Disable Rapidshyp feature (RAPIDSHYP_ENABLED=false)
2. **Level 2**: Contact lead developer for code rollback
3. **Level 3**: Contact DevOps for infrastructure rollback
4. **Level 4**: Contact DBA for database rollback
5. **Level 5**: Contact Rapidshyp support for API issues

This deployment guide ensures a safe, monitored rollout of Rapidshyp integration with multiple fallback options and clear rollback procedures.
