"""
CDN configuration for Django using jsDelivr
This module provides settings and utilities for integrating jsDelivr CDN
"""

import os
from pathlib import Path

# GitHub repository information
REPO_OWNER = os.getenv('CDN_REPO_OWNER', 'sohangpurh53')
REPO_NAME = os.getenv('CDN_REPO_NAME', 'triumph-static-assets')
REPO_VERSION = os.getenv('CDN_REPO_VERSION', 'main')  # or use specific version tags like 'v1.0.0'

# Base URL for jsDelivr CDN
CDN_BASE_URL = os.getenv('CDN_URL', f'https://cdn.jsdelivr.net/gh/{REPO_OWNER}/{REPO_NAME}@{REPO_VERSION}')

# Determine if we should use the CDN based on environment
USE_CDN = os.getenv('USE_CDN', 'False').lower() == 'true'

# CDN URL settings
if USE_CDN:
    # Use CDN for static files
    STATIC_URL = f'{CDN_BASE_URL}/static/'

    # Use CDN for media files if they're in the repository
    # Note: For user-uploaded content, you'll still need to use your server
    MEDIA_URL = f'{CDN_BASE_URL}/media/'
else:
    # Use local static and media URLs
    STATIC_URL = '/static/'
    MEDIA_URL = '/media/'

# Function to get a CDN URL for a static file
def get_cdn_url(path):
    """
    Get the CDN URL for a static file

    Args:
        path: The path to the file relative to the repository root

    Returns:
        The full URL to the file
    """
    # Ensure path starts with a slash
    if not path.startswith('/'):
        path = f'/{path}'

    if USE_CDN:
        return f'{CDN_BASE_URL}{path}'
    else:
        # For local development, return the local path
        if path.startswith('/static/'):
            return f'{STATIC_URL}{path[8:]}'
        elif path.startswith('/media/'):
            return f'{MEDIA_URL}{path[7:]}'
        return path
