"""
Comprehensive tests for product views and API endpoints
Tests product listing, filtering, search, categories, brands, and reviews
"""

import pytest
import json
from decimal import Decimal
from unittest.mock import patch
from django.test import TestCase
from django.urls import reverse
from django.contrib.auth import get_user_model
from rest_framework.test import APITestCase, APIClient
from rest_framework import status
from rest_framework_simplejwt.tokens import RefreshToken

from products.models import (
    Product, Category, SubCategorie, Brand, GST, 
    ProductImage, ProductVariant, Review
)

User = get_user_model()

class TestCategoryViews(APITestCase):
    """Test category-related API endpoints"""
    
    def setUp(self):
        """Set up test data"""
        self.category1 = Category.objects.create(
            name='Electronics',
            description='Electronic products',
            is_active=True
        )
        self.category2 = Category.objects.create(
            name='Clothing',
            description='Fashion items',
            is_active=True
        )
        self.inactive_category = Category.objects.create(
            name='Inactive Category',
            is_active=False
        )
        
        # Create subcategories
        self.subcategory1 = SubCategorie.objects.create(
            name='Smartphones',
            category=self.category1,
            is_active=True
        )
        self.subcategory2 = SubCategorie.objects.create(
            name='Laptops',
            category=self.category1,
            is_active=True
        )

        # Create brands and products so categories appear in the list
        self.brand = Brand.objects.create(name='Apple', is_active=True)
        self.gst = GST.objects.create(name='Standard GST', rate=Decimal('18.00'))

        # Create products for each category
        Product.objects.create(
            name='iPhone 13',
            category=self.category1,
            subcategory=self.subcategory1,
            brand=self.brand,
            price=Decimal('999.00'),
            gst=self.gst,
            stock=50,
            is_active=True
        )

        Product.objects.create(
            name='T-Shirt',
            category=self.category2,
            brand=self.brand,
            price=Decimal('29.99'),
            gst=self.gst,
            stock=100,
            is_active=True
        )
    
    def test_list_categories(self):
        """Test listing all active categories"""
        url = reverse('category-list')
        response = self.client.get(url)
        
        self.assertEqual(response.status_code, status.HTTP_200_OK)
        self.assertEqual(len(response.data), 2)  # Only active categories
        
        category_names = [cat['name'] for cat in response.data]
        self.assertIn('Electronics', category_names)
        self.assertIn('Clothing', category_names)
        self.assertNotIn('Inactive Category', category_names)
    
    def test_category_detail(self):
        """Test getting category details"""
        url = reverse('category-detail', kwargs={'slug': self.category1.slug})
        response = self.client.get(url)

        self.assertEqual(response.status_code, status.HTTP_200_OK)
        self.assertEqual(response.data['name'], 'Electronics')
        self.assertEqual(response.data['description'], 'Electronic products')
    
    def test_category_with_subcategories(self):
        """Test category response includes subcategories"""
        url = reverse('category-detail', kwargs={'slug': self.category1.slug})
        response = self.client.get(url)

        self.assertEqual(response.status_code, status.HTTP_200_OK)
        self.assertIn('children', response.data)
        self.assertEqual(len(response.data['children']), 2)
    
    def test_category_not_found(self):
        """Test accessing non-existent category"""
        url = reverse('category-detail', kwargs={'slug': 'non-existent-category'})
        response = self.client.get(url)

        self.assertEqual(response.status_code, status.HTTP_404_NOT_FOUND)

class TestBrandViews(APITestCase):
    """Test brand-related API endpoints"""
    
    def setUp(self):
        """Set up test data"""
        self.brand1 = Brand.objects.create(
            name='Apple',
            description='Technology company',
            is_active=True
        )
        self.brand2 = Brand.objects.create(
            name='Samsung',
            description='Electronics manufacturer',
            is_active=True
        )
        self.inactive_brand = Brand.objects.create(
            name='Inactive Brand',
            is_active=False
        )
    
    def test_list_brands(self):
        """Test listing all active brands"""
        url = reverse('brands-list')
        response = self.client.get(url)
        
        self.assertEqual(response.status_code, status.HTTP_200_OK)
        self.assertEqual(len(response.data), 2)  # Only active brands
        
        brand_names = [brand['name'] for brand in response.data]
        self.assertIn('Apple', brand_names)
        self.assertIn('Samsung', brand_names)
        self.assertNotIn('Inactive Brand', brand_names)
    


class TestProductViews(APITestCase):
    """Test product-related API endpoints"""
    
    def setUp(self):
        """Set up test data"""
        self.category = Category.objects.create(name='Electronics')
        self.subcategory = SubCategorie.objects.create(
            name='Smartphones',
            category=self.category
        )
        self.brand = Brand.objects.create(name='Apple')
        self.gst = GST.objects.create(
            name='Standard GST',
            rate=Decimal('18.00')
        )
        
        self.product1 = Product.objects.create(
            name='iPhone 13',
            description='Latest iPhone model',
            category=self.category,
            subcategory=self.subcategory,
            brand=self.brand,
            price=Decimal('999.00'),
            gst=self.gst,
            stock=50,
            is_active=True
        )
        
        self.product2 = Product.objects.create(
            name='iPhone 12',
            description='Previous iPhone model',
            category=self.category,
            subcategory=self.subcategory,
            brand=self.brand,
            price=Decimal('799.00'),
            gst=self.gst,
            stock=30,
            is_active=True
        )
        
        self.inactive_product = Product.objects.create(
            name='Inactive Product',
            category=self.category,
            brand=self.brand,
            price=Decimal('100.00'),
            stock=0,
            is_active=False
        )
    
    def test_list_products(self):
        """Test listing all active products"""
        url = reverse('product-list')
        response = self.client.get(url)
        
        self.assertEqual(response.status_code, status.HTTP_200_OK)
        self.assertEqual(len(response.data['results']), 2)  # Only active products
        
        product_names = [product['name'] for product in response.data['results']]
        self.assertIn('iPhone 13', product_names)
        self.assertIn('iPhone 12', product_names)
        self.assertNotIn('Inactive Product', product_names)
    
    def test_product_detail(self):
        """Test getting product details"""
        url = reverse('product-detail', kwargs={'slug': self.product1.slug})
        response = self.client.get(url)

        self.assertEqual(response.status_code, status.HTTP_200_OK)
        self.assertEqual(response.data['name'], 'iPhone 13')
        self.assertEqual(response.data['price'], '999.00')
        self.assertEqual(response.data['category']['name'], 'Electronics')
        self.assertEqual(response.data['brand']['name'], 'Apple')
    
    def test_filter_products_by_category(self):
        """Test filtering products by category"""
        url = reverse('product-list')
        response = self.client.get(url, {'category': self.category.id})
        
        self.assertEqual(response.status_code, status.HTTP_200_OK)
        self.assertEqual(len(response.data['results']), 2)
    
    def test_filter_products_by_brand(self):
        """Test filtering products by brand"""
        url = reverse('product-list')
        response = self.client.get(url, {'brand': self.brand.id})
        
        self.assertEqual(response.status_code, status.HTTP_200_OK)
        self.assertEqual(len(response.data['results']), 2)
    
    def test_filter_products_by_price_range(self):
        """Test filtering products by price range"""
        url = reverse('product-list')
        response = self.client.get(url, {'price': '800-1000'})

        self.assertEqual(response.status_code, status.HTTP_200_OK)
        self.assertEqual(len(response.data['results']), 1)
        self.assertEqual(response.data['results'][0]['name'], 'iPhone 13')
    
    def test_search_products(self):
        """Test searching products by name"""
        url = reverse('product-list')
        response = self.client.get(url, {'search': 'iPhone 13'})
        
        self.assertEqual(response.status_code, status.HTTP_200_OK)
        self.assertEqual(len(response.data['results']), 1)
        self.assertEqual(response.data['results'][0]['name'], 'iPhone 13')
    
    def test_search_products_partial_match(self):
        """Test searching products with partial match"""
        url = reverse('product-list')
        response = self.client.get(url, {'search': 'iPhone'})
        
        self.assertEqual(response.status_code, status.HTTP_200_OK)
        self.assertEqual(len(response.data['results']), 2)
    
    def test_order_products_by_price(self):
        """Test ordering products by price"""
        url = reverse('product-list')
        response = self.client.get(url, {'ordering': 'price'})
        
        self.assertEqual(response.status_code, status.HTTP_200_OK)
        prices = [Decimal(product['price']) for product in response.data['results']]
        self.assertEqual(prices, sorted(prices))
    
    def test_order_products_by_price_desc(self):
        """Test ordering products by price descending"""
        url = reverse('product-list')
        response = self.client.get(url, {'ordering': '-price'})
        
        self.assertEqual(response.status_code, status.HTTP_200_OK)
        prices = [Decimal(product['price']) for product in response.data['results']]
        self.assertEqual(prices, sorted(prices, reverse=True))
    
    def test_product_pagination(self):
        """Test product list pagination"""
        # Create more products to test pagination
        for i in range(15):
            Product.objects.create(
                name=f'Test Product {i}',
                category=self.category,
                brand=self.brand,
                price=Decimal('100.00'),
                stock=10
            )
        
        url = reverse('product-list')
        response = self.client.get(url, {'page': 1, 'page_size': 10})
        
        self.assertEqual(response.status_code, status.HTTP_200_OK)
        self.assertEqual(len(response.data['results']), 10)
        self.assertIn('next', response.data)
        self.assertIn('count', response.data)

class TestReviewViews(APITestCase):
    """Test review-related API endpoints"""
    
    def setUp(self):
        """Set up test data"""
        self.user = User.objects.create_user(
            email='<EMAIL>',
            password='testpass123',
            name='Test User'
        )
        
        self.category = Category.objects.create(name='Electronics')
        self.brand = Brand.objects.create(name='Apple')
        self.product = Product.objects.create(
            name='iPhone 13',
            category=self.category,
            brand=self.brand,
            price=Decimal('999.00'),
            stock=50
        )
        
        # Authenticate user
        refresh = RefreshToken.for_user(self.user)
        self.client.credentials(HTTP_AUTHORIZATION=f'Bearer {refresh.access_token}')
    
    def test_create_review(self):
        """Test creating a product review"""
        url = reverse('review-list', kwargs={'product_slug': self.product.slug})
        review_data = {
            'rating': 5,
            'comment': 'Excellent product!'
        }

        response = self.client.post(url, review_data)

        self.assertEqual(response.status_code, status.HTTP_201_CREATED)
        self.assertTrue(Review.objects.filter(user=self.user, product=self.product).exists())
    
    def test_list_product_reviews(self):
        """Test listing reviews for a product"""
        # Create test review
        Review.objects.create(
            product=self.product,
            user=self.user,
            rating=5,
            comment='Great product!'
        )

        url = reverse('review-list', kwargs={'product_slug': self.product.slug})
        response = self.client.get(url)

        self.assertEqual(response.status_code, status.HTTP_200_OK)
        self.assertEqual(len(response.data), 1)
        self.assertEqual(response.data[0]['rating'], 5)
    
    def test_create_review_unauthenticated(self):
        """Test creating review without authentication"""
        self.client.credentials()  # Remove authentication

        url = reverse('review-list', kwargs={'product_slug': self.product.slug})
        review_data = {
            'rating': 5,
            'comment': 'Excellent product!'
        }

        response = self.client.post(url, review_data)

        self.assertEqual(response.status_code, status.HTTP_401_UNAUTHORIZED)
    
    def test_create_duplicate_review(self):
        """Test creating duplicate review for same product"""
        # Create first review
        Review.objects.create(
            product=self.product,
            user=self.user,
            rating=5,
            comment='First review'
        )

        # Try to create second review
        url = reverse('review-list', kwargs={'product_slug': self.product.slug})
        review_data = {
            'rating': 3,
            'comment': 'Second review'
        }

        response = self.client.post(url, review_data)

        self.assertEqual(response.status_code, status.HTTP_400_BAD_REQUEST)
    
    def test_create_review_invalid_rating(self):
        """Test creating review with invalid rating"""
        url = reverse('review-list', kwargs={'product_slug': self.product.slug})
        review_data = {
            'rating': 6,  # Invalid rating (max is 5)
            'comment': 'Invalid rating'
        }

        response = self.client.post(url, review_data)

        self.assertEqual(response.status_code, status.HTTP_400_BAD_REQUEST)

class TestProductImageViews(APITestCase):
    """Test product image-related endpoints"""
    
    def setUp(self):
        """Set up test data"""
        self.user = User.objects.create_user(
            email='<EMAIL>',
            password='testpass123',
            name='Admin User',
            is_staff=True,
            is_superuser=True  # Make user admin for product image access
        )

        # Authenticate user
        refresh = RefreshToken.for_user(self.user)
        self.client.credentials(HTTP_AUTHORIZATION=f'Bearer {refresh.access_token}')

        self.category = Category.objects.create(name='Electronics')
        self.brand = Brand.objects.create(name='Apple')
        self.product = Product.objects.create(
            name='iPhone 13',
            category=self.category,
            brand=self.brand,
            price=Decimal('999.00'),
            stock=50
        )

        self.product_image = ProductImage.objects.create(
            product=self.product,
            is_primary=True
        )
    
    def test_list_product_images(self):
        """Test listing product images"""
        url = reverse('product-image-list', kwargs={'product_slug': self.product.slug})
        response = self.client.get(url, {'product': self.product.id})

        self.assertEqual(response.status_code, status.HTTP_200_OK)
        self.assertEqual(len(response.data), 1)
        self.assertEqual(response.data[0]['is_primary'], True)

class TestProductVariantViews(APITestCase):
    """Test product variant-related endpoints"""
    
    def setUp(self):
        """Set up test data"""
        self.user = User.objects.create_user(
            email='<EMAIL>',
            password='testpass123',
            name='Admin User',
            is_staff=True,
            is_superuser=True  # Make user admin for product variant access
        )

        # Authenticate user
        refresh = RefreshToken.for_user(self.user)
        self.client.credentials(HTTP_AUTHORIZATION=f'Bearer {refresh.access_token}')

        self.category = Category.objects.create(name='Electronics')
        self.brand = Brand.objects.create(name='Apple')
        self.product = Product.objects.create(
            name='iPhone 13',
            category=self.category,
            brand=self.brand,
            price=Decimal('999.00'),
            stock=50
        )
        
        self.variant1 = ProductVariant.objects.create(
            product=self.product,
            name='128GB Space Gray',
            sku='IPHONE13-128GB-SG',
            price_adjustment=Decimal('0.00'),
            stock=25
        )

        self.variant2 = ProductVariant.objects.create(
            product=self.product,
            name='256GB Space Gray',
            sku='IPHONE13-256GB-SG',
            price_adjustment=Decimal('100.00'),
            stock=15
        )
    
    def test_list_product_variants(self):
        """Test listing product variants"""
        url = reverse('product-variant-list', kwargs={'product_slug': self.product.slug})
        response = self.client.get(url)

        self.assertEqual(response.status_code, status.HTTP_200_OK)
        self.assertEqual(len(response.data), 2)

        variant_names = [variant['name'] for variant in response.data]
        self.assertIn('128GB Space Gray', variant_names)
        self.assertIn('256GB Space Gray', variant_names)

class TestProductAPIOptimization(APITestCase):
    """Test product API performance optimizations"""
    
    def setUp(self):
        """Set up test data"""
        self.category = Category.objects.create(name='Electronics')
        self.brand = Brand.objects.create(name='Apple')
        
        # Create multiple products for testing
        for i in range(20):
            Product.objects.create(
                name=f'Product {i}',
                category=self.category,
                brand=self.brand,
                price=Decimal('100.00'),
                stock=10
            )
    
    def test_product_list_select_related(self):
        """Test that product list uses select_related for optimization"""
        url = reverse('product-list')

        # Optimized performance: Uses select_related and prefetch_related for efficient queries
        # Should be significantly fewer queries than the original 45
        from django.test.utils import override_settings
        from django.db import connection

        with override_settings(DEBUG=True):
            connection.queries_log.clear()
            response = self.client.get(url)
            query_count = len(connection.queries)

        # Ensure optimization is working (should be much less than 45 queries)
        self.assertLess(query_count, 10, f"Expected optimized queries (<10), got {query_count}")
        self.assertEqual(response.status_code, status.HTTP_200_OK)
    
    def test_product_list_pagination(self):
        """Test product list pagination works correctly"""
        url = reverse('product-list')
        response = self.client.get(url, {'page_size': 5})
        
        self.assertEqual(response.status_code, status.HTTP_200_OK)
        self.assertEqual(len(response.data['results']), 5)
        self.assertIn('next', response.data)
        self.assertIn('previous', response.data)
        self.assertIn('count', response.data)
