[phases.setup]
nixpkgs = ["nodejs_20"]

[phases.install]
cmds = ["npm install --legacy-peer-deps"]

[phases.build]
cmds = ["npm run build:seo --legacy-peer-deps"]

[start]
cmd = "npm run start"

[variables]
NODE_ENV = "production"

# Caching configuration
[phases.install.cache]
# Cache node_modules for faster builds
directories = ["node_modules"]
key = ["package-lock.json", "package.json"]

# Optimize for Next.js
[phases.build.cache]
directories = [".next/cache"]
key = ["package.json", "next.config.ts", "tsconfig.json"]


