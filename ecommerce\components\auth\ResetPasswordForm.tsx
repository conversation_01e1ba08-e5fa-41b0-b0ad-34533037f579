"use client";

import React, { useState } from 'react';
import { motion } from 'framer-motion';
import { KeyRound, Eye, EyeOff, CheckCircle, ArrowRight } from 'lucide-react';
import { Formik, Form, Field, ErrorMessage, FormikHelpers } from 'formik';
import * as Yup from 'yup';
import Link from 'next/link';
import { useRouter } from 'next/navigation';
import { useToast } from '../../hooks/use-toast';
import { validatePasswordStrength, validatePasswordMatch } from '../../utils/passwordValidation';
import PasswordStrengthIndicator from './PasswordStrengthIndicator';
import AuthSpinner from '../ui/loading/AuthSpinner';
import useApi from '../../hooks/useApi';
import { MAIN_URL, RESET_PASSWORD } from '../../constant/urls';

interface ResetPasswordFormValues {
  newPassword: string;
  confirmPassword: string;
}

interface ResetPasswordFormProps {
  token: string;
}

const validationSchema: Yup.Schema<ResetPasswordFormValues> = Yup.object({
  newPassword: Yup.string()
    .min(8, 'Password must be at least 8 characters long')
    .required('New password is required')
    .test('password-strength', 'Password does not meet security requirements', function(value) {
      if (!value) return false;
      const validation = validatePasswordStrength(value);
      return validation.isValid;
    }),
  confirmPassword: Yup.string()
    .required('Please confirm your password')
    .test('passwords-match', 'Passwords do not match', function(value) {
      const { newPassword } = this.parent;
      if (!value || !newPassword) return false;
      return validatePasswordMatch(newPassword, value).isValid;
    }),
});

const ResetPasswordForm: React.FC<ResetPasswordFormProps> = ({ token }) => {
  const [showPassword, setShowPassword] = useState(false);
  const [showConfirmPassword, setShowConfirmPassword] = useState(false);
  const [isSuccess, setIsSuccess] = useState(false);
  const { create } = useApi(MAIN_URL);
  const { toast } = useToast();
  const router = useRouter();

  const handleSubmit = async (
    values: ResetPasswordFormValues,
    { setSubmitting, setFieldError }: FormikHelpers<ResetPasswordFormValues>
  ) => {
    setSubmitting(true);
    
    try {
      const response = await create(RESET_PASSWORD, {
        token,
        new_password: values.newPassword,
        confirm_password: values.confirmPassword,
      });
      
      setIsSuccess(true);
      
      toast({
        variant: "success",
        title: "Password reset successful",
        description: "Your password has been updated. You can now login with your new password.",
      });
      
      // Redirect to login after 3 seconds
      setTimeout(() => {
        router.push('/auth/login');
      }, 3000);
      
    } catch (err: any) {
      console.error('Reset password error:', err);
      
      let errorMessage = 'Failed to reset password. Please try again.';
      
      if (err?.response?.data) {
        const errorData = err.response.data;
        if (errorData.token) {
          errorMessage = Array.isArray(errorData.token) ? errorData.token[0] : errorData.token;
        } else if (errorData.new_password) {
          errorMessage = Array.isArray(errorData.new_password) ? errorData.new_password[0] : errorData.new_password;
        } else if (errorData.error) {
          errorMessage = errorData.error;
        }
      }
      
      toast({
        title: "Reset Failed",
        description: errorMessage,
        variant: "destructive",
      });
      
      // If token is invalid, redirect to forgot password
      if (errorMessage.toLowerCase().includes('token') || errorMessage.toLowerCase().includes('expired')) {
        setTimeout(() => {
          router.push('/auth/forgot-password');
        }, 2000);
      }
    } finally {
      setSubmitting(false);
    }
  };

  if (isSuccess) {
    return (
      <motion.div
        initial={{ opacity: 0, y: 20 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ duration: 0.5 }}
        className="text-center space-y-6"
      >
        <div className="flex justify-center">
          <div className="p-4 bg-green-50 rounded-full">
            <CheckCircle className="w-8 h-8 text-green-600" />
          </div>
        </div>
        
        <div className="space-y-2">
          <h2 className="text-2xl font-bold text-gray-800">
            Password Reset Successful!
          </h2>
          <p className="text-gray-600">
            Your password has been successfully updated.
          </p>
        </div>
        
        <div className="bg-green-50 border border-green-200 rounded-lg p-4">
          <p className="text-green-800 text-sm">
            You can now login with your new password. Redirecting you to the login page...
          </p>
        </div>
        
        <Link
          href="/auth/login"
          className="inline-flex items-center gap-2 text-blue-600 hover:text-blue-800 font-medium transition-colors duration-300"
        >
          <span>Go to login</span>
          <ArrowRight className="w-4 h-4" />
        </Link>
      </motion.div>
    );
  }

  return (
    <motion.div
      initial={{ opacity: 0, y: 20 }}
      animate={{ opacity: 1, y: 0 }}
      transition={{ duration: 0.5 }}
      className="space-y-6"
    >
      <div className="text-center space-y-2">
        <h2 className="text-2xl font-bold text-gray-800">
          Reset your password
        </h2>
        <p className="text-gray-600">
          Enter your new password below. Make sure it's strong and secure.
        </p>
      </div>

      <Formik<ResetPasswordFormValues>
        initialValues={{ newPassword: '', confirmPassword: '' }}
        validationSchema={validationSchema}
        onSubmit={handleSubmit}
      >
        {({ isSubmitting, values, errors, touched }) => (
          <Form className="space-y-6">
            {/* New Password Field */}
            <div>
              <label
                htmlFor="newPassword"
                className="block mb-2 text-sm font-medium text-gray-700"
              >
                New Password
              </label>
              <div className="relative">
                <span className="absolute inset-y-0 left-0 flex items-center p-1 pl-3 text-gray-400">
                  <KeyRound className="w-5 h-5" />
                </span>
                <Field
                  type={showPassword ? "text" : "password"}
                  name="newPassword"
                  className="pl-12 pr-12 w-full bg-gray-50 text-gray-600 border border-gray-300 sm:text-sm rounded-lg focus:ring-2 focus:outline-none focus:ring-blue-300 focus:border-blue-500 p-2.5 py-3 px-4 transition-all duration-300"
                  placeholder="Enter your new password"
                />
                <button
                  type="button"
                  className="absolute inset-y-0 right-0 flex items-center p-1 pr-3 text-gray-400 hover:text-gray-600"
                  onClick={() => setShowPassword(!showPassword)}
                >
                  {showPassword ? <EyeOff className="w-5 h-5" /> : <Eye className="w-5 h-5" />}
                </button>
                <ErrorMessage
                  name="newPassword"
                  component="p"
                  className="text-red-500 text-xs mt-1"
                />
              </div>
              
              {/* Password Strength Indicator */}
              {values.newPassword && (
                <div className="mt-3">
                  <PasswordStrengthIndicator 
                    password={values.newPassword}
                    showRequirements={true}
                  />
                </div>
              )}
            </div>

            {/* Confirm Password Field */}
            <div>
              <label
                htmlFor="confirmPassword"
                className="block mb-2 text-sm font-medium text-gray-700"
              >
                Confirm New Password
              </label>
              <div className="relative">
                <span className="absolute inset-y-0 left-0 flex items-center p-1 pl-3 text-gray-400">
                  <KeyRound className="w-5 h-5" />
                </span>
                <Field
                  type={showConfirmPassword ? "text" : "password"}
                  name="confirmPassword"
                  className="pl-12 pr-12 w-full bg-gray-50 text-gray-600 border border-gray-300 sm:text-sm rounded-lg focus:ring-2 focus:outline-none focus:ring-blue-300 focus:border-blue-500 p-2.5 py-3 px-4 transition-all duration-300"
                  placeholder="Confirm your new password"
                />
                <button
                  type="button"
                  className="absolute inset-y-0 right-0 flex items-center p-1 pr-3 text-gray-400 hover:text-gray-600"
                  onClick={() => setShowConfirmPassword(!showConfirmPassword)}
                >
                  {showConfirmPassword ? <EyeOff className="w-5 h-5" /> : <Eye className="w-5 h-5" />}
                </button>
                <ErrorMessage
                  name="confirmPassword"
                  component="p"
                  className="text-red-500 text-xs mt-1"
                />
              </div>
            </div>

            <motion.button
              type="submit"
              className="w-full text-white bg-blue-600 hover:bg-blue-700 focus:ring-4 focus:outline-none focus:ring-blue-300 font-medium rounded-lg text-sm px-5 py-3 text-center transition-all duration-300 shadow-md hover:shadow-lg flex justify-center items-center gap-2"
              disabled={isSubmitting}
              whileHover={{ scale: 1.02 }}
              whileTap={{ scale: 0.98 }}
            >
              {isSubmitting ? (
                <>
                  <AuthSpinner size="sm" color="border-t-white" />
                  <span>Resetting password...</span>
                </>
              ) : (
                <>
                  <KeyRound className="w-4 h-4" />
                  <span>Reset password</span>
                </>
              )}
            </motion.button>
          </Form>
        )}
      </Formik>

      <div className="text-center">
        <Link
          href="/auth/login"
          className="text-gray-600 hover:text-gray-800 transition-colors duration-300 text-sm"
        >
          Back to login
        </Link>
      </div>
    </motion.div>
  );
};

export default ResetPasswordForm;
