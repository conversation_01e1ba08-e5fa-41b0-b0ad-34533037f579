"use client";

import React, { Suspense, useEffect, useState } from 'react';
import { motion } from 'framer-motion';
import { useSearchParams, useRouter } from 'next/navigation';
import { AlertTriangle } from 'lucide-react';
import ResetPasswordForm from '../../../components/auth/ResetPasswordForm';
import { Toaster } from '../../../components/ui/toaster';
import AuthSpinner from '../../../components/ui/loading/AuthSpinner';
import Link from 'next/link';

const ResetPasswordContent: React.FC = () => {
  const searchParams = useSearchParams();
  const router = useRouter();
  const [token, setToken] = useState<string | null>(null);
  const [isValidating, setIsValidating] = useState(true);

  useEffect(() => {
    const tokenParam = searchParams.get('token');
    
    if (!tokenParam) {
      // No token provided, redirect to forgot password
      setTimeout(() => {
        router.push('/auth/forgot-password');
      }, 3000);
    } else {
      setToken(tokenParam);
    }
    
    setIsValidating(false);
  }, [searchParams, router]);

  if (isValidating) {
    return (
      <div className="flex w-full h-screen justify-center items-center">
        <div className="flex flex-col items-center gap-4">
          <AuthSpinner size="lg" color="border-t-blue-600" />
          <p className="text-gray-600 animate-pulse">Validating reset token...</p>
        </div>
      </div>
    );
  }

  if (!token) {
    return (
      <div className="flex justify-center items-center min-h-screen w-full bg-gray-50">
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.5 }}
          className="flex flex-col w-4/5 md:w-[30rem] mx-auto p-4 md:p-8 bg-white rounded-2xl shadow-xl border border-gray-100 text-center"
        >
          <div className="flex justify-center mb-4">
            <div className="p-4 bg-red-50 rounded-full">
              <AlertTriangle className="w-8 h-8 text-red-600" />
            </div>
          </div>
          
          <h2 className="text-2xl font-bold text-gray-800 mb-2">
            Invalid Reset Link
          </h2>
          
          <p className="text-gray-600 mb-6">
            The password reset link is missing or invalid. Please request a new password reset link.
          </p>
          
          <div className="space-y-3">
            <Link
              href="/auth/forgot-password"
              className="w-full inline-block text-white bg-blue-600 hover:bg-blue-700 focus:ring-4 focus:outline-none focus:ring-blue-300 font-medium rounded-lg text-sm px-5 py-3 text-center transition-all duration-300"
            >
              Request New Reset Link
            </Link>
            
            <Link
              href="/auth/login"
              className="block text-gray-600 hover:text-gray-800 transition-colors duration-300 text-sm"
            >
              Back to login
            </Link>
          </div>
          
          <div className="mt-6 text-xs text-gray-500">
            Redirecting to forgot password page in 3 seconds...
          </div>
        </motion.div>
      </div>
    );
  }

  return (
    <div className="flex justify-center items-center min-h-screen w-full bg-gray-50 py-8">
      <motion.div
        initial={{ opacity: 0, y: 20 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ duration: 0.5 }}
        className="flex flex-col w-4/5 md:w-[30rem] mx-auto p-4 md:p-8 bg-white rounded-2xl shadow-xl border border-gray-100"
      >
        {/* Header */}
        <motion.div
          className="flex flex-row gap-3 pb-6 border-b border-gray-100"
          initial={{ opacity: 0, y: -10 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ delay: 0.2, duration: 0.5 }}
        >
          <div className="p-2 bg-blue-50 rounded-lg">
            <img 
              src="/logotriumph.png" 
              alt="Triumph Enterprises Logo" 
              width="40" 
              className="drop-shadow-sm" 
            />
          </div>
          <div className="flex flex-col justify-center">
            <h1 className="text-xl md:text-2xl font-bold text-gray-800">
              TRIUMPH ENTERPRISES
            </h1>
            <p className="text-xs text-gray-500">
              Secure Password Reset
            </p>
          </div>
        </motion.div>

        {/* Form Content */}
        <motion.div
          className="pt-6"
          initial={{ opacity: 0 }}
          animate={{ opacity: 1 }}
          transition={{ delay: 0.4, duration: 0.5 }}
        >
          <ResetPasswordForm token={token} />
        </motion.div>

        {/* Security Notice */}
        <motion.div
          className="mt-8 pt-6 border-t border-gray-100"
          initial={{ opacity: 0 }}
          animate={{ opacity: 1 }}
          transition={{ delay: 0.6, duration: 0.5 }}
        >
          <div className="bg-amber-50 border border-amber-200 rounded-lg p-4">
            <div className="text-xs text-amber-800 space-y-1">
              <p className="font-medium">⚠️ Security Requirements:</p>
              <ul className="space-y-0.5 text-amber-700">
                <li>• Password must be at least 8 characters long</li>
                <li>• Must contain uppercase, lowercase, number & special character</li>
                <li>• Cannot reuse any of your last 5 passwords</li>
                <li>• This reset link expires in 15 minutes</li>
              </ul>
            </div>
          </div>
        </motion.div>

        {/* Footer */}
        <motion.div
          className="mt-6 text-center"
          initial={{ opacity: 0 }}
          animate={{ opacity: 1 }}
          transition={{ delay: 0.8, duration: 0.5 }}
        >
          <p className="text-xs text-gray-500">
            Need help? Contact support at{' '}
            <a 
              href="mailto:<EMAIL>" 
              className="text-blue-600 hover:text-blue-800 transition-colors duration-300"
            >
              <EMAIL>
            </a>
          </p>
        </motion.div>
      </motion.div>
      <Toaster />
    </div>
  );
};

const ResetPasswordPage: React.FC = () => {
  return (
    <Suspense 
      fallback={
        <div className="flex w-full h-screen justify-center items-center">
          <div className="flex flex-col items-center gap-4">
            <AuthSpinner size="lg" color="border-t-blue-600" />
            <p className="text-gray-600 animate-pulse">Loading...</p>
          </div>
        </div>
      }
    >
      <ResetPasswordContent />
    </Suspense>
  );
};

export default ResetPasswordPage;
