import os
import json
import time
import requests
import random
import logging
from PIL import Image
from io import BytesIO

# Set up logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler("unsplash_scraper.log"),
        logging.StreamHandler()
    ]
)
logger = logging.getLogger(__name__)

# Constants
MAX_IMAGES_PER_PRODUCT = 5
OUTPUT_DIR = "output/images"
JSON_FILE_PATH = "output/json/product_data.json"

# Create output directory structure
os.makedirs(OUTPUT_DIR, exist_ok=True)

def download_image(url, product_dir, filename):
    """Download an image from URL and save it to the specified directory."""
    try:
        response = requests.get(url, timeout=10)
        if response.status_code == 200:
            # Verify it's an image
            try:
                img = Image.open(BytesIO(response.content))
                img_path = os.path.join(product_dir, filename)
                img.save(img_path)
                logger.info(f"Downloaded image: {img_path}")
                return img_path
            except Exception as e:
                logger.error(f"Not a valid image: {url}, Error: {str(e)}")
                return None
        else:
            logger.error(f"Failed to download image: {url}, Status code: {response.status_code}")
            return None
    except Exception as e:
        logger.error(f"Error downloading image: {url}, Error: {str(e)}")
        return None

def get_unsplash_images(category):
    """Get images from Unsplash based on category."""
    image_urls = []
    
    # Map product categories to search terms
    search_terms = {
        "Furniture Locks": "furniture lock",
        "Rim Locks": "door lock",
        "Mortise Locks": "mortise lock",
        "Padlocks": "padlock"
    }
    
    # Default to "lock" if category not in mapping
    search_term = search_terms.get(category, "lock")
    
    # Unsplash API URL (using their public API without authentication)
    url = f"https://api.unsplash.com/search/photos?query={search_term}&per_page=30&client_id=YOUR_ACCESS_KEY"
    
    try:
        # For demo purposes, we'll use their public search endpoint instead
        # This is not ideal but will work for testing
        public_url = f"https://unsplash.com/napi/search/photos?query={search_term}&per_page=20"
        
        response = requests.get(public_url)
        
        if response.status_code == 200:
            data = response.json()
            results = data.get("results", [])
            
            for result in results:
                image_url = result.get("urls", {}).get("regular")
                if image_url and image_url not in image_urls:
                    image_urls.append(image_url)
                    if len(image_urls) >= MAX_IMAGES_PER_PRODUCT:
                        break
            
            logger.info(f"Found {len(image_urls)} images on Unsplash for category: {category}")
        else:
            logger.error(f"Failed to search Unsplash. Status code: {response.status_code}")
            
            # Fallback to some predefined lock image URLs if the API fails
            fallback_urls = [
                "https://images.unsplash.com/photo-1517646287270-a5a9ca602e5c",
                "https://images.unsplash.com/photo-1582650625119-3a31f8fa2699",
                "https://images.unsplash.com/photo-1558424087-896a94142c7e",
                "https://images.unsplash.com/photo-1622630998477-20aa696ecb05",
                "https://images.unsplash.com/photo-1598900863662-a6a8c3a32a0a"
            ]
            
            image_urls = fallback_urls[:MAX_IMAGES_PER_PRODUCT]
            logger.info(f"Using {len(image_urls)} fallback images for category: {category}")
    
    except Exception as e:
        logger.error(f"Error searching Unsplash: {str(e)}")
        
        # Use fallback URLs in case of any error
        fallback_urls = [
            "https://images.unsplash.com/photo-1517646287270-a5a9ca602e5c",
            "https://images.unsplash.com/photo-1582650625119-3a31f8fa2699",
            "https://images.unsplash.com/photo-1558424087-896a94142c7e",
            "https://images.unsplash.com/photo-1622630998477-20aa696ecb05",
            "https://images.unsplash.com/photo-1598900863662-a6a8c3a32a0a"
        ]
        
        image_urls = fallback_urls[:MAX_IMAGES_PER_PRODUCT]
        logger.info(f"Using {len(image_urls)} fallback images for category: {category}")
    
    return image_urls

def process_product(product):
    """Process a single product to find and download images."""
    # Get product details
    model_number = product.get("model_number", "")
    name = product.get("name", "")
    brand = product.get("brand", "")
    category = product.get("category", "")
    
    # Create directory for product images
    product_dir = os.path.join(OUTPUT_DIR, f"{brand}_{category}_{model_number}")
    os.makedirs(product_dir, exist_ok=True)
    
    logger.info(f"Processing product: {name} (Model: {model_number})")
    
    # Get images from Unsplash based on category
    image_urls = get_unsplash_images(category)
    
    # Download images
    image_paths = []
    for i, url in enumerate(image_urls):
        filename = f"{model_number}_{i+1}.jpg"
        img_path = download_image(url, product_dir, filename)
        if img_path:
            # Convert to relative path
            rel_path = os.path.relpath(img_path, os.getcwd())
            image_paths.append(rel_path)
    
    # Update product with image paths
    product["images"] = image_paths
    
    logger.info(f"Completed processing {name} (Model: {model_number}). Found {len(image_paths)} images.")
    
    # Add a small delay to avoid being blocked
    time.sleep(random.uniform(0.5, 1.5))
    
    return product

def main():
    """Main function to process all products."""
    logger.info("Starting Unsplash image scraping process")
    
    # Load product data from JSON file
    try:
        with open(JSON_FILE_PATH, 'r', encoding='utf-8') as f:
            data = json.load(f)
            products = data.get("products", [])
            logger.info(f"Loaded {len(products)} products from {JSON_FILE_PATH}")
    except Exception as e:
        logger.error(f"Error loading product data: {str(e)}")
        return
    
    # Process products one by one
    updated_products = []
    for i, product in enumerate(products):
        try:
            updated_product = process_product(product)
            updated_products.append(updated_product)
            logger.info(f"Completed {i+1}/{len(products)} products")
        except Exception as e:
            logger.error(f"Error processing product: {str(e)}")
            # Add the original product to maintain data integrity
            updated_products.append(product)
    
    # Update the JSON file with new image paths
    try:
        data["products"] = updated_products
        with open(JSON_FILE_PATH, 'w', encoding='utf-8') as f:
            json.dump(data, f, indent=2)
        logger.info(f"Updated {JSON_FILE_PATH} with image paths")
    except Exception as e:
        logger.error(f"Error updating JSON file: {str(e)}")
        
        # Save a backup in case the main file update fails
        try:
            backup_path = JSON_FILE_PATH.replace(".json", "_updated.json")
            with open(backup_path, 'w', encoding='utf-8') as f:
                json.dump(data, f, indent=2)
            logger.info(f"Saved backup to {backup_path}")
        except Exception as e:
            logger.error(f"Error saving backup file: {str(e)}")
    
    logger.info("Unsplash image scraping process completed")

if __name__ == "__main__":
    main()
