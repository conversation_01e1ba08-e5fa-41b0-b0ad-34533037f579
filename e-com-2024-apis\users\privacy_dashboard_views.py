"""
Privacy Performance Dashboard Views
Provides API endpoints for monitoring privacy feature performance
"""

from rest_framework.views import APIView
from rest_framework.response import Response
from rest_framework import permissions, status
from django.contrib.auth import get_user_model
from django.utils import timezone
from .privacy_performance_monitor import privacy_monitor
from .privacy_security_audit import run_privacy_security_audit
import logging

User = get_user_model()
logger = logging.getLogger(__name__)

class PrivacyPerformanceDashboardView(APIView):
    """API endpoint for privacy performance dashboard data"""
    
    permission_classes = [permissions.IsAdminUser]  # Only admin users can access
    
    def get(self, request):
        """Get privacy performance dashboard data"""
        try:
            dashboard_data = privacy_monitor.get_performance_dashboard_data()
            
            return Response({
                'success': True,
                'data': dashboard_data,
                'message': 'Privacy performance data retrieved successfully'
            })
            
        except Exception as e:
            logger.error(f"Error retrieving privacy performance data: {str(e)}")
            return Response({
                'success': False,
                'error': 'Failed to retrieve performance data',
                'message': str(e)
            }, status=status.HTTP_500_INTERNAL_SERVER_ERROR)

class PrivacyPerformanceReportView(APIView):
    """API endpoint for detailed privacy performance reports"""
    
    permission_classes = [permissions.IsAdminUser]
    
    def get(self, request):
        """Generate detailed privacy performance report"""
        try:
            # Get report period from query params (default 7 days)
            days = int(request.query_params.get('days', 7))
            
            if days > 30:  # Limit to 30 days max
                days = 30
            
            report = privacy_monitor.generate_performance_report(days=days)
            
            return Response({
                'success': True,
                'data': report,
                'message': f'Privacy performance report generated for last {days} days'
            })
            
        except Exception as e:
            logger.error(f"Error generating privacy performance report: {str(e)}")
            return Response({
                'success': False,
                'error': 'Failed to generate performance report',
                'message': str(e)
            }, status=status.HTTP_500_INTERNAL_SERVER_ERROR)

class PrivacySecurityAuditView(APIView):
    """API endpoint for privacy security audit"""
    
    permission_classes = [permissions.IsAdminUser]
    
    def get(self, request):
        """Run privacy security audit"""
        try:
            audit_results = run_privacy_security_audit()
            
            return Response({
                'success': True,
                'data': audit_results,
                'message': 'Privacy security audit completed successfully'
            })
            
        except Exception as e:
            logger.error(f"Error running privacy security audit: {str(e)}")
            return Response({
                'success': False,
                'error': 'Failed to run security audit',
                'message': str(e)
            }, status=status.HTTP_500_INTERNAL_SERVER_ERROR)

class PrivacyMetricsView(APIView):
    """API endpoint for real-time privacy metrics"""
    
    permission_classes = [permissions.IsAdminUser]
    
    def get(self, request):
        """Get real-time privacy metrics"""
        try:
            from django.core.cache import cache
            from .models import UserConsent, DataDeletionRequest
            from datetime import timedelta
            
            # Calculate real-time metrics
            now = timezone.now()
            last_24h = now - timedelta(hours=24)
            last_7d = now - timedelta(days=7)
            
            metrics = {
                'timestamp': now.isoformat(),
                'consent_metrics': {
                    'total_consents': UserConsent.objects.count(),
                    'consents_last_24h': UserConsent.objects.filter(
                        granted_at__gte=last_24h
                    ).count(),
                    'consents_last_7d': UserConsent.objects.filter(
                        granted_at__gte=last_7d
                    ).count(),
                    'consent_types': {}
                },
                'deletion_metrics': {
                    'total_requests': DataDeletionRequest.objects.count(),
                    'pending_requests': DataDeletionRequest.objects.filter(
                        status='PENDING'
                    ).count(),
                    'requests_last_24h': DataDeletionRequest.objects.filter(
                        request_date__gte=last_24h
                    ).count(),
                    'requests_last_7d': DataDeletionRequest.objects.filter(
                        request_date__gte=last_7d
                    ).count()
                },
                'user_metrics': {
                    'total_users': User.objects.filter(is_active=True).count(),
                    'users_with_consents': UserConsent.objects.values('user').distinct().count(),
                    'consent_adoption_rate': 0
                }
            }
            
            # Calculate consent adoption rate
            total_users = metrics['user_metrics']['total_users']
            users_with_consents = metrics['user_metrics']['users_with_consents']
            if total_users > 0:
                metrics['user_metrics']['consent_adoption_rate'] = round(
                    (users_with_consents / total_users) * 100, 1
                )
            
            # Get consent type breakdown
            for consent_type, description in UserConsent.CONSENT_TYPES:
                granted_count = UserConsent.objects.filter(
                    consent_type=consent_type,
                    granted=True
                ).count()
                
                metrics['consent_metrics']['consent_types'][consent_type] = {
                    'description': description,
                    'granted_count': granted_count,
                    'adoption_rate': round((granted_count / total_users) * 100, 1) if total_users > 0 else 0
                }
            
            return Response({
                'success': True,
                'data': metrics,
                'message': 'Privacy metrics retrieved successfully'
            })
            
        except Exception as e:
            logger.error(f"Error retrieving privacy metrics: {str(e)}")
            return Response({
                'success': False,
                'error': 'Failed to retrieve privacy metrics',
                'message': str(e)
            }, status=status.HTTP_500_INTERNAL_SERVER_ERROR)

class PrivacyHealthCheckView(APIView):
    """API endpoint for privacy system health check"""
    
    permission_classes = [permissions.IsAdminUser]
    
    def get(self, request):
        """Perform privacy system health check"""
        try:
            health_status = {
                'timestamp': timezone.now().isoformat(),
                'overall_status': 'healthy',
                'components': {},
                'alerts': []
            }
            
            # Check database connectivity
            try:
                User.objects.count()
                health_status['components']['database'] = {
                    'status': 'healthy',
                    'message': 'Database connection successful'
                }
            except Exception as e:
                health_status['components']['database'] = {
                    'status': 'unhealthy',
                    'message': f'Database connection failed: {str(e)}'
                }
                health_status['overall_status'] = 'unhealthy'
            
            # Check cache connectivity
            try:
                from django.core.cache import cache
                cache.set('health_check', 'test', 10)
                cache.get('health_check')
                health_status['components']['cache'] = {
                    'status': 'healthy',
                    'message': 'Cache system operational'
                }
            except Exception as e:
                health_status['components']['cache'] = {
                    'status': 'unhealthy',
                    'message': f'Cache system failed: {str(e)}'
                }
                health_status['overall_status'] = 'degraded'
            
            # Check performance monitoring
            try:
                dashboard_data = privacy_monitor.get_performance_dashboard_data()
                alerts = dashboard_data.get('alerts', [])
                
                if alerts:
                    health_status['alerts'].extend(alerts)
                    if any(alert.get('severity') == 'high' for alert in alerts):
                        health_status['overall_status'] = 'degraded'
                
                health_status['components']['performance_monitoring'] = {
                    'status': 'healthy',
                    'message': f'Performance monitoring active, {len(alerts)} alerts'
                }
            except Exception as e:
                health_status['components']['performance_monitoring'] = {
                    'status': 'unhealthy',
                    'message': f'Performance monitoring failed: {str(e)}'
                }
                health_status['overall_status'] = 'degraded'
            
            # Check security audit system
            try:
                audit_results = run_privacy_security_audit()
                risk_level = audit_results.get('risk_assessment', {}).get('level', 'UNKNOWN')
                
                if risk_level in ['HIGH', 'MEDIUM']:
                    health_status['overall_status'] = 'degraded'
                    health_status['alerts'].append({
                        'type': 'security',
                        'severity': 'high' if risk_level == 'HIGH' else 'medium',
                        'message': f'Security audit detected {risk_level} risk level'
                    })
                
                health_status['components']['security_audit'] = {
                    'status': 'healthy',
                    'message': f'Security audit completed, risk level: {risk_level}'
                }
            except Exception as e:
                health_status['components']['security_audit'] = {
                    'status': 'unhealthy',
                    'message': f'Security audit failed: {str(e)}'
                }
                health_status['overall_status'] = 'degraded'
            
            return Response({
                'success': True,
                'data': health_status,
                'message': f'Privacy system health check completed - Status: {health_status["overall_status"]}'
            })
            
        except Exception as e:
            logger.error(f"Error performing privacy health check: {str(e)}")
            return Response({
                'success': False,
                'error': 'Failed to perform health check',
                'message': str(e)
            }, status=status.HTTP_500_INTERNAL_SERVER_ERROR)
