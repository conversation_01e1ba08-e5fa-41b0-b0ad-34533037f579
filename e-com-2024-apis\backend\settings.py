import os
from pathlib import Path
from datetime import timedelta
from dotenv import load_dotenv

load_dotenv()

BASE_DIR = Path(__file__).resolve().parent.parent

# Generate a secure secret key if not provided
SECRET_KEY = os.getenv("DJANGO_SECRET_KEY", "django-insecure-dev-key-for-local-development-only-change-in-production")

# Security: Only enable debug in development
DEBUG = os.getenv("DEVELOPMENT", "False").lower() == "true"

# Encryption key validation for production
ENCRYPTION_KEY = os.getenv('ENCRYPTION_KEY')
if not ENCRYPTION_KEY and not DEBUG:
    raise ValueError("ENCRYPTION_KEY environment variable is required for production")

# Security: Restrict allowed hosts
ALLOWED_HOSTS = [
    "localhost",
    "127.0.0.1",
    "testserver",  # For testing
    "api-ecom.dev.trio.net.in",
    "trio.net.in",
    "dev.trio.net.in",
    # Add your production domains here
]

INSTALLED_APPS = [
    "django.contrib.admin",
    "django.contrib.auth",
    "django.contrib.contenttypes",
    "django.contrib.sessions",
    "django.contrib.messages",
    "django.contrib.staticfiles",
    # Third party apps
    "rest_framework",
    "rest_framework_simplejwt",
    "rest_framework_simplejwt.token_blacklist",
    "corsheaders",
    "drf_yasg",
    "django_filters",
    "minio_storage",
    # Local apps
    "backend",  # Include backend app for management commands
    "users",
    "products",
    "orders",
    "promotions",
    "dashboard",
    "payment_gateway",
    "shipping",  # Rapidshyp shipping integration
]



MIDDLEWARE = [
    "django.middleware.security.SecurityMiddleware",
    "whitenoise.middleware.WhiteNoiseMiddleware",  # Add WhiteNoise for static file serving
    "django.contrib.sessions.middleware.SessionMiddleware",
    "corsheaders.middleware.CorsMiddleware",
    "django.middleware.common.CommonMiddleware",
    "django.middleware.csrf.CsrfViewMiddleware",
    "django.contrib.auth.middleware.AuthenticationMiddleware",
    "django.contrib.messages.middleware.MessageMiddleware",
    "django.middleware.clickjacking.XFrameOptionsMiddleware",
    "django.middleware.gzip.GZipMiddleware",  # Add GZip compression
]

# Security Headers Configuration
SECURE_SSL_REDIRECT = not DEBUG  # Force HTTPS in production
SECURE_HSTS_SECONDS = 31536000 if not DEBUG else 0  # 1 year HSTS
SECURE_HSTS_INCLUDE_SUBDOMAINS = not DEBUG
SECURE_HSTS_PRELOAD = not DEBUG
SECURE_CONTENT_TYPE_NOSNIFF = True
SECURE_BROWSER_XSS_FILTER = True
X_FRAME_OPTIONS = 'DENY'
SECURE_REFERRER_POLICY = 'strict-origin-when-cross-origin'

# Session Security
SESSION_COOKIE_SECURE = not DEBUG  # HTTPS only in production
SESSION_COOKIE_HTTPONLY = True
SESSION_COOKIE_SAMESITE = 'Lax'  # Changed from Strict to Lax for better compatibility
SESSION_COOKIE_AGE = 3600  # 1 hour session timeout

# CSRF Security
CSRF_COOKIE_SECURE = not DEBUG  # HTTPS only in production
CSRF_COOKIE_HTTPONLY = True
CSRF_COOKIE_SAMESITE = 'Lax'

ROOT_URLCONF = "backend.urls"

TEMPLATES = [
    {
        "BACKEND": "django.template.backends.django.DjangoTemplates",
        "DIRS": [],
        "APP_DIRS": True,
        "OPTIONS": {
            "context_processors": [
                "django.template.context_processors.debug",
                "django.template.context_processors.request",
                "django.contrib.auth.context_processors.auth",
                "django.contrib.messages.context_processors.messages",
            ],
        },
    },
]

WSGI_APPLICATION = "backend.wsgi.application"
AUTH_USER_MODEL = "users.Customer"

# Database replica configuration
def get_database_config(host, port, name, user, password, is_replica=False):
    """Helper function to generate database configuration"""
    try:
        import dj_db_conn_pool.backends.postgresql  # noqa: F401 - Check if module is available
        db_engine = "dj_db_conn_pool.backends.postgresql"
        pool_size = 10 if is_replica else 20  # Smaller pool for replicas
        db_options = {
            "POOL_OPTIONS": {
                "POOL_SIZE": pool_size,
                "MAX_OVERFLOW": 5 if is_replica else 10,
                "RECYCLE": 300,  # Recycle connections after 5 minutes
            }
        }
    except ImportError:
        db_engine = "django.db.backends.postgresql"
        db_options = {}

    return {
        "ENGINE": db_engine,
        "NAME": name,
        "USER": user,
        "PASSWORD": password,
        "HOST": host,
        "PORT": port,
        **db_options
    }

# Use connection pooling in production, regular connection in development
if os.getenv("DEVELOPMENT"):
    DATABASES = {
        "default": get_database_config(
            host=os.getenv("DB_HOST"),
            port=os.getenv("DB_PORT"),
            name=os.getenv("DB_NAME"),
            user=os.getenv("DB_USER"),
            password=os.getenv("DB_PASSWORD")
        )
        
    }
    if os.getenv("DB_READ_HOST"):
        DATABASES["read_replica"] = get_database_config(
                host=os.getenv("DB_READ_HOST"),
                port=os.getenv("DB_READ_PORT", os.getenv("DB_PORT")),
                name=os.getenv("DB_READ_NAME", os.getenv("DB_NAME")),
                user=os.getenv("DB_READ_USER", os.getenv("DB_USER")),
                password=os.getenv("DB_READ_PASSWORD", os.getenv("DB_PASSWORD")),
                is_replica=True
            )

else:
    # Production database configuration with replica support
    DATABASES = {
        "default": get_database_config(
            host=os.getenv("DB_HOST"),
            port=os.getenv("DB_PORT"),
            name=os.getenv("DB_NAME"),
            user=os.getenv("DB_USER"),
            password=os.getenv("DB_PASSWORD")
        )
    }

    # Add read replica if configured
    if os.getenv("DB_READ_HOST"):
        DATABASES["read_replica"] = get_database_config(
            host=os.getenv("DB_READ_HOST"),
            port=os.getenv("DB_READ_PORT", os.getenv("DB_PORT")),
            name=os.getenv("DB_READ_NAME", os.getenv("DB_NAME")),
            user=os.getenv("DB_READ_USER", os.getenv("DB_USER")),
            password=os.getenv("DB_READ_PASSWORD", os.getenv("DB_PASSWORD")),
            is_replica=True
        )

        # Enable database routing for read/write splitting
        DATABASE_ROUTERS = ['backend.db_router.DatabaseRouter']

        print(f"Read replica configured: {os.getenv('DB_READ_HOST')}")
    else:
        print("No read replica configured - using single database")

# Database synchronization is automatically enabled when replicas are configured
# The backend app handles signal registration in its __init__.py
if len(DATABASES) > 1:
    print("Database synchronization enabled for product data")
else:
    print("Single database mode - no synchronization needed")

# Redis Cache Configuration
REDIS_URL = os.getenv("REDIS_URL", "redis://127.0.0.1:6379/1")

# Extract Redis components for more reliable connection
REDIS_HOST = os.getenv("REDIS_HOST", "127.0.0.1")
REDIS_PORT = os.getenv("REDIS_PORT", "6379")
REDIS_DB = os.getenv("REDIS_DB", "0")
REDIS_USERNAME = os.getenv("REDIS_USERNAME", "")
REDIS_PASSWORD = os.getenv("REDIS_PASSWORD", "")

# Always try to use Redis for caching, with fallback to LocMem if Redis is not available
try:
    import redis

    # Test Redis connection - try both URL and direct connection methods
    try:
        # First try with URL
        redis_client = redis.from_url(REDIS_URL, socket_timeout=2.0)
        redis_client.ping()
    except (redis.exceptions.ConnectionError, redis.exceptions.AuthenticationError):
        # If URL fails, try with direct connection parameters
        if REDIS_USERNAME:
            redis_client = redis.Redis(
                host=REDIS_HOST,
                port=int(REDIS_PORT),
                db=int(REDIS_DB),
                username=REDIS_USERNAME,
                password=REDIS_PASSWORD,
                socket_timeout=2.0
            )
        else:
            redis_client = redis.Redis(
                host=REDIS_HOST,
                port=int(REDIS_PORT),
                db=int(REDIS_DB),
                password=REDIS_PASSWORD if REDIS_PASSWORD else None,
                socket_timeout=2.0
            )
        redis_client.ping()  # Will raise an exception if connection fails

    # Redis is available, use it for caching
    # Determine the best connection string format based on authentication
    if REDIS_USERNAME:
        # Redis 6+ with ACL username/password authentication
        redis_location = f"redis://{REDIS_USERNAME}:{REDIS_PASSWORD}@{REDIS_HOST}:{REDIS_PORT}/{REDIS_DB}"
        redis_options = {
            "CLIENT_CLASS": "django_redis.client.DefaultClient",
            # "PARSER_CLASS": "redis.connection.HiredisParser",  # Commented out as it may not be available
            "CONNECTION_POOL_KWARGS": {"max_connections": 100},
            "SOCKET_CONNECT_TIMEOUT": 5,  # seconds
            "SOCKET_TIMEOUT": 5,  # seconds
            "IGNORE_EXCEPTIONS": True,  # Don't crash on Redis connection issues
        }
    elif REDIS_PASSWORD:
        # Redis with password authentication only
        redis_location = f"redis://:{REDIS_PASSWORD}@{REDIS_HOST}:{REDIS_PORT}/{REDIS_DB}"
        redis_options = {
            "CLIENT_CLASS": "django_redis.client.DefaultClient",
            # "PARSER_CLASS": "redis.connection.HiredisParser",  # Commented out as it may not be available
            "CONNECTION_POOL_KWARGS": {"max_connections": 100},
            "SOCKET_CONNECT_TIMEOUT": 5,  # seconds
            "SOCKET_TIMEOUT": 5,  # seconds
            "IGNORE_EXCEPTIONS": True,  # Don't crash on Redis connection issues
        }
    else:
        # Redis without authentication
        redis_location = f"redis://{REDIS_HOST}:{REDIS_PORT}/{REDIS_DB}"
        redis_options = {
            "CLIENT_CLASS": "django_redis.client.DefaultClient",
            # "PARSER_CLASS": "redis.connection.HiredisParser",  # Commented out as it may not be available
            "CONNECTION_POOL_KWARGS": {"max_connections": 100},
            "SOCKET_CONNECT_TIMEOUT": 5,  # seconds
            "SOCKET_TIMEOUT": 5,  # seconds
            "IGNORE_EXCEPTIONS": True,  # Don't crash on Redis connection issues
        }

    print(f"Using Redis cache with location: {redis_location}")

    CACHES = {
        "default": {
            "BACKEND": "django_redis.cache.RedisCache",
            "LOCATION": redis_location,
            "OPTIONS": redis_options,
            "KEY_PREFIX": "ecom",
            "TIMEOUT": 60 * 60 * 24,  # 1 day default timeout
        }
    }

    # Use Redis for session storage
    SESSION_ENGINE = "django.contrib.sessions.backends.cache"
    SESSION_CACHE_ALIAS = "default"

    print("Redis cache configured successfully")

except (ImportError, redis.exceptions.ConnectionError, redis.exceptions.TimeoutError) as e:
    # Redis is not available, fallback to LocMem
    print(f"Redis connection failed, falling back to LocMemCache: {str(e)}")
    CACHES = {
        "default": {
            "BACKEND": "django.core.cache.backends.locmem.LocMemCache",
            "LOCATION": "unique-snowflake",
        }
    }


AUTH_PASSWORD_VALIDATORS = [
    {
        "NAME": "django.contrib.auth.password_validation.UserAttributeSimilarityValidator",
    },
    {
        "NAME": "django.contrib.auth.password_validation.MinimumLengthValidator",
    },
    {
        "NAME": "django.contrib.auth.password_validation.CommonPasswordValidator",
    },
    {
        "NAME": "django.contrib.auth.password_validation.NumericPasswordValidator",
    },
]

LANGUAGE_CODE = "en-us"

USE_I18N = True
USE_L10N = True
USE_TZ = True

# Import MinIO settings
from .minio_settings import *

# Static files configuration
STATIC_URL = "/static/"
# Use environment variable for STATIC_ROOT, fallback to 'static' directory
STATIC_ROOT_ENV = os.getenv('STATIC_ROOT')
if STATIC_ROOT_ENV:
    # If STATIC_ROOT is relative, make it relative to BASE_DIR
    if not os.path.isabs(STATIC_ROOT_ENV):
        STATIC_ROOT = os.path.join(BASE_DIR, STATIC_ROOT_ENV)
    else:
        STATIC_ROOT = STATIC_ROOT_ENV
else:
    STATIC_ROOT = os.path.join(BASE_DIR, "static")

# Static files directories - ensure admin static files are included
# Avoid conflict with STATIC_ROOT in production
STATICFILES_DIRS = []
staticfiles_dir = os.path.join(BASE_DIR, "staticfiles")
if staticfiles_dir != STATIC_ROOT and os.path.exists(staticfiles_dir):
    STATICFILES_DIRS.append(staticfiles_dir)

# Static files finders - ensure admin static files are found
STATICFILES_FINDERS = [
    'django.contrib.staticfiles.finders.FileSystemFinder',
    'django.contrib.staticfiles.finders.AppDirectoriesFinder',
]

# WhiteNoise configuration for serving static files in production
STATICFILES_STORAGE = 'whitenoise.storage.CompressedManifestStaticFilesStorage'

# WhiteNoise settings
WHITENOISE_USE_FINDERS = True
WHITENOISE_AUTOREFRESH = DEBUG
WHITENOISE_MAX_AGE = 31536000 if not DEBUG else 0  # 1 year cache in production

# Media settings - these will be overridden by MinIO settings when USE_MINIO is True
MEDIA_URL = "/media/"
MEDIA_ROOT = os.path.join(BASE_DIR, "media")

DEFAULT_AUTO_FIELD = "django.db.models.BigAutoField"

# REST Framework settings with security enhancements
REST_FRAMEWORK = {
    "DEFAULT_AUTHENTICATION_CLASSES": (
        "rest_framework_simplejwt.authentication.JWTAuthentication",
    ),
    "DEFAULT_PERMISSION_CLASSES": [],
    "DEFAULT_PAGINATION_CLASS": "rest_framework.pagination.PageNumberPagination",
    "DEFAULT_FILTER_BACKENDS": (
        "django_filters.rest_framework.DjangoFilterBackend",
        # 'rest_framework.filters.CharFilter',
        "rest_framework.filters.OrderingFilter",
        "rest_framework.filters.SearchFilter",
    ),
    # ============================================================================
    # THROTTLING CONFIGURATION - Real-World E-Commerce Settings
    # ============================================================================
    #
    # Strategy: Amazon/Flipkart-inspired throttling with security focus
    #
    # Key Principles:
    # 1. User Experience First: Allow legitimate shopping behavior
    # 2. Security Protection: Prevent abuse and attacks
    # 3. Resource Management: Protect server resources
    # 4. Business Continuity: Don't block potential customers
    #
    # Rate Limits Explained:
    # - Anonymous users: Can browse freely but limited on actions
    # - Authenticated users: Higher limits for better shopping experience
    # - Security endpoints: Strict limits to prevent attacks
    # - Resource-intensive: Lower limits to prevent server overload
    #
    # Real-world benchmarks:
    # - Amazon: ~20-30 requests/minute for browsing
    # - Flipkart: ~15-25 requests/minute for shopping
    # - Our limits: Balanced for Indian e-commerce patterns
    # ============================================================================

    "DEFAULT_THROTTLE_CLASSES": [
        "rest_framework.throttling.AnonRateThrottle",
        "rest_framework.throttling.UserRateThrottle",
        "rest_framework.throttling.ScopedRateThrottle",
    ],
    "DEFAULT_THROTTLE_RATES": {
        # ============================================================================
        # VPS-OPTIMIZED THROTTLING (3 Core, 8GB RAM)
        # Capacity Score: 73/100 (MEDIUM_CAPACITY)
        # ============================================================================

        # Anonymous users (browsing, searching, viewing products)
        "anon": "700/hour",  # 11.7 requests per minute - optimized for VPS capacity

        # Authenticated users (higher limits for logged-in customers)
        "user": "1400/hour",  # 23.3 requests per minute - balanced for VPS

        # Authentication endpoints (login, register, social login)
        "login": "7/minute",  # Strong brute force protection for VPS

        # Payment processing (critical security endpoint)
        "payment": "14/hour",  # Conservative payment limits for VPS

        # Admin operations (reasonable limits for VPS)
        "admin": "350/hour",  # Scaled down for VPS capacity

        # Password reset (security-sensitive)
        "password_reset": "5/hour",  # Keep security-focused limit

        # Product search and catalog browsing
        "search": "210/hour",  # 3.5 searches per minute - VPS optimized

        # Cart operations (add, remove, update)
        "cart": "84/hour",  # 1.4 per minute - VPS appropriate

        # Order operations (place order, view orders)
        "orders": "42/hour",  # 0.7 per minute - conservative for VPS

        # Wishlist operations
        "wishlist": "70/hour",  # Scaled for VPS capacity

        # Contact/support forms
        "contact": "7/hour",  # Conservative to prevent VPS overload

        # File uploads (profile images, etc.)
        "upload": "21/hour",  # Reduced for VPS I/O limitations

        # API data export (CSV downloads, etc.)
        "export": "7/hour",  # Very conservative for VPS resources

        # Social media integrations
        "social": "35/hour",  # Scaled down for VPS
    },

    # Custom throttle cache settings
    "DEFAULT_THROTTLE_CACHE": "default",

    # Throttle key function for better IP detection
    "NUM_PROXIES": int(os.getenv("NUM_PROXIES", "0")),  # Set if behind load balancer
    # Security: Add renderer classes control
    "DEFAULT_RENDERER_CLASSES": [
        "rest_framework.renderers.JSONRenderer",
    ] + (["rest_framework.renderers.BrowsableAPIRenderer"] if DEBUG else []),
}

# Environment-based throttling adjustments for VPS
if DEBUG:
    # Development: Higher limits for testing (but still VPS-appropriate)
    REST_FRAMEWORK["DEFAULT_THROTTLE_RATES"].update({
        "anon": "1400/hour",    # 2x production for VPS testing
        "user": "2800/hour",    # 2x production for VPS testing
        "login": "14/minute",   # 2x production for testing
        "payment": "24/hour",   # Conservative increase for testing
        "search": "420/hour",   # 2x production for search testing
        "cart": "168/hour",     # 2x production for cart testing
        "upload": "42/hour",    # 2x production for upload testing
    })

# Production throttling enhancements
if not DEBUG:
    # Add burst protection for production
    REST_FRAMEWORK["DEFAULT_THROTTLE_RATES"].update({
        # Burst protection: Short-term limits
        "anon_burst": "100/minute",  # Prevents rapid-fire requests
        "user_burst": "200/minute",  # Higher burst for authenticated users
        "login_burst": "5/minute",   # Very strict burst protection for login
        "api_burst": "300/minute",   # API endpoint burst protection
    })

# Custom throttle settings for specific use cases
CUSTOM_THROTTLE_SETTINGS = {
    # E-commerce specific patterns
    "CHECKOUT_THROTTLE": "10/hour",      # Checkout process protection
    "REVIEW_THROTTLE": "20/hour",        # Product review submissions
    "SUPPORT_THROTTLE": "15/hour",       # Customer support requests
    "NEWSLETTER_THROTTLE": "5/hour",     # Newsletter subscriptions

    # Security-sensitive operations
    "PROFILE_UPDATE_THROTTLE": "10/hour",    # Profile changes
    "ADDRESS_THROTTLE": "20/hour",           # Address management
    "PAYMENT_METHOD_THROTTLE": "10/hour",   # Payment method changes

    # Resource-intensive operations
    "BULK_EXPORT_THROTTLE": "3/hour",       # Large data exports
    "IMAGE_UPLOAD_THROTTLE": "20/hour",     # Image uploads
    "SEARCH_HEAVY_THROTTLE": "100/hour",    # Complex search queries
}

# JWT settings with enhanced security
SIMPLE_JWT = {
    "ACCESS_TOKEN_LIFETIME": timedelta(minutes=15),  # Reduced from 60 to 15 minutes
    "REFRESH_TOKEN_LIFETIME": timedelta(days=7),  # Reduced from 30 to 7 days
    "ROTATE_REFRESH_TOKENS": True,  # Enable token rotation for security
    "BLACKLIST_AFTER_ROTATION": True,
    "UPDATE_LAST_LOGIN": True,
    "ALGORITHM": "HS256",
    "SIGNING_KEY": SECRET_KEY,  # Use Django's SECRET_KEY for consistency
    "VERIFYING_KEY": None,
    "AUDIENCE": None,
    # "ISSUER": "trio.net.in",  # Temporarily disabled for debugging
    "AUTH_HEADER_TYPES": ("Bearer",),
    "AUTH_HEADER_NAME": "HTTP_AUTHORIZATION",
    "USER_ID_FIELD": "id",
    "USER_ID_CLAIM": "user_id",
    "AUTH_TOKEN_CLASSES": ("rest_framework_simplejwt.tokens.AccessToken",),
    "TOKEN_TYPE_CLAIM": "token_type",
}

# CORS settings
CORS_ALLOWED_ORIGINS = [
    "http://localhost:3000",
    "http://localhost:8080",
    "http://localhost:3001",
    "https://e-com-2024-6gg4jrnpj-wesolvestechgmailcoms-projects.vercel.app",
    "https://e-com-2024.vercel.app",
    "https://trio.net.in",
    "https://dev.trio.net.in",
]

# Additional CORS settings to fix header issues
CORS_ALLOW_HEADERS = [
    'accept',
    'accept-encoding',
    'authorization',
    'content-type',
    'dnt',
    'origin',
    'user-agent',
    'x-csrftoken',
    'x-requested-with',
    'cache-control',
    'pragma',
]

CORS_ALLOW_CREDENTIALS = True

# Allow all HTTP methods for CORS
CORS_ALLOW_METHODS = [
    'DELETE',
    'GET',
    'OPTIONS',
    'PATCH',
    'POST',
    'PUT',
]

# Expose headers that the frontend might need
CORS_EXPOSE_HEADERS = [
    'content-type',
    'x-csrftoken',
]

# CORS preflight cache time (in seconds)
CORS_PREFLIGHT_MAX_AGE = 86400  # 24 hours

# Allow CORS for all origins in development/debugging (temporary)
# CORS_ALLOW_ALL_ORIGINS = DEBUG  # Uncomment for debugging only

# Custom user model

# Stripe settings
STRIPE_PUBLIC_KEY = "your-stripe-public-key"
STRIPE_SECRET_KEY = "your-stripe-secret-key"

# PhonePe settings
PHONEPE_CLIENT_ID = os.getenv("PHONEPE_CLIENT_ID")
PHONEPE_CLIENT_SECRET = os.getenv("PHONEPE_CLIENT_SECRET")
PHONEPE_CALLBACK_URL = os.getenv("PHONEPE_CALLBACK_URL")
PHONEPE_ENVIRONMENT = os.getenv("PHONEPE_ENVIRONMENT", "UAT")
FRONTEND_URL = os.getenv("FRONTEND_URL", "http://localhost:3000")

# Google OAuth settings
GOOGLE_CLIENT_ID = os.getenv("GOOGLE_CLIENT_ID")
GOOGLE_CLIENT_SECRET = os.getenv("GOOGLE_CLIENT_SECRET")

TIME_ZONE = "Asia/Kolkata"

CSRF_TRUSTED_ORIGINS = [
    "https://dev.trio.net.in",
    "https://trio.net.in",
    "https://e-com-2024.vercel.app",
    "https://e-com-2024-6gg4jrnpj-wesolvestechgmailcoms-projects.vercel.app",
    "http://localhost:3000",
    "http://localhost:8080",
    "http://localhost:3001",
]

# Add custom CSRF_ORIGINS if provided
if os.getenv("CSRF_ORIGINS"):
    CSRF_TRUSTED_ORIGINS.extend(os.getenv("CSRF_ORIGINS").split(","))

# Email settings
EMAIL_BACKEND = 'django.core.mail.backends.smtp.EmailBackend'
EMAIL_HOST = os.getenv('EMAIL_HOST', 'smtp.hostinger.com')
EMAIL_PORT = int(os.getenv('EMAIL_PORT', 465))
# For Hostinger with port 465, we need to use SSL, not TLS
EMAIL_USE_SSL = os.getenv('EMAIL_SSL', 'True').lower() == 'true'
EMAIL_USE_TLS = os.getenv('EMAIL_USE_TLS', 'False').lower() == 'true'
EMAIL_HOST_USER = os.getenv('EMAIL_HOST_USER', '<EMAIL>')
EMAIL_HOST_PASSWORD = os.getenv('EMAIL_HOST_PASSWORD', '')
DEFAULT_FROM_EMAIL = os.getenv('DEFAULT_FROM_EMAIL', '<EMAIL>')

# Email timeout settings to prevent long hanging connections
EMAIL_TIMEOUT = 30  # 30 seconds timeout

# Security Email Notification Settings
SECURITY_EMAIL_NOTIFICATIONS = {
    'ENABLED': os.getenv('SECURITY_EMAIL_ENABLED', 'True').lower() == 'true',
    'RECIPIENTS': [
        os.getenv('DEFAULT_FROM_EMAIL', '<EMAIL>'),
        '<EMAIL>'
    ],
    'THROTTLE_MINUTES': int(os.getenv('SECURITY_EMAIL_THROTTLE', '30')),  # Minimum time between similar alerts
    'HIGH_PRIORITY_EVENTS': [
        'IP_BLOCKED',
        'MULTIPLE_FAILED_LOGINS',
        'SUSPICIOUS_ACTIVITY',
        'ACCOUNT_LOCKED',
        'DATA_BREACH_DETECTED'
    ],
    'MEDIUM_PRIORITY_EVENTS': [
        'UNUSUAL_LOGIN_PATTERN',
        'NEW_DEVICE_LOGIN',
        'ADMIN_ACTION',
        'BULK_DATA_ACCESS'
    ]
}

# Security Logging Configuration
# Check if file logging should be disabled (e.g., during build)
DISABLE_FILE_LOGGING = os.getenv('DISABLE_FILE_LOGGING', 'false').lower() == 'true'

# Create logs directory if it doesn't exist and file logging is enabled
if not DISABLE_FILE_LOGGING:
    logs_dir = os.path.join(BASE_DIR, 'logs')
    os.makedirs(logs_dir, exist_ok=True)

LOGGING = {
    'version': 1,
    'disable_existing_loggers': False,
    'formatters': {
        'verbose': {
            'format': '[{levelname}] {asctime} {name} {process:d} {thread:d} {message}',
            'style': '{',
        },
        'security': {
            'format': '[SECURITY] {asctime} {levelname} {message} - IP: {ip} - User: {user}',
            'style': '{',
        },
        'security_console': {
            'format': '[SECURITY] {asctime} {levelname} {message}',
            'style': '{',
        },
    },
    'handlers': {
        'console': {
            'level': 'DEBUG' if DEBUG else 'INFO',
            'class': 'logging.StreamHandler',
            'formatter': 'verbose',
        },
        'security_console': {
            'level': 'WARNING',
            'class': 'logging.StreamHandler',
            'formatter': 'security_console',
        },
    },
    'loggers': {
        'django': {
            'handlers': ['console'],
            'level': 'INFO',
            'propagate': True,
        },
        'security': {
            'handlers': ['security_console'],
            'level': 'WARNING',
            'propagate': False,
        },
        'django.security': {
            'handlers': ['console'],
            'level': 'WARNING',
            'propagate': False,
        },
    },
}

# Add file handlers only if file logging is not disabled
if not DISABLE_FILE_LOGGING:
    LOGGING['handlers']['file'] = {
        'level': 'INFO',
        'class': 'logging.FileHandler',
        'filename': os.path.join(BASE_DIR, 'logs', 'django.log'),
        'formatter': 'verbose',
    }
    LOGGING['handlers']['security_file'] = {
        'level': 'WARNING',
        'class': 'logging.FileHandler',
        'filename': os.path.join(BASE_DIR, 'logs', 'security.log'),
        'formatter': 'security',
    }

    # Update loggers to include file handlers
    LOGGING['loggers']['django']['handlers'] = ['file', 'console']
    LOGGING['loggers']['security']['handlers'] = ['security_file', 'security_console']
    LOGGING['loggers']['django.security']['handlers'] = ['security_file']

# ============================================================================
# RAPIDSHYP SHIPPING INTEGRATION SETTINGS
# ============================================================================

# Rapidshyp API Configuration
RAPIDSHYP_ENABLED = os.getenv('RAPIDSHYP_ENABLED', 'False').lower() == 'true'
RAPIDSHYP_API_KEY = os.getenv('RAPIDSHYP_API_KEY', '')
RAPIDSHYP_BASE_URL = os.getenv('RAPIDSHYP_BASE_URL', 'https://api.rapidshyp.com/rapidshyp/apis/v1')
RAPIDSHYP_STORE_NAME = os.getenv('RAPIDSHYP_STORE_NAME', 'DEFAULT')
RAPIDSHYP_DEFAULT_PICKUP_PINCODE = os.getenv('RAPIDSHYP_DEFAULT_PICKUP_PINCODE', '110001')
RAPIDSHYP_SANDBOX_MODE = os.getenv('RAPIDSHYP_SANDBOX_MODE', 'True').lower() == 'true'

# Rapidshyp Feature Flags
RAPIDSHYP_FEATURES = {
    'ENABLE_RATE_CACHING': True,
    'ENABLE_TRACKING_SYNC': True,
    'ENABLE_AUTO_PICKUP_SCHEDULING': True,
    'ENABLE_LABEL_GENERATION': True,
    'ENABLE_WEBHOOK_PROCESSING': True,
    'ENABLE_FALLBACK_SHIPPING': True,  # Always fallback to existing methods
}

# Rapidshyp Cache Settings
RAPIDSHYP_CACHE_SETTINGS = {
    'SHIPPING_RATES_TIMEOUT': 300,  # 5 minutes
    'TRACKING_DATA_TIMEOUT': 60,   # 1 minute
    'SERVICEABILITY_TIMEOUT': 3600, # 1 hour
}

# Rapidshyp API Settings
RAPIDSHYP_API_SETTINGS = {
    'TIMEOUT': 30,  # seconds
    'RETRY_ATTEMPTS': 3,
    'RETRY_DELAY': 1,  # seconds
    'MAX_RETRY_DELAY': 10,  # seconds
}

# Rapidshyp Webhook Settings
RAPIDSHYP_WEBHOOK_SECRET = os.getenv('RAPIDSHYP_WEBHOOK_SECRET', '')
RAPIDSHYP_WEBHOOK_SETTINGS = {
    'ALLOWED_IPS': [
        # Add Rapidshyp webhook IPs here when available
    ],
    'SIGNATURE_HEADER': 'X-Rapidshyp-Signature',
    'TIMESTAMP_HEADER': 'X-Rapidshyp-Timestamp',
    'MAX_TIMESTAMP_DIFF': 300,  # 5 minutes
}

# Default Pickup Location Configuration
RAPIDSHYP_DEFAULT_PICKUP = {
    'contactName': os.getenv('RAPIDSHYP_CONTACT_NAME', 'Triumph Enterprises'),
    'pickupName': os.getenv('RAPIDSHYP_PICKUP_NAME', 'Main Warehouse'),
    'pickupEmail': os.getenv('RAPIDSHYP_PICKUP_EMAIL', '<EMAIL>'),
    'pickupPhone': os.getenv('RAPIDSHYP_PICKUP_PHONE', '9848486452'),
    'pickupAddress1': os.getenv('RAPIDSHYP_PICKUP_ADDRESS1', 'D.No. 5-5-190/65A, Ehata Nooruddin Shah Qadri'),
    'pickupAddress2': os.getenv('RAPIDSHYP_PICKUP_ADDRESS2', 'Patel Nagar, Darussalam'),
    'pinCode': RAPIDSHYP_DEFAULT_PICKUP_PINCODE
}

print(f"Rapidshyp integration {'enabled' if RAPIDSHYP_ENABLED else 'disabled'}")
if RAPIDSHYP_ENABLED and not RAPIDSHYP_API_KEY:
    print("WARNING: Rapidshyp is enabled but no API key is configured")
