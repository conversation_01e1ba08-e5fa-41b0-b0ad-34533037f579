/**
 * Secure storage utilities for handling sensitive data in the browser
 * Provides encryption for localStorage/sessionStorage data
 */

import CryptoJS from 'crypto-js';

// Use a combination of environment variable and browser fingerprint for encryption
const getEncryptionKey = (): string => {
  // In production, this should come from environment variables
  const baseKey = process.env.NEXT_PUBLIC_ENCRYPTION_KEY || 'default-key-change-in-production';
  
  // Add browser fingerprint for additional security
  const browserFingerprint = typeof window !== 'undefined' 
    ? `${navigator.userAgent}-${window.screen.width}-${window.screen.height}`
    : 'server-side';
  
  return CryptoJS.SHA256(baseKey + browserFingerprint).toString();
};

export interface SecureStorageOptions {
  useSessionStorage?: boolean;
  expirationMinutes?: number;
}

export class SecureStorage {
  private encryptionKey: string;
  private storage: Storage | null;

  constructor(options: SecureStorageOptions = {}) {
    this.encryptionKey = getEncryptionKey();
    
    if (typeof window !== 'undefined') {
      this.storage = options.useSessionStorage ? sessionStorage : localStorage;
    } else {
      this.storage = null;
    }
  }

  /**
   * Encrypt and store data
   */
  setItem(key: string, value: any, expirationMinutes?: number): boolean {
    if (!this.storage) return false;

    try {
      const dataToStore = {
        value,
        timestamp: Date.now(),
        expiration: expirationMinutes ? Date.now() + (expirationMinutes * 60 * 1000) : null
      };

      const encrypted = CryptoJS.AES.encrypt(
        JSON.stringify(dataToStore), 
        this.encryptionKey
      ).toString();

      this.storage.setItem(key, encrypted);
      return true;
    } catch (error) {
      console.error('SecureStorage: Failed to store item', error);
      return false;
    }
  }

  /**
   * Retrieve and decrypt data
   */
  getItem<T = any>(key: string): T | null {
    if (!this.storage) return null;

    try {
      const encrypted = this.storage.getItem(key);
      if (!encrypted) return null;

      // Check if the data is already in plain text (for backward compatibility)
      try {
        const plainData = JSON.parse(encrypted);
        if (plainData && typeof plainData === 'object' && plainData.value !== undefined) {
          // This looks like unencrypted data, migrate it
          this.setItem(key, plainData.value, plainData.expiration ?
            Math.max(0, Math.floor((plainData.expiration - Date.now()) / (60 * 1000))) : undefined);
          return plainData.value;
        }
      } catch {
        // Not plain JSON, continue with decryption
      }

      const decrypted = CryptoJS.AES.decrypt(encrypted, this.encryptionKey);
      const decryptedString = decrypted.toString(CryptoJS.enc.Utf8);

      if (!decryptedString) {
        // Decryption failed, remove invalid item
        console.warn(`SecureStorage: Failed to decrypt item '${key}', removing corrupted data`);
        this.removeItem(key);
        return null;
      }

      const data = JSON.parse(decryptedString);

      // Check expiration
      if (data.expiration && Date.now() > data.expiration) {
        this.removeItem(key);
        return null;
      }

      return data.value;
    } catch (error) {
      console.error(`SecureStorage: Failed to retrieve item '${key}'`, error);
      // Remove corrupted item to prevent future errors
      try {
        this.removeItem(key);
      } catch (removeError) {
        console.error(`SecureStorage: Failed to remove corrupted item '${key}'`, removeError);
      }
      return null;
    }
  }

  /**
   * Remove item from storage
   */
  removeItem(key: string): void {
    if (!this.storage) return;
    this.storage.removeItem(key);
  }

  /**
   * Clear all items from storage
   */
  clear(): void {
    if (!this.storage) return;
    this.storage.clear();
  }

  /**
   * Check if item exists and is not expired
   */
  hasItem(key: string): boolean {
    return this.getItem(key) !== null;
  }

  /**
   * Get all keys in storage
   */
  getAllKeys(): string[] {
    if (!this.storage) return [];
    
    const keys: string[] = [];
    for (let i = 0; i < this.storage.length; i++) {
      const key = this.storage.key(i);
      if (key) keys.push(key);
    }
    return keys;
  }

  /**
   * Clean up expired items
   */
  cleanupExpired(): number {
    if (!this.storage) return 0;

    const keys = this.getAllKeys();
    let cleanedCount = 0;

    keys.forEach(key => {
      try {
        const encrypted = this.storage!.getItem(key);
        if (!encrypted) return;

        const decrypted = CryptoJS.AES.decrypt(encrypted, this.encryptionKey);
        const decryptedString = decrypted.toString(CryptoJS.enc.Utf8);
        
        if (!decryptedString) {
          this.removeItem(key);
          cleanedCount++;
          return;
        }

        const data = JSON.parse(decryptedString);
        if (data.expiration && Date.now() > data.expiration) {
          this.removeItem(key);
          cleanedCount++;
        }
      } catch (error) {
        // Remove corrupted items
        this.removeItem(key);
        cleanedCount++;
      }
    });

    return cleanedCount;
  }
}

// Pre-configured instances
export const secureLocalStorage = new SecureStorage({ useSessionStorage: false });
export const secureSessionStorage = new SecureStorage({ useSessionStorage: true });

// Token management utilities
export class TokenManager {
  private storage: SecureStorage;

  constructor(useSessionStorage = true) {
    this.storage = new SecureStorage({ useSessionStorage });
  }

  /**
   * Store authentication tokens
   */
  setTokens(accessToken: string, refreshToken: string): void {
    // Access token expires in 15 minutes (as per backend config)
    this.storage.setItem('access_token', accessToken, 15);
    // Refresh token expires in 7 days (as per backend config)
    this.storage.setItem('refresh_token', refreshToken, 7 * 24 * 60);
  }

  /**
   * Get access token
   */
  getAccessToken(): string | null {
    return this.storage.getItem('access_token');
  }

  /**
   * Get refresh token
   */
  getRefreshToken(): string | null {
    return this.storage.getItem('refresh_token');
  }

  /**
   * Remove all tokens
   */
  clearTokens(): void {
    this.storage.removeItem('access_token');
    this.storage.removeItem('refresh_token');
  }

  /**
   * Check if user is authenticated
   */
  isAuthenticated(): boolean {
    return this.getAccessToken() !== null;
  }

  /**
   * Check if refresh token is available
   */
  canRefresh(): boolean {
    return this.getRefreshToken() !== null;
  }

  /**
   * Store user data
   */
  setUserData(userData: any): void {
    this.storage.setItem('user_data', userData, 24 * 60); // 24 hours
  }

  /**
   * Get user data
   */
  getUserData(): any {
    return this.storage.getItem('user_data');
  }

  /**
   * Clear user data
   */
  clearUserData(): void {
    this.storage.removeItem('user_data');
  }

  /**
   * Clear all authentication data
   */
  logout(): void {
    this.clearTokens();
    this.clearUserData();
    // Clean up any other auth-related data
    this.storage.removeItem('cart_data');
    this.storage.removeItem('wishlist_data');
  }
}

// Default token manager instance
export const tokenManager = new TokenManager(true); // Use session storage

// Utility functions for common operations
export const authUtils = {
  /**
   * Store authentication response
   */
  storeAuthResponse: (response: { accessToken: string; refreshToken: string; [key: string]: any }) => {
    tokenManager.setTokens(response.accessToken, response.refreshToken);
    
    // Store user data (excluding tokens)
    const { accessToken, refreshToken, ...userData } = response;
    tokenManager.setUserData(userData);
  },

  /**
   * Get authorization header
   */
  getAuthHeader: (): { Authorization: string } | {} => {
    const token = tokenManager.getAccessToken();
    return token ? { Authorization: `Bearer ${token}` } : {};
  },

  /**
   * Check if user is authenticated
   */
  isAuthenticated: (): boolean => {
    return tokenManager.isAuthenticated();
  },

  /**
   * Logout user
   */
  logout: (): void => {
    tokenManager.logout();
  },

  /**
   * Get current user data
   */
  getCurrentUser: () => {
    return tokenManager.getUserData();
  }
};

// Auto cleanup expired items on page load
if (typeof window !== 'undefined') {
  // Clean up expired items when the page loads
  setTimeout(() => {
    secureLocalStorage.cleanupExpired();
    secureSessionStorage.cleanupExpired();
  }, 1000);

  // Set up periodic cleanup (every 5 minutes)
  setInterval(() => {
    secureLocalStorage.cleanupExpired();
    secureSessionStorage.cleanupExpired();
  }, 5 * 60 * 1000);
}
