# from rest_framework import viewsets, permissions, status
# from rest_framework.decorators import action
# from rest_framework.response import Response
# from .models import Category, Product, ProductImage, ProductVariant, Review
# from .serializers import (
#     CategorySerializer, CategoryDetailSerializer,
#     ProductSerializer, ProductDetailSerializer,
#     ProductImageSerializer, ProductVariantSerializer,
#     ReviewSerializer, ReviewCreateSerializer
# )

# class CategoryViewSet(viewsets.ModelViewSet):
#     queryset = Category.objects.all()
#     serializer_class = CategorySerializer
#     permission_classes = [permissions.IsAuthenticatedOrReadOnly]
#     lookup_field = 'slug'

#     def get_serializer_class(self):
#         if self.action == 'retrieve':
#             return CategoryDetailSerializer
#         return CategorySerializer

#     @action(detail=False, methods=['get'])
#     def root_categories(self, request):
#         root_categories = Category.objects.filter(parent=None)
#         serializer = CategoryDetailSerializer(root_categories, many=True)
#         return Response(serializer.data)

# class ProductViewSet(viewsets.ModelViewSet):
#     queryset = Product.objects.filter(is_active=True)
#     serializer_class = ProductSerializer
#     permission_classes = [permissions.IsAuthenticatedOrReadOnly]
#     filterset_fields = ['category', 'price', 'stock']
#     search_fields = ['name', 'description']
#     ordering_fields = ['created_at', 'price', 'name']
#     lookup_field = 'slug'

#     def get_queryset(self):
#         queryset = super().get_queryset()
#         if self.request.user.is_staff:
#             return Product.objects.all()
#         return queryset

#     def get_serializer_class(self):
#         if self.action == 'retrieve':
#             return ProductDetailSerializer
#         return ProductSerializer

#     @action(detail=True, methods=['post'], permission_classes=[permissions.IsAdminUser])
#     def add_variant(self, request, slug=None):
#         product = self.get_object()
#         serializer = ProductVariantSerializer(data=request.data)

#         if serializer.is_valid():
#             serializer.save(product=product)
#             return Response(serializer.data, status=status.HTTP_201_CREATED)
#         return Response(serializer.errors, status=status.HTTP_400_BAD_REQUEST)

#     @action(detail=True, methods=['post'], permission_classes=[permissions.IsAdminUser])
#     def add_image(self, request, slug=None):
#         product = self.get_object()
#         serializer = ProductImageSerializer(data=request.data)

#         if serializer.is_valid():
#             serializer.save(product=product)
#             return Response(serializer.data, status=status.HTTP_201_CREATED)
#         return Response(serializer.errors, status=status.HTTP_400_BAD_REQUEST)

#     @action(detail=True, methods=['post'], permission_classes=[permissions.IsAuthenticated])
#     def add_review(self, request, slug=None):
#         product = self.get_object()
#         serializer = ReviewCreateSerializer(
#             data={'product': product.id, **request.data},
#             context={'request': request}
#         )

#         if serializer.is_valid():
#             serializer.save()
#             return Response(serializer.data, status=status.HTTP_201_CREATED)
#         return Response(serializer.errors, status=status.HTTP_400_BAD_REQUEST)

#     @action(detail=False, methods=['get'])
#     def featured(self, request):
#         # You can customize this to return featured products based on your criteria
#         featured_products = self.get_queryset().order_by('-created_at')[:6]
#         serializer = self.get_serializer(featured_products, many=True)
#         return Response(serializer.data)

# class ProductVariantViewSet(viewsets.ModelViewSet):
#     serializer_class = ProductVariantSerializer
#     permission_classes = [permissions.IsAdminUser]

#     def get_queryset(self):
#         return ProductVariant.objects.filter(product__slug=self.kwargs['product_slug'])

# class ProductImageViewSet(viewsets.ModelViewSet):
#     serializer_class = ProductImageSerializer
#     permission_classes = [permissions.IsAdminUser]

#     def get_queryset(self):
#         return ProductImage.objects.filter(product__slug=self.kwargs['product_slug'])

# class ReviewViewSet(viewsets.ModelViewSet):
#     serializer_class = ReviewSerializer
#     permission_classes = [permissions.IsAuthenticatedOrReadOnly]

#     def get_queryset(self):
#         return Review.objects.filter(product__slug=self.kwargs['product_slug'])

#     def get_permissions(self):
#         if self.action in ['update', 'partial_update', 'destroy']:
#             return [permissions.IsAuthenticated()]
#         return super().get_permissions()

#     def perform_create(self, serializer):
#         product = Product.objects.get(slug=self.kwargs['product_slug'])
#         serializer.save(user=self.request.user, product=product)

from rest_framework import generics, permissions, status, views
from rest_framework.decorators import api_view, permission_classes
from rest_framework.response import Response
# Database models imports moved to backend.db_utils for replica support
from .models import Category, Product, ProductImage, ProductVariant, Review, Brand, SubCategorie


def get_optimized_product_queryset(base_queryset=None, use_replica=True):
    """
    Returns an optimized Product queryset with proper select_related,
    prefetch_related, and annotations to avoid N+1 queries.

    Args:
        base_queryset: Base queryset to optimize (defaults to Product.objects.all())
        use_replica: Whether to use read replica for this query (default: True)

    Returns:
        Optimized queryset with replica support
    """
    # Import here to avoid circular imports
    from backend.db_utils import ReplicaAwareQuerySet

    # Use the new replica-aware utility while maintaining backward compatibility
    return ReplicaAwareQuerySet.get_optimized_product_queryset(
        base_queryset=base_queryset,
        use_replica=use_replica
    )
from .serializers import (
    CategoryNameOnlySerializer,
    OptimizedCategoryNameOnlySerializer,
    CategoryDetailSerializer,
    ProductSerializer,
    ProductListSerializer,
    ProductDetailSerializer,
    ProductImageSerializer,
    ProductVariantSerializer,
    ReviewSerializer,
    ReviewCreateSerializer,
    BrandNameOnlySerializer,
    BrandSerializer,
    AdminCategorySerializer,
    CategorySerializer
)
from products.pagination import ProductPagination,CategoryProductsPagination
from django_filters import rest_framework as filters
from django.utils.decorators import method_decorator
from django.views.decorators.cache import cache_page


class ProductFilter(filters.FilterSet):
    category = filters.CharFilter(
        method="filter_category"
    )  # Use a CharFilter to handle the custom list format
    brand = filters.CharFilter(method="filter_brand")
    price = filters.CharFilter(method="filter_price")

    class Meta:
        model = Product
        fields = [
            "category",
            "brand",
            "price",
            "stock",
        ]  # Other fields you want to filter

    def filter_category(self, queryset, name, value):
        """Custom filter to handle list input for category."""
        if value:
            # Handle both single values and list representations
            try:
                # First try to convert to int (single value)
                category_id = int(value)
                queryset = queryset.filter(category__id=category_id)
            except ValueError:
                # If that fails, try to eval as a list
                try:
                    category_ids = eval(value)  # Use eval carefully! Consider using json.loads for safety.
                    queryset = queryset.filter(category__id__in=category_ids)
                except (SyntaxError, NameError, TypeError):
                    pass  # Handle the case where the input is invalid
        return queryset

    def filter_brand(self, queryset, name, value):
        """Custom filter to handle list input for brand."""
        if value:
            # Handle both single values and list representations
            try:
                # First try to convert to int (single value)
                brand_id = int(value)
                queryset = queryset.filter(brand__id=brand_id)
            except ValueError:
                # If that fails, try to eval as a list
                try:
                    brand_ids = eval(value)  # Use eval carefully! Consider using json.loads for safety.
                    queryset = queryset.filter(brand__id__in=brand_ids)
                except (SyntaxError, NameError, TypeError):
                    pass  # Handle the case where the input is invalid
        return queryset

    def filter_price(self, queryset, name, value):
        """Custom filter to handle price range input."""
        if value:
            # Expecting value in the format 'min_price-max_price'
            try:
                min_price, max_price = map(float, value.split("-"))  # Convert to float
                queryset = queryset.filter(price__gte=min_price, price__lte=max_price)
            except ValueError:
                # Handle the case where the input is invalid (non-numeric)
                pass  # You could also raise an exception or log an error if needed
        return queryset



# Category Views
class CategoryListView(generics.ListAPIView):
    serializer_class = OptimizedCategoryNameOnlySerializer
    permission_classes = [permissions.AllowAny]

    def get_queryset(self):
        from django.db.models import Count, Q, F
        from django.db import models

        # Annotate categories with product counts
        queryset = Category.objects.filter(is_active=True).annotate(
            # Count products directly in this category
            product_count=Count(
                'products',
                filter=Q(products__is_active=True),
                distinct=True
            ),
            # Count products in subcategories
            subcategory_product_count=Count(
                'subcategories__products',
                filter=Q(
                    subcategories__is_active=True,
                    subcategories__products__is_active=True
                ),
                distinct=True
            )
        ).annotate(
            # Total products in category and its subcategories
            total_product_count=F('product_count') + F('subcategory_product_count')
        ).prefetch_related(
            models.Prefetch(
                'subcategories',
                queryset=SubCategorie.objects.filter(is_active=True).annotate(
                    product_count=Count(
                        'products',
                        filter=Q(products__is_active=True),
                        distinct=True
                    )
                ).filter(product_count__gt=0)  # Only subcategories with products
            )
        ).filter(
            # Only include categories that have products either directly or in subcategories
            Q(product_count__gt=0) | Q(subcategory_product_count__gt=0)
        ).order_by('name')

        return queryset

    @method_decorator(cache_page(60 * 15))
    def get(self, request, *args, **kwargs):
        return self.list(request, *args, **kwargs)


class BrandListView(generics.ListAPIView):
    serializer_class = BrandSerializer
    permission_classes = [permissions.AllowAny]

    def get_queryset(self):
        return Brand.objects.filter(is_active=True)


class CategoryDetailView(generics.RetrieveAPIView):
    serializer_class = CategoryDetailSerializer
    permission_classes = [permissions.IsAuthenticatedOrReadOnly]
    lookup_field = "slug"

    def get_queryset(self):
        return Category.objects.all()


@api_view(["GET"])
def root_categories(request):
    try:
        root_categories = Category.objects.filter(parent=None)
        serializer = CategoryDetailSerializer(root_categories, many=True, context={'request': request})
        return Response(serializer.data)
    except:
        return Response({"error": "Could not retrieve root categories"}, status=status.HTTP_500_INTERNAL_SERVER_ERROR)


# Product Views
class ProductListView(generics.ListAPIView):
    serializer_class = ProductListSerializer
    permission_classes = [permissions.AllowAny]
    # filterset_fields = ['category', 'price', 'stock']
    filterset_class = ProductFilter
    search_fields = ["name"]
    ordering_fields = ["created_at", "price", "name"]
    pagination_class = ProductPagination

    def get_queryset(self):
        if self.request.user.is_staff:
            base_queryset = Product.objects.all().order_by('-created_at')
        else:
            base_queryset = Product.objects.filter(is_active=True).order_by('-created_at')

        # Use optimized queryset utility function with replica support
        # Read operations can safely use replicas since this is a list view
        return get_optimized_product_queryset(base_queryset, use_replica=True)

    def get_serializer(self, *args, **kwargs):
        is_admin = self.request.query_params.get("is_admin", False)
        if not is_admin:
            kwargs["fields"] = [
                "id",
                "name",
                "price",
                "mrp",
                "base_price",
                "gst_amount",
                "average_rating",
                "images",
                'image',
                "category",
                "slug",
                "brand",
            ]
        return super().get_serializer(*args, **kwargs)


class ProductDetailView(generics.RetrieveAPIView):
    serializer_class = ProductDetailSerializer
    permission_classes = [permissions.IsAuthenticatedOrReadOnly]
    lookup_field = "slug"

    def get_queryset(self):
        if self.request.user.is_staff:
            base_queryset = Product.objects.all()
        else:
            base_queryset = Product.objects.filter(is_active=True)

        # Use optimized queryset with additional subcategory select_related
        return get_optimized_product_queryset(base_queryset).select_related('subcategory')


@api_view(["POST"])
def add_product_variant(request, slug):
    product = generics.get_object_or_404(Product, slug=slug)
    if not request.user.is_staff:
        return Response(status=status.HTTP_403_FORBIDDEN)
    serializer = ProductVariantSerializer(data=request.data)
    if serializer.is_valid():
        serializer.save(product=product)
        return Response(serializer.data, status=status.HTTP_201_CREATED)
    return Response(serializer.errors, status=status.HTTP_400_BAD_REQUEST)


@api_view(["POST"])
def add_product_image(request, slug):
    product = generics.get_object_or_404(Product, slug=slug)
    if not request.user.is_staff:
        return Response(status=status.HTTP_403_FORBIDDEN)
    serializer = ProductImageSerializer(data=request.data)
    if serializer.is_valid():
        serializer.save(product=product)
        return Response(serializer.data, status=status.HTTP_201_CREATED)
    return Response(serializer.errors, status=status.HTTP_400_BAD_REQUEST)


@api_view(["POST"])
def add_review(request, slug):
    product = generics.get_object_or_404(Product, slug=slug)
    serializer = ReviewCreateSerializer(
        data={"product": product.id, **request.data}, context={"request": request}
    )
    if serializer.is_valid():
        serializer.save()
        return Response(serializer.data, status=status.HTTP_201_CREATED)
    return Response(serializer.errors, status=status.HTTP_400_BAD_REQUEST)


@api_view(["GET"])
@permission_classes([permissions.AllowAny])
def featured_products(request):
    from .utils import get_featured_products

    category = request.query_params.get("category", None)
    random = request.query_params.get("random", "false").lower() == "true"
    limit = int(request.query_params.get("limit", 8 if random else 5))

    # If no specific filters, use cached featured products
    if not category and not random:
        featured_products = get_featured_products(limit)
        serializer = ProductListSerializer(featured_products, many=True, context={'request': request})
        return Response(serializer.data)

    # Otherwise, apply filters with optimized query
    base_queryset = Product.objects.filter(is_active=True)
    queryset = get_optimized_product_queryset(base_queryset)

    if category:
        queryset = queryset.filter(category__slug=category)

    # If random parameter is true, order randomly
    if random:
        # Use Django's random ordering
        featured_products = queryset.order_by("?")[:limit]
    else:
        # Otherwise use the default ordering by creation date
        featured_products = queryset.order_by("-created_at")[:limit]

    serializer = ProductListSerializer(featured_products, many=True, context={'request': request})
    return Response(serializer.data)


# Product Variant Views
class ProductVariantListView(generics.ListCreateAPIView):
    serializer_class = ProductVariantSerializer
    permission_classes = [permissions.IsAdminUser]

    def get_queryset(self):
        return ProductVariant.objects.filter(product__slug=self.kwargs["product_slug"])


# Product Image Views
class ProductImageListView(generics.ListCreateAPIView):
    serializer_class = ProductImageSerializer
    permission_classes = [permissions.IsAdminUser]

    def get_queryset(self):
        return ProductImage.objects.filter(product__slug=self.kwargs["product_slug"])


# Review Views
class ReviewListView(generics.ListCreateAPIView):
    permission_classes = [permissions.IsAuthenticatedOrReadOnly]

    def get_queryset(self):
        return Review.objects.filter(product__slug=self.kwargs["product_slug"])

    def get_serializer_class(self):
        if self.request.method == 'POST':
            return ReviewCreateSerializer
        return ReviewSerializer

    def perform_create(self, serializer):
        product = Product.objects.get(slug=self.kwargs["product_slug"])
        user = self.request.user

        # Set default title if not provided
        if not serializer.validated_data.get("title"):
            serializer.validated_data["title"] = f"Review for {product.name}"

        serializer.save(user=user, product=product)


class ReviewDetailView(generics.RetrieveUpdateDestroyAPIView):
    serializer_class = ReviewSerializer
    permission_classes = [permissions.IsAuthenticatedOrReadOnly]

    def get_permissions(self):
        if self.request.method in ["PUT", "PATCH", "DELETE"]:
            return [permissions.IsAuthenticated()]
        return super().get_permissions()

    def get_queryset(self):
        return Review.objects.filter(product__slug=self.kwargs["product_slug"])

class AdminCategoryListView(views.APIView):
    permission_classes = [permissions.IsAdminUser]
    def get(self, request, *args, **kwargs):
        try:
            categories = Category.objects.all().order_by('created_at')  # Order categories as needed
            serializer = AdminCategorySerializer(categories, many=True, context={'request': request})
            return Response(serializer.data)
        except Exception as e:
            return Response({"error": str(e)}, status=status.HTTP_500_INTERNAL_SERVER_ERROR)

class CategoryCreateView(generics.CreateAPIView):
    serializer_class = CategorySerializer

    def get_queryset(self):
        return Category.objects.all()

class CategoryUpdateView(generics.RetrieveUpdateAPIView):
    serializer_class = CategorySerializer

    def get_queryset(self):
        return Category.objects.all()




class CategoryProductsAPIView(views.APIView, CategoryProductsPagination):
    permission_classes = [permissions.AllowAny]

    def get(self, request, slug, *args, **kwargs):
        try:
            # First try to find as subcategory
            try:
                subcategory = SubCategorie.objects.get(slug=slug, is_active=True)
                base_queryset = Product.objects.filter(
                    subcategory=subcategory,
                    is_active=True
                )
                products = get_optimized_product_queryset(base_queryset)

                # Paginate and serialize the products
                paginated_products = self.paginate_queryset(products, request, view=self)
                serialized_products = ProductListSerializer(paginated_products, many=True, context={'request': request}).data

                response_data = {
                    "category": {
                        "id": subcategory.category.id,
                        "name": subcategory.category.name,
                        "slug": subcategory.category.slug,
                    },
                    "subcategory": {
                        "id": subcategory.id,
                        "name": subcategory.name,
                        "slug": subcategory.slug
                    },
                    "products": serialized_products
                }
                return self.get_paginated_response(response_data)

            except SubCategorie.DoesNotExist:
                # If not found as subcategory, try as category
                category = Category.objects.get(slug=slug, is_active=True)
                subcategory_slug = request.query_params.get('subcategory')

                base_queryset = Product.objects.filter(
                    category=category,
                    is_active=True
                )
                products = get_optimized_product_queryset(base_queryset)

                if subcategory_slug:
                    subcategory = SubCategorie.objects.get(
                        slug=subcategory_slug,
                        category=category,
                        is_active=True
                    )
                    products = products.filter(subcategory=subcategory)

                # Paginate and serialize the products
                paginated_products = self.paginate_queryset(products, request, view=self)
                serialized_products = ProductListSerializer(paginated_products, many=True, context={'request': request}).data

                response_data = {
                    "category": {
                        "id": category.id,
                        "name": category.name,
                        "slug": category.slug,
                    },
                    "products": serialized_products
                }

                return self.get_paginated_response(response_data)

        except (Category.DoesNotExist, SubCategorie.DoesNotExist) as e:
            return Response(
                {"error": f"Category not found: {str(e)}"},
                status=status.HTTP_404_NOT_FOUND
            )
        except Exception as e:
            return Response(
                {"error": f"An error occurred: {str(e)}"},
                status=status.HTTP_500_INTERNAL_SERVER_ERROR
            )