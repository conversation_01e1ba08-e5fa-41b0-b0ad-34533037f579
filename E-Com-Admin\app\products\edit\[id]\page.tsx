"use client"
import { useState, useEffect } from "react";
import { But<PERSON> } from "@/components/ui/button";
import { Card, CardContent } from "@/components/ui/card";
import { ArrowLeft, Eye, Loader2 } from "lucide-react";
import { ProductForm } from "@/components/products/ProductForm";
import { ProductPreview } from "@/components/products/ProductPreview";
import { useToast } from "@/components/ui/use-toast";
import { useParams, useRouter } from "next/navigation";
import { useProducts } from "@/hooks/useProducts";
import { ProductFormData, Product } from "@/types/product";

const EditProduct = () => {
  const { id } = useParams();
  const router = useRouter();
  const { toast } = useToast();
  const [showPreview, setShowPreview] = useState(false);
  const [product, setProduct] = useState<Product | null>(null);
  const [loading, setLoading] = useState(true);
  const { updateProduct, getProductById } = useProducts();

  // Load product data
  useEffect(() => {
    const loadProduct = async () => {
      if (!id || Array.isArray(id)) return;

      try {
        setLoading(true);
        const productData = await getProductById(id);
        if (productData) {
          setProduct(productData);
        } else {
          toast({
            title: "Error",
            description: "Product not found",
            variant: "destructive",
          });
          router.push("/products");
        }
      } catch {
        toast({
          title: "Error",
          description: "Failed to load product",
          variant: "destructive",
        });
        router.push("/products");
      } finally {
        setLoading(false);
      }
    };

    loadProduct();
  }, [id, getProductById, toast, router]);

  const handleSubmit = async (data: ProductFormData) => {
    if (!id || Array.isArray(id)) return;

    try {
      const productData = {
        name: data.name,
        description: data.description,
        price: parseFloat(data.price),
        // mrp: data.mrp ? parseFloat(data.mrp) : undefined,
        stock: parseInt(data.stock),
        category: data.category,
        brand: data.brand || undefined,
        is_active: data.is_active,
       
      };

      const result = await updateProduct(parseInt(id), productData);

      if (result) {
        toast({
          title: "Product updated",
          description: `Successfully updated ${data.name}`,
        });
        router.push("/products");
      } else {
        toast({
          title: "Error",
          description: "Failed to update product",
          variant: "destructive",
        });
      }
    } catch {
      toast({
        title: "Error",
        description: "Failed to update product",
        variant: "destructive",
      });
    }
  };

  if (loading) {
    return (
      <div className="flex items-center justify-center min-h-[400px]">
        <Loader2 className="h-8 w-8 animate-spin" />
      </div>
    );
  }

  if (!product) {
    return (
      <div className="text-center">
        <p>Product not found</p>
      </div>
    );
  }

  // Convert product data to form data
  const initialData: Partial<ProductFormData> = {
    name: product.name,
    description: product.description,
    price: product.price.toString(),
    category: product.category.id,
    subcategory: product.subcategory?.id,
    brand: product.brand?.id,
    gst: product.gst?.id,
    stock: product.stock.toString(),
    is_active: product.is_active,
    images: product.images.map(img => img.image),
  };

  return (
    <div className="space-y-6 animate-fadeIn">
      <div className="flex items-center justify-between">
        <div className="flex items-center space-x-2">
          <Button variant="ghost" size="icon" onClick={() => router.push("/products")}>
            <ArrowLeft className="h-4 w-4" />
          </Button>
          <h1 className="text-3xl font-bold">Edit Product</h1>
        </div>
        <Button onClick={() => setShowPreview(!showPreview)}>
          <Eye className="mr-2 h-4 w-4" />
          {showPreview ? "Hide Preview" : "Show Preview"}
        </Button>
      </div>

      <div className="grid gap-6 md:grid-cols-2">
        <Card>
          <CardContent className="pt-6">
            <ProductForm
              onSubmit={handleSubmit}
              productId={Array.isArray(id) ? id[0] : id}
              initialData={initialData}
            />
          </CardContent>
        </Card>

        {showPreview && (
          <Card>
            <CardContent className="pt-6">
              <ProductPreview />
            </CardContent>
          </Card>
        )}
      </div>
    </div>
  );
};

export default EditProduct;