/**
 * Custom hook for order tracking with Rapidshyp integration
 */

import { useState, useCallback, useRef, useEffect } from 'react';
import useApi from './useApi';
import { MAIN_URL, SHIPPING_TRACK_ORDER } from '@/constant/urls';
import type {
  OrderTrackingResponse,
  UseOrderTrackingReturn
} from '@/types/shipping';

interface UseOrderTrackingOptions {
  autoRefresh?: boolean;
  refreshInterval?: number; // in milliseconds
}

export const useOrderTracking = (options: UseOrderTrackingOptions = {}): UseOrderTrackingReturn => {
  const { autoRefresh = false, refreshInterval = 60000 } = options; // Default 1 minute
  
  const [trackingData, setTrackingData] = useState<OrderTrackingResponse | undefined>();
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  
  const { read } = useApi<OrderTrackingResponse>(MAIN_URL);
  const intervalRef = useRef<NodeJS.Timeout | null>(null);
  const abortControllerRef = useRef<AbortController | null>(null);
  const currentOrderIdRef = useRef<string | null>(null);

  const trackOrder = useCallback(async (orderId: string) => {
    // Cancel any ongoing request
    if (abortControllerRef.current) {
      abortControllerRef.current.abort();
    }

    // Clear any existing interval
    if (intervalRef.current) {
      clearInterval(intervalRef.current);
    }

    currentOrderIdRef.current = orderId;
    setLoading(true);
    setError(null);

    const performTracking = async () => {
      try {
        if (!orderId) {
          throw new Error('Order ID is required');
        }

        console.log('Tracking order:', orderId);

        const response = await read(SHIPPING_TRACK_ORDER(orderId));

        if (typeof response === 'string') {
          // Error response
          throw new Error(response);
        }

        if (!response || !response.success) {
          throw new Error('Failed to get tracking information');
        }

        setTrackingData(response);

        console.log('Order tracking result:', {
          orderId,
          trackingAvailable: response.tracking_available,
          source: response.source,
          status: response.current_status || response.order_status
        });

      } catch (err) {
        if (err instanceof Error && err.name === 'AbortError') {
          // Request was aborted, don't update state
          return;
        }

        const errorMessage = err instanceof Error ? err.message : 'Failed to track order';
        console.error('Order tracking error:', errorMessage);
        setError(errorMessage);
        setTrackingData(undefined);
      }
    };

    try {
      // Initial tracking call
      await performTracking();

      // Set up auto-refresh if enabled
      if (autoRefresh && refreshInterval > 0) {
        intervalRef.current = setInterval(async () => {
          // Only refresh if we're still tracking the same order
          if (currentOrderIdRef.current === orderId) {
            try {
              await performTracking();
            } catch (err) {
              console.error('Auto-refresh tracking error:', err);
              // Don't update error state for auto-refresh failures
            }
          }
        }, refreshInterval);
      }

    } finally {
      setLoading(false);
    }
  }, [read, autoRefresh, refreshInterval]);

  const clearTracking = useCallback(() => {
    // Cancel any ongoing request
    if (abortControllerRef.current) {
      abortControllerRef.current.abort();
    }

    // Clear interval
    if (intervalRef.current) {
      clearInterval(intervalRef.current);
    }

    currentOrderIdRef.current = null;
    setTrackingData(undefined);
    setError(null);
    setLoading(false);
  }, []);

  // Cleanup on unmount
  useEffect(() => {
    return () => {
      if (abortControllerRef.current) {
        abortControllerRef.current.abort();
      }
      if (intervalRef.current) {
        clearInterval(intervalRef.current);
      }
    };
  }, []);

  return {
    trackingData,
    loading,
    error,
    trackOrder,
    clearTracking
  };
};

export default useOrderTracking;
