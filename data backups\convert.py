#!/usr/bin/env python3
"""
Beautiful PDF to Markdown Converter
Converts PDF files to well-structured, readable Markdown format.
"""

import os
import sys
import re
import argparse
from pathlib import Path
from typing import List, Optional, Tuple
import warnings
warnings.filterwarnings('ignore')

try:
    import pymupdf as fitz  # PyMuPDF
except ImportError:
    try:
        import fitz
    except ImportError:
        print("Error: PyMuPDF not installed. Install with: pip install pymupdf")
        sys.exit(1)

class PDFToMarkdownConverter:
    def __init__(self, enhance_formatting: bool = True):
        self.enhance_formatting = enhance_formatting
        
    def extract_text_with_formatting(self, pdf_path: str) -> List[dict]:
        """Extract text with formatting information from PDF."""
        doc = fitz.open(pdf_path)
        pages_data = []
        
        for page_num in range(doc.page_count):
            page = doc[page_num]
            
            # Get text blocks with formatting
            blocks = page.get_text("dict")
            page_text = self._process_blocks(blocks)
            
            pages_data.append({
                'page_num': page_num + 1,
                'content': page_text
            })
        
        doc.close()
        return pages_data
    
    def _process_blocks(self, blocks: dict) -> List[dict]:
        """Process text blocks and extract formatting information."""
        processed_blocks = []
        
        for block in blocks.get("blocks", []):
            if block.get("type") == 0:  # Text block
                for line in block.get("lines", []):
                    line_text = ""
                    font_sizes = []
                    font_flags = []
                    
                    for span in line.get("spans", []):
                        text = span.get("text", "").strip()
                        if text:
                            line_text += text + " "
                            font_sizes.append(span.get("size", 12))
                            font_flags.append(span.get("flags", 0))
                    
                    if line_text.strip():
                        # Determine formatting based on font size and flags
                        avg_font_size = sum(font_sizes) / len(font_sizes) if font_sizes else 12
                        is_bold = any(flag & 2**4 for flag in font_flags)  # Bold flag
                        is_italic = any(flag & 2**1 for flag in font_flags)  # Italic flag
                        
                        processed_blocks.append({
                            'text': line_text.strip(),
                            'font_size': avg_font_size,
                            'is_bold': is_bold,
                            'is_italic': is_italic,
                            'bbox': line.get("bbox", [0, 0, 0, 0])
                        })
        
        return processed_blocks
    
    def _classify_text_type(self, block: dict, base_font_size: float) -> str:
        """Classify text as header, paragraph, list item, etc."""
        text = block['text']
        font_size = block['font_size']
        is_bold = block['is_bold']
        
        # Header detection
        if font_size > base_font_size * 1.5:
            return 'h1'
        elif font_size > base_font_size * 1.3:
            return 'h2'
        elif font_size > base_font_size * 1.1 or is_bold:
            return 'h3'
        
        # List detection
        if re.match(r'^\s*[-•·]\s+', text) or re.match(r'^\s*\d+\.\s+', text):
            return 'list_item'
        
        # Quote detection (indented text)
        if text.startswith('    ') or text.startswith('\t'):
            return 'quote'
        
        return 'paragraph'
    
    def _clean_text(self, text: str) -> str:
        """Clean and normalize text."""
        # Remove excessive whitespace
        text = re.sub(r'\s+', ' ', text)
        
        # Fix common PDF extraction issues
        text = re.sub(r'([a-z])([A-Z])', r'\1 \2', text)  # Add space between joined words
        text = re.sub(r'(\w)-\s+(\w)', r'\1\2', text)  # Fix hyphenated words split across lines
        
        # Clean up punctuation
        text = re.sub(r'\s+([,.!?;:])', r'\1', text)
        text = re.sub(r'([.!?])\s*([A-Z])', r'\1 \2', text)
        
        return text.strip()
    
    def _format_as_markdown(self, pages_data: List[dict]) -> str:
        """Convert extracted data to Markdown format."""
        markdown_lines = []
        
        # Calculate base font size (most common font size)
        all_font_sizes = []
        for page in pages_data:
            for block in page['content']:
                all_font_sizes.append(block['font_size'])
        
        base_font_size = max(set(all_font_sizes), key=all_font_sizes.count) if all_font_sizes else 12
        
        current_list_level = 0
        prev_type = None
        
        for page in pages_data:
            # Add page separator for multi-page documents
            if len(pages_data) > 1 and page['page_num'] > 1:
                markdown_lines.append(f"\n---\n*Page {page['page_num']}*\n")
            
            for block in page['content']:
                text = self._clean_text(block['text'])
                if not text:
                    continue
                
                text_type = self._classify_text_type(block, base_font_size)
                
                # Add spacing between different sections
                if prev_type and prev_type != text_type and text_type != 'list_item':
                    markdown_lines.append("")
                
                # Format based on type
                if text_type == 'h1':
                    markdown_lines.append(f"# {text}")
                elif text_type == 'h2':
                    markdown_lines.append(f"## {text}")
                elif text_type == 'h3':
                    markdown_lines.append(f"### {text}")
                elif text_type == 'list_item':
                    # Clean up list markers
                    clean_text = re.sub(r'^\s*[-•·]\s+', '', text)
                    clean_text = re.sub(r'^\s*\d+\.\s+', '', clean_text)
                    markdown_lines.append(f"- {clean_text}")
                elif text_type == 'quote':
                    markdown_lines.append(f"> {text}")
                else:  # paragraph
                    # Apply italic formatting if detected
                    if block['is_italic'] and not block['is_bold']:
                        text = f"*{text}*"
                    elif block['is_bold'] and not any(text_type.startswith('h') for text_type in ['h1', 'h2', 'h3']):
                        text = f"**{text}**"
                    
                    markdown_lines.append(text)
                
                prev_type = text_type
        
        return self._post_process_markdown('\n'.join(markdown_lines))
    
    def _post_process_markdown(self, markdown: str) -> str:
        """Post-process markdown for better readability."""
        lines = markdown.split('\n')
        processed_lines = []
        
        for i, line in enumerate(lines):
            # Remove excessive empty lines
            if line.strip() == "" and i > 0 and lines[i-1].strip() == "":
                continue
            
            # Ensure proper spacing around headers
            if line.startswith('#'):
                if i > 0 and processed_lines and processed_lines[-1].strip():
                    processed_lines.append("")
                processed_lines.append(line)
                if i < len(lines) - 1 and lines[i+1].strip():
                    processed_lines.append("")
            else:
                processed_lines.append(line)
        
        # Join and clean up final markdown
        result = '\n'.join(processed_lines)
        
        # Remove excessive line breaks
        result = re.sub(r'\n{3,}', '\n\n', result)
        
        # Ensure document ends with single newline
        result = result.rstrip() + '\n'
        
        return result
    
    def convert(self, pdf_path: str, output_path: Optional[str] = None) -> str:
        """Convert PDF to Markdown."""
        if not os.path.exists(pdf_path):
            raise FileNotFoundError(f"PDF file not found: {pdf_path}")
        
        print(f"Converting {pdf_path} to Markdown...")
        
        # Extract text with formatting
        pages_data = self.extract_text_with_formatting(pdf_path)
        
        # Convert to markdown
        markdown_content = self._format_as_markdown(pages_data)
        
        # Determine output path
        if output_path is None:
            pdf_stem = Path(pdf_path).stem
            output_path = f"{pdf_stem}.md"
        
        # Write to file
        with open(output_path, 'w', encoding='utf-8') as f:
            f.write(markdown_content)
        
        print(f"✅ Conversion complete! Output saved to: {output_path}")
        print(f"📄 Processed {len(pages_data)} pages")
        
        return output_path

def main():
    parser = argparse.ArgumentParser(
        description="Convert PDF files to beautiful, readable Markdown format",
        formatter_class=argparse.RawDescriptionHelpFormatter,
        epilog="""
Examples:
  python pdf_to_md.py document.pdf
  python pdf_to_md.py document.pdf -o output.md
  python pdf_to_md.py document.pdf --no-formatting
        """
    )
    
    parser.add_argument('pdf_file', help='Path to the PDF file to convert')
    parser.add_argument('-o', '--output', help='Output Markdown file path (optional)')
    parser.add_argument('--no-formatting', action='store_true', 
                       help='Disable enhanced formatting detection')
    
    args = parser.parse_args()
    
    try:
        converter = PDFToMarkdownConverter(enhance_formatting=not args.no_formatting)
        output_file = converter.convert(args.pdf_file, args.output)
        
        # Show preview of converted content
        with open(output_file, 'r', encoding='utf-8') as f:
            content = f.read()
            preview = content[:500] + "..." if len(content) > 500 else content
            print(f"\n📖 Preview of converted content:\n{'-' * 50}")
            print(preview)
            print('-' * 50)
            
    except Exception as e:
        print(f"❌ Error: {e}")
        sys.exit(1)

if __name__ == "__main__":
    main()