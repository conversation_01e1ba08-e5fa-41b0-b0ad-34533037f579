"use client";

import { useSession } from "next-auth/react";
import { useNew<PERSON><PERSON> } from "@/hooks/useNewApi";
import { GET_PROMO_CODE, MAIN_URL } from "@/constant/urls";
import { useEffect, useState, useRef } from "react";

export default function PromoBanner() {
  const { data }: any = useSession();
  const [promoCode, setPromoCode] = useState<any>(null);
  const [hasError, setHasError] = useState<boolean>(false);
  const apiCallAttempted = useRef<boolean>(false);

  const { get, loading, error } = useNewApi({
    baseURL: MAIN_URL,
    token: data?.user?.access,
  });

  const fetchPromotion = async () => {
    // Skip if we've already tried to fetch
    if (apiCallAttempted.current) return;

    apiCallAttempted.current = true;

    try {
      const response = await get<any>(GET_PROMO_CODE);
      if (response.data) {
        setPromoCode(response.data);
      }
      if (response.error) {
        console.log("Error fetching promotion:", response.error);
        setHasError(true);
      }
    } catch (err) {
      console.log("Exception fetching promotion:", err);
      setHasError(true);
    }
  };

  useEffect(() => {
    fetchPromotion();
  }, []);

  // Don't render anything if there's no promo code or if there was an error
  if (!promoCode || hasError) {
    return null;
  }

  return (
    <div className="container mx-auto w-full bg-theme-header text-white py-2 text-center text-sm">
      <p className="font-medium">
        <span className="font-semibold uppercase text-theme-accent-secondary">
        {promoCode?.description}
        </span>{" "}
        <span className="font-normal">: <span className="bg-theme-accent-primary/20 px-2 py-0.5 rounded text-white">{promoCode?.code}</span></span>
      </p>
    </div>
  );
}
