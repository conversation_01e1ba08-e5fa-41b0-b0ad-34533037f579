"use client";
import Product from "@/components/product/Product";
import ProductCardLoading from "@/components/ui/loading/ProductCardLoading";
import Pagination from "@/components/ui/newPagination";
import { CATEGORIZE_PRODUCTS, MAIN_URL } from "@/constant/urls";
import useApi from "@/hooks/useApi";
import MainHOF from "@/layout/MainHOF";
import { useParams } from "next/navigation";
import React, { useEffect, useState } from "react";

const CategorizeProducts = () => {
  const { slug } = useParams();
  const { data, loading, read }: any = useApi(MAIN_URL);
  const [currentPage, setCurrentPage] = useState(1);
  useEffect(() => {
    let url = `${CATEGORIZE_PRODUCTS(slug)}?page=${currentPage}`;
    read(url);
  }, [slug, currentPage]);
  
  if (loading) {
    return (
      <MainHOF>
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-6">
          {Array.from({ length: 10 }).map((_, i) => (
            <ProductCardLoading key={i} />
          ))}
        </div>
        <div className="mt-8">
          <Pagination
            itemsPerPage={10}
            currentPage={currentPage}
            setCurrentPage={(page:any) => setCurrentPage(page)}
            count={data?.count ?? 0}
          />
        </div>
      </MainHOF>
    );
  }
  return (
    <MainHOF>
      <div>
        <section className="container mx-auto px-4 py-12">
          <h2 className="text-3xl font-bold mb-8">
            {data && data?.results.category.name}
          </h2>
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
            {Array.isArray(data?.results?.products) &&
              data?.results?.products.map((product: any) => (
                <Product {...product} key={product.id} />
              ))}
          </div>
          <div className="mt-8">
            <Pagination
              itemsPerPage={10}
              currentPage={currentPage}
              setCurrentPage={(page:any) => setCurrentPage(page)}
              count={data?.count ?? 0}
            />
          </div>
        </section>
      </div>
    </MainHOF>
  );
};

export default CategorizeProducts;
