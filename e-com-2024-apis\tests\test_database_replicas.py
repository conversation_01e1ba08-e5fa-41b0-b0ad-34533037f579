"""
Tests for database replica functionality and read/write splitting.

This test suite verifies that:
1. Database routing works correctly
2. Read operations use replicas when available
3. Write operations always use primary database
4. Fallback mechanisms work when replicas are unavailable
5. Existing functionality is preserved
"""

from unittest.mock import patch
from django.test import TestCase
from django.contrib.auth import get_user_model

from backend.db_router import DatabaseRouter, ReplicaAwareManager
from backend.db_utils import ReplicaAwareQuerySet, DatabaseHealthChecker
from products.models import Product, Category, Brand

User = get_user_model()


class DatabaseRouterTest(TestCase):
    """Test the database router functionality"""
    
    def setUp(self):
        self.router = DatabaseRouter()
    
    def test_router_initialization_single_db(self):
        """Test router initialization with single database"""
        # Default configuration should use 'default' for reads
        self.assertEqual(self.router.read_databases, ['default'])
        self.assertEqual(self.router.write_database, 'default')
    
    def test_router_initialization_with_replica(self):
        """Test router initialization with read replica"""
        # Mock the settings instead of overriding them
        with patch('django.conf.settings.DATABASES', {
            'default': {'ENGINE': 'django.db.backends.sqlite3', 'NAME': ':memory:'},
            'read_replica': {'ENGINE': 'django.db.backends.sqlite3', 'NAME': ':memory:'}
        }):
            router = DatabaseRouter()
            self.assertIn('read_replica', router.read_databases)
            self.assertEqual(router.write_database, 'default')
    
    def test_db_for_write(self):
        """Test that writes always go to primary database"""
        db = self.router.db_for_write(Product)
        self.assertEqual(db, 'default')
    
    def test_db_for_read_single_db(self):
        """Test read routing with single database"""
        db = self.router.db_for_read(Product)
        self.assertEqual(db, 'default')
    
    def test_allow_migrate(self):
        """Test that migrations only run on primary database"""
        # Should allow migrations on default
        self.assertTrue(self.router.allow_migrate('default', 'products'))
        
        # Should not allow migrations on replica
        self.assertFalse(self.router.allow_migrate('read_replica', 'products'))


class ReplicaAwareManagerTest(TestCase):
    """Test the replica-aware manager utilities"""
    
    def setUp(self):
        self.category = Category.objects.create(name='Test Category')
        self.brand = Brand.objects.create(name='Test Brand')
        self.product = Product.objects.create(
            name='Test Product',
            price=100.00,
            category=self.category,
            brand=self.brand
        )
    
    def test_using_read_replica_single_db(self):
        """Test using read replica with single database configuration"""
        queryset = Product.objects.all()
        replica_queryset = ReplicaAwareManager.using_read_replica(queryset)
        
        # Should return the same queryset when no replica is available
        self.assertEqual(list(queryset), list(replica_queryset))
    
    def test_using_primary(self):
        """Test forcing queryset to use primary database"""
        queryset = Product.objects.all()
        primary_queryset = ReplicaAwareManager.using_primary(queryset)
        
        # Should use default database
        self.assertEqual(primary_queryset.db, 'default')
    
    def test_get_read_database(self):
        """Test getting read database name"""
        db_name = ReplicaAwareManager.get_read_database()
        self.assertEqual(db_name, 'default')  # Should fallback to default
    
    def test_get_write_database(self):
        """Test getting write database name"""
        db_name = ReplicaAwareManager.get_write_database()
        self.assertEqual(db_name, 'default')


class ReplicaAwareQuerySetTest(TestCase):
    """Test the replica-aware queryset utilities"""
    
    def setUp(self):
        self.category = Category.objects.create(name='Electronics')
        self.brand = Brand.objects.create(name='Apple')
        self.product = Product.objects.create(
            name='iPhone',
            price=999.00,
            category=self.category,
            brand=self.brand,
            stock=10
        )
        self.user = User.objects.create_user(
            email='<EMAIL>',
            password='testpass123',
            name='Test User'
        )
    
    def test_get_optimized_product_queryset(self):
        """Test optimized product queryset with replica support"""
        queryset = ReplicaAwareQuerySet.get_optimized_product_queryset(use_replica=True)
        
        # Should return products with optimizations
        products = list(queryset)
        self.assertEqual(len(products), 1)
        self.assertEqual(products[0].name, 'iPhone')
        
        # Should have annotations
        product = products[0]
        self.assertTrue(hasattr(product, 'avg_rating'))
        self.assertTrue(hasattr(product, 'review_count'))
    
    def test_get_optimized_product_queryset_no_replica(self):
        """Test optimized product queryset without replica"""
        queryset = ReplicaAwareQuerySet.get_optimized_product_queryset(use_replica=False)
        
        # Should still work and return optimized queryset
        products = list(queryset)
        self.assertEqual(len(products), 1)
        self.assertEqual(products[0].name, 'iPhone')
    
    def test_get_optimized_user_queryset(self):
        """Test optimized user queryset with replica support"""
        queryset = ReplicaAwareQuerySet.get_optimized_user_queryset(use_replica=True)
        
        # Should return users with prefetched data
        users = list(queryset)
        self.assertEqual(len(users), 1)
        self.assertEqual(users[0].email, '<EMAIL>')


class DatabaseHealthCheckerTest(TestCase):
    """Test database health checking functionality"""
    
    def test_check_replica_health_single_db(self):
        """Test health check with single database"""
        status = DatabaseHealthChecker.check_replica_health()
        
        # Should report default database as healthy
        self.assertIn('default', status)
        self.assertEqual(status['default'], 'healthy')
    
    def test_get_healthy_read_database(self):
        """Test getting healthy read database"""
        db_name = DatabaseHealthChecker.get_healthy_read_database()
        self.assertEqual(db_name, 'default')


class BackwardCompatibilityTest(TestCase):
    """Test that existing functionality is preserved"""
    
    def setUp(self):
        self.category = Category.objects.create(name='Test Category')
        self.brand = Brand.objects.create(name='Test Brand')
        self.product = Product.objects.create(
            name='Test Product',
            price=100.00,
            category=self.category,
            brand=self.brand
        )
    
    def test_existing_product_queries_work(self):
        """Test that existing product queries still work"""
        # Test basic queries
        products = Product.objects.all()
        self.assertEqual(products.count(), 1)
        
        # Test filtered queries
        active_products = Product.objects.filter(is_active=True)
        self.assertEqual(active_products.count(), 1)
        
        # Test related queries
        products_with_category = Product.objects.select_related('category')
        self.assertEqual(products_with_category.count(), 1)
    
    def test_existing_optimized_queries_work(self):
        """Test that existing optimized queries still work"""
        from products.views import get_optimized_product_queryset
        
        # Should work with default parameters
        queryset = get_optimized_product_queryset()
        products = list(queryset)
        self.assertEqual(len(products), 1)
        
        # Should work with custom base queryset
        base_queryset = Product.objects.filter(is_active=True)
        queryset = get_optimized_product_queryset(base_queryset)
        products = list(queryset)
        self.assertEqual(len(products), 1)


class MultiDatabaseTest(TestCase):
    """Test functionality with multiple databases configured"""

    def test_router_with_multiple_databases(self):
        """Test router behavior with multiple databases"""
        # Mock the settings instead of overriding them
        with patch('django.conf.settings.DATABASES', {
            'default': {'ENGINE': 'django.db.backends.sqlite3', 'NAME': ':memory:'},
            'read_replica': {'ENGINE': 'django.db.backends.sqlite3', 'NAME': ':memory:'}
        }):
            router = DatabaseRouter()

            # Should detect read replica
            self.assertIn('read_replica', router.read_databases)

            # Writes should still go to default
            self.assertEqual(router.db_for_write(Product), 'default')

    def test_replica_aware_queries_with_multiple_dbs(self):
        """Test replica-aware queries with multiple databases"""
        # This test would need actual database setup to be meaningful
        # For now, just verify the code doesn't break
        queryset = ReplicaAwareQuerySet.get_optimized_product_queryset(use_replica=True)
        self.assertIsNotNone(queryset)


class PerformanceTest(TestCase):
    """Test that replica functionality doesn't degrade performance"""
    
    def setUp(self):
        # Create test data
        self.category = Category.objects.create(name='Electronics')
        self.brand = Brand.objects.create(name='Apple')
        
        # Create multiple products for performance testing
        for i in range(10):
            Product.objects.create(
                name=f'Product {i}',
                price=100.00 + i,
                category=self.category,
                brand=self.brand,
                stock=10
            )
    
    def test_optimized_queries_performance(self):
        """Test that optimized queries maintain performance"""
        from django.test.utils import override_settings
        from django.db import connection
        
        with override_settings(DEBUG=True):
            connection.queries_log.clear()
            
            # Use optimized queryset
            queryset = ReplicaAwareQuerySet.get_optimized_product_queryset()
            list(queryset)  # Force evaluation
            
            # Should use minimal queries due to optimizations
            query_count = len(connection.queries)
            self.assertLess(query_count, 5, f"Expected <5 queries, got {query_count}")
