/**
 * Secure API client with automatic token management and security features
 */

import axios, { AxiosInstance, AxiosRequestConfig, AxiosResponse } from 'axios';
import { authUtils, tokenManager } from './secureStorage';
import { MAIN_URL, TOKEN_REFFRESH } from '../constant/urls';

class SecureApiClient {
  private client: AxiosInstance;
  private isRefreshing = false;
  private failedQueue: Array<{
    resolve: (value?: any) => void;
    reject: (error?: any) => void;
  }> = [];

  constructor(baseURL: string = MAIN_URL) {
    this.client = axios.create({
      baseURL,
      timeout: 30000, // 30 seconds timeout
      headers: {
        'Content-Type': 'application/json',
      },
    });

    this.setupInterceptors();
  }

  private setupInterceptors() {
    // Request interceptor to add auth headers
    this.client.interceptors.request.use(
      (config) => {
        // Add security headers
        config.headers = {
          ...config.headers,
          'X-Requested-With': 'XMLHttpRequest',
          'Cache-Control': 'no-cache',
          'Pragma': 'no-cache',
        };

        // Add auth token if available
        const authHeaders = authUtils.getAuthHeader();
        if (authHeaders.Authorization) {
          config.headers.Authorization = authHeaders.Authorization;
        }

        // Log API calls for security monitoring (in development)
        if (process.env.NODE_ENV === 'development') {
          console.log(`[API] ${config.method?.toUpperCase()} ${config.url}`);
        }

        return config;
      },
      (error) => {
        return Promise.reject(error);
      }
    );

    // Response interceptor to handle token refresh
    this.client.interceptors.response.use(
      (response) => {
        return response;
      },
      async (error) => {
        const originalRequest = error.config;

        // Handle 401 errors (unauthorized)
        if (error.response?.status === 401 && !originalRequest._retry) {
          if (this.isRefreshing) {
            // If already refreshing, queue the request
            return new Promise((resolve, reject) => {
              this.failedQueue.push({ resolve, reject });
            }).then((token) => {
              originalRequest.headers.Authorization = `Bearer ${token}`;
              return this.client(originalRequest);
            }).catch((err) => {
              return Promise.reject(err);
            });
          }

          originalRequest._retry = true;
          this.isRefreshing = true;

          try {
            const newToken = await this.refreshToken();
            if (newToken) {
              // Process failed queue
              this.processQueue(null, newToken);
              
              // Retry original request
              originalRequest.headers.Authorization = `Bearer ${newToken}`;
              return this.client(originalRequest);
            } else {
              // Refresh failed, logout user
              this.processQueue(new Error('Token refresh failed'), null);
              this.handleLogout();
              return Promise.reject(error);
            }
          } catch (refreshError) {
            this.processQueue(refreshError, null);
            this.handleLogout();
            return Promise.reject(refreshError);
          } finally {
            this.isRefreshing = false;
          }
        }

        // Handle rate limiting (429)
        if (error.response?.status === 429) {
          console.warn('[API] Rate limit exceeded. Please slow down requests.');
          
          // Extract retry-after header if available
          const retryAfter = error.response.headers['retry-after'];
          if (retryAfter) {
            const delay = parseInt(retryAfter) * 1000; // Convert to milliseconds
            console.log(`[API] Retrying after ${delay}ms`);
            
            return new Promise((resolve) => {
              setTimeout(() => {
                resolve(this.client(originalRequest));
              }, delay);
            });
          }
        }

        // Handle server errors (5xx)
        if (error.response?.status >= 500) {
          console.error('[API] Server error:', error.response.status);
          // Could implement retry logic here
        }

        return Promise.reject(error);
      }
    );
  }

  private async refreshToken(): Promise<string | null> {
    try {
      const refreshToken = tokenManager.getRefreshToken();
      if (!refreshToken) {
        throw new Error('No refresh token available');
      }

      const response = await axios.post(`${MAIN_URL}${TOKEN_REFFRESH}`, {
        refresh: refreshToken,
      });

      if (response.data?.access) {
        const newAccessToken = response.data.access;
        
        // Update tokens in secure storage
        tokenManager.setTokens(newAccessToken, refreshToken);
        
        return newAccessToken;
      } else {
        throw new Error('Invalid refresh response');
      }
    } catch (error) {
      console.error('[API] Token refresh failed:', error);
      return null;
    }
  }

  private processQueue(error: any, token: string | null) {
    this.failedQueue.forEach(({ resolve, reject }) => {
      if (error) {
        reject(error);
      } else {
        resolve(token);
      }
    });

    this.failedQueue = [];
  }

  private handleLogout() {
    // Clear all auth data
    authUtils.logout();
    
    // Redirect to login page
    if (typeof window !== 'undefined') {
      window.location.href = '/auth/login';
    }
  }

  // Public API methods
  async get<T = any>(url: string, config?: AxiosRequestConfig): Promise<AxiosResponse<T>> {
    return this.client.get(url, config);
  }

  async post<T = any>(url: string, data?: any, config?: AxiosRequestConfig): Promise<AxiosResponse<T>> {
    return this.client.post(url, data, config);
  }

  async put<T = any>(url: string, data?: any, config?: AxiosRequestConfig): Promise<AxiosResponse<T>> {
    return this.client.put(url, data, config);
  }

  async patch<T = any>(url: string, data?: any, config?: AxiosRequestConfig): Promise<AxiosResponse<T>> {
    return this.client.patch(url, data, config);
  }

  async delete<T = any>(url: string, config?: AxiosRequestConfig): Promise<AxiosResponse<T>> {
    return this.client.delete(url, config);
  }

  // Utility methods
  isAuthenticated(): boolean {
    return authUtils.isAuthenticated();
  }

  getCurrentUser() {
    return authUtils.getCurrentUser();
  }

  logout() {
    this.handleLogout();
  }

  // Security utility methods
  async uploadFile(url: string, file: File, onProgress?: (progress: number) => void): Promise<AxiosResponse> {
    const formData = new FormData();
    formData.append('file', file);

    return this.client.post(url, formData, {
      headers: {
        'Content-Type': 'multipart/form-data',
      },
      onUploadProgress: (progressEvent) => {
        if (onProgress && progressEvent.total) {
          const progress = Math.round((progressEvent.loaded * 100) / progressEvent.total);
          onProgress(progress);
        }
      },
    });
  }

  // Privacy and compliance methods
  async exportUserData(): Promise<AxiosResponse> {
    return this.client.get('/api/v1/users/privacy/export/', {
      responseType: 'text', // Handle CSV response as text
    });
  }

  async requestDataDeletion(reason?: string): Promise<AxiosResponse> {
    return this.post('/api/v1/users/privacy/delete/', { reason });
  }

  async updateConsent(consents: Record<string, boolean>): Promise<AxiosResponse> {
    return this.post('/api/v1/users/privacy/consent/', { consents });
  }

  async getConsentStatus(): Promise<AxiosResponse> {
    return this.get('/api/v1/users/privacy/consent/');
  }
}

// Create and export a singleton instance
export const secureApiClient = new SecureApiClient();

// Export the class for custom instances
export { SecureApiClient };

// Utility function for quick API calls
export const api = {
  get: (url: string, config?: AxiosRequestConfig) => secureApiClient.get(url, config),
  post: (url: string, data?: any, config?: AxiosRequestConfig) => secureApiClient.post(url, data, config),
  put: (url: string, data?: any, config?: AxiosRequestConfig) => secureApiClient.put(url, data, config),
  patch: (url: string, data?: any, config?: AxiosRequestConfig) => secureApiClient.patch(url, data, config),
  delete: (url: string, config?: AxiosRequestConfig) => secureApiClient.delete(url, config),
};

export default secureApiClient;
