# Production Security Deployment Guide

## 🚨 CRITICAL: DO NOT DEPLOY WITHOUT COMPLETING ALL STEPS

### Pre-Deployment Security Checklist

#### 1. Generate Production Secrets (REQUIRED)

```bash
# 1. Generate Django Secret Key
python -c 'from django.core.management.utils import get_random_secret_key; print("DJANGO_SECRET_KEY=" + get_random_secret_key())'

# 2. Generate JWT Secret Key  
python -c 'import secrets; print("JWT_SECRET_KEY=" + secrets.token_urlsafe(64))'

# 3. Generate Encryption Key
python -c "from cryptography.fernet import Fernet; print('ENCRYPTION_KEY=' + Fernet.generate_key().decode())"

# 4. Generate Database Password
python -c 'import secrets; import string; chars = string.ascii_letters + string.digits + "!@#$%^&*"; print("DB_PASSWORD=" + "".join(secrets.choice(chars) for _ in range(32)))'
```

#### 2. Create Production Environment File

```bash
# Copy template and fill with generated secrets
cp .env.production.template .env.production

# Edit with your secure values
nano .env.production
```

#### 3. Update Production Settings

```python
# backend/settings.py - Verify these are set correctly:
DEBUG = os.getenv("DEVELOPMENT", "False").lower() == "true"  # Will be False in production
SECRET_KEY = os.getenv("DJANGO_SECRET_KEY", "CHANGE-THIS")
ALLOWED_HOSTS = ["yourdomain.com", "www.yourdomain.com", "api.yourdomain.com"]
```

## 🔒 DEPLOYMENT STEPS

### Step 1: Server Security Setup

```bash
# 1. Update system packages
sudo apt update && sudo apt upgrade -y

# 2. Configure firewall
sudo ufw enable
sudo ufw allow ssh
sudo ufw allow 80
sudo ufw allow 443

# 3. Install fail2ban for intrusion prevention
sudo apt install fail2ban -y
sudo systemctl enable fail2ban
sudo systemctl start fail2ban
```

### Step 2: SSL Certificate Setup

```bash
# Install certbot for Let's Encrypt
sudo apt install certbot python3-certbot-nginx -y

# Generate SSL certificate
sudo certbot --nginx -d yourdomain.com -d www.yourdomain.com -d api.yourdomain.com

# Verify auto-renewal
sudo certbot renew --dry-run
```

### Step 3: Database Security

```bash
# 1. Secure PostgreSQL installation
sudo -u postgres psql

# 2. Create production database and user
CREATE DATABASE triumph_ecommerce_prod;
CREATE USER triumph_prod_user WITH ENCRYPTED PASSWORD 'your-secure-password';
GRANT ALL PRIVILEGES ON DATABASE triumph_ecommerce_prod TO triumph_prod_user;
ALTER USER triumph_prod_user CREATEDB;
\q

# 3. Configure PostgreSQL for SSL
sudo nano /etc/postgresql/*/main/postgresql.conf
# Add: ssl = on

sudo systemctl restart postgresql
```

### Step 4: Application Deployment

```bash
# 1. Clone repository
git clone https://github.com/your-repo/ecommerce.git
cd ecommerce

# 2. Set up virtual environment
python3 -m venv venv
source venv/bin/activate

# 3. Install dependencies
pip install -r requirements.txt

# 4. Copy production environment
cp .env.production.template .env
# Edit .env with your production values

# 5. Run security checks
python manage.py check --deploy

# 6. Run migrations
python manage.py migrate

# 7. Collect static files
python manage.py collectstatic --noinput

# 8. Create superuser
python manage.py createsuperuser
```

### Step 5: Web Server Configuration

#### Nginx Configuration
```nginx
# /etc/nginx/sites-available/ecommerce
server {
    listen 80;
    server_name yourdomain.com www.yourdomain.com;
    return 301 https://$server_name$request_uri;
}

server {
    listen 443 ssl http2;
    server_name yourdomain.com www.yourdomain.com;
    
    # SSL Configuration
    ssl_certificate /etc/letsencrypt/live/yourdomain.com/fullchain.pem;
    ssl_certificate_key /etc/letsencrypt/live/yourdomain.com/privkey.pem;
    ssl_protocols TLSv1.2 TLSv1.3;
    ssl_ciphers ECDHE-RSA-AES256-GCM-SHA512:DHE-RSA-AES256-GCM-SHA512;
    ssl_prefer_server_ciphers off;
    
    # Security Headers
    add_header X-Frame-Options DENY;
    add_header X-Content-Type-Options nosniff;
    add_header X-XSS-Protection "1; mode=block";
    add_header Strict-Transport-Security "max-age=31536000; includeSubDomains; preload";
    add_header Referrer-Policy "strict-origin-when-cross-origin";
    
    # Rate Limiting
    limit_req_zone $binary_remote_addr zone=login:10m rate=5r/m;
    limit_req_zone $binary_remote_addr zone=api:10m rate=100r/m;
    
    location / {
        proxy_pass http://127.0.0.1:3000;
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;
    }
    
    location /api/ {
        limit_req zone=api burst=20 nodelay;
        proxy_pass http://127.0.0.1:8000;
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;
    }
    
    location /api/users/login/ {
        limit_req zone=login burst=3 nodelay;
        proxy_pass http://127.0.0.1:8000;
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;
    }
}
```

### Step 6: Process Management

#### Systemd Service for Django
```ini
# /etc/systemd/system/ecommerce-api.service
[Unit]
Description=E-commerce Django API
After=network.target

[Service]
Type=exec
User=www-data
Group=www-data
WorkingDirectory=/path/to/ecommerce/e-com-2024-apis
Environment=PATH=/path/to/ecommerce/e-com-2024-apis/venv/bin
ExecStart=/path/to/ecommerce/e-com-2024-apis/venv/bin/gunicorn backend.wsgi:application --bind 127.0.0.1:8000 --workers 3
Restart=always

[Install]
WantedBy=multi-user.target
```

#### Systemd Service for Next.js
```ini
# /etc/systemd/system/ecommerce-frontend.service
[Unit]
Description=E-commerce Next.js Frontend
After=network.target

[Service]
Type=exec
User=www-data
Group=www-data
WorkingDirectory=/path/to/ecommerce/ecommerce
ExecStart=/usr/bin/npm start
Restart=always
Environment=NODE_ENV=production

[Install]
WantedBy=multi-user.target
```

### Step 7: Security Monitoring Setup

```bash
# 1. Set up log rotation
sudo nano /etc/logrotate.d/ecommerce

# Add:
/path/to/ecommerce/e-com-2024-apis/logs/*.log {
    daily
    missingok
    rotate 52
    compress
    delaycompress
    notifempty
    create 644 www-data www-data
}

# 2. Set up automated security cleanup (cron)
sudo crontab -e

# Add:
# Daily security cleanup at 2 AM
0 2 * * * /path/to/ecommerce/e-com-2024-apis/venv/bin/python /path/to/ecommerce/e-com-2024-apis/manage.py security_cleanup --days=90

# Weekly security audit on Sundays at 3 AM
0 3 * * 0 /path/to/ecommerce/e-com-2024-apis/venv/bin/python /path/to/ecommerce/e-com-2024-apis/manage.py security_audit --days=7 --format=text > /var/log/ecommerce-security-audit.log
```

## 🔍 POST-DEPLOYMENT VERIFICATION

### Step 1: Security Audit
```bash
# Run comprehensive security audit
python manage.py security_audit --format=text

# Expected result: Risk score should be < 20 for production
```

### Step 2: SSL/TLS Testing
```bash
# Test SSL configuration
curl -I https://yourdomain.com

# Use online tools:
# - SSL Labs: https://www.ssllabs.com/ssltest/
# - Security Headers: https://securityheaders.com/
```

### Step 3: Security Headers Verification
```bash
# Check security headers
curl -I https://yourdomain.com

# Should include:
# - Strict-Transport-Security
# - X-Frame-Options: DENY
# - X-Content-Type-Options: nosniff
# - X-XSS-Protection: 1; mode=block
```

### Step 4: Rate Limiting Test
```bash
# Test login rate limiting
for i in {1..10}; do curl -X POST https://yourdomain.com/api/users/login/ -d '{"email":"test","password":"test"}'; done

# Should get 429 Too Many Requests after 5 attempts
```

## 🚨 SECURITY INCIDENT RESPONSE

### Monitoring Commands
```bash
# Check for suspicious activity
python manage.py security_audit --days=1

# View recent security events
tail -f logs/security.log

# Check failed login attempts
python manage.py shell -c "from backend.security_monitoring import FailedLoginAttempt; print(FailedLoginAttempt.objects.filter(timestamp__gte=timezone.now()-timedelta(hours=24)).count())"
```

### Emergency Response
```bash
# Block suspicious IP
sudo ufw deny from <suspicious-ip>

# Clear all user sessions
python manage.py shell -c "from django.contrib.sessions.models import Session; Session.objects.all().delete()"

# Force password reset for all users (if needed)
python manage.py shell -c "from django.contrib.auth import get_user_model; User = get_user_model(); User.objects.update(password='!')"
```

## ✅ PRODUCTION READINESS CHECKLIST

### Security Configuration
- [ ] All default passwords changed
- [ ] Strong secret keys generated and configured
- [ ] DEBUG=False in production
- [ ] HTTPS enabled with valid SSL certificates
- [ ] Security headers configured
- [ ] Rate limiting enabled
- [ ] Database access secured
- [ ] Firewall configured

### Monitoring & Logging
- [ ] Security logging enabled
- [ ] Log rotation configured
- [ ] Automated security audits scheduled
- [ ] Monitoring alerts configured
- [ ] Backup procedures tested

### Compliance
- [ ] User consent collection implemented
- [ ] Data retention policies configured
- [ ] Privacy policy updated
- [ ] Audit logging enabled
- [ ] Data export functionality tested
- [ ] Data deletion procedures verified

### Performance & Reliability
- [ ] Load testing completed
- [ ] Backup and recovery tested
- [ ] Monitoring dashboards configured
- [ ] Error tracking enabled
- [ ] Performance monitoring active

**🎯 FINAL VERIFICATION**: Risk score should be < 20/100 after production deployment

**📞 SUPPORT**: Document all configuration changes and maintain security update schedule
