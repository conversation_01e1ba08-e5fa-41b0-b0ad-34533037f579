import os
import django
import random
from faker import Faker

# Set up Django environment
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'backend.settings')
django.setup()
from products.models import Category, Product

fake = Faker()

def create_categories():
    categories = []
    for _ in range(10):  # Creating 10 categories
        category_name = fake.word().capitalize()
        category, created = Category.objects.get_or_create(
            name=category_name,
            slug=fake.slug(category_name),
            description=fake.sentence(),
            is_active=True,
        )
        categories.append(category)
    return categories

def create_products(categories):
    for _ in range(100):  # Creating 100 products
        product_name = fake.sentence(nb_words=3)
        Product.objects.create(
            name=product_name,
            slug=fake.slug(product_name),
            description=fake.text(),
            category=random.choice(categories),
            price=round(random.uniform(5.0, 100.0), 2),
            stock=random.randint(1, 50),
            is_active=True,
        )

def populate_database():
    categories = create_categories()
    create_products(categories)
    print("Successfully populated the database with 10 categories and 100 products.")

if __name__ == "__main__":
    populate_database()
