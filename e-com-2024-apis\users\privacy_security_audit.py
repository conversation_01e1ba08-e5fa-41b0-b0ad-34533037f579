"""
Enhanced Security Audit System for Privacy Features
Monitors and reports on privacy-related security concerns
"""

import logging
from datetime import datetime, timedelta
from django.utils import timezone
from django.contrib.auth import get_user_model
from django.db.models import Count, Q
from .models import UserConsent, DataDeletionRequest
from backend.security_monitoring import log_security_event

User = get_user_model()
logger = logging.getLogger(__name__)

class PrivacySecurityAuditor:
    """Comprehensive security auditor for privacy features"""
    
    def __init__(self):
        self.high_risk_threshold = 10  # Number of suspicious activities
        self.medium_risk_threshold = 5
        self.audit_period_days = 30
    
    def run_full_audit(self):
        """Run complete privacy security audit"""
        audit_results = {
            'timestamp': timezone.now().isoformat(),
            'audit_period_days': self.audit_period_days,
            'findings': []
        }
        
        # Check for suspicious data export patterns
        export_findings = self._audit_data_exports()
        if export_findings:
            audit_results['findings'].extend(export_findings)
        
        # Check for unusual consent patterns
        consent_findings = self._audit_consent_patterns()
        if consent_findings:
            audit_results['findings'].extend(consent_findings)
        
        # Check for deletion request anomalies
        deletion_findings = self._audit_deletion_requests()
        if deletion_findings:
            audit_results['findings'].extend(deletion_findings)
        
        # Check for potential data breaches
        breach_findings = self._audit_potential_breaches()
        if breach_findings:
            audit_results['findings'].extend(breach_findings)
        
        # Generate risk assessment
        audit_results['risk_assessment'] = self._assess_overall_risk(audit_results['findings'])
        
        # Log audit results
        self._log_audit_results(audit_results)
        
        return audit_results
    
    def _audit_data_exports(self):
        """Audit data export patterns for suspicious activity"""
        findings = []
        cutoff_date = timezone.now() - timedelta(days=self.audit_period_days)
        
        # Check for excessive exports from single users
        from backend.security_monitoring import SecurityEvent
        
        try:
            export_events = SecurityEvent.objects.filter(
                event_type='DATA_ACCESS',
                timestamp__gte=cutoff_date,
                details__action='export'
            ).values('user').annotate(export_count=Count('id'))
            
            for event in export_events:
                if event['export_count'] > self.high_risk_threshold:
                    findings.append({
                        'type': 'EXCESSIVE_DATA_EXPORTS',
                        'severity': 'HIGH',
                        'user_id': event['user'],
                        'export_count': event['export_count'],
                        'description': f"User {event['user']} has exported data {event['export_count']} times in {self.audit_period_days} days"
                    })
                elif event['export_count'] > self.medium_risk_threshold:
                    findings.append({
                        'type': 'FREQUENT_DATA_EXPORTS',
                        'severity': 'MEDIUM',
                        'user_id': event['user'],
                        'export_count': event['export_count'],
                        'description': f"User {event['user']} has exported data {event['export_count']} times in {self.audit_period_days} days"
                    })
        except Exception as e:
            logger.error(f"Error auditing data exports: {str(e)}")
        
        return findings
    
    def _audit_consent_patterns(self):
        """Audit consent patterns for anomalies"""
        findings = []
        cutoff_date = timezone.now() - timedelta(days=self.audit_period_days)
        
        # Check for users who frequently change consent
        consent_changes = UserConsent.objects.filter(
            granted_at__gte=cutoff_date
        ).values('user').annotate(change_count=Count('id'))
        
        for change in consent_changes:
            if change['change_count'] > self.high_risk_threshold:
                findings.append({
                    'type': 'EXCESSIVE_CONSENT_CHANGES',
                    'severity': 'MEDIUM',
                    'user_id': change['user'],
                    'change_count': change['change_count'],
                    'description': f"User {change['user']} has changed consent {change['change_count']} times in {self.audit_period_days} days"
                })
        
        # Check for mass consent withdrawals (potential breach indicator)
        recent_withdrawals = UserConsent.objects.filter(
            withdrawn_at__gte=cutoff_date,
            withdrawn_at__isnull=False
        ).count()
        
        total_users = User.objects.filter(is_active=True).count()
        withdrawal_rate = (recent_withdrawals / total_users) * 100 if total_users > 0 else 0
        
        if withdrawal_rate > 20:  # More than 20% withdrawal rate
            findings.append({
                'type': 'MASS_CONSENT_WITHDRAWAL',
                'severity': 'HIGH',
                'withdrawal_count': recent_withdrawals,
                'withdrawal_rate': round(withdrawal_rate, 2),
                'description': f"Unusual mass consent withdrawal detected: {withdrawal_rate:.2f}% of users withdrew consent recently"
            })
        
        return findings
    
    def _audit_deletion_requests(self):
        """Audit deletion requests for patterns"""
        findings = []
        cutoff_date = timezone.now() - timedelta(days=self.audit_period_days)
        
        # Check for spike in deletion requests
        recent_deletions = DataDeletionRequest.objects.filter(
            request_date__gte=cutoff_date
        ).count()
        
        total_users = User.objects.filter(is_active=True).count()
        deletion_rate = (recent_deletions / total_users) * 100 if total_users > 0 else 0
        
        if deletion_rate > 10:  # More than 10% deletion rate
            findings.append({
                'type': 'SPIKE_IN_DELETION_REQUESTS',
                'severity': 'HIGH',
                'deletion_count': recent_deletions,
                'deletion_rate': round(deletion_rate, 2),
                'description': f"Unusual spike in deletion requests: {deletion_rate:.2f}% of users requested deletion recently"
            })
        
        return findings
    
    def _audit_potential_breaches(self):
        """Check for potential data breach indicators"""
        findings = []
        cutoff_date = timezone.now() - timedelta(days=7)  # Check last 7 days for breaches
        
        try:
            from backend.security_monitoring import SecurityEvent
            
            # Check for failed authentication attempts
            failed_auth_events = SecurityEvent.objects.filter(
                event_type='AUTHENTICATION_FAILURE',
                timestamp__gte=cutoff_date
            ).count()
            
            if failed_auth_events > 100:  # More than 100 failed attempts in a week
                findings.append({
                    'type': 'EXCESSIVE_FAILED_AUTH',
                    'severity': 'HIGH',
                    'failed_attempts': failed_auth_events,
                    'description': f"Excessive failed authentication attempts detected: {failed_auth_events} in the last 7 days"
                })
            
            # Check for unusual access patterns
            unusual_access = SecurityEvent.objects.filter(
                event_type='DATA_ACCESS',
                timestamp__gte=cutoff_date,
                details__suspicious=True
            ).count()
            
            if unusual_access > 0:
                findings.append({
                    'type': 'SUSPICIOUS_DATA_ACCESS',
                    'severity': 'HIGH',
                    'suspicious_access_count': unusual_access,
                    'description': f"Suspicious data access patterns detected: {unusual_access} events in the last 7 days"
                })
                
        except Exception as e:
            logger.error(f"Error auditing potential breaches: {str(e)}")
        
        return findings
    
    def _assess_overall_risk(self, findings):
        """Assess overall risk level based on findings"""
        high_severity_count = len([f for f in findings if f.get('severity') == 'HIGH'])
        medium_severity_count = len([f for f in findings if f.get('severity') == 'MEDIUM'])
        
        if high_severity_count > 0:
            risk_level = 'HIGH'
        elif medium_severity_count > 2:
            risk_level = 'MEDIUM'
        elif medium_severity_count > 0:
            risk_level = 'LOW'
        else:
            risk_level = 'MINIMAL'
        
        return {
            'level': risk_level,
            'high_severity_issues': high_severity_count,
            'medium_severity_issues': medium_severity_count,
            'total_issues': len(findings),
            'recommendation': self._get_risk_recommendation(risk_level)
        }
    
    def _get_risk_recommendation(self, risk_level):
        """Get recommendations based on risk level"""
        recommendations = {
            'HIGH': 'Immediate action required. Review all findings and implement security measures.',
            'MEDIUM': 'Monitor closely and consider implementing additional security measures.',
            'LOW': 'Continue regular monitoring. No immediate action required.',
            'MINIMAL': 'Privacy security is operating normally.'
        }
        return recommendations.get(risk_level, 'Unknown risk level')
    
    def _log_audit_results(self, audit_results):
        """Log audit results for record keeping"""
        risk_level = audit_results['risk_assessment']['level']
        total_issues = audit_results['risk_assessment']['total_issues']
        
        logger.info(f"Privacy Security Audit completed: Risk Level: {risk_level}, Issues Found: {total_issues}")
        
        # Log high-severity issues separately
        high_severity_issues = [f for f in audit_results['findings'] if f.get('severity') == 'HIGH']
        for issue in high_severity_issues:
            logger.warning(f"HIGH SEVERITY PRIVACY ISSUE: {issue['type']} - {issue['description']}")

def run_privacy_security_audit():
    """Convenience function to run privacy security audit"""
    auditor = PrivacySecurityAuditor()
    return auditor.run_full_audit()
