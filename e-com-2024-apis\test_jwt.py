#!/usr/bin/env python
"""
Test JWT configuration and token validation
"""

import os
import sys
import django
from django.conf import settings

# Setup Django
sys.path.append(os.path.dirname(os.path.abspath(__file__)))
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'backend.settings')
django.setup()

from rest_framework_simplejwt.tokens import AccessToken
from django.contrib.auth import get_user_model
import requests

User = get_user_model()

def test_jwt_settings():
    """Test JWT configuration"""
    print("=== JWT CONFIGURATION TEST ===")
    
    jwt_settings = getattr(settings, 'SIMPLE_JWT', {})
    print("JWT Settings:")
    for key, value in jwt_settings.items():
        if key == 'SIGNING_KEY':
            print(f"  {key}: {str(value)[:20]}...")
        else:
            print(f"  {key}: {value}")
    print()

def test_token_generation():
    """Test token generation and validation"""
    print("=== TOKEN GENERATION TEST ===")
    
    user = User.objects.first()
    if not user:
        print("No users found in database")
        return None
        
    print(f"Testing with user: {user.email} (ID: {user.id})")
    
    try:
        # Generate token
        token = AccessToken.for_user(user)
        token_str = str(token)
        print(f"Generated token: {token_str[:50]}...")
        
        # Try to validate the same token
        validated_token = AccessToken(token_str)
        print("Token validation: SUCCESS")
        print(f"User ID from token: {validated_token['user_id']}")
        
        return token_str
        
    except Exception as e:
        print(f"Token generation/validation failed: {e}")
        import traceback
        traceback.print_exc()
        return None

def test_api_with_token(token):
    """Test API endpoints with the generated token"""
    print("\n=== API ENDPOINT TEST ===")
    
    if not token:
        print("No token available for testing")
        return
    
    endpoints = [
        "http://localhost:8000/api/v1/users/detail/",
        "http://localhost:8000/api/v1/products/categories/",
    ]
    
    headers = {
        "Authorization": f"Bearer {token}",
        "Content-Type": "application/json"
    }
    
    for url in endpoints:
        try:
            print(f"\nTesting: {url}")
            response = requests.get(url, headers=headers, timeout=5)
            print(f"Status: {response.status_code}")
            
            if response.status_code == 200:
                print("✅ SUCCESS")
            else:
                print(f"❌ FAILED: {response.text[:200]}")
                
        except requests.exceptions.ConnectionError:
            print("❌ CONNECTION ERROR: Django server not running")
        except Exception as e:
            print(f"❌ ERROR: {e}")

def test_frontend_token():
    """Test with a token from the frontend logs"""
    print("\n=== FRONTEND TOKEN TEST ===")
    
    # This is the token from your frontend logs
    frontend_token = "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9"  # Add the full token here if you have it
    
    if len(frontend_token) < 50:
        print("Please add the full frontend token to test")
        return
    
    try:
        # Try to validate the frontend token
        validated_token = AccessToken(frontend_token)
        print("Frontend token validation: SUCCESS")
        print(f"User ID: {validated_token['user_id']}")
        
        # Test API with frontend token
        test_api_with_token(frontend_token)
        
    except Exception as e:
        print(f"Frontend token validation failed: {e}")

def main():
    print("🔍 JWT AUTHENTICATION DEBUG")
    print("=" * 50)
    
    test_jwt_settings()
    token = test_token_generation()
    test_api_with_token(token)
    test_frontend_token()
    
    print("\n" + "=" * 50)
    print("🔧 If tokens are valid but API calls fail:")
    print("1. Check Django server logs for authentication errors")
    print("2. Verify CORS settings allow the Authorization header")
    print("3. Check if any middleware is blocking the requests")

if __name__ == "__main__":
    main()
