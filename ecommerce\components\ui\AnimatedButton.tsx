import { ButtonHTMLAttributes } from "react";
import { cn } from "@/lib/utils";

interface AnimatedButtonProps extends ButtonHTMLAttributes<HTMLButtonElement> {
  variant?: "primary" | "secondary";
}

const AnimatedButton = ({
  children,
  className,
  variant = "primary",
  ...props
}: AnimatedButtonProps) => {
  return (
    <button
      className={cn(
        "neo-button px-6 py-3 rounded-lg font-medium transition-all duration-300",
        variant === "primary"
          ? "bg-neutral-900 text-white hover:bg-neutral-800"
          : "bg-white text-neutral-900 hover:bg-neutral-50",
        className
      )}
      {...props}
    >
      {children}
    </button>
  );
};

export default AnimatedButton;