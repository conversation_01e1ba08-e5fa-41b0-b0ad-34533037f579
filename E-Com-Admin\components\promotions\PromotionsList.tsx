import { useState, useEffect, useCallback } from "react";
import { Badge } from "@/components/ui/badge";
import { Button } from "@/components/ui/button";
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from "@/components/ui/table";
import { Edit2, Trash2, Refresh<PERSON>w } from "lucide-react";
import { PromotionDialog } from "./PromotionDialog";
import { usePromotions, type Promotion } from "@/hooks/usePromotions";
import { useToast } from "@/hooks/use-toast";

export const PromotionsList = () => {
  const [editingPromotion, setEditingPromotion] = useState<Promotion | null>(null);
  const [promotions, setPromotions] = useState<Promotion[]>([]);
  const { getPromotions, deletePromotion, loading, error } = usePromotions();
  const { toast } = useToast();

  // Fetch promotions on component mount


  const fetchPromotions = useCallback(async () => {
  const response = await getPromotions();
  if (response) {
    setPromotions(response.results);
  } else if (error) {
    toast({
      title: "Error",
      description: error,
      variant: "destructive",
    });
  }
}, [error, getPromotions, setPromotions, toast]);
  useEffect(() => {
    fetchPromotions();
  }, [fetchPromotions]);
  const handleDelete = async (code: string) => {
    if (window.confirm("Are you sure you want to delete this promotion?")) {
      const success = await deletePromotion(code);
      if (success) {
        toast({
          title: "Success",
          description: "Promotion deleted successfully",
        });
        fetchPromotions(); // Refresh the list
      } else if (error) {
        toast({
          title: "Error",
          description: error,
          variant: "destructive",
        });
      }
    }
  };

  const getStatusBadge = (promotion: Promotion) => {
    const now = new Date();
    const startDate = new Date(promotion.start_date);
    const endDate = new Date(promotion.end_date);

    if (!promotion.is_active) {
      return <Badge variant="secondary">Inactive</Badge>;
    } else if (now < startDate) {
      return <Badge variant="outline">Scheduled</Badge>;
    } else if (now > endDate) {
      return <Badge variant="secondary">Expired</Badge>;
    } else {
      return <Badge>Active</Badge>;
    }
  };

  const formatValue = (promotion: Promotion) => {
    const value = parseFloat(promotion.discount_value);
    return promotion.discount_type === "PERCENTAGE" ? `${value}%` : `$${value}`;
  };

  return (
    <>
      <div className="flex justify-between items-center mb-4">
        <h2 className="text-lg font-semibold">Promotions List</h2>
        <Button
          variant="outline"
          size="sm"
          onClick={fetchPromotions}
          disabled={loading}
        >
          <RefreshCw className={`h-4 w-4 mr-2 ${loading ? 'animate-spin' : ''}`} />
          Refresh
        </Button>
      </div>

      <div className="rounded-md border">
        <Table>
          <TableHeader>
            <TableRow>
              <TableHead>Code</TableHead>
              <TableHead>Description</TableHead>
              <TableHead>Discount</TableHead>
              <TableHead>Duration</TableHead>
              <TableHead>Usage</TableHead>
              <TableHead>Status</TableHead>
              <TableHead className="text-right">Actions</TableHead>
            </TableRow>
          </TableHeader>
          <TableBody>
            {loading ? (
              <TableRow>
                <TableCell colSpan={7} className="text-center py-8">
                  Loading promotions...
                </TableCell>
              </TableRow>
            ) : promotions?.length === 0 ? (
              <TableRow>
                <TableCell colSpan={7} className="text-center py-8">
                  No promotions found
                </TableCell>
              </TableRow>
            ) : (
              promotions?.map((promotion) => (
                <TableRow key={promotion.id}>
                  <TableCell className="font-medium">{promotion.code}</TableCell>
                  <TableCell className="max-w-xs truncate">{promotion.description}</TableCell>
                  <TableCell>{formatValue(promotion)}</TableCell>
                  <TableCell>
                    {new Date(promotion.start_date).toLocaleDateString()} -{" "}
                    {new Date(promotion.end_date).toLocaleDateString()}
                  </TableCell>
                  <TableCell>
                    {promotion.times_used}
                    {promotion.usage_limit && ` / ${promotion.usage_limit}`}
                  </TableCell>
                  <TableCell>{getStatusBadge(promotion)}</TableCell>
                  <TableCell className="text-right">
                    <div className="flex justify-end space-x-2">
                      <Button
                        variant="ghost"
                        size="icon"
                        onClick={() => setEditingPromotion(promotion)}
                      >
                        <Edit2 className="h-4 w-4" />
                      </Button>
                      <Button
                        variant="ghost"
                        size="icon"
                        onClick={() => handleDelete(promotion.code)}
                      >
                        <Trash2 className="h-4 w-4" />
                      </Button>
                    </div>
                  </TableCell>
                </TableRow>
              ))
            )}
          </TableBody>
        </Table>
      </div>

      <PromotionDialog
        open={editingPromotion !== null}
        onOpenChange={(open) => {
          if (!open) {
            setEditingPromotion(null);
          }
        }}
        promotion={editingPromotion}
        onSuccess={fetchPromotions}
      />
    </>
  );
};