// Use environment variable for API URL, fallback to localhost for development
export const MAIN_URL = process.env.NEXT_PUBLIC_API_URL || "http://localhost:8000";

const version = "/api/v1/";

// Product APIs
export const PRODUCTS = `${version}products/`;
export const PRODUCT_DETAIL_BY_ID = (id: number) => `${version}products/id/${id}/`;
export const PRODUCT_CREATE = `${version}products/create/`;
export const PRODUCT_UPDATE = (id: number) => `${version}products/${id}/update/`;
export const PRODUCT_DELETE = (id: number) => `${version}products/${id}/delete/`;
export const PRODUCT_IMAGES = (slug: number) => `${version}products/${slug}/images/`;
export const PRODUCT_IMAGE_UPLOAD = `${version}products/images/upload/`;
export const CATEGORIES = `${version}products/categories/`;
export const CATEGORY_CREATE = `${version}products/api/categories/create/`;
export const CATEGORY_UPDATE = (id: number) => `${version}products/api/categories/${id}/update/`;
export const SUBCATEGORIES = `${version}dashboard/subcategories/`;
export const SUBCATEGORIES_BY_CATEGORY = (categoryId: number) => `${version}dashboard/subcategories/?category=${categoryId}`;
export const BRANDS = `${version}products/brands/`;
export const GST_RATES = `${version}dashboard/gst-rates/`;
export const FUTURED_PRODUCTS = `${version}products/feature/products/`;

// User Authentication APIs
export const USER_SIGNUP = `${version}users/`;
export const USER_LOGIN = `${version}users/login/`;
export const ADMIN_LOGIN = `${version}users/admin/login/`;
export const USER_LOGOUT = `${version}users/logout/`;
export const USER_SOCIAL_LOGIN = `${version}users/social/login/`;
export const TOKEN_REFFRESH = `${version}users/token/refresh/`;
export const USER_DETAIL = `${version}users/detail/`;
export const USER_REFFRESH_BLACKLIST = `${version}users/token/blacklist/`;

// User Management APIs
export const USER_ADDRESS = `${version}users/addresses/`;
export const ADD_TO_WISHLIST = `${version}users/wishlist/`;
export const REMOVE_FROM_WISHLIST = `${version}users/remove/wishlist/`;

// Order Management APIs
export const USER_CART = `${version}orders/cart/`;
export const ADD_TO_CART = `${version}orders/cart/add-item/`;
export const UPDATE_CART = `${version}orders/cart/update-item/`;
export const ORDERS = `${version}orders/`;

// Dashboard & Analytics APIs
export const DASHBOARD_ANALYTICS = `${version}dashboard/analytics/`;
export const DASHBOARD_ANALYTICS_EXPORT = `${version}dashboard/analytics/export/`;
export const USER_STATISTICS = `${version}users/statistics/`;
export const USER_GRAPH_DATA = `${version}users/graph-data/`;

// Admin Management APIs
export const CUSTOMERS_LIST = `${version}users/customers/`;
export const ADMIN_ORDERS = `${version}orders/admin/orders/`;
export const ADMIN_CATEGORIES = `${version}products/admin/categories/`;

// Single Order Management APIs
export const ORDER_DETAIL = (orderId: string) => `${version}orders/${orderId}/`;
export const ORDER_UPDATE_STATUS = (orderId: string) => `${version}orders/${orderId}/update-status/`;
export const ORDER_UPDATE_TRACKING = (orderId: string) => `${version}orders/${orderId}/update-tracking/`;
export const ORDER_REFUND = (orderId: string) => `${version}orders/${orderId}/refund/`;

// Privacy & Compliance APIs
export const PRIVACY_DASHBOARD = `${version}users/privacy/dashboard/`;
export const PRIVACY_METRICS = `${version}users/privacy/metrics/`;
export const PRIVACY_REPORT = `${version}users/privacy/report/`;
export const PRIVACY_AUDIT = `${version}users/privacy/audit/`;
export const PRIVACY_HEALTH = `${version}users/privacy/health/`;

// Admin Security Management APIs
export const ADMIN_IP_MANAGEMENT = `${version}users/admin/security/ip-management/`;
export const ADMIN_IP_MANAGEMENT_DETAIL = (ipId: number) => `${version}users/admin/security/ip-management/${ipId}/`;
export const ADMIN_IP_HISTORY = `${version}users/admin/security/ip-history/`;
export const ADMIN_SECURITY_EVENTS = `${version}users/admin/security/events/`;
export const ADMIN_SECURITY_EVENTS_DETAIL = (eventId: number) => `${version}users/admin/security/events/${eventId}/`;
export const ADMIN_SECURITY_THREATS = `${version}users/admin/security/threats/`;
export const ADMIN_SECURITY_THREATS_DETAIL = (threatId: number) => `${version}users/admin/security/threats/${threatId}/`;
export const ADMIN_DELETION_REQUESTS = `${version}users/admin/privacy/deletion-requests/`;
export const ADMIN_DELETION_REQUESTS_DETAIL = (requestId: number) => `${version}users/admin/privacy/deletion-requests/${requestId}/`;
export const ADMIN_BULK_OPERATIONS = `${version}users/admin/bulk-operations/`;
export const ADMIN_AUDIT_TRAIL = `${version}users/admin/audit-trail/`;
export const ADMIN_COMPLIANCE_REPORT = `${version}users/admin/compliance-report/`;

// Payment Gateway APIs
export const PAYMENT_PHONEPE_INITIATE = `${version}payments/phonepe/initiate/`;

// Payment Management APIs (Admin Dashboard)
export const PAYMENT_ANALYTICS = `${version}dashboard/payments/analytics/`;
export const PAYMENT_LIST = `${version}dashboard/payments/`;

// Promotion APIs
export const PROMOTIONS = `${version}promotions/`;
export const PROMOTION_USAGE = `${version}promotions/usages/detail/`;
export const PROMOCODE_APPLY = `${version}promotions/apply/code/`;
export const GET_PROMO_CODE = `${version}promotions/get/single/promotion/`;

// Shipping & Additional APIs
export const SHIPPING_METHODS = `${version}orders/shipping-methods/`;

// User Profile APIs
export const PROFILE_UPDATE = `${version}users/profile/update/`;
export const CONTACT_FORM = `${version}users/contact/`;
export const FORGOT_PASSWORD = `${version}users/forgot-password/`;
export const RESET_PASSWORD = `${version}users/reset-password/`;

// Order Status Constants - Must match backend ORDER_STATUS_CHOICES
export const ORDER_STATUS = {
  PENDING: 'PENDING',
  PROCESSING: 'PROCESSING',
  PAID: 'PAID',
  SHIPPED: 'SHIPPED',
  DELIVERED: 'DELIVERED',
  CANCELLED: 'CANCELLED',
  REFUNDED: 'REFUNDED',
  PAYMENT_FAILED: 'PAYMENT_FAILED',
} as const;

export const ORDER_STATUS_CHOICES = [
  { value: ORDER_STATUS.PENDING, label: 'Pending' },
  { value: ORDER_STATUS.PROCESSING, label: 'Processing' },
  { value: ORDER_STATUS.PAID, label: 'Paid' },
  { value: ORDER_STATUS.SHIPPED, label: 'Shipped' },
  { value: ORDER_STATUS.DELIVERED, label: 'Delivered' },
  { value: ORDER_STATUS.CANCELLED, label: 'Cancelled' },
  { value: ORDER_STATUS.REFUNDED, label: 'Refunded' },
] as const;

export type OrderStatusType = typeof ORDER_STATUS[keyof typeof ORDER_STATUS];