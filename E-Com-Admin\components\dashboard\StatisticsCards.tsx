"use client";

import { <PERSON>, <PERSON><PERSON>ontent, <PERSON><PERSON><PERSON><PERSON>, CardTitle } from "@/components/ui/card";

import { Skeleton } from "@/components/ui/skeleton";
import {
  Users,
  ShoppingBag,
  Package,
  ArrowUpIcon,
  ArrowDownIcon,
  IndianRupee
} from "lucide-react";
import { cn } from "@/lib/utils";

interface StatisticsData {
  last_30_days: {
    total_revenue: number;
    new_customers: number;
    total_orders: number;
  };
  previous_30_days: {
    total_revenue: number;
    new_customers: number;
    total_orders: number;
  };
  percentage_delta: {
    revenue_delta: number | null;
    new_customers_delta: number | null;
    total_orders_delta: number | null;
  };
  active_products: number;
}

interface StatisticsCardsProps {
  data: StatisticsData | null;
  loading: boolean;
  error: string | null;
}

const formatCurrency = (amount: number) => {
  return new Intl.NumberFormat('en-IN', {
    style: 'currency',
    currency: 'INR',
    minimumFractionDigits: 0,
    maximumFractionDigits: 0,
  }).format(amount);
};

const formatNumber = (num: number) => {
  return new Intl.NumberFormat('en-US').format(num);
};

const DeltaIndicator = ({ delta, className }: { delta: number | null; className?: string }) => {
  if (delta === null) return <span className="text-xs text-muted-foreground">No data</span>;

  const isPositive = delta > 0;
  const isNeutral = delta === 0;

  return (
    <div className={cn("flex items-center gap-1 text-xs", className)}>
      {!isNeutral && (
        isPositive ? (
          <ArrowUpIcon className="h-3 w-3 text-green-500 flex-shrink-0" />
        ) : (
          <ArrowDownIcon className="h-3 w-3 text-red-500 flex-shrink-0" />
        )
      )}
      <span className={cn(
        "truncate",
        isPositive ? "text-green-600" : isNeutral ? "text-muted-foreground" : "text-red-600"
      )}>
        <span className="sm:hidden">
          {isPositive ? "+" : ""}{delta.toFixed(1)}%
        </span>
        <span className="hidden sm:inline">
          {isPositive ? "+" : ""}{delta.toFixed(1)}% from last month
        </span>
      </span>
    </div>
  );
};

const StatisticCard = ({
  title,
  value,
  delta,
  icon: Icon,
  loading,
  formatter = formatNumber,
  iconColor = "text-muted-foreground"
}: {
  title: string;
  value: number;
  delta: number | null;
  // eslint-disable-next-line @typescript-eslint/no-explicit-any
  icon: any;
  loading: boolean;
  formatter?: (value: number) => string;
  iconColor?: string;
}) => (
  <Card className="relative overflow-hidden transition-all duration-200 hover:shadow-lg border-0 bg-gradient-to-br from-white to-gray-50/50 min-h-[120px] sm:min-h-[140px]">
    <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2 px-4 sm:px-6 pt-4 sm:pt-6">
      <CardTitle className="text-xs sm:text-sm font-medium text-gray-600 truncate pr-2">{title}</CardTitle>
      <div className={cn("h-4 w-4 sm:h-5 sm:w-5 transition-colors flex-shrink-0", iconColor)}>
        <Icon className="h-full w-full" />
      </div>
    </CardHeader>
    <CardContent className="px-4 sm:px-6 pb-4 sm:pb-6">
      {loading ? (
        <div className="space-y-2">
          <Skeleton className="h-6 sm:h-8 w-20 sm:w-24" />
          <Skeleton className="h-3 sm:h-4 w-24 sm:w-32" />
        </div>
      ) : (
        <>
          <div className="text-lg sm:text-2xl font-bold text-gray-900 mb-1 break-all">
            {formatter(value)}
          </div>
          <DeltaIndicator delta={delta} />
        </>
      )}
    </CardContent>
    <div className="absolute top-0 right-0 w-16 h-16 sm:w-20 sm:h-20 bg-gradient-to-br from-blue-500/5 to-purple-500/5 rounded-full -translate-y-8 sm:-translate-y-10 translate-x-8 sm:translate-x-10" />
  </Card>
);

export const StatisticsCards: React.FC<StatisticsCardsProps> = ({ data, loading, error }) => {
  if (error) {
    return (
      <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-4">
        {[1, 2, 3, 4].map((i) => (
          <Card key={i} className="border-red-200 bg-red-50/50">
            <CardContent className="pt-6">
              <div className="text-center text-red-600">
                <p className="text-sm">Failed to load data</p>
              </div>
            </CardContent>
          </Card>
        ))}
      </div>
    );
  }

  return (
    <div className="grid gap-3 sm:gap-4 grid-cols-1 sm:grid-cols-2 lg:grid-cols-4">
      <StatisticCard
        title="Total Revenue"
        value={data?.last_30_days.total_revenue || 0}
        delta={data?.percentage_delta.revenue_delta || null}
        icon={IndianRupee}
        loading={loading}
        formatter={formatCurrency}
        iconColor="text-green-500"
      />

      <StatisticCard
        title="New Customers"
        value={data?.last_30_days.new_customers || 0}
        delta={data?.percentage_delta.new_customers_delta || null}
        icon={Users}
        loading={loading}
        iconColor="text-blue-500"
      />

      <StatisticCard
        title="Total Orders"
        value={data?.last_30_days.total_orders || 0}
        delta={data?.percentage_delta.total_orders_delta || null}
        icon={ShoppingBag}
        loading={loading}
        iconColor="text-purple-500"
      />

      <StatisticCard
        title="Active Products"
        value={data?.active_products || 0}
        delta={null}
        icon={Package}
        loading={loading}
        iconColor="text-orange-500"
      />
    </div>
  );
};
