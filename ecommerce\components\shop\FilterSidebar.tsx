import { Sheet, SheetContent } from "../../components/ui/sheet";
import { Slider } from "../../components/ui/slider";
import { Checkbox } from "../../components/ui/checkbox";
import { Label } from "../../components/ui/label";
import { Button } from "../../components/ui/button";
import { Star } from "lucide-react";
import { BRANDS, CATEGORIES, MAIN_URL } from "../../constant/urls";
import { useEffect, useState } from "react";
import useApi from "../../hooks/useApi";
import { ProductFilterLoading } from "../ui/loading/ProductFilterLoading";
import { DualRangeSlider } from "../ui/DualSlider";

interface FilterSidebarProps {
  filters: {
    priceRange: number[];
    categories: string[];
    brands: string[];
    rating: number;
  };
  onFiltersChange: (filters: FilterSidebarProps["filters"]) => void;
  isOpen: boolean;
  onClose: () => void;
  onPageChange: (page: number) => void
}

export const FilterSidebar = ({
  filters,
  onFiltersChange,
  isOpen,
  onClose,
  onPageChange
}: FilterSidebarProps) => {
  // const categories = ["Electronics", "Audio", "Computers", "Accessories"];
  // const brands = ["Apple", "Samsung", "Sony", "Dell"];
  const { read } = useApi(MAIN_URL);
  // const categories = Array.isArray(data) ? data : []
  const [categories, setCategories] = useState<any>([]);
  const [brands, setBrands] = useState<any>([]);
  const [loading, setLoading] = useState<boolean>(false);
  // Create temporary state for filters
  const [tempFilters, setTempFilters] = useState(filters);

  // Update temporary filters when props change
  useEffect(() => {
    setTempFilters(filters);
  }, [filters]);

  const updatePriceRange = (value: number[]) => {
    setTempFilters({ ...tempFilters, priceRange: value });
  };

  const toggleCategory = (category: string) => {
    const newCategories = tempFilters.categories.includes(category)
      ? tempFilters.categories.filter((c) => c !== category)
      : [...tempFilters.categories, category];
    setTempFilters({ ...tempFilters, categories: newCategories });
  };

  const toggleBrand = (brand: string) => {
    const newBrands = tempFilters.brands.includes(brand)
      ? tempFilters.brands.filter((b) => b !== brand)
      : [...tempFilters.brands, brand];
    setTempFilters({ ...tempFilters, brands: newBrands });
  };

  const handleSave = () => {
    onFiltersChange(tempFilters);
    onPageChange(1);
    onClose();
  };

  const getCategories = async () => {
    const res = await read(CATEGORIES);
    if (Array.isArray(res)) {
      setCategories(res);
    }
  };

  const getBrands = async () => {
    const res = await read(BRANDS);
    if (Array.isArray(res)) {
      setBrands(res);
    }
  };

  const getBrandCategory = async () => {
    setLoading(true);
    await getCategories();
    await getBrands();
    setLoading(false);
  };

  useEffect(() => {
    getBrandCategory();
  }, []);

  const content = (
    <div className="space-y-6">
      <div className="mb-4">
        <h3 className="font-semibold mb-4">Price Range</h3>
        <DualRangeSlider
          label={() => <>₹</>}
          lableContenPos={"left"}
          labelPosition="bottom"
          value={tempFilters.priceRange}
          onValueChange={updatePriceRange}
          min={0}
          max={10000}
          step={1}
        />
        {/* <Slider
          defaultValue={[0, 10000]}
          max={1000}
          step={10}
          value={tempFilters.priceRange}
          onValueChange={updatePriceRange}
        /> */}
        {/* <div className="flex justify-between mt-2 text-sm text-muted-foreground">
          <span>₹{tempFilters.priceRange[0]}</span>
          <span>₹{tempFilters.priceRange[1]}</span>
        </div> */}
      </div>

      {/* Only show Categories section if there are categories */}
      {Array.isArray(categories) && categories.length > 0 && (
        <div>
          <h3 className="font-semibold mb-4">Categories</h3>
          <div className="space-y-2 h-[400px] overflow-y-scroll">
            {categories.map(({ id, name }) => (
              <div key={id} className="flex items-center space-x-2 p-1.5 rounded-md hover:bg-gray-100 transition-colors duration-200">
                <Checkbox
                  id={`category-${id}`}
                  checked={tempFilters.categories.includes(id)}
                  onCheckedChange={() => toggleCategory(id)}
                  className="h-4 w-4"
                />
                <Label htmlFor={`category-${id}`} className="cursor-pointer text-sm sm:text-base truncate">{name}</Label>
              </div>
            ))}
          </div>
        </div>
      )}

      {/* Enhanced Brands section with logos */}
      {Array.isArray(brands) && brands.length > 0 && (
        <div>
          <h3 className="font-semibold mb-4">Brands</h3>
          <div className="space-y-3 max-h-[300px] overflow-y-auto pr-2">
            {brands.map(({ id, name, image, image_url }) => (
              <div key={id} className="flex items-center space-x-3 p-1.5 rounded-md hover:bg-gray-100 transition-colors duration-200">
                <Checkbox
                  id={`brand-${id}`}
                  checked={tempFilters.brands.includes(id)}
                  onCheckedChange={() => toggleBrand(id)}
                  className="h-4 w-4"
                />
                <div className="flex items-center flex-1">
                  {(image || image_url) && (
                    <div className="mr-2 w-7 h-7 sm:w-8 sm:h-8 rounded-md border border-gray-200 shadow-sm bg-white flex items-center justify-center p-1 overflow-hidden flex-shrink-0">
                      <img
                        src={image_url || `${MAIN_URL}${image}`}
                        alt={`${name} logo`}
                        className="max-w-full max-h-full object-contain"
                        onError={(e) => {
                          // Hide the image on error
                          const imgElement = e.currentTarget as HTMLImageElement;
                          imgElement.style.display = 'none';
                        }}
                      />
                    </div>
                  )}
                  <Label htmlFor={`brand-${id}`} className="cursor-pointer text-sm sm:text-base truncate">{name}</Label>
                </div>
              </div>
            ))}
          </div>
        </div>
      )}

      {/* <div>
        <h3 className="font-semibold mb-4">Rating</h3>
        <div className="space-y-2">
          {[4, 3, 2, 1].map((rating) => (
            <div key={rating} className="flex items-center space-x-2">
              <Checkbox
                id={`rating-${rating}`}
                checked={tempFilters.rating === rating}
                onCheckedChange={() =>
                  setTempFilters({ ...tempFilters, rating })
                }
              />
              <Label htmlFor={`rating-${rating}`} className="flex items-center">
                {Array.from({ length: rating }).map((_, i) => (
                  <Star
                    key={i}
                    className="h-4 w-4 fill-yellow-400 text-yellow-400"
                  />
                ))}
                <span className="ml-1">& up</span>
              </Label>
            </div>
          ))}
        </div>
      </div> */}

      <Button onClick={handleSave} className="w-full">
        Save Filters
      </Button>
    </div>
  );

  return (
    <>
      {/* Desktop sidebar */}
      <div className="hidden lg:block w-64 flex-shrink-0">
        {loading ? <ProductFilterLoading /> : content}
      </div>

      {/* Mobile sidebar */}
      <Sheet open={isOpen} onOpenChange={onClose}>
        <SheetContent
          side="left"
          className="w-full sm:w-[300px] overflow-y-scroll"
        >
          {loading ? <ProductFilterLoading /> : content}
        </SheetContent>
      </Sheet>
    </>
  );
};
