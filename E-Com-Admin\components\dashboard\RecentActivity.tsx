"use client";

import { <PERSON>, <PERSON><PERSON>ontent, <PERSON><PERSON><PERSON><PERSON>, <PERSON>Title } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { Button } from "@/components/ui/button";
import { Skeleton } from "@/components/ui/skeleton";
import {
  Activity,
  ShoppingCart,
  Users,
  Package,
  Eye,
  ArrowRight
} from "lucide-react";
import { cn } from "@/lib/utils";
import { useRouter } from "next/navigation";

interface RecentOrder {
  id: string;
  customer_name: string;
  total: number;
  status: string;
  created_at: string;
  items_count: number;
}

interface RecentActivityProps {
  recentOrders: RecentOrder[];
  loading: boolean;
  error: string | null;
}

const getStatusColor = (status: string) => {
  switch (status.toLowerCase()) {
    case 'pending':
      return 'bg-yellow-100 text-yellow-800 border-yellow-200';
    case 'processing':
      return 'bg-blue-100 text-blue-800 border-blue-200';
    case 'shipped':
      return 'bg-purple-100 text-purple-800 border-purple-200';
    case 'delivered':
      return 'bg-green-100 text-green-800 border-green-200';
    case 'cancelled':
      return 'bg-red-100 text-red-800 border-red-200';
    default:
      return 'bg-gray-100 text-gray-800 border-gray-200';
  }
};

const formatDate = (dateString: string) => {
  const date = new Date(dateString);
  const now = new Date();
  const diffInHours = Math.floor((now.getTime() - date.getTime()) / (1000 * 60 * 60));
  
  if (diffInHours < 1) {
    return 'Just now';
  } else if (diffInHours < 24) {
    return `${diffInHours}h ago`;
  } else {
    const diffInDays = Math.floor(diffInHours / 24);
    return `${diffInDays}d ago`;
  }
};

const ActivitySkeleton = () => (
  <div className="space-y-4">
    {[1, 2, 3, 4, 5].map((i) => (
      <div key={i} className="flex items-center space-x-4 p-3">
        <Skeleton className="h-10 w-10 rounded-full" />
        <div className="space-y-2 flex-1">
          <Skeleton className="h-4 w-3/4" />
          <Skeleton className="h-3 w-1/2" />
        </div>
        <Skeleton className="h-6 w-16" />
      </div>
    ))}
  </div>
);

export const RecentActivity: React.FC<RecentActivityProps> = ({
  recentOrders,
  loading,
  error
}) => {
  const router = useRouter();

  const handleViewAllOrders = () => {
    router.push('/orders');
  };

  if (error) {
    return (
      <Card className="border-red-200 bg-red-50/50">
        <CardContent className="pt-6">
          <div className="text-center text-red-600">
            <p className="text-sm">Failed to load recent activity</p>
          </div>
        </CardContent>
      </Card>
    );
  }

  return (
    <Card className="border-0 shadow-lg bg-gradient-to-br from-white to-gray-50/50">
      <CardHeader className="flex flex-col sm:flex-row sm:items-center sm:justify-between space-y-2 sm:space-y-0 pb-3 sm:pb-6">
        <div className="min-w-0 flex-1">
          <CardTitle className="flex items-center gap-2 text-gray-900 text-lg sm:text-xl">
            <Activity className="h-4 w-4 sm:h-5 sm:w-5 text-blue-500" />
            <span className="truncate">Recent Activity</span>
          </CardTitle>
          <p className="text-xs sm:text-sm text-muted-foreground mt-1">
            Latest orders and customer activities
          </p>
        </div>
        <Button
          variant="ghost"
          size="sm"
          className="text-blue-600 hover:text-blue-700 text-xs sm:text-sm self-start sm:self-auto"
          onClick={handleViewAllOrders}
        >
          <span className="hidden sm:inline">View All</span>
          <span className="sm:hidden">All</span>
          <ArrowRight className="h-3 w-3 sm:h-4 sm:w-4 ml-1" />
        </Button>
      </CardHeader>
      <CardContent>
        {loading ? (
          <ActivitySkeleton />
        ) : recentOrders.length === 0 ? (
          <div className="text-center py-6 sm:py-8 text-gray-500">
            <ShoppingCart className="h-10 w-10 sm:h-12 sm:w-12 mx-auto mb-3 sm:mb-4 text-gray-300" />
            <p className="text-xs sm:text-sm">No recent orders found</p>
          </div>
        ) : (
          <div className="space-y-3 sm:space-y-4">
            {recentOrders.slice(0, 6).map((order) => (
              <div
                key={order.id}
                className="flex flex-col sm:flex-row sm:items-center sm:justify-between p-3 sm:p-4 rounded-lg border border-gray-100 hover:border-gray-200 hover:shadow-sm transition-all duration-200 bg-white/50 space-y-2 sm:space-y-0"
              >
                <div className="flex items-center space-x-3 min-w-0 flex-1">
                  <div className="h-8 w-8 sm:h-10 sm:w-10 rounded-full bg-gradient-to-br from-blue-500 to-purple-600 flex items-center justify-center flex-shrink-0">
                    <ShoppingCart className="h-4 w-4 sm:h-5 sm:w-5 text-white" />
                  </div>
                  <div className="min-w-0 flex-1">
                    <div className="flex flex-col sm:flex-row sm:items-center gap-1 sm:gap-2">
                      <p className="font-medium text-gray-900 text-sm truncate">
                        Order #{order.id.slice(-6)}
                      </p>
                      <Badge
                        variant="outline"
                        className={cn("text-xs self-start sm:self-auto", getStatusColor(order.status))}
                      >
                        {order.status}
                      </Badge>
                    </div>
                    <div className="flex flex-col sm:flex-row sm:items-center gap-1 sm:gap-2 text-xs text-gray-500 mt-1">
                      <div className="flex items-center gap-1">
                        <Users className="h-3 w-3" />
                        <span className="truncate">{order.customer_name}</span>
                      </div>
                      <span className="hidden sm:inline">•</span>
                      <div className="flex items-center gap-1">
                        <Package className="h-3 w-3" />
                        <span>{order.items_count} items</span>
                      </div>
                    </div>
                  </div>
                </div>

                <div className="flex justify-between sm:block sm:text-right flex-shrink-0">
                  <p className="font-semibold text-gray-900 text-sm sm:text-base">
                    ₹{Number(order?.total).toFixed(2)}
                  </p>
                  <p className="text-xs text-gray-500">
                    {formatDate(order.created_at)}
                  </p>
                </div>
              </div>
            ))}
          </div>
        )}

        {!loading && recentOrders.length > 6 && (
          <div className="mt-3 sm:mt-4 pt-3 sm:pt-4 border-t border-gray-100">
            <Button
              variant="outline"
              className="w-full text-xs sm:text-sm h-8 sm:h-10"
              size="sm"
              onClick={handleViewAllOrders}
            >
              <Eye className="h-3 w-3 sm:h-4 sm:w-4 mr-2" />
              <span className="hidden sm:inline">View {recentOrders.length - 6} More Orders</span>
              <span className="sm:hidden">+{recentOrders.length - 6} More</span>
            </Button>
          </div>
        )}
      </CardContent>
    </Card>
  );
};
