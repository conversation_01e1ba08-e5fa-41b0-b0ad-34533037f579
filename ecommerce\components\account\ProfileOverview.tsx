import {
  <PERSON><PERSON>,
  AvatarFallback,
  AvatarImage,
} from "../../components/ui/avatar";
import { But<PERSON> } from "../../components/ui/button";
import {
  Sheet,
  <PERSON>et<PERSON>ontent,
  <PERSON><PERSON><PERSON>eader,
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
  SheetTrigger,
} from "@/components/ui/sheet";
import { Card, CardContent } from "../../components/ui/card";
import { Pencil } from "lucide-react";
import EditProfileForm from "./EditProfileForm";
import { useState } from "react";
import "../../styles/profile.style.css";
import { MAIN_URL } from "@/constant/urls";

const dummyData = {
  name: "",
  email: "",
  phone_number: "",
  date_of_birth: "",
};

export const ProfileOverview = (props: any) => {
  const [open, setOpen] = useState(false);
  const [user, setUser] = useState(props ?? dummyData);

  const { name, email, phone_number, date_of_birth, display_image_url }: any =
    user ?? dummyData;

  return (
    <Card className="border-0 shadow-md hover:shadow-lg transition-all duration-300 overflow-hidden rounded-2xl bg-white">
      <CardContent className="flex items-center md:flex-row flex-col gap-8 p-8">
        <div className="relative">
          <Avatar className="h-28 w-28 border-4 border-white shadow-lg">
            <AvatarImage
              src={
                display_image_url
                  ? MAIN_URL + display_image_url
                  : "user/avatar-photo.jpg"
              }
              alt={name}
              loading="lazy"
              className="object-cover"
            />
            <AvatarFallback className="bg-gradient-to-br from-blue-500 to-purple-600 text-white text-xl">
              {name ? name.charAt(0).toUpperCase() : "U"}
            </AvatarFallback>
          </Avatar>
        </div>

        <div className="flex-1 flex items-center md:items-start flex-col space-y-1.5">
          <h2 className="text-xl sm:text-2xl font-bold text-gray-800">{name || "Welcome"}</h2>
          <div className="space-y-1 mt-1">
            {email && (
              <p className="text-gray-600 flex items-center gap-2">
                <svg xmlns="http://www.w3.org/2000/svg" className="h-4 w-4 text-gray-400" viewBox="0 0 20 20" fill="currentColor">
                  <path d="M2.003 5.884L10 9.882l7.997-3.998A2 2 0 0016 4H4a2 2 0 00-1.997 1.884z" />
                  <path d="M18 8.118l-8 4-8-4V14a2 2 0 002 2h12a2 2 0 002-2V8.118z" />
                </svg>
                {email}
              </p>
            )}
            {phone_number && (
              <p className="text-gray-600 flex items-center gap-2">
                <svg xmlns="http://www.w3.org/2000/svg" className="h-4 w-4 text-gray-400" viewBox="0 0 20 20" fill="currentColor">
                  <path d="M2 3a1 1 0 011-1h2.153a1 1 0 01.986.836l.74 4.435a1 1 0 01-.54 1.06l-1.548.773a11.037 11.037 0 006.105 6.105l.774-1.548a1 1 0 011.059-.54l4.435.74a1 1 0 01.836.986V17a1 1 0 01-1 1h-2C7.82 18 2 12.18 2 5V3z" />
                </svg>
                {phone_number}
              </p>
            )}
            {date_of_birth && (
              <p className="text-gray-600 flex items-center gap-2">
                <svg xmlns="http://www.w3.org/2000/svg" className="h-4 w-4 text-gray-400" viewBox="0 0 20 20" fill="currentColor">
                  <path fillRule="evenodd" d="M6 2a1 1 0 00-1 1v1H4a2 2 0 00-2 2v10a2 2 0 002 2h12a2 2 0 002-2V6a2 2 0 00-2-2h-1V3a1 1 0 10-2 0v1H7V3a1 1 0 00-1-1zm0 5a1 1 0 000 2h8a1 1 0 100-2H6z" clipRule="evenodd" />
                </svg>
                {date_of_birth}
              </p>
            )}
          </div>
        </div>

        <Sheet open={open} onOpenChange={setOpen}>
          <SheetTrigger asChild>
            <Button
              variant="outline"
              className="flex items-center gap-2 bg-gradient-to-r from-blue-50 to-indigo-50 border border-blue-200 text-blue-700 hover:bg-blue-100 hover:text-blue-800 transition-all duration-300 px-5 py-2 rounded-lg shadow-sm"
            >
              <Pencil className="h-4 w-4" />
              Edit Profile
            </Button>
          </SheetTrigger>
          <SheetContent>
            <SheetHeader>
              <SheetTitle>Edit Profile</SheetTitle>
            </SheetHeader>
            <div className="h-full overflow-y-scroll py-6">
              <EditProfileForm user={user} setUser={setUser} onClose={() => setOpen(false)} />
            </div>
          </SheetContent>
        </Sheet>
      </CardContent>
    </Card>
  );
};
