# PhonePe Payment Gateway Integration

This module integrates PhonePe payment gateway with the e-commerce platform using PhonePe's official Python SDK.

## Features

- Initiate payments through PhonePe
- Handle payment callbacks
- Process webhook notifications
- Track payment status

## Configuration

The following environment variables need to be set in the `.env` file:

```
PHONEPE_CLIENT_ID=your-client-id
PHONEPE_CLIENT_SECRET=your-client-secret
PHONEPE_CALLBACK_URL=https://your-domain.com/api/v1/payments/phonepe/callback/
PHONEPE_ENVIRONMENT=UAT  # UAT for testing, PRODUCTION for production
FRONTEND_URL=https://your-frontend-domain.com
```

For production, update the environment variables accordingly:

```
PHONEPE_ENVIRONMENT=PRODUCTION
```

## API Endpoints

### 1. Initiate Payment

**Endpoint:** `POST /api/v1/payments/phonepe/initiate/<order_id>/`

**Authentication:** Required

**Description:** Initiates a payment for the specified order using PhonePe.

**Response:**
```json
{
  "transaction_id": "TX_1234567890abcdef_1620000000",
  "payment_url": "https://pay.phonepe.com/checkout/1234567890abcdef"
}
```

### 2. Payment Callback

**Endpoint:** `GET /api/v1/payments/phonepe/callback/`

**Authentication:** Not required

**Description:** Handles the callback from PhonePe after payment completion. The user is redirected to this URL after completing the payment.

**Query Parameters:**
- `order_id`: The ID of the order

**Behavior:** Redirects to the frontend success or failure page based on the payment status.

### 3. Payment Webhook

**Endpoint:** `POST /api/v1/payments/phonepe/webhook/`

**Authentication:** Not required (validated using PhonePe's signature verification)

**Description:** Receives webhook notifications from PhonePe about payment status changes.

**Headers:**
- `Authorization`: PhonePe's signature for webhook validation

**Request Body:** PhonePe webhook payload

**Response:**
```json
{
  "status": "success"
}
```

## Implementation Details

### PhonePe Service

The `PhonePeService` class in `services.py` handles all interactions with the PhonePe SDK:

- Initializing the PhonePe client
- Generating transaction IDs
- Initiating payments
- Checking payment status
- Validating webhook data

### Database Models

The integration uses the following model fields:

**Order Model:**
- `phonepe_transaction_id`: Stores the PhonePe transaction ID
- `phonepe_payment_url`: Stores the payment URL for redirection

**Payment Model:**
- `payment_method`: Includes 'PHONEPE' as an option
- `phonepe_transaction_details`: Stores the complete transaction details from PhonePe

## Testing

To test the integration:

1. Set up the environment variables with test credentials
2. Create an order through the API
3. Initiate a payment for the order
4. Complete the payment on the PhonePe test page
5. Verify that the callback and webhook are processed correctly

## Troubleshooting

Common issues:

1. **Payment initiation fails**: Check the merchant ID and salt key
2. **Callback not working**: Ensure the callback URL is accessible from the internet
3. **Webhook not received**: Verify the webhook URL is correctly configured in the PhonePe dashboard

For more information, refer to the [PhonePe Developer Documentation](https://developer.phonepe.com/v1/reference/python-sdk-introduction-standard-checkout).
