"""
Database Synchronization for Product Data

This module provides automatic synchronization of product data
(products, categories, brands) across primary and replica databases.
"""

import logging
from django.db import transaction, connections
from django.db.models.signals import post_save, post_delete
from django.dispatch import receiver
from django.conf import settings
from django.core.management.color import no_style
from django.db import connection

logger = logging.getLogger(__name__)


class DatabaseSynchronizer:
    """Handle database synchronization between primary and replicas"""
    
    @staticmethod
    def get_replica_databases():
        """Get list of replica database names"""
        replica_dbs = []
        for db_name in settings.DATABASES.keys():
            if db_name != 'default' and 'read' in db_name.lower():
                replica_dbs.append(db_name)
        return replica_dbs
    
    @staticmethod
    def sync_model_to_replicas(instance, operation='save'):
        """
        Sync a model instance to all replica databases

        Args:
            instance: Model instance to sync
            operation: 'save' or 'delete'
        """
        replica_dbs = DatabaseSynchronizer.get_replica_databases()

        if not replica_dbs:
            logger.debug("No replica databases configured for sync")
            return

        model_class = instance.__class__

        for db_name in replica_dbs:
            try:
                if operation == 'save':
                    # Create or update the instance in replica using get_or_create to avoid signals
                    field_values = {}
                    for field in model_class._meta.fields:
                        if field.name != 'id':  # Skip primary key for get_or_create
                            field_values[field.name] = getattr(instance, field.name)

                    # Use get_or_create to avoid triggering signals
                    replica_instance, created = model_class.objects.using(db_name).get_or_create(
                        pk=instance.pk,
                        defaults=field_values
                    )

                    # If not created, update the existing instance
                    if not created:
                        for field_name, field_value in field_values.items():
                            setattr(replica_instance, field_name, field_value)
                        # Use update() to avoid triggering signals
                        model_class.objects.using(db_name).filter(pk=instance.pk).update(**field_values)

                    logger.debug(f"Synced {model_class.__name__} {instance.pk} to {db_name}")

                elif operation == 'delete':
                    # Delete the instance from replica
                    try:
                        # Use delete() on queryset to avoid triggering signals
                        deleted_count = model_class.objects.using(db_name).filter(pk=instance.pk).delete()[0]
                        if deleted_count > 0:
                            logger.debug(f"Deleted {model_class.__name__} {instance.pk} from {db_name}")
                        else:
                            logger.debug(f"{model_class.__name__} {instance.pk} not found in {db_name}")
                    except Exception as delete_error:
                        logger.debug(f"Error deleting {model_class.__name__} {instance.pk} from {db_name}: {delete_error}")

            except Exception as e:
                logger.error(f"Failed to sync {model_class.__name__} {instance.pk} to {db_name}: {e}")
    
    @staticmethod
    def bulk_sync_to_replicas(model_class, queryset=None):
        """
        Bulk sync all instances of a model to replicas
        
        Args:
            model_class: Model class to sync
            queryset: Optional queryset to sync (defaults to all objects)
        """
        replica_dbs = DatabaseSynchronizer.get_replica_databases()
        
        if not replica_dbs:
            logger.info("No replica databases configured for bulk sync")
            return
        
        if queryset is None:
            queryset = model_class.objects.all()
        
        total_objects = queryset.count()
        logger.info(f"Starting bulk sync of {total_objects} {model_class.__name__} objects")
        
        for db_name in replica_dbs:
            try:
                # Clear existing data in replica
                model_class.objects.using(db_name).all().delete()
                
                # Bulk create in replica
                objects_to_create = []
                for obj in queryset:
                    # Create a copy without pk to force creation
                    obj_copy = model_class()
                    for field in model_class._meta.fields:
                        if field.name != 'id':  # Skip primary key
                            setattr(obj_copy, field.name, getattr(obj, field.name))
                    objects_to_create.append(obj_copy)
                
                model_class.objects.using(db_name).bulk_create(objects_to_create)
                logger.info(f"Bulk synced {len(objects_to_create)} {model_class.__name__} objects to {db_name}")
                
            except Exception as e:
                logger.error(f"Failed to bulk sync {model_class.__name__} to {db_name}: {e}")


# Signal handlers for automatic synchronization
@receiver(post_save, sender='products.Product')
def sync_product_save(sender, instance, created, **kwargs):
    """Sync product saves to replica databases"""
    if hasattr(settings, '_DISABLE_SYNC') and settings._DISABLE_SYNC:
        return
    
    try:
        DatabaseSynchronizer.sync_model_to_replicas(instance, 'save')
    except Exception as e:
        logger.error(f"Failed to sync product {instance.pk}: {e}")


@receiver(post_delete, sender='products.Product')
def sync_product_delete(sender, instance, **kwargs):
    """Sync product deletions to replica databases"""
    if hasattr(settings, '_DISABLE_SYNC') and settings._DISABLE_SYNC:
        return
    
    try:
        DatabaseSynchronizer.sync_model_to_replicas(instance, 'delete')
    except Exception as e:
        logger.error(f"Failed to sync product deletion {instance.pk}: {e}")


@receiver(post_save, sender='products.Category')
def sync_category_save(sender, instance, created, **kwargs):
    """Sync category saves to replica databases"""
    if hasattr(settings, '_DISABLE_SYNC') and settings._DISABLE_SYNC:
        return
    
    try:
        DatabaseSynchronizer.sync_model_to_replicas(instance, 'save')
    except Exception as e:
        logger.error(f"Failed to sync category {instance.pk}: {e}")


@receiver(post_delete, sender='products.Category')
def sync_category_delete(sender, instance, **kwargs):
    """Sync category deletions to replica databases"""
    if hasattr(settings, '_DISABLE_SYNC') and settings._DISABLE_SYNC:
        return
    
    try:
        DatabaseSynchronizer.sync_model_to_replicas(instance, 'delete')
    except Exception as e:
        logger.error(f"Failed to sync category deletion {instance.pk}: {e}")


@receiver(post_save, sender='products.Brand')
def sync_brand_save(sender, instance, created, **kwargs):
    """Sync brand saves to replica databases"""
    if hasattr(settings, '_DISABLE_SYNC') and settings._DISABLE_SYNC:
        return
    
    try:
        DatabaseSynchronizer.sync_model_to_replicas(instance, 'save')
    except Exception as e:
        logger.error(f"Failed to sync brand {instance.pk}: {e}")


@receiver(post_delete, sender='products.Brand')
def sync_brand_delete(sender, instance, **kwargs):
    """Sync brand deletions to replica databases"""
    if hasattr(settings, '_DISABLE_SYNC') and settings._DISABLE_SYNC:
        return
    
    try:
        DatabaseSynchronizer.sync_model_to_replicas(instance, 'delete')
    except Exception as e:
        logger.error(f"Failed to sync brand deletion {instance.pk}: {e}")


# Additional models that should be synced
@receiver(post_save, sender='products.SubCategorie')
def sync_subcategory_save(sender, instance, created, **kwargs):
    """Sync subcategory saves to replica databases"""
    if hasattr(settings, '_DISABLE_SYNC') and settings._DISABLE_SYNC:
        return
    
    try:
        DatabaseSynchronizer.sync_model_to_replicas(instance, 'save')
    except Exception as e:
        logger.error(f"Failed to sync subcategory {instance.pk}: {e}")


@receiver(post_delete, sender='products.SubCategorie')
def sync_subcategory_delete(sender, instance, **kwargs):
    """Sync subcategory deletions to replica databases"""
    if hasattr(settings, '_DISABLE_SYNC') and settings._DISABLE_SYNC:
        return
    
    try:
        DatabaseSynchronizer.sync_model_to_replicas(instance, 'delete')
    except Exception as e:
        logger.error(f"Failed to sync subcategory deletion {instance.pk}: {e}")


@receiver(post_save, sender='products.GST')
def sync_gst_save(sender, instance, created, **kwargs):
    """Sync GST saves to replica databases"""
    if hasattr(settings, '_DISABLE_SYNC') and settings._DISABLE_SYNC:
        return
    
    try:
        DatabaseSynchronizer.sync_model_to_replicas(instance, 'save')
    except Exception as e:
        logger.error(f"Failed to sync GST {instance.pk}: {e}")


@receiver(post_delete, sender='products.GST')
def sync_gst_delete(sender, instance, **kwargs):
    """Sync GST deletions to replica databases"""
    if hasattr(settings, '_DISABLE_SYNC') and settings._DISABLE_SYNC:
        return
    
    try:
        DatabaseSynchronizer.sync_model_to_replicas(instance, 'delete')
    except Exception as e:
        logger.error(f"Failed to sync GST deletion {instance.pk}: {e}")


def enable_sync():
    """Enable database synchronization"""
    if hasattr(settings, '_DISABLE_SYNC'):
        delattr(settings, '_DISABLE_SYNC')
    logger.info("Database synchronization enabled")


def disable_sync():
    """Disable database synchronization (useful for bulk operations)"""
    settings._DISABLE_SYNC = True
    logger.info("Database synchronization disabled")


def sync_status():
    """Check if synchronization is enabled"""
    return not (hasattr(settings, '_DISABLE_SYNC') and settings._DISABLE_SYNC)
