/**
 * Rapidshyp Rate Calculator Component - Main shipping rate calculator
 */

import React, { useState, useEffect, useCallback } from 'react';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Separator } from '@/components/ui/separator';
import { Calculator, MapPin, Package, Zap } from 'lucide-react';
import { cn } from '@/lib/utils';
import { PincodeValidator } from './PincodeValidator';
import { ShippingRateSelector } from './ShippingRateSelector';
import { useShippingRates } from '@/hooks/useShippingRates';
import type { RapidshypRateCalculatorProps, CourierRate } from '@/types/shipping';

export const RapidshypRateCalculator: React.FC<RapidshypRateCalculatorProps> = ({
  deliveryPincode,
  orderWeight = 1.0,
  isCOD = false,
  orderTotal = 0,
  onRateSelect,
  selectedRate,
  className
}) => {
  const [pincode, setPincode] = useState(deliveryPincode);
  const [isPincodeValid, setIsPincodeValid] = useState(false);
  const [isPincodeServiceable, setIsPincodeServiceable] = useState(false);
  const [hasCalculatedRates, setHasCalculatedRates] = useState(false);

  const {
    rates,
    loading,
    error,
    source,
    minimumRate,
    calculateRates,
    clearRates
  } = useShippingRates();

  // Update pincode when prop changes
  useEffect(() => {
    setPincode(deliveryPincode);
  }, [deliveryPincode]);

  // Calculate rates when pincode becomes valid and serviceable
  const handleRateCalculation = useCallback(async () => {
    if (isPincodeValid && isPincodeServiceable && pincode.length === 6) {
      try {
        await calculateRates({
          delivery_pincode: pincode,
          weight: orderWeight,
          cod: isCOD,
          total_value: orderTotal
        });
        setHasCalculatedRates(true);
      } catch (err) {
        console.error('Rate calculation failed:', err);
      }
    }
  }, [isPincodeValid, isPincodeServiceable, pincode, orderWeight, isCOD, orderTotal, calculateRates]);

  // Auto-calculate rates when conditions are met
  useEffect(() => {
    if (isPincodeValid && isPincodeServiceable && !hasCalculatedRates) {
      const timeoutId = setTimeout(handleRateCalculation, 500);
      return () => clearTimeout(timeoutId);
    }
  }, [isPincodeValid, isPincodeServiceable, hasCalculatedRates, handleRateCalculation]);

  // Clear rates when pincode becomes invalid
  useEffect(() => {
    if (!isPincodeValid || !isPincodeServiceable) {
      clearRates();
      setHasCalculatedRates(false);
    }
  }, [isPincodeValid, isPincodeServiceable, clearRates]);

  // Auto-select minimum rate if no rate is selected
  useEffect(() => {
    if (rates.length > 0 && !selectedRate && minimumRate) {
      onRateSelect(minimumRate);
    }
  }, [rates, selectedRate, minimumRate, onRateSelect]);

  const handlePincodeChange = (newPincode: string) => {
    setPincode(newPincode);
    setHasCalculatedRates(false);
  };

  const handleValidationChange = (isValid: boolean, isServiceable: boolean) => {
    setIsPincodeValid(isValid);
    setIsPincodeServiceable(isServiceable);
  };

  const handleRateSelection = (rate: CourierRate) => {
    onRateSelect(rate);
  };

  const formatCurrency = (amount: number) => {
    return new Intl.NumberFormat('en-IN', {
      style: 'currency',
      currency: 'INR',
      minimumFractionDigits: 0,
      maximumFractionDigits: 2
    }).format(amount);
  };

  const shouldShowRates = isPincodeValid && isPincodeServiceable && (rates.length > 0 || loading || error);

  return (
    <Card className={cn("w-full", className)}>
      <CardHeader className="pb-4">
        <div className="flex items-center justify-between">
          <CardTitle className="text-lg font-semibold flex items-center">
            <Calculator className="h-5 w-5 mr-2 text-primary" />
            Shipping Calculator
          </CardTitle>
          
          {source === 'rapidshyp' && (
            <Badge variant="default" className="bg-green-100 text-green-700 border-green-200">
              <Zap className="h-3 w-3 mr-1" />
              Live Rates
            </Badge>
          )}
        </div>
        
        <p className="text-sm text-gray-600">
          Get real-time shipping rates for your delivery location
        </p>
      </CardHeader>

      <CardContent className="space-y-6">
        {/* Pincode Input Section */}
        <div className="space-y-4">
          <div className="flex items-center text-sm font-medium text-gray-700">
            <MapPin className="h-4 w-4 mr-2" />
            Delivery Location
          </div>
          
          <PincodeValidator
            value={pincode}
            onChange={handlePincodeChange}
            onValidationChange={handleValidationChange}
            label="Enter delivery pincode"
            placeholder="6-digit pincode"
          />
        </div>

        {/* Order Details */}
        {(orderWeight > 0 || orderTotal > 0) && (
          <>
            <Separator />
            <div className="space-y-3">
              <div className="flex items-center text-sm font-medium text-gray-700">
                <Package className="h-4 w-4 mr-2" />
                Package Details
              </div>
              
              <div className="grid grid-cols-2 gap-4 text-sm">
                {orderWeight > 0 && (
                  <div className="flex justify-between">
                    <span className="text-gray-600">Weight:</span>
                    <span className="font-medium">{orderWeight} kg</span>
                  </div>
                )}
                
                {orderTotal > 0 && (
                  <div className="flex justify-between">
                    <span className="text-gray-600">Order Value:</span>
                    <span className="font-medium">{formatCurrency(orderTotal)}</span>
                  </div>
                )}
                
                <div className="flex justify-between">
                  <span className="text-gray-600">Payment:</span>
                  <span className="font-medium">{isCOD ? 'Cash on Delivery' : 'Prepaid'}</span>
                </div>
              </div>
            </div>
          </>
        )}

        {/* Shipping Rates Section */}
        {shouldShowRates && (
          <>
            <Separator />
            <ShippingRateSelector
              rates={rates}
              selectedRate={selectedRate}
              onRateSelect={handleRateSelection}
              loading={loading}
              error={error}
              source={source}
            />
          </>
        )}

        {/* Minimum Rate Highlight */}
        {minimumRate && rates.length > 1 && (
          <div className="mt-4 p-3 bg-blue-50 border border-blue-200 rounded-lg">
            <div className="flex items-center justify-between text-sm">
              <span className="text-blue-700 font-medium">
                💡 Best Rate Available:
              </span>
              <span className="text-blue-900 font-semibold">
                {formatCurrency(minimumRate.total_freight)} via {minimumRate.courier_name}
              </span>
            </div>
          </div>
        )}

        {/* Help Text */}
        {!isPincodeValid && pincode.length === 0 && (
          <div className="text-center py-4">
            <p className="text-sm text-gray-500">
              Enter your delivery pincode to see available shipping options
            </p>
          </div>
        )}
      </CardContent>
    </Card>
  );
};

export default RapidshypRateCalculator;
