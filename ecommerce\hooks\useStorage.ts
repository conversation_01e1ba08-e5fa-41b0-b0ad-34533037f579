/**
 * Custom hook for safely accessing browser storage (localStorage and sessionStorage)
 * This hook ensures that storage is only accessed on the client side
 */

import { useState, useEffect } from 'react';

type StorageType = 'local' | 'session';

interface StorageOperations {
  getItem: (key: string) => string | null;
  setItem: (key: string, value: string) => void;
  removeItem: (key: string) => void;
  clear: () => void;
}

export function useStorage(type: StorageType = 'local'): StorageOperations {
  const [storage, setStorage] = useState<Storage | null>(null);

  // Initialize storage on client side only
  useEffect(() => {
    if (typeof window !== 'undefined') {
      setStorage(type === 'local' ? window.localStorage : window.sessionStorage);
    }
  }, [type]);

  const getItem = (key: string): string | null => {
    try {
      if (storage) {
        return storage.getItem(key);
      }
      return null;
    } catch (error) {
      console.error(`Error getting item from ${type}Storage:`, error);
      return null;
    }
  };

  const setItem = (key: string, value: string): void => {
    try {
      if (storage) {
        storage.setItem(key, value);
      }
    } catch (error) {
      console.error(`Error setting item in ${type}Storage:`, error);
    }
  };

  const removeItem = (key: string): void => {
    try {
      if (storage) {
        storage.removeItem(key);
      }
    } catch (error) {
      console.error(`Error removing item from ${type}Storage:`, error);
    }
  };

  const clear = (): void => {
    try {
      if (storage) {
        storage.clear();
      }
    } catch (error) {
      console.error(`Error clearing ${type}Storage:`, error);
    }
  };

  return { getItem, setItem, removeItem, clear };
}

export default useStorage;
