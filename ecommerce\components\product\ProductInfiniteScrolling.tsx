import { ProductType } from "@/types/product";
import { useEffect, useRef, useState } from "react";
import ProductCardLoading from "../ui/loading/ProductCardLoading";
import Product from "./Product";

interface ProductInfiniteScrollingProps {
  products: ProductType[];
  isLoading: boolean;
  observerTarget: React.RefObject<HTMLDivElement>;
}

const ProductInfiniteScrolling: React.FC<ProductInfiniteScrollingProps> = ({
  products,
  isLoading,
  observerTarget,
}) => {
  // Don't render anything if there are no products
  if (!products || products.length === 0) {
    return null;
  }

  return (
    <div className="container mx-auto h-[100vh]">
      <div className="grid grid-cols-1 xs:grid-cols-2 sm:grid-cols-2 md:grid-cols-3 lg:grid-cols-4 xl:grid-cols-4 2xl:grid-cols-5 gap-3 xs:gap-4 sm:gap-5 md:gap-6 lg:gap-6">
        {products.map((product) => (
          <Product key={product.id} {...product} />
        ))}
      </div>
      <div ref={observerTarget} className="h-4 w-full my-8">
        {isLoading && (
          <div className="grid grid-cols-1 xs:grid-cols-2 sm:grid-cols-2 md:grid-cols-3 lg:grid-cols-4 xl:grid-cols-4 2xl:grid-cols-5 gap-3 xs:gap-4 sm:gap-5 md:gap-6 lg:gap-6">
            {Array.from({ length: 4 }).map((_, i) => (
              <ProductCardLoading key={i} />
            ))}
          </div>
        )}
      </div>
    </div>
  );
};

export default ProductInfiniteScrolling;
