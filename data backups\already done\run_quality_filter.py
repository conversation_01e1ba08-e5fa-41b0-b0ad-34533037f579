#!/usr/bin/env python3
"""
Simple runner for the image quality filter
"""

import sys
from image_quality_filter import ImageQualityFilter

def main():
    print("🔍 Haier Image Quality Filter")
    print("=" * 40)
    
    # Ask user for confirmation
    print("This will analyze and remove low-quality images from the haier_images folder.")
    print("Low-quality images will be moved to 'removed_images' folder (not deleted).")
    print("\nQuality criteria:")
    print("- Minimum size: 300x300 pixels")
    print("- Minimum file size: 10KB")
    print("- Remove duplicates")
    print("- Remove blank/solid color images")
    print("- Remove corrupted images")
    
    response = input("\nProceed with quality filtering? (y/N): ").strip().lower()
    
    if response not in ['y', 'yes']:
        print("Operation cancelled.")
        return
    
    # Run the filter
    filter_tool = ImageQualityFilter()
    filter_tool.process_all_folders()
    
    print("\n✅ Quality filtering completed!")
    print("Check 'image_quality_filter.log' for detailed results.")

if __name__ == "__main__":
    main()
