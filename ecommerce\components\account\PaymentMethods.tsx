import { But<PERSON> } from "../../components/ui/button";
import { Card, CardContent } from "../../components/ui/card";
import { Plus, CreditCard, Trash2 } from "lucide-react";
import { useToast } from "../../hooks/use-toast";
import { Badge } from "../ui/badge";

export const PaymentMethods = ({ payment }: any) => {
  const { toast } = useToast();

  const handleDelete = (id: number) => {
    toast({
      variant: "info",
      title: "Payment method deleted",
      description: "The payment method has been removed from your account.",
    });
  };

  return (
    <div className="space-y-4">
      <div className="flex justify-between items-center">
        <h2 className="text-lg sm:text-xl font-semibold">Payment Methods</h2>
        <Button className="flex items-center gap-2">
          <Plus className="h-4 w-4" />
          Add New Card
        </Button>
      </div>

      <div className="grid gap-4 md:grid-cols-2">
        {Array.isArray(payment) &&
          payment.map((method) => (
            <Card key={method.id}>
              <CardContent className="p-6">
                <div className="flex justify-between items-start">
                  <div className="flex items-center gap-2">
                    <CreditCard className="h-5 w-5" />
                    <div>
                      <p className="font-medium">
                        {method.card_type} ending in {method.last_four}
                      </p>
                    </div>
                    {method.is_default && (
                      <Badge variant="secondary">Default</Badge>
                    )}
                  </div>
                  <Button
                    variant="ghost"
                    size="icon"
                    onClick={() => handleDelete(method.id)}
                  >
                    <Trash2 className="h-4 w-4" />
                  </Button>
                </div>
              </CardContent>
            </Card>
          ))}
      </div>
    </div>
  );
};
