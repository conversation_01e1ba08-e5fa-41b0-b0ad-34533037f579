# PhonePe Payment Gateway Technical Implementation

## 1. Environment Setup and Configuration

### 1.1 Environment Variables
Add the following environment variables to the project:

```
PHONEPE_MERCHANT_ID=your-merchant-id
PHONEPE_SALT_KEY=your-salt-key
PHONEPE_SALT_INDEX=your-salt-index
PHONEPE_API_ENDPOINT=https://api.phonepe.com/apis/hermes/pg/v1
PHONEPE_CALLBACK_URL=https://your-domain.com/api/v1/payments/phonepe/callback
PHONEPE_WEBHOOK_URL=https://your-domain.com/api/v1/payments/phonepe/webhook
PHONEPE_ENVIRONMENT=UAT  # UAT for testing, PROD for production
```

### 1.2 Django Settings Configuration
Add the following to `settings.py`:

```python
# PhonePe settings
PHONEPE_MERCHANT_ID = os.getenv("PHONEPE_MERCHANT_ID")
PHONEPE_SALT_KEY = os.getenv("PHONEPE_SALT_KEY")
PHONEPE_SALT_INDEX = os.getenv("PHONEPE_SALT_INDEX")
PHONEPE_API_ENDPOINT = os.getenv("PHONEPE_API_ENDPOINT")
PHONEPE_CALLBACK_URL = os.getenv("PHONEPE_CALLBACK_URL")
PHONEPE_WEBHOOK_URL = os.getenv("PHONEPE_WEBHOOK_URL")
PHONEPE_ENVIRONMENT = os.getenv("PHONEPE_ENVIRONMENT", "UAT")
```

## 2. Database Model Updates

### 2.1 Update Order Model
Add PhonePe-specific fields to the Order model:

```python
# Add to orders/models.py in the Order model
phonepe_transaction_id = models.CharField(max_length=100, blank=True)
phonepe_payment_url = models.URLField(max_length=500, blank=True)
```

### 2.2 Update Payment Model
Add PhonePe as a payment method option:

```python
# Update Payment model in orders/models.py
PAYMENT_METHOD_CHOICES = [
    ('PENDING', 'Pending'),
    ('PROCESSING', 'Processing'),
    ('COMPLETED', 'Completed'),
    ('FAILED', 'Failed'),
    ('REFUNDED', 'Refunded'),
    ('PHONEPE', 'PhonePe'),  # Add this line
]

# Add this field to the Payment model
phonepe_transaction_details = models.JSONField(null=True, blank=True)
```

## 3. PhonePe Service Implementation

### 3.1 Create PhonePe Service Module
Create a new file `payments/services/phonepe_service.py`:

```python
import base64
import hashlib
import json
import uuid
import requests
from django.conf import settings
from datetime import datetime

class PhonePeService:
    def __init__(self):
        self.merchant_id = settings.PHONEPE_MERCHANT_ID
        self.salt_key = settings.PHONEPE_SALT_KEY
        self.salt_index = settings.PHONEPE_SALT_INDEX
        self.api_endpoint = settings.PHONEPE_API_ENDPOINT
        self.callback_url = settings.PHONEPE_CALLBACK_URL
        self.environment = settings.PHONEPE_ENVIRONMENT
    
    def _generate_transaction_id(self):
        """Generate a unique transaction ID"""
        return f"TX_{uuid.uuid4().hex[:16]}_{int(datetime.now().timestamp())}"
    
    def _calculate_checksum(self, data):
        """Calculate checksum for request data"""
        # Convert data to JSON string
        data_string = json.dumps(data, separators=(',', ':'))
        
        # Encode data string to bytes
        encoded_data = data_string.encode('utf-8')
        
        # Base64 encode the data
        base64_data = base64.b64encode(encoded_data).decode('utf-8')
        
        # Create checksum string
        checksum_string = f"{base64_data}/pg/v1/pay{self.salt_key}"
        
        # Generate SHA256 hash
        checksum = hashlib.sha256(checksum_string.encode('utf-8')).hexdigest()
        
        # Append salt index
        return f"{checksum}###{self.salt_index}"
    
    def initiate_payment(self, order, amount):
        """Initiate a payment with PhonePe"""
        transaction_id = self._generate_transaction_id()
        
        # Prepare payment data
        payload = {
            "merchantId": self.merchant_id,
            "merchantTransactionId": transaction_id,
            "merchantUserId": f"MUID_{order.user.id}",
            "amount": int(amount * 100),  # Convert to paise
            "redirectUrl": f"{self.callback_url}?order_id={order.id}",
            "redirectMode": "REDIRECT",
            "callbackUrl": f"{self.callback_url}?order_id={order.id}",
            "mobileNumber": order.user.phone_number,
            "paymentInstrument": {
                "type": "PAY_PAGE"
            }
        }
        
        # Calculate checksum
        base64_payload = base64.b64encode(json.dumps(payload).encode()).decode()
        checksum = self._calculate_checksum(payload)
        
        # Prepare request headers
        headers = {
            "Content-Type": "application/json",
            "X-VERIFY": checksum
        }
        
        # Prepare request body
        request_body = {
            "request": base64_payload
        }
        
        # Make API request
        response = requests.post(
            f"{self.api_endpoint}/pay",
            headers=headers,
            json=request_body
        )
        
        # Process response
        if response.status_code == 200:
            response_data = response.json()
            if response_data.get("success"):
                # Update order with transaction ID and payment URL
                order.phonepe_transaction_id = transaction_id
                order.phonepe_payment_url = response_data.get("data", {}).get("instrumentResponse", {}).get("redirectInfo", {}).get("url")
                order.save()
                
                return {
                    "success": True,
                    "transaction_id": transaction_id,
                    "payment_url": order.phonepe_payment_url
                }
        
        # Handle failure
        return {
            "success": False,
            "error": response.json().get("error", {}).get("message", "Payment initiation failed")
        }
    
    def check_payment_status(self, transaction_id):
        """Check the status of a payment"""
        # Prepare request headers
        headers = {
            "Content-Type": "application/json",
            "X-VERIFY": self._calculate_checksum({"merchantId": self.merchant_id, "merchantTransactionId": transaction_id})
        }
        
        # Make API request
        response = requests.get(
            f"{self.api_endpoint}/status/{self.merchant_id}/{transaction_id}",
            headers=headers
        )
        
        # Process response
        if response.status_code == 200:
            return response.json()
        
        return {"success": False, "error": "Failed to check payment status"}
    
    def process_refund(self, transaction_id, amount, refund_id=None):
        """Process a refund for a payment"""
        if not refund_id:
            refund_id = f"REFUND_{uuid.uuid4().hex[:10]}"
        
        # Prepare refund data
        payload = {
            "merchantId": self.merchant_id,
            "merchantTransactionId": transaction_id,
            "merchantRefundId": refund_id,
            "amount": int(amount * 100),  # Convert to paise
            "callbackUrl": f"{self.callback_url}/refund?refund_id={refund_id}"
        }
        
        # Calculate checksum
        checksum = self._calculate_checksum(payload)
        
        # Prepare request headers
        headers = {
            "Content-Type": "application/json",
            "X-VERIFY": checksum
        }
        
        # Make API request
        response = requests.post(
            f"{self.api_endpoint}/refund",
            headers=headers,
            json=payload
        )
        
        # Process response
        if response.status_code == 200:
            return response.json()
        
        return {"success": False, "error": "Failed to process refund"}
    
    def verify_webhook_signature(self, request_data, signature):
        """Verify the signature of a webhook request"""
        # Calculate expected signature
        expected_signature = hashlib.sha256(
            (request_data + self.salt_key).encode()
        ).hexdigest() + "###" + self.salt_index
        
        # Compare with provided signature
        return expected_signature == signature
```

## 4. API Endpoints Implementation

### 4.1 Create Payments App
Create a new Django app for payment processing:

```bash
python manage.py startapp payments
```

### 4.2 Create Payment Views
Create `payments/views.py`:

```python
from django.http import JsonResponse, HttpResponseRedirect
from django.views.decorators.csrf import csrf_exempt
from django.views.decorators.http import require_POST
from django.shortcuts import get_object_or_404
from django.conf import settings
from rest_framework.views import APIView
from rest_framework.response import Response
from rest_framework import status, permissions
from orders.models import Order, Payment
from .services.phonepe_service import PhonePeService
import json
import logging

logger = logging.getLogger(__name__)

class InitiatePhonePePaymentView(APIView):
    permission_classes = [permissions.IsAuthenticated]
    
    def post(self, request, order_id):
        # Get the order
        order = get_object_or_404(Order, id=order_id, user=request.user)
        
        # Check if order is already paid
        if order.status == 'PAID':
            return Response(
                {"detail": "Order is already paid"},
                status=status.HTTP_400_BAD_REQUEST
            )
        
        # Initialize PhonePe service
        phonepe_service = PhonePeService()
        
        # Initiate payment
        result = phonepe_service.initiate_payment(order, order.total)
        
        if result.get("success"):
            return Response({
                "transaction_id": result.get("transaction_id"),
                "payment_url": result.get("payment_url")
            })
        else:
            return Response(
                {"detail": result.get("error")},
                status=status.HTTP_400_BAD_REQUEST
            )

@csrf_exempt
def phonepe_callback(request):
    """Handle PhonePe payment callback"""
    try:
        # Get order ID from query parameters
        order_id = request.GET.get('order_id')
        if not order_id:
            logger.error("No order ID provided in callback")
            return HttpResponseRedirect(f"{settings.FRONTEND_URL}/payment-failed")
        
        # Get the order
        order = get_object_or_404(Order, id=order_id)
        
        # Initialize PhonePe service
        phonepe_service = PhonePeService()
        
        # Check payment status
        result = phonepe_service.check_payment_status(order.phonepe_transaction_id)
        
        if result.get("success") and result.get("data", {}).get("paymentState") == "COMPLETED":
            # Update order status
            order.status = "PAID"
            order.save()
            
            # Create payment record
            Payment.objects.create(
                order=order,
                amount=order.total,
                status="COMPLETED",
                payment_method="PHONEPE",
                transaction_id=order.phonepe_transaction_id,
                phonepe_transaction_details=result.get("data")
            )
            
            # Redirect to success page
            return HttpResponseRedirect(f"{settings.FRONTEND_URL}/order-confirmation?order_id={order_id}")
        else:
            # Update order status
            order.status = "PAYMENT_FAILED"
            order.save()
            
            # Redirect to failure page
            return HttpResponseRedirect(f"{settings.FRONTEND_URL}/payment-failed")
    except Exception as e:
        logger.error(f"Error in PhonePe callback: {str(e)}")
        return HttpResponseRedirect(f"{settings.FRONTEND_URL}/payment-failed")

@csrf_exempt
@require_POST
def phonepe_webhook(request):
    """Handle PhonePe webhooks"""
    try:
        # Get request data
        request_data = request.body.decode('utf-8')
        payload = json.loads(request_data)
        
        # Get signature from headers
        signature = request.headers.get('X-VERIFY')
        if not signature:
            logger.error("No signature provided in webhook")
            return JsonResponse({"status": "error", "message": "No signature provided"}, status=400)
        
        # Initialize PhonePe service
        phonepe_service = PhonePeService()
        
        # Verify signature
        if not phonepe_service.verify_webhook_signature(request_data, signature):
            logger.error("Invalid signature in webhook")
            return JsonResponse({"status": "error", "message": "Invalid signature"}, status=400)
        
        # Process webhook event
        event_type = payload.get("event")
        transaction_id = payload.get("data", {}).get("merchantTransactionId")
        
        if not transaction_id:
            logger.error("No transaction ID in webhook payload")
            return JsonResponse({"status": "error", "message": "No transaction ID provided"}, status=400)
        
        # Get the order
        try:
            order = Order.objects.get(phonepe_transaction_id=transaction_id)
        except Order.DoesNotExist:
            logger.error(f"Order not found for transaction ID: {transaction_id}")
            return JsonResponse({"status": "error", "message": "Order not found"}, status=404)
        
        # Process different event types
        if event_type == "PAYMENT_SUCCESS":
            # Update order status
            order.status = "PAID"
            order.save()
            
            # Create payment record if it doesn't exist
            Payment.objects.get_or_create(
                order=order,
                transaction_id=transaction_id,
                defaults={
                    "amount": order.total,
                    "status": "COMPLETED",
                    "payment_method": "PHONEPE",
                    "phonepe_transaction_details": payload.get("data")
                }
            )
        elif event_type == "PAYMENT_FAILED":
            # Update order status
            order.status = "PAYMENT_FAILED"
            order.save()
        elif event_type == "PAYMENT_REFUNDED":
            # Update order status
            order.status = "REFUNDED"
            order.save()
            
            # Create refund payment record
            Payment.objects.create(
                order=order,
                amount=-order.total,
                status="COMPLETED",
                payment_method="PHONEPE_REFUND",
                transaction_id=f"REFUND_{transaction_id}",
                phonepe_transaction_details=payload.get("data")
            )
        
        return JsonResponse({"status": "success"})
    except Exception as e:
        logger.error(f"Error in PhonePe webhook: {str(e)}")
        return JsonResponse({"status": "error", "message": str(e)}, status=500)
```

### 4.3 Create Payment URLs
Create `payments/urls.py`:

```python
from django.urls import path
from . import views

app_name = 'payments'

urlpatterns = [
    path('phonepe/initiate/<str:order_id>/', views.InitiatePhonePePaymentView.as_view(), name='phonepe-initiate'),
    path('phonepe/callback/', views.phonepe_callback, name='phonepe-callback'),
    path('phonepe/webhook/', views.phonepe_webhook, name='phonepe-webhook'),
]
```

### 4.4 Update Main URLs
Update `backend/urls.py` to include payment URLs:

```python
# Add to existing urlpatterns
path('api/v1/payments/', include('payments.urls', namespace='payments')),
```

## 5. Frontend Integration

### 5.1 Update Payment Form Component
Update the PaymentForm component to include PhonePe as an option:

```tsx
// Add to PaymentForm.tsx
import { RadioGroup, RadioGroupItem } from "../ui/radio-group";

// Update PaymentInfo interface
interface PaymentInfo {
  paymentMethod: string;  // Add this line
  cardNumber: string;
  expiryDate: string;
  cvv: string;
  saveCard: boolean;
}

// Add payment method selection in the form
<div className="space-y-4">
  <div>
    <Label>Payment Method</Label>
    <RadioGroup
      value={paymentInfo.paymentMethod}
      onValueChange={(value) => handleChange("paymentMethod", value)}
      className="mt-2 space-y-2"
    >
      <div className="flex items-center space-x-2">
        <RadioGroupItem value="card" id="card" />
        <Label htmlFor="card">Credit/Debit Card</Label>
      </div>
      <div className="flex items-center space-x-2">
        <RadioGroupItem value="phonepe" id="phonepe" />
        <Label htmlFor="phonepe">PhonePe</Label>
      </div>
    </RadioGroup>
  </div>
  
  {paymentInfo.paymentMethod === 'card' && (
    // Existing card form fields
  )}
  
  {paymentInfo.paymentMethod === 'phonepe' && (
    <div className="p-4 border rounded-md bg-muted">
      <p className="text-sm">You will be redirected to PhonePe to complete your payment.</p>
    </div>
  )}
</div>
```

### 5.2 Update Checkout Page
Update the checkout page to handle PhonePe payment flow:

```tsx
// Add to checkout/page.tsx
import { useRouter } from "next/navigation";
import { MAIN_URL, ORDERS, PAYMENTS_PHONEPE_INITIATE } from "@/constant/urls";

// Add to handlePlaceOrder function
const handlePlaceOrder = async () => {
  try {
    let request: any = {
      shipping_address_id: shippingAddress?.id,
      billing_address_id: shippingAddress?.id,
      shipping_method_id: shippingMethod?.id,
      notes: "",
    };

    if (promotion) {
      request["promo_code"] = promotion?.code;
    }

    const res: any = await create(ORDERS, request);

    if (Boolean(res?.id)) {
      if (paymentInfo.paymentMethod === 'phonepe') {
        // Initiate PhonePe payment
        const paymentRes = await fetch(`${MAIN_URL}${PAYMENTS_PHONEPE_INITIATE}/${res.id}/`, {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json',
            'Authorization': `Bearer ${session?.user?.access}`
          }
        });
        
        if (paymentRes.ok) {
          const paymentData = await paymentRes.json();
          // Redirect to PhonePe payment page
          window.location.href = paymentData.payment_url;
          return;
        } else {
          toast({
            title: "Payment initiation failed!",
            description: "Please try again later.",
          });
        }
      } else {
        // Regular flow for other payment methods
        toast({
          title: "Order placed successfully!",
          description: "Thank you for your purchase.",
        });
        router.push(`/order-confirmation?order_id=${res?.id}`);
      }
    } else {
      toast({
        title: "Something went wrong!",
        description: "Please try again later.",
      });
    }
  } catch (error) {
    console.log(error);
  } finally {
    localStorage.removeItem("promotion");
  }
};
```

## 6. Security Considerations

### 6.1 Secure Environment Variables
Ensure all PhonePe credentials are stored as environment variables and not hardcoded.

### 6.2 Implement IP Whitelisting
Add IP whitelisting for PhonePe webhook endpoints in production.

### 6.3 Implement Rate Limiting
Add rate limiting to prevent abuse of payment endpoints.

### 6.4 Comprehensive Logging
Implement detailed logging for all payment-related operations for audit and debugging.

## 7. Testing Plan

1. Test payment initiation in sandbox environment
2. Test payment callbacks with various scenarios
3. Test webhook handling with simulated events
4. Test refund processing
5. Test error handling and edge cases

## 8. Deployment Checklist

1. Add all required environment variables to production environment
2. Run database migrations
3. Update frontend URLs to point to production endpoints
4. Configure webhook URL in PhonePe dashboard
5. Test end-to-end payment flow in production
6. Monitor initial transactions closely
