"use client";

import { useState, useEffect, useCallback } from 'react';
import { useSession } from 'next-auth/react';
import useApi from './useApi';
import { MAIN_URL, ORDER_DETAIL, ORDER_UPDATE_STATUS, ORDER_UPDATE_TRACKING, ORDER_REFUND } from '@/constant/urls';
import { Order } from './useOrders';

interface OrderDetailState {
  order: Order | null;
  loading: boolean;
  error: string | null;
}

export const useOrderDetail = (orderId: string) => {
  const { data: session, status } = useSession();
  const [orderState, setOrderState] = useState<OrderDetailState>({
    order: null,
    loading: true,
    error: null,
  });

  const { read, update } = useApi(MAIN_URL);

  const fetchOrderDetail = useCallback(async () => {
    // Only fetch if authenticated and orderId is provided
    if (status !== 'authenticated' || !orderId) {
      return;
    }

    try {
      setOrderState(prev => ({
        ...prev,
        loading: true,
        error: null
      }));

      const endpoint = ORDER_DETAIL(orderId);
      const result = await read(endpoint);

      if (typeof result === 'string') {
        throw new Error(result);
      }

      const orderData = result as Order;

      setOrderState(prev => ({
        ...prev,
        order: orderData,
        loading: false,
      }));
    } catch (error) {
      console.error('Error fetching order detail:', error);
      setOrderState(prev => ({
        ...prev,
        loading: false,
        error: 'Failed to load order details'
      }));
    }
  }, [read, status, orderId]);

  const updateOrderStatus = useCallback(async (newStatus: string) => {
    if (status !== 'authenticated' || !orderId) {
      return false;
    }

    try {
      const result = await update(ORDER_UPDATE_STATUS(orderId), {
        status: newStatus
      });

      if (typeof result === 'string') {
        throw new Error(result);
      }

      // Update the order in the local state
      setOrderState(prev => ({
        ...prev,
        order: prev.order ? { ...prev.order, status: newStatus } : null
      }));

      return true;
    } catch (error) {
      console.error('Error updating order status:', error);
      return false;
    }
  }, [update, status, orderId]);

  const updateTrackingNumber = useCallback(async (trackingNumber: string) => {
    if (status !== 'authenticated' || !orderId) {
      return false;
    }

    try {
      const result = await update(ORDER_UPDATE_TRACKING(orderId), {
        tracking_number: trackingNumber
      });

      if (typeof result === 'string') {
        throw new Error(result);
      }

      // Update the order in the local state
      setOrderState(prev => ({
        ...prev,
        order: prev.order ? { ...prev.order, tracking_number: trackingNumber } : null
      }));

      return true;
    } catch (error) {
      console.error('Error updating tracking number:', error);
      return false;
    }
  }, [update, status, orderId]);

  const refundOrder = useCallback(async () => {
    if (status !== 'authenticated' || !orderId) {
      return false;
    }

    try {
      const result = await update(ORDER_REFUND(orderId), {});

      if (typeof result === 'string') {
        throw new Error(result);
      }

      // Update the order status to refunded in local state
      setOrderState(prev => ({
        ...prev,
        order: prev.order ? { ...prev.order, status: 'REFUNDED' } : null
      }));

      return true;
    } catch (error) {
      console.error('Error refunding order:', error);
      return false;
    }
  }, [update, status, orderId]);

  useEffect(() => {
    fetchOrderDetail();
  }, [fetchOrderDetail]);

  return {
    ...orderState,
    fetchOrderDetail,
    updateOrderStatus,
    updateTrackingNumber,
    refundOrder,
    refreshOrder: () => fetchOrderDetail(),
  };
};
