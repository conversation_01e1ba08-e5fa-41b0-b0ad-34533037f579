'use client';

import React, { useState, useEffect } from 'react';
import { X, Shield, Settings } from 'lucide-react';
import { secureLocalStorage } from '../../lib/secureStorage';
import { secureApiClient } from '../../lib/secureApiClient';

interface ConsentPreferences {
  essential: boolean;
  marketing: boolean;
  analytics: boolean;
  personalization: boolean;
  third_party: boolean;
  cookies: boolean;
}

interface ConsentBannerProps {
  onConsentGiven?: (preferences: ConsentPreferences) => void;
}

export const ConsentBanner: React.FC<ConsentBannerProps> = ({ onConsentGiven }) => {
  const [isVisible, setIsVisible] = useState(false);
  const [showDetails, setShowDetails] = useState(false);
  const [preferences, setPreferences] = useState<ConsentPreferences>({
    essential: true, // Always required
    marketing: false,
    analytics: false,
    personalization: false,
    third_party: false,
    cookies: false,
  });
  const [isLoading, setIsLoading] = useState(false);

  useEffect(() => {
    checkConsentStatus();

    // Listen for consent updates from privacy settings
    const handleConsentUpdate = () => {
      checkConsentStatus();
    };

    window.addEventListener('consentUpdated', handleConsentUpdate);

    return () => {
      window.removeEventListener('consentUpdated', handleConsentUpdate);
    };
  }, []);

  const checkConsentStatus = async () => {
    try {
      // Check if consent has been given in this session
      const sessionConsentGiven = sessionStorage.getItem('consent_given_session');
      if (sessionConsentGiven) {
        return; // Don't show banner if consent already given in this session
      }

      // Check if consent has been given before (persistent)
      const consentGiven = secureLocalStorage.getItem('consent_given');
      if (consentGiven) {
        return; // Don't show banner if consent already given
      }

      // Check if user is authenticated and has consent records
      if (secureApiClient.isAuthenticated()) {
        try {
          const response = await secureApiClient.getConsentStatus();
          if (response.data && Object.keys(response.data).length > 0) {
            // Check if user has actually granted any non-essential consents
            const hasGrantedConsents = Object.entries(response.data).some(([key, value]: [string, any]) =>
              key !== 'ESSENTIAL' && value.granted === true
            );

            if (hasGrantedConsents) {
              // User has granted consents, don't show banner
              secureLocalStorage.setItem('consent_given', true, 365 * 24 * 60); // 1 year
              sessionStorage.setItem('consent_given_session', 'true');
              return;
            }
          }
        } catch (error) {
          console.log('Could not fetch consent status:', error);
        }
      }

      // Show banner after a short delay
      setTimeout(() => setIsVisible(true), 1000);
    } catch (error) {
      console.error('Error checking consent status:', error);
      setTimeout(() => setIsVisible(true), 1000);
    }
  };

  const handleAcceptAll = async () => {
    const allAccepted: ConsentPreferences = {
      essential: true,
      marketing: true,
      analytics: true,
      personalization: true,
      third_party: true,
      cookies: true,
    };

    await saveConsent(allAccepted);
  };

  const handleAcceptSelected = async () => {
    await saveConsent(preferences);
  };

  const handleRejectAll = async () => {
    const essentialOnly: ConsentPreferences = {
      essential: true,
      marketing: false,
      analytics: false,
      personalization: false,
      third_party: false,
      cookies: false,
    };

    await saveConsent(essentialOnly);
  };

  const saveConsent = async (consentPreferences: ConsentPreferences) => {
    setIsLoading(true);
    try {
      // Save to secure storage
      secureLocalStorage.setItem('consent_preferences', consentPreferences, 365 * 24 * 60); // 1 year
      secureLocalStorage.setItem('consent_given', true, 365 * 24 * 60); // 1 year
      secureLocalStorage.setItem('consent_date', new Date().toISOString(), 365 * 24 * 60);

      // Mark consent as given for this session to prevent showing banner again
      sessionStorage.setItem('consent_given_session', 'true');

      // Save to server if authenticated
      if (secureApiClient.isAuthenticated()) {
        try {
          const consentData = {
            ESSENTIAL: consentPreferences.essential,
            MARKETING: consentPreferences.marketing,
            ANALYTICS: consentPreferences.analytics,
            PERSONALIZATION: consentPreferences.personalization,
            THIRD_PARTY: consentPreferences.third_party,
            COOKIES: consentPreferences.cookies,
          };

          await secureApiClient.updateConsent(consentData);
        } catch (error) {
          console.error('Failed to save consent to server:', error);
          // Continue anyway - local storage is saved
        }
      }

      // Call callback if provided
      if (onConsentGiven) {
        onConsentGiven(consentPreferences);
      }

      // Hide banner
      setIsVisible(false);

      // Initialize analytics/tracking based on consent
      initializeServices(consentPreferences);
    } catch (error) {
      console.error('Error saving consent:', error);
    } finally {
      setIsLoading(false);
    }
  };

  const initializeServices = (consentPreferences: ConsentPreferences) => {
    // Initialize Google Analytics if analytics consent given
    if (consentPreferences.analytics && typeof window !== 'undefined') {
      // Initialize GA4 here
      console.log('Analytics consent given - initializing tracking');
    }

    // Initialize marketing pixels if marketing consent given
    if (consentPreferences.marketing && typeof window !== 'undefined') {
      console.log('Marketing consent given - initializing marketing tools');
    }

    // Set cookie preferences
    if (!consentPreferences.cookies && typeof document !== 'undefined') {
      // Clear non-essential cookies
      console.log('Cookies consent denied - clearing non-essential cookies');
    }
  };

  const handlePreferenceChange = (key: keyof ConsentPreferences, value: boolean) => {
    if (key === 'essential') return; // Essential cookies cannot be disabled
    
    setPreferences(prev => ({
      ...prev,
      [key]: value
    }));
  };

  if (!isVisible) return null;

  return (
    <div className="fixed bottom-0 left-0 right-0 z-50 bg-white border-t border-gray-200 shadow-lg animate-slide-up">
      <div className="max-w-7xl mx-auto p-3 sm:p-4">
        {!showDetails ? (
          // Simple banner view
          <div className="flex flex-col sm:flex-row items-start sm:items-center justify-between gap-4">
            <div className="flex items-start gap-3 flex-1">
              <Shield className="h-6 w-6 text-blue-600 mt-1 flex-shrink-0" />
              <div>
                <h3 className="font-semibold text-gray-900 mb-1">
                  We value your privacy
                </h3>
                <p className="text-sm text-gray-600">
                  We use cookies and similar technologies to enhance your experience, 
                  analyze site usage, and assist in marketing. You can manage your preferences below.
                </p>
              </div>
            </div>
            
            <div className="flex flex-col sm:flex-row gap-2 w-full sm:w-auto">
              <button
                onClick={() => setShowDetails(true)}
                className="flex items-center justify-center gap-1 px-3 py-2 text-sm border border-gray-300 rounded-md hover:bg-gray-50 transition-colors order-3 sm:order-1"
                disabled={isLoading}
              >
                <Settings className="h-4 w-4" />
                <span className="hidden sm:inline">Customize</span>
                <span className="sm:hidden">Settings</span>
              </button>
              <div className="flex gap-2 order-1 sm:order-2">
                <button
                  onClick={handleRejectAll}
                  className="flex-1 sm:flex-none px-4 py-2 text-sm border border-gray-300 rounded-md hover:bg-gray-50 transition-colors"
                  disabled={isLoading}
                >
                  Reject All
                </button>
                <button
                  onClick={handleAcceptAll}
                  className="flex-1 sm:flex-none px-4 py-2 text-sm bg-blue-600 text-white rounded-md hover:bg-blue-700 transition-colors font-medium"
                  disabled={isLoading}
                >
                  {isLoading ? 'Saving...' : 'Accept All'}
                </button>
              </div>
            </div>
          </div>
        ) : (
          // Detailed preferences view
          <div className="space-y-4">
            <div className="flex items-center justify-between">
              <h3 className="text-lg font-semibold text-gray-900">
                Privacy Preferences
              </h3>
              <button
                onClick={() => setShowDetails(false)}
                className="p-1 hover:bg-gray-100 rounded"
              >
                <X className="h-5 w-5" />
              </button>
            </div>

            <div className="grid gap-4 sm:grid-cols-2">
              {/* Essential Cookies */}
              <div className="p-3 border border-gray-200 rounded-lg bg-gray-50">
                <div className="flex items-center justify-between mb-2">
                  <h4 className="font-medium text-gray-900">Essential</h4>
                  <div className="bg-green-100 text-green-800 text-xs px-2 py-1 rounded">
                    Always Active
                  </div>
                </div>
                <p className="text-sm text-gray-600">
                  Required for the website to function properly. Cannot be disabled.
                </p>
              </div>

              {/* Marketing */}
              <div className="p-3 border border-gray-200 rounded-lg">
                <div className="flex items-center justify-between mb-2">
                  <h4 className="font-medium text-gray-900">Marketing</h4>
                  <label className="relative inline-flex items-center cursor-pointer">
                    <input
                      type="checkbox"
                      checked={preferences.marketing}
                      onChange={(e) => handlePreferenceChange('marketing', e.target.checked)}
                      className="sr-only peer"
                    />
                    <div className="w-11 h-6 bg-gray-200 peer-focus:outline-none peer-focus:ring-4 peer-focus:ring-blue-300 rounded-full peer peer-checked:after:translate-x-full peer-checked:after:border-white after:content-[''] after:absolute after:top-[2px] after:left-[2px] after:bg-white after:border-gray-300 after:border after:rounded-full after:h-5 after:w-5 after:transition-all peer-checked:bg-blue-600"></div>
                  </label>
                </div>
                <p className="text-sm text-gray-600">
                  Used to show you relevant ads and marketing content.
                </p>
              </div>

              {/* Analytics */}
              <div className="p-3 border border-gray-200 rounded-lg">
                <div className="flex items-center justify-between mb-2">
                  <h4 className="font-medium text-gray-900">Analytics</h4>
                  <label className="relative inline-flex items-center cursor-pointer">
                    <input
                      type="checkbox"
                      checked={preferences.analytics}
                      onChange={(e) => handlePreferenceChange('analytics', e.target.checked)}
                      className="sr-only peer"
                    />
                    <div className="w-11 h-6 bg-gray-200 peer-focus:outline-none peer-focus:ring-4 peer-focus:ring-blue-300 rounded-full peer peer-checked:after:translate-x-full peer-checked:after:border-white after:content-[''] after:absolute after:top-[2px] after:left-[2px] after:bg-white after:border-gray-300 after:border after:rounded-full after:h-5 after:w-5 after:transition-all peer-checked:bg-blue-600"></div>
                  </label>
                </div>
                <p className="text-sm text-gray-600">
                  Help us understand how you use our website to improve it.
                </p>
              </div>

              {/* Personalization */}
              <div className="p-3 border border-gray-200 rounded-lg">
                <div className="flex items-center justify-between mb-2">
                  <h4 className="font-medium text-gray-900">Personalization</h4>
                  <label className="relative inline-flex items-center cursor-pointer">
                    <input
                      type="checkbox"
                      checked={preferences.personalization}
                      onChange={(e) => handlePreferenceChange('personalization', e.target.checked)}
                      className="sr-only peer"
                    />
                    <div className="w-11 h-6 bg-gray-200 peer-focus:outline-none peer-focus:ring-4 peer-focus:ring-blue-300 rounded-full peer peer-checked:after:translate-x-full peer-checked:after:border-white after:content-[''] after:absolute after:top-[2px] after:left-[2px] after:bg-white after:border-gray-300 after:border after:rounded-full after:h-5 after:w-5 after:transition-all peer-checked:bg-blue-600"></div>
                  </label>
                </div>
                <p className="text-sm text-gray-600">
                  Customize content and recommendations based on your preferences.
                </p>
              </div>
            </div>

            <div className="flex flex-col sm:flex-row gap-2 pt-4 border-t border-gray-200">
              <button
                onClick={handleRejectAll}
                className="px-4 py-2 text-sm border border-gray-300 rounded-md hover:bg-gray-50 transition-colors"
                disabled={isLoading}
              >
                Reject All
              </button>
              <button
                onClick={handleAcceptSelected}
                className="px-4 py-2 text-sm bg-blue-600 text-white rounded-md hover:bg-blue-700 transition-colors"
                disabled={isLoading}
              >
                {isLoading ? 'Saving...' : 'Save Preferences'}
              </button>
              <button
                onClick={handleAcceptAll}
                className="px-4 py-2 text-sm bg-green-600 text-white rounded-md hover:bg-green-700 transition-colors"
                disabled={isLoading}
              >
                {isLoading ? 'Saving...' : 'Accept All'}
              </button>
            </div>
          </div>
        )}
      </div>
    </div>
  );
};

export default ConsentBanner;
