import React from "react";

export const CartMenuLoading = () => {
  return (
    <div className="bg-white w-full md:min-w-[350px] p-4 md:p-6 rounded-lg shadow-xl border border-gray-200 mx-auto md:mx-0 max-w-[95vw] md:max-w-md">
      <h1 className="text-xl md:text-2xl font-bold mb-4 text-theme-text-primary">
        Your shopping cart
      </h1>
      <div className="w-full overflow-x-auto">
        <div className="w-full min-w-[300px]">
          <div className="flex justify-between font-semibold text-theme-text-primary pb-2 border-b border-gray-300">
            <span className="flex-1 text-sm md:text-base">Product</span>
            <span className="flex-1 text-center text-sm md:text-base">Qty</span>
            <span className="flex-1 text-right text-sm md:text-base">Price</span>
          </div>

          <div className="pt-2 pb-4 border-b border-gray-200">
            <div className="flex gap-2 md:gap-4">
              <div className="h-[60px] md:h-[75px] w-[60px] md:w-[75px] bg-gray-200 animate-pulse flex-1 rounded-md"></div>
              <div className="flex-1 flex items-center gap-1 md:gap-2 justify-center">
                <div className="h-[18px] w-[18px] md:h-[20px] md:w-[20px] bg-gray-200 animate-pulse rounded-md"></div>
                <div className="h-[8px] w-[8px] md:h-[10px] md:w-[10px] bg-gray-200 animate-pulse rounded-md"></div>
                <div className="h-[18px] w-[18px] md:h-[20px] md:w-[20px] bg-gray-200 animate-pulse rounded-md"></div>
              </div>
              <div className="h-[18px] w-[40px] md:h-[20px] md:w-[50px] bg-gray-200 animate-pulse flex-1 ml-auto rounded-md"></div>
            </div>
          </div>

          <div className="flex justify-between mt-3 md:mt-4 items-center">
            <span className="font-bold text-sm md:text-base text-theme-text-primary">
              Total payment
            </span>
            <div className="h-[18px] w-[80px] md:h-[20px] md:w-[100px] bg-gray-200 animate-pulse rounded-md"></div>
          </div>

          <div className="h-[36px] md:h-[40px] bg-theme-accent-primary/60 animate-pulse w-full mt-3 md:mt-4 rounded-md"></div>
        </div>
      </div>
    </div>
  );
};
