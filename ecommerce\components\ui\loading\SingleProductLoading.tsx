import React from "react";

const SingleProductLoading = () => {
  return (
    <div id="webcrumbs" className="animate-pulse">
      <div className="w-full bg-white rounded-lg shadow-md p-6">
        <div className="flex lg:flex-row flex-col gap-10">
          <div className="flex flex-col gap-6 w-[250px]">
            <div className="w-[250px] h-[250px] bg-gray-300 rounded"></div>
          </div>
          <div className="flex flex-col gap-4 w-full lg:w-[600px]">
            <div className="h-6 bg-gray-300 rounded mb-2"></div>
            <div className="flex items-center gap-2">
              <div className="h-4 bg-gray-300 rounded w-1/2"></div>
            </div>
            <div className="h-6 bg-gray-300 rounded my-2"></div>
            <div className="flex flex-col gap-2">
              <div className="h-4 bg-gray-300 rounded w-1/4 mb-2"></div>
              <div className="h-4 bg-gray-300 rounded w-1/4"></div>
            </div>
            <div className="h-10 bg-gray-300 rounded w-[120px] mt-2"></div>
          </div>
        </div>
        <div className="mt-10">
          <div className="flex lg:flex-row flex-col gap-6 mt-4">
            <div className="w-full bg-white rounded-md border border-neutral-300 p-4">
              <div className="w-full h-[150px] bg-gray-300 rounded mb-4"></div>
              <div className="h-4 bg-gray-300 rounded w-full mt-2"></div>
              <div className="h-6 bg-gray-300 rounded w-1/2 my-2"></div>
              <div className="h-10 bg-gray-300 rounded w-full mt-2"></div>
            </div>
            <div className="w-full bg-white rounded-md border border-neutral-300 p-4">
              <div className="w-full h-[150px] bg-gray-300 rounded mb-4"></div>
              <div className="h-4 bg-gray-300 rounded w-full mt-2"></div>
              <div className="h-6 bg-gray-300 rounded w-1/2 my-2"></div>
              <div className="h-10 bg-gray-300 rounded w-full mt-2"></div>
            </div>
            <div className="w-full bg-white rounded-md border border-neutral-300 p-4">
              <div className="w-full h-[150px] bg-gray-300 rounded mb-4"></div>
              <div className="h-4 bg-gray-300 rounded w-full mt-2"></div>
              <div className="h-6 bg-gray-300 rounded w-1/2 my-2"></div>
              <div className="h-10 bg-gray-300 rounded w-full mt-2"></div>
            </div>
            <div className="w-full bg-white rounded-md border border-neutral-300 p-4">
              <div className="w-full h-[150px] bg-gray-300 rounded mb-4"></div>
              <div className="h-4 bg-gray-300 rounded w-full mt-2"></div>
              <div className="h-6 bg-gray-300 rounded w-1/2 my-2"></div>
              <div className="h-10 bg-gray-300 rounded w-full mt-2"></div>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default SingleProductLoading;
