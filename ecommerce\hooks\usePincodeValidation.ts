/**
 * Custom hook for pincode validation with Rapidshyp serviceability check
 */

import { useState, useCallback, useRef } from 'react';
import useApi from './useApi';
import { MAIN_URL, SHIPPING_VALIDATE_PINCODE } from '@/constant/urls';
import type {
  PincodeValidationRequest,
  PincodeValidationResponse,
  UsePincodeValidationReturn
} from '@/types/shipping';

export const usePincodeValidation = (): UsePincodeValidationReturn => {
  const [isValid, setIsValid] = useState(false);
  const [isServiceable, setIsServiceable] = useState(false);
  const [isValidating, setIsValidating] = useState(false);
  const [message, setMessage] = useState('');
  const [error, setError] = useState<string | null>(null);
  
  const { create } = useApi<PincodeValidationResponse>(MAIN_URL);
  const timeoutRef = useRef<NodeJS.Timeout | null>(null);
  const abortControllerRef = useRef<AbortController | null>(null);

  const validatePincode = useCallback(async (pincode: string) => {
    // Clear any existing timeout
    if (timeoutRef.current) {
      clearTimeout(timeoutRef.current);
    }

    // Cancel any ongoing request
    if (abortControllerRef.current) {
      abortControllerRef.current.abort();
    }

    // Reset state
    setError(null);
    setMessage('');

    // Basic validation first
    const cleanPincode = pincode.trim();
    
    if (!cleanPincode) {
      setIsValid(false);
      setIsServiceable(false);
      setMessage('');
      return;
    }

    if (cleanPincode.length !== 6 || !/^\d{6}$/.test(cleanPincode)) {
      setIsValid(false);
      setIsServiceable(false);
      setMessage('Pincode must be 6 digits');
      return;
    }

    // Set basic validity
    setIsValid(true);
    setMessage('Checking serviceability...');

    // Debounce the API call
    timeoutRef.current = setTimeout(async () => {
      setIsValidating(true);
      abortControllerRef.current = new AbortController();

      try {
        const requestData: PincodeValidationRequest = {
          pincode: cleanPincode
        };

        console.log('Validating pincode:', cleanPincode);

        const response = await create(SHIPPING_VALIDATE_PINCODE, requestData);

        if (typeof response === 'string') {
          // Error response
          throw new Error(response);
        }

        if (!response) {
          throw new Error('No response received');
        }

        setIsValid(response.is_valid);
        setIsServiceable(response.is_serviceable);
        setMessage(response.message);

        console.log('Pincode validation result:', {
          pincode: cleanPincode,
          isValid: response.is_valid,
          isServiceable: response.is_serviceable,
          message: response.message
        });

      } catch (err) {
        if (err instanceof Error && err.name === 'AbortError') {
          // Request was aborted, don't update state
          return;
        }

        const errorMessage = err instanceof Error ? err.message : 'Validation failed';
        console.error('Pincode validation error:', errorMessage);
        
        // On API error, assume pincode is valid but serviceability unknown
        setIsValid(true);
        setIsServiceable(true); // Assume serviceable to not block user
        setMessage('Pincode format is valid');
        setError(errorMessage);
      } finally {
        setIsValidating(false);
        abortControllerRef.current = null;
      }
    }, 500); // 500ms debounce

  }, [create]);

  const clearValidation = useCallback(() => {
    // Clear timeout
    if (timeoutRef.current) {
      clearTimeout(timeoutRef.current);
    }

    // Cancel any ongoing request
    if (abortControllerRef.current) {
      abortControllerRef.current.abort();
    }

    setIsValid(false);
    setIsServiceable(false);
    setIsValidating(false);
    setMessage('');
    setError(null);
  }, []);

  return {
    isValid,
    isServiceable,
    isValidating,
    message,
    error,
    validatePincode,
    clearValidation
  };
};

export default usePincodeValidation;
