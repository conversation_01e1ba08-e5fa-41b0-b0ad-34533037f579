import { NextRequest, NextResponse } from 'next/server';
import fs from 'fs';
import path from 'path';

/**
 * Custom route handler for serving robots.txt with proper headers
 * This ensures that the Content-Type header is set correctly
 */
export async function GET(request: NextRequest) {
  try {
    // Path to the static robots.txt file
    const robotsPath = path.join(process.cwd(), 'public', 'robots.txt');
    
    // Check if the file exists
    if (fs.existsSync(robotsPath)) {
      // Read the robots file
      const robotsContent = fs.readFileSync(robotsPath, 'utf-8');
      
      // Return the robots.txt with proper headers
      return new NextResponse(robotsContent, {
        status: 200,
        headers: {
          'Content-Type': 'text/plain',
          'Cache-Control': 'public, max-age=3600, stale-while-revalidate=86400',
        },
      });
    } else {
      // If the static robots.txt doesn't exist, try to use the dynamic one
      return NextResponse.next();
    }
  } catch (error) {
    console.error('Error serving robots.txt:', error);
    return NextResponse.next();
  }
}
