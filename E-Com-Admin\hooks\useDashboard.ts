"use client";

import { useState, useEffect, useCallback } from 'react';
import { useSession } from 'next-auth/react';
import useApi from './useApi';
import {
  MAIN_URL,
  DASHBOARD_ANALYTICS,
  USER_STATISTICS,
  USER_GRAPH_DATA,
  ADMIN_ORDERS
} from '@/constant/urls';

interface StatisticsData {
  last_30_days: {
    total_revenue: number;
    new_customers: number;
    total_orders: number;
  };
  previous_30_days: {
    total_revenue: number;
    new_customers: number;
    total_orders: number;
  };
  percentage_delta: {
    revenue_delta: number | null;
    new_customers_delta: number | null;
    total_orders_delta: number | null;
  };
  active_products: number;
}

interface AnalyticsData {
  product_performance: Array<{
    id: number;
    name: string;
    sales: number;
    revenue: number;
    growth: string;
  }>;
  sales_chart: Array<{
    name: string;
    sales: number;
  }>;
  customer_demographics: Array<{
    name: string;
    value: number;
  }>;
}

interface GraphData {
  salesData: Array<{
    name: string;
    value: number;
  }>;
  sales_data: Array<{
    name: string;
    value: number;
  }>;
  productData: Array<{
    name: string;
    sales: number;
  }>;
}

interface RecentOrder {
  id: string;
  customer_name: string;
  total: number;
  status: string;
  created_at: string;
  items_count: number;
}

interface DashboardData {
  statistics: StatisticsData | null;
  analytics: AnalyticsData | null;
  graphData: GraphData | null;
  recentOrders: RecentOrder[];
}

interface DashboardState extends DashboardData {
  loading: {
    statistics: boolean;
    analytics: boolean;
    graphData: boolean;
    recentOrders: boolean;
  };
  error: {
    statistics: string | null;
    analytics: string | null;
    graphData: string | null;
    recentOrders: string | null;
  };
}

export const useDashboard = (dateRange?: string) => {
  const { data: session, status } = useSession();
  const [dashboardState, setDashboardState] = useState<DashboardState>({
    statistics: null,
    analytics: null,
    graphData: null,
    recentOrders: [],
    loading: {
      statistics: true,
      analytics: true,
      graphData: true,
      recentOrders: true,
    },
    error: {
      statistics: null,
      analytics: null,
      graphData: null,
      recentOrders: null,
    },
  });

  const { read } = useApi(MAIN_URL);

  // Helper function to get date range parameters
  const getDateRangeParams = useCallback((range: string = '30d') => {
    const now = new Date();
    let daysBack = 30;

    switch (range) {
      case '7d':
        daysBack = 7;
        break;
      case '30d':
        daysBack = 30;
        break;
      case '90d':
        daysBack = 90;
        break;
      case '1y':
        daysBack = 365;
        break;
      default:
        daysBack = 30;
    }

    const dateFrom = new Date(now.getTime() - (daysBack * 24 * 60 * 60 * 1000));
    const dateTo = now;

    return {
      date_from: dateFrom.toISOString().split('T')[0],
      date_to: dateTo.toISOString().split('T')[0]
    };
  }, []);

  const fetchStatistics = useCallback(async () => {
    // Only fetch if authenticated
    if (status !== 'authenticated') {
      return;
    }

    try {
      setDashboardState(prev => ({
        ...prev,
        loading: { ...prev.loading, statistics: true },
        error: { ...prev.error, statistics: null }
      }));

      const result = await read(USER_STATISTICS);

      if (typeof result === 'string') {
        throw new Error(result);
      }

      setDashboardState(prev => ({
        ...prev,
        statistics: result as StatisticsData,
        loading: { ...prev.loading, statistics: false }
      }));
    } catch (error) {
      console.error('Error fetching statistics:', error);
      setDashboardState(prev => ({
        ...prev,
        loading: { ...prev.loading, statistics: false },
        error: { ...prev.error, statistics: 'Failed to load statistics data' }
      }));
    }
  }, [read, status]);

  const fetchAnalytics = useCallback(async (customDateRange?: string) => {
    // Only fetch if authenticated
    if (status !== 'authenticated') {
      return;
    }

    try {
      setDashboardState(prev => ({
        ...prev,
        loading: { ...prev.loading, analytics: true },
        error: { ...prev.error, analytics: null }
      }));

      // Get date range parameters
      const dateParams = getDateRangeParams(customDateRange || dateRange);
      const queryParams = new URLSearchParams(dateParams).toString();
      const url = `${DASHBOARD_ANALYTICS}?${queryParams}`;

      const result = await read(url);

      if (typeof result === 'string') {
        throw new Error(result);
      }

      setDashboardState(prev => ({
        ...prev,
        analytics: result as AnalyticsData,
        loading: { ...prev.loading, analytics: false }
      }));
    } catch (error) {
      console.error('Error fetching analytics:', error);
      setDashboardState(prev => ({
        ...prev,
        loading: { ...prev.loading, analytics: false },
        error: { ...prev.error, analytics: 'Failed to load analytics data' }
      }));
    }
  }, [read, status, dateRange, getDateRangeParams]);

  const fetchGraphData = useCallback(async () => {
    // Only fetch if authenticated
    if (status !== 'authenticated') {
      return;
    }

    try {
      setDashboardState(prev => ({
        ...prev,
        loading: { ...prev.loading, graphData: true },
        error: { ...prev.error, graphData: null }
      }));

      const result = await read(USER_GRAPH_DATA);

      if (typeof result === 'string') {
        throw new Error(result);
      }

      setDashboardState(prev => ({
        ...prev,
        graphData: result as GraphData,
        loading: { ...prev.loading, graphData: false }
      }));
    } catch (error) {
      console.error('Error fetching graph data:', error);
      setDashboardState(prev => ({
        ...prev,
        loading: { ...prev.loading, graphData: false },
        error: { ...prev.error, graphData: 'Failed to load graph data' }
      }));
    }
  }, [read, status]);

  const fetchRecentOrders = useCallback(async () => {
    // Only fetch if authenticated
    if (status !== 'authenticated') {
      return;
    }

    try {
      setDashboardState(prev => ({
        ...prev,
        loading: { ...prev.loading, recentOrders: true },
        error: { ...prev.error, recentOrders: null }
      }));

      const result = await read(`${ADMIN_ORDERS}?page_size=10&ordering=-created_at`);

      if (typeof result === 'string') {
        throw new Error(result);
      }

      const ordersData = result as any;
      const orders = ordersData.results || ordersData;

      setDashboardState(prev => ({
        ...prev,
        recentOrders: Array.isArray(orders) ? orders : [],
        loading: { ...prev.loading, recentOrders: false }
      }));
    } catch (error) {
      console.error('Error fetching recent orders:', error);
      setDashboardState(prev => ({
        ...prev,
        recentOrders: [],
        loading: { ...prev.loading, recentOrders: false },
        error: { ...prev.error, recentOrders: 'Failed to load recent orders' }
      }));
    }
  }, [read, status]);

  const refreshDashboard = useCallback((customDateRange?: string) => {
    fetchStatistics();
    fetchAnalytics(customDateRange);
    fetchGraphData();
    fetchRecentOrders();
  }, [fetchStatistics, fetchAnalytics, fetchGraphData, fetchRecentOrders]);

  // Function to update data with new date range
  const updateDateRange = useCallback((newDateRange: string) => {
    fetchAnalytics(newDateRange);
  }, [fetchAnalytics]);

  useEffect(() => {
    refreshDashboard();
  }, [refreshDashboard]);

  return {
    ...dashboardState,
    refreshDashboard,
    updateDateRange,
    isLoading: Object.values(dashboardState.loading).some(loading => loading),
    hasError: Object.values(dashboardState.error).some(error => error !== null),
  };
};
