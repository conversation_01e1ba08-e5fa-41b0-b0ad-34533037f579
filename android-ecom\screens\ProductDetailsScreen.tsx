import {
  View,
  Text,
  Image,
  ScrollView,
  StyleSheet,
  TouchableOpacity,
  FlatList,
  ActivityIndicator,
} from "react-native";
import { SafeAreaView } from "react-native-safe-area-context";
import { MaterialCommunityIcons, MaterialIcons } from "@expo/vector-icons";
import { useEffect, useState } from "react";
import axios from "axios";
import { BaseURL } from "@/constants/ApiEndpoint";
import {
  horizontalScale,
  moderateScale,
  verticalScale,
} from "@/constants/Normalizer";
import { useAuth } from "@/context/AuthContext";
import { addToCart, WishlistService } from "@/hooks/API";
import { showSuccessToast } from "@/toast/ToastProvider";

export default function ProductDetailsScreen({ route, navigation }) {
  const [product, setProduct] = useState({});
  const [loading, setLoading] = useState(true);
  const slug = route.params?.product?.slug;
  const { accessToken, isAuthenticated } = useAuth();
  const [isInWishlist, setIsInWishlist] = useState(false);
  const [wishlistLoading, setWishlistLoading] = useState(false);

  useEffect(() => {
    const checkWishlistStatus = async () => {
      const status = await WishlistService.isInWishlist(Number(product.id));
      setIsInWishlist(status);
    };

    checkWishlistStatus();
  }, [product.id]);
  const fetchProduct = async () => {
    setLoading(true);
    try {
      const response = await axios.get(BaseURL + `/api/v1/products/${slug}`);
      if (response) {
        setProduct(response.data);
      }
    } catch (error) {
      console.log("error while fetching single products", error);
    } finally {
      setLoading(false);
    }
  };
  useEffect(() => {
    fetchProduct();
  }, [slug]);

  const newproduct = route?.params?.product || {
    id: "1",
    name: "Wireless Noise Cancelling Headphones",
    price: 299.99,
    image:
      "https://api.a0.dev/assets/image?text=premium%20wireless%20headphones%20product%20photo&aspect=1:1",
    rating: 4.5,
    description:
      "Experience premium sound quality with these wireless noise-cancelling headphones. Features include 30-hour battery life, touch controls, and premium comfort.",
    specifications: [
      "Bluetooth 5.0",
      "Active Noise Cancellation",
      "30-hour battery life",
      "Touch controls",
      "Voice assistant support",
    ],
  };

  const handleToggleWishlist = async () => {
    if (!isAuthenticated) {
      navigation.navigate("Auth", {
        returnTo: "ProductDetails",
      });
      return;
    }
    
    try {
      setWishlistLoading(true);
      const success = await WishlistService.toggleWishlist(
        Number(product.id),
        product.name
      );
      
      if (success) {
        // Update local state to reflect current wishlist status
        const newStatus = await WishlistService.isInWishlist(Number(product.id));
        setIsInWishlist(newStatus);
      }
    } catch (error) {
      console.error("Wishlist operation error:", error);
    } finally {
      setWishlistLoading(false);
    }
  };
  const handleAddToCart = async (
    productId: any,
    quantity: number,
    accessToken: string
  ) => {
    try {
      if (!isAuthenticated) {
        navigation.navigate("Auth", {
          returnTo: "Cart",
        });
        return;
      }
      const response = await addToCart(productId, quantity, accessToken);
      return response;
    } catch (error) {
      console.log("Error adding to cart", error);
    }
  };

  if (loading) {
    return (
      <SafeAreaView style={styles.loadingContainer}>
        <ActivityIndicator size="large" color="#2563EB" />
      </SafeAreaView>
    );
  }

  return (
    <SafeAreaView style={styles.container}>
      <ScrollView>
        <View style={styles.header}>
          <TouchableOpacity
            onPress={() => navigation.goBack()}
            style={styles.backButton}
          >
            <MaterialIcons name="arrow-back" size={24} color="#333" />
          </TouchableOpacity>
          <TouchableOpacity
            style={styles.wishlistButton}
            onPress={handleToggleWishlist}
            disabled={wishlistLoading}
          >
            <MaterialCommunityIcons
              name={isInWishlist ? "heart" : "heart-outline"}
              size={20}
              color={isInWishlist ? "#FF3B30" : "#666666"}
            />
          </TouchableOpacity>
        </View>

        <FlatList
          data={product?.images || [product.image]}
          horizontal
          pagingEnabled
          showsHorizontalScrollIndicator={false}
          keyExtractor={(_, index) => index.toString()}
          renderItem={({ item }) => (
            <Image source={{ uri: item?.image }} style={styles.carouselImage} />
          )}
        />

        <View style={styles.content}>
          <Text style={styles.name}>{product?.name}</Text>

          {product?.average_rating && (
            <View style={styles.ratingContainer}>
              <MaterialIcons name="star" size={20} color="#FFD700" />
              <Text style={styles.rating}>
                {Number(product?.average_rating).toFixed(1)}
              </Text>
            </View>
          )}

          <Text style={styles.price}>₹{Number(product?.price).toFixed(2)}</Text>

          <Text style={styles.sectionTitle}>Description</Text>
          <Text style={styles.description}>{product?.description}</Text>

          <Text style={styles.sectionTitle}>Specifications</Text>
          <View style={styles.specItem}>
            <MaterialIcons name="check-circle" size={16} color="#2563EB" />
            <Text style={styles.specText}>{product?.description}</Text>
          </View>
          {/* {product.specifications.map((spec, index) => (
           
          ))} */}
        </View>
      </ScrollView>

      <View style={styles.bottomBar}>
        <TouchableOpacity
          style={styles.addToCartButton}
          onPress={() => handleAddToCart(product?.id, 1, accessToken)}
        >
          <MaterialIcons name="shopping-cart" size={24} color="white" />
          <Text style={styles.buttonText}>Add to Cart</Text>
        </TouchableOpacity>

        <TouchableOpacity
          style={styles.buyNowButton}
          onPress={() => navigation.navigate("Checkout")}
        >
          <Text style={styles.buttonText}>Buy Now</Text>
        </TouchableOpacity>
      </View>
    </SafeAreaView>
  );
}

const styles = StyleSheet.create({
  loadingContainer: {
    flex: 1,
    justifyContent: "center",
    alignItems: "center",
  },
  container: {
    flex: 1,
    backgroundColor: "#ffffff",
  },
  carouselImage: {
    width: horizontalScale(377),
    height: verticalScale(300),
    resizeMode: "cover",
    borderRadius: moderateScale(5),
    borderColor: "#f3f4f6",
    borderWidth: 1,
    objectFit: "contain",
  },
  header: {
    flexDirection: "row",
    justifyContent: "space-between",
    padding: 16,
    position: "absolute",
    top: 0,
    left: 0,
    right: 0,
    zIndex: 1,
  },
  backButton: {
    backgroundColor: "white",
    borderRadius: 20,
    padding: 8,
  },
  wishlistButton: {
    backgroundColor: "white",
    borderRadius: 20,
    padding: 8,
  },
  image: {
    width: "100%",
    height: 300,
    resizeMode: "cover",
  },
  content: {
    padding: 16,
  },
  name: {
    fontSize: 24,
    fontWeight: "bold",
    marginBottom: 8,
  },
  ratingContainer: {
    flexDirection: "row",
    alignItems: "center",
    marginBottom: 8,
  },
  rating: {
    marginLeft: 4,
    fontSize: 16,
    color: "#666",
  },
  price: {
    fontSize: 24,
    fontWeight: "bold",
    color: "#2563EB",
    marginBottom: 16,
  },
  sectionTitle: {
    fontSize: 18,
    fontWeight: "600",
    marginTop: 16,
    marginBottom: 8,
  },
  description: {
    fontSize: 16,
    lineHeight: 24,
    color: "#666",
  },
  specItem: {
    flexDirection: "row",
    alignItems: "center",
    marginVertical: 4,
  },
  specText: {
    marginLeft: 8,
    fontSize: 14,
    color: "#666",
  },
  bottomBar: {
    flexDirection: "row",
    padding: 16,
    borderTopWidth: 1,
    borderTopColor: "#f3f4f6",
    backgroundColor: "white",
  },
  addToCartButton: {
    flex: 1,
    flexDirection: "row",
    backgroundColor: "#2563EB",
    padding: 16,
    borderRadius: 12,
    alignItems: "center",
    justifyContent: "center",
    marginRight: 8,
  },
  buyNowButton: {
    flex: 1,
    backgroundColor: "#1E40AF",
    padding: 16,
    borderRadius: 12,
    alignItems: "center",
    justifyContent: "center",
    marginLeft: 8,
  },
  buttonText: {
    color: "white",
    fontSize: 16,
    fontWeight: "600",
    marginLeft: 8,
  },
});
