"use client";
import {  useEffect, useState } from "react";
import { But<PERSON> } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from "@/components/ui/table";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import { Plus, Trash2, Edit, Loader2 } from "lucide-react";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Checkbox } from "@/components/ui/checkbox";
import { useRouter } from "next/navigation";
import useApi from "@/hooks/useApi";
import { useProducts } from "@/hooks/useProducts";
import { useToast } from "@/components/ui/use-toast";
import { BRANDS, CATEGORIES, MAIN_URL, PRODUCTS } from "@/constant/urls";
import { ProductImage } from "@/components/ui/optimized-image";
import Pagination from "@/components/pagination/Pagination";
import { ProductsResponse } from "@/types/product";

const Products = () => {
  const router = useRouter();
  const { toast } = useToast();
  const [selectedProducts, setSelectedProducts] = useState<string[]>([]);
  const [selectFilters, setSelectFilters] = useState({
    category: undefined as string | undefined,
    brand: undefined as string | undefined,
    stock: undefined as string | undefined,
    search: "",
  });
  const [itemsPerPage, setItemsPerPage] = useState(25);
  const [currentPage, setCurrentPage] = useState(1);
  const [deletingProducts, setDeletingProducts] = useState<string[]>([]);

  const { read, data } = useApi<ProductsResponse>(MAIN_URL);
  const { read: categoryGet, data: category } = useApi(MAIN_URL);
  const { read: brandGet, data: brand } = useApi(MAIN_URL);
  const { deleteProduct } = useProducts();
  const handleSelectAll = () => {
    
    const products = data?.results || [];
    if (selectedProducts.length === products.length) {
      setSelectedProducts([]);
    } else {
      // eslint-disable-next-line @typescript-eslint/no-explicit-any
      setSelectedProducts(products.map((product: any) => product.id));
    }
  };

  const handleSelectProduct = (productId: string) => {
    if (selectedProducts.includes(productId)) {
      setSelectedProducts(selectedProducts.filter((id) => id !== productId));
    } else {
      setSelectedProducts([...selectedProducts, productId]);
    }
  };

  const handleDeleteProduct = async (productId: string) => {
    if (deletingProducts.includes(productId)) return;

    if (!confirm("Are you sure you want to delete this product?")) return;

    setDeletingProducts(prev => [...prev, productId]);

    try {
      const success = await deleteProduct(productId);
      if (success) {
        toast({
          title: "Product deleted",
          description: "Product has been successfully deleted",
        });
        // Refresh the products list
        handleProductFilter();
        setSelectedProducts(prev => prev.filter(id => id !== productId));
      } else {
        toast({
          title: "Error",
          description: "Failed to delete product",
          variant: "destructive",
        });
      }
    } catch {
      toast({
        title: "Error",
        description: "Failed to delete product",
        variant: "destructive",
      });
    } finally {
      setDeletingProducts(prev => prev.filter(id => id !== productId));
    }
  };

  const handleDeleteSelected = async () => {
    if (selectedProducts.length === 0) return;

    if (!confirm(`Are you sure you want to delete ${selectedProducts.length} selected products?`)) return;

    const deletePromises = selectedProducts.map(id => deleteProduct(id));

    try {
      const results = await Promise.all(deletePromises);
      const successCount = results.filter(Boolean).length;

      if (successCount > 0) {
        toast({
          title: "Products deleted",
          description: `Successfully deleted ${successCount} product(s)`,
        });
        handleProductFilter();
        setSelectedProducts([]);
      }

      if (successCount < selectedProducts.length) {
        toast({
          title: "Partial success",
          description: `${successCount} of ${selectedProducts.length} products deleted`,
          variant: "destructive",
        });
      }
    } catch {
      toast({
        title: "Error",
        description: "Failed to delete selected products",
        variant: "destructive",
      });
    }
  };

  const handleProductFilter = async () => {
    try {
      let url = `${PRODUCTS}?is_admin=True&page_size=${itemsPerPage}&page=${currentPage}`;

      if (selectFilters.category) {
        url += `&category=[${selectFilters.category}]`;
      }
      if (selectFilters.category === "0") {
        url = `${PRODUCTS}?is_admin=True`;
      }
      if (selectFilters.brand) {
        url += `&brand=[${selectFilters.brand}]`;
      }
      if (selectFilters.search) {
        url += `&search=${selectFilters.search}`;
      }
      // if (selectFilters.stock) {
      //   url += `&stock=${selectFilters.stock}`;
      // }
      await read(url);
    } catch (error) {
      console.log(error);
    }
  };

  useEffect(() => {
    read(PRODUCTS + "?is_admin=True");
    categoryGet(CATEGORIES);
    brandGet(BRANDS);
      // eslint-disable-next-line react-hooks/exhaustive-deps
  }, []);

  useEffect(() => {
    const handler = setTimeout(handleProductFilter, 500);

    return () => clearTimeout(handler);
    
  // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [selectFilters, currentPage, itemsPerPage]);

  return (
    <div className="space-y-6 animate-fadeIn">
      <div className="flex items-center justify-between">
        <h1 className="text-3xl font-bold">Products</h1>
        <Button onClick={() => router.push("/products/new")}>
          <Plus className="mr-2 h-4 w-4" />
          Add Product
        </Button>
      </div>

      <Card>
        <CardHeader>
          <CardTitle>Filters</CardTitle>
        </CardHeader>
        <CardContent className="space-y-4">
          <div className="grid gap-4 md:grid-cols-4">
            <div className="flex flex-col space-y-1.5">
              <Select
                onValueChange={(value) =>
                  setSelectFilters({ ...selectFilters, category: value })
                }
              >
                <SelectTrigger>
                  <SelectValue placeholder="Category" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="0">All Categories</SelectItem>
                  {Array.isArray(category) &&
                    category.map((category) => (
                      <SelectItem key={category?.id} value={category?.id}>
                        {category?.name}
                      </SelectItem>
                    ))}
                </SelectContent>
              </Select>
            </div>
            <div className="flex flex-col space-y-1.5">
              <Select
                onValueChange={(value) =>
                  setSelectFilters({ ...selectFilters, stock: value })
                }
              >
                <SelectTrigger>
                  <SelectValue placeholder="Stock Status" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="10">In Stock</SelectItem>
                  <SelectItem value="5">Low Stock</SelectItem>
                  <SelectItem value="0">Out of Stock</SelectItem>
                </SelectContent>
              </Select>
            </div>
            <div className="flex flex-col space-y-1.5">
              <Select
                onValueChange={(value) =>
                  setSelectFilters({ ...selectFilters, brand: value })
                }
              >
                <SelectTrigger>
                  <SelectValue placeholder="Brand" />
                </SelectTrigger>
                <SelectContent>
                  {Array.isArray(brand) &&
                    brand.map((brand) => (
                      <SelectItem key={brand?.id} value={brand?.id}>
                        {brand?.name}
                      </SelectItem>
                    ))}
                </SelectContent>
              </Select>
            </div>
            <div className="flex flex-col space-y-1.5">
              <Input
                placeholder="Search products..."
                value={selectFilters.search}
                onChange={(e) =>
                  setSelectFilters({ ...selectFilters, search: e.target.value })
                }
              />
            </div>
          </div>
        </CardContent>
      </Card>

      <Card>
        <CardContent className="pt-6">
          <div className="flex items-center justify-between mb-4">
            <div className="flex items-center space-x-2">
              {selectedProducts.length > 0 && (
                <>
                  <Button variant="outline" size="sm">
                    <Edit className="mr-2 h-4 w-4" />
                    Edit Selected
                  </Button>
                  <Button
                    variant="outline"
                    size="sm"
                    onClick={handleDeleteSelected}
                    disabled={selectedProducts.length === 0}
                  >
                    <Trash2 className="mr-2 h-4 w-4" />
                    Delete Selected ({selectedProducts.length})
                  </Button>
                </>
              )}
            </div>
          </div>
          <div className="flex items-center w-full justify-end my-2 space-x-2">
            <span className="text-sm text-muted-foreground">
              Items per page:
            </span>
            <Select
              value={itemsPerPage.toString()}
              onValueChange={(value) => {
                setItemsPerPage(Number(value));
                setCurrentPage(1);
              }}
            >
              <SelectTrigger className="w-[100px]">
                <SelectValue />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="20">20</SelectItem>
                <SelectItem value="25">25</SelectItem>
                <SelectItem value="50">50</SelectItem>
                <SelectItem value="100">100</SelectItem>
              </SelectContent>
            </Select>
          </div>
          <div className="rounded-md border">
            <Table>
              <TableHeader>
                <TableRow>
                  <TableHead className="w-12">
                    <Checkbox
                      checked={selectedProducts.length === data?.count}
                      onCheckedChange={handleSelectAll}
                    />
                  </TableHead>
                  <TableHead>Product</TableHead>
                  <TableHead>Price</TableHead>
                  <TableHead>Stock</TableHead>
                  <TableHead>Category</TableHead>
                  <TableHead>Brand</TableHead>
                  <TableHead>Status</TableHead>
                  <TableHead>Image</TableHead>
                  <TableHead className="w-24">Actions</TableHead>
                </TableRow>
              </TableHeader>
              <TableBody>
                {Array.isArray(data?.results) &&
                  data?.results?.map((product) => (
                    <TableRow key={product.id}>
                      <TableCell>
                        <Checkbox
                          checked={selectedProducts.includes(String(product.id))}
                          onCheckedChange={() =>
                            handleSelectProduct(String(product.id))
                          }
                        />
                      </TableCell>
                      <TableCell className="font-medium">
                        {product.name}
                      </TableCell>
                      <TableCell>₹{product.price}</TableCell>
                      <TableCell>{product.stock}</TableCell>
                      <TableCell>{product?.category?.name}</TableCell>
                      <TableCell>{product?.brand?.name}</TableCell>
                      <TableCell>
                        <Checkbox
                          checked={product?.is_active}
                          onCheckedChange={() =>
                            handleSelectProduct(String(product.id))
                          }
                        />
                      </TableCell>
                      <TableCell>
                        <ProductImage
                          src={product?.images?.[0]?.image || "/assets/product_icon.jpg"}
                          alt={product?.name || "Product image"}
                          size="md"
                        />
                      </TableCell>
                      <TableCell>
                        <div className="flex items-center space-x-2">
                          <Button
                            variant="ghost"
                            size="icon"
                            onClick={() =>
                              router.push(`/products/edit/${product.id}`)
                            }
                          >
                            <Edit className="h-4 w-4" />
                          </Button>
                          <Button
                            variant="ghost"
                            size="icon"
                            onClick={() => handleDeleteProduct(String(product.id))}
                            disabled={deletingProducts.includes(String(product.id))}
                          >
                            {deletingProducts.includes(String(product.id)) ? (
                              <Loader2 className="h-4 w-4 animate-spin" />
                            ) : (
                              <Trash2 className="h-4 w-4" />
                            )}
                          </Button>
                        </div>
                      </TableCell>
                    </TableRow>
                  ))}
              </TableBody>
            </Table>
          </div>
          <Pagination
            itemsPerPage={itemsPerPage}
            currentPage={currentPage}
            setCurrentPage={setCurrentPage}
            count={data?.count ?? 0}
          />
        </CardContent>
      </Card>
    </div>
  );
};

export default Products;
