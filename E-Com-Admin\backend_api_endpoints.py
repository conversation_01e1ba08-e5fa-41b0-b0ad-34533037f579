# Django Backend API Endpoints for Product Management
# Add these to your e-com-2024-apis/products/views.py

from rest_framework import generics, permissions, status
from rest_framework.decorators import api_view, permission_classes
from rest_framework.response import Response
from django.shortcuts import get_object_or_404
from .models import Product, ProductImage, Category, Brand
from .serializers import (
    ProductSerializer,
    ProductDetailSerializer,
    ProductImageSerializer,
    CategorySerializer,
    BrandSerializer
)

class ProductCreateView(generics.CreateAPIView):
    """Create a new product"""
    serializer_class = ProductSerializer
    permission_classes = [permissions.IsAdminUser]
    
    def perform_create(self, serializer):
        # Auto-generate slug from name if not provided
        serializer.save()

class ProductUpdateView(generics.RetrieveUpdateAPIView):
    """Update an existing product"""
    queryset = Product.objects.all()
    serializer_class = ProductSerializer
    permission_classes = [permissions.IsAdminUser]
    lookup_field = 'id'

class ProductDeleteView(generics.DestroyAPIView):
    """Delete a product"""
    queryset = Product.objects.all()
    permission_classes = [permissions.IsAdminUser]
    lookup_field = 'id'

@api_view(['POST'])
@permission_classes([permissions.IsAdminUser])
def upload_product_image(request):
    """Upload a product image"""
    serializer = ProductImageSerializer(data=request.data)
    if serializer.is_valid():
        image = serializer.save()
        return Response({
            'id': image.id,
            'image': request.build_absolute_uri(image.image.url),
            'is_primary': image.is_primary
        }, status=status.HTTP_201_CREATED)
    return Response(serializer.errors, status=status.HTTP_400_BAD_REQUEST)

# Add these URL patterns to your e-com-2024-apis/products/urls.py:

"""
from .views import (
    ProductCreateView,
    ProductUpdateView, 
    ProductDeleteView,
    upload_product_image
)

# Add to urlpatterns:
path('create/', ProductCreateView.as_view(), name='product-create'),
path('<int:id>/update/', ProductUpdateView.as_view(), name='product-update'),
path('<int:id>/delete/', ProductDeleteView.as_view(), name='product-delete'),
path('images/upload/', upload_product_image, name='product-image-upload'),
"""

# Update your ProductSerializer to handle creation properly:

"""
class ProductSerializer(serializers.ModelSerializer):
    category_name = serializers.CharField(source='category.name', read_only=True)
    brand_name = serializers.CharField(source='brand.name', read_only=True)
    images = ProductImageSerializer(many=True, read_only=True)

    class Meta:
        model = Product
        fields = [
            'id', 'name', 'slug', 'description', 'price', 'mrp', 'base_price',
            'stock', 'is_active', 'category', 'category_name', 'brand', 'brand_name',
            'images', 'created_at', 'updated_at'
        ]
        read_only_fields = ['slug', 'created_at', 'updated_at']

    def create(self, validated_data):
        # Handle category and brand by ID
        return super().create(validated_data)

    def update(self, instance, validated_data):
        # Handle partial updates
        return super().update(instance, validated_data)
"""

# IMPLEMENTATION STEPS:

# 1. Copy the views above to your e-com-2024-apis/products/views.py
# 2. Add the URL patterns to your e-com-2024-apis/products/urls.py
# 3. Update your ProductSerializer if needed
# 4. Set up proper file upload handling in settings.py:

"""
# In settings.py
MEDIA_URL = '/media/'
MEDIA_ROOT = os.path.join(BASE_DIR, 'media')

# For production, consider using AWS S3 or similar
"""

# 5. Ensure your Product model has the required fields:

"""
class Product(models.Model):
    name = models.CharField(max_length=255)
    slug = models.SlugField(unique=True)
    description = models.TextField()
    price = models.DecimalField(max_digits=10, decimal_places=2)
    mrp = models.DecimalField(max_digits=10, decimal_places=2, null=True, blank=True)
    base_price = models.DecimalField(max_digits=10, decimal_places=2)
    stock = models.PositiveIntegerField()
    is_active = models.BooleanField(default=True)
    category = models.ForeignKey(Category, on_delete=models.CASCADE)
    brand = models.ForeignKey(Brand, on_delete=models.SET_NULL, null=True, blank=True)
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)

    def save(self, *args, **kwargs):
        if not self.slug:
            self.slug = slugify(self.name)
        super().save(*args, **kwargs)
"""
