#!/usr/bin/env python
"""
Comprehensive Privacy Features Test Runner
Runs all privacy-related tests and generates detailed reports
"""

import os
import sys
import django
import time
import json
from datetime import datetime

# Add the project directory to Python path
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

# Setup Django
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'backend.settings')
django.setup()

from django.test.utils import get_runner
from django.conf import settings
from django.core.management import call_command
from users.privacy_performance_monitor import privacy_monitor
from users.privacy_security_audit import run_privacy_security_audit

class PrivacyTestRunner:
    """Comprehensive test runner for privacy features"""
    
    def __init__(self):
        self.test_results = {
            'timestamp': datetime.now().isoformat(),
            'tests': {},
            'performance': {},
            'security': {},
            'summary': {}
        }
    
    def run_all_tests(self):
        """Run all privacy-related tests"""
        print("🔒 Starting Comprehensive Privacy Features Test Suite")
        print("=" * 60)
        
        # 1. Run unit tests
        print("\n📋 Running Unit Tests...")
        self._run_unit_tests()
        
        # 2. Run CSV export tests
        print("\n📊 Testing CSV Export Functionality...")
        self._test_csv_export()
        
        # 3. Run consent management tests
        print("\n🍪 Testing Consent Management...")
        self._test_consent_management()
        
        # 4. Run performance tests
        print("\n⚡ Running Performance Tests...")
        self._run_performance_tests()
        
        # 5. Run security audit
        print("\n🛡️ Running Security Audit...")
        self._run_security_audit()
        
        # 6. Generate summary
        print("\n📈 Generating Test Summary...")
        self._generate_summary()
        
        # 7. Save results
        self._save_results()
        
        # 8. Display results
        self._display_results()
    
    def _run_unit_tests(self):
        """Run Django unit tests for privacy features"""
        try:
            from django.test.runner import DiscoverRunner
            
            test_runner = DiscoverRunner(verbosity=1, interactive=False)
            
            # Run specific privacy tests
            test_labels = [
                'users.tests.test_privacy_comprehensive',
            ]
            
            start_time = time.time()
            failures = test_runner.run_tests(test_labels)
            end_time = time.time()
            
            self.test_results['tests']['unit_tests'] = {
                'status': 'PASSED' if failures == 0 else 'FAILED',
                'failures': failures,
                'execution_time': round(end_time - start_time, 2),
                'test_labels': test_labels
            }
            
            if failures == 0:
                print("✅ Unit tests passed")
            else:
                print(f"❌ Unit tests failed ({failures} failures)")
                
        except Exception as e:
            print(f"❌ Unit tests error: {str(e)}")
            self.test_results['tests']['unit_tests'] = {
                'status': 'ERROR',
                'error': str(e),
                'execution_time': 0
            }
    
    def _test_csv_export(self):
        """Test CSV export functionality"""
        try:
            # Import and run CSV export test
            from test_csv_export import test_csv_generation
            
            start_time = time.time()
            success = test_csv_generation()
            end_time = time.time()
            
            self.test_results['tests']['csv_export'] = {
                'status': 'PASSED' if success else 'FAILED',
                'execution_time': round(end_time - start_time, 2),
                'features_tested': [
                    'CSV structure validation',
                    'Order items inclusion',
                    'Data completeness',
                    'Format correctness'
                ]
            }
            
            if success:
                print("✅ CSV export tests passed")
            else:
                print("❌ CSV export tests failed")
                
        except Exception as e:
            print(f"❌ CSV export test error: {str(e)}")
            self.test_results['tests']['csv_export'] = {
                'status': 'ERROR',
                'error': str(e),
                'execution_time': 0
            }
    
    def _test_consent_management(self):
        """Test consent management functionality"""
        try:
            from django.contrib.auth import get_user_model
            from users.models import UserConsent
            from users.privacy_views import ConsentManagementView
            from django.test import RequestFactory
            
            User = get_user_model()
            
            # Create test user
            test_user = User.objects.create_user(
                email='<EMAIL>',
                name='Test Consent User',
                password='testpass123'
            )
            
            start_time = time.time()
            
            # Test consent creation
            UserConsent.objects.create(
                user=test_user,
                consent_type='MARKETING',
                granted=True
            )
            
            # Test consent retrieval
            consent = UserConsent.objects.get(
                user=test_user,
                consent_type='MARKETING'
            )
            
            # Test consent withdrawal
            consent.withdraw_consent('127.0.0.1', 'test-agent')
            
            end_time = time.time()
            
            # Cleanup
            test_user.delete()
            
            self.test_results['tests']['consent_management'] = {
                'status': 'PASSED',
                'execution_time': round(end_time - start_time, 2),
                'features_tested': [
                    'Consent creation',
                    'Consent retrieval',
                    'Consent withdrawal',
                    'Data persistence'
                ]
            }
            
            print("✅ Consent management tests passed")
            
        except Exception as e:
            print(f"❌ Consent management test error: {str(e)}")
            self.test_results['tests']['consent_management'] = {
                'status': 'ERROR',
                'error': str(e),
                'execution_time': 0
            }
    
    def _run_performance_tests(self):
        """Run performance tests"""
        try:
            start_time = time.time()
            
            # Get performance dashboard data
            dashboard_data = privacy_monitor.get_performance_dashboard_data()
            
            # Generate performance report
            report = privacy_monitor.generate_performance_report(days=1)
            
            end_time = time.time()
            
            self.test_results['performance'] = {
                'status': 'COMPLETED',
                'execution_time': round(end_time - start_time, 2),
                'dashboard_data': dashboard_data,
                'report_summary': {
                    'total_operations': report['summary'].get('total_operations', 0),
                    'overall_avg_time': report['summary'].get('overall_avg_time', 0),
                    'active_alerts': report['summary'].get('active_alerts', 0)
                },
                'recommendations_count': len(report.get('recommendations', []))
            }
            
            print("✅ Performance tests completed")
            
        except Exception as e:
            print(f"❌ Performance test error: {str(e)}")
            self.test_results['performance'] = {
                'status': 'ERROR',
                'error': str(e),
                'execution_time': 0
            }
    
    def _run_security_audit(self):
        """Run security audit"""
        try:
            start_time = time.time()
            
            audit_results = run_privacy_security_audit()
            
            end_time = time.time()
            
            self.test_results['security'] = {
                'status': 'COMPLETED',
                'execution_time': round(end_time - start_time, 2),
                'risk_level': audit_results['risk_assessment']['level'],
                'total_findings': audit_results['risk_assessment']['total_issues'],
                'high_severity_issues': audit_results['risk_assessment']['high_severity_issues'],
                'medium_severity_issues': audit_results['risk_assessment']['medium_severity_issues'],
                'recommendation': audit_results['risk_assessment']['recommendation']
            }
            
            risk_level = audit_results['risk_assessment']['level']
            if risk_level in ['MINIMAL', 'LOW']:
                print(f"✅ Security audit completed - Risk Level: {risk_level}")
            else:
                print(f"⚠️ Security audit completed - Risk Level: {risk_level}")
            
        except Exception as e:
            print(f"❌ Security audit error: {str(e)}")
            self.test_results['security'] = {
                'status': 'ERROR',
                'error': str(e),
                'execution_time': 0
            }
    
    def _generate_summary(self):
        """Generate test summary"""
        total_tests = len([t for t in self.test_results['tests'].values() if t.get('status')])
        passed_tests = len([t for t in self.test_results['tests'].values() if t.get('status') == 'PASSED'])
        failed_tests = len([t for t in self.test_results['tests'].values() if t.get('status') == 'FAILED'])
        error_tests = len([t for t in self.test_results['tests'].values() if t.get('status') == 'ERROR'])
        
        total_time = sum([
            t.get('execution_time', 0) for t in self.test_results['tests'].values()
        ]) + self.test_results['performance'].get('execution_time', 0) + \
            self.test_results['security'].get('execution_time', 0)
        
        self.test_results['summary'] = {
            'total_tests': total_tests,
            'passed_tests': passed_tests,
            'failed_tests': failed_tests,
            'error_tests': error_tests,
            'success_rate': round((passed_tests / total_tests) * 100, 1) if total_tests > 0 else 0,
            'total_execution_time': round(total_time, 2),
            'overall_status': 'PASSED' if failed_tests == 0 and error_tests == 0 else 'FAILED'
        }
    
    def _save_results(self):
        """Save test results to file"""
        timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
        filename = f'privacy_test_results_{timestamp}.json'
        
        with open(filename, 'w') as f:
            json.dump(self.test_results, f, indent=2)
        
        print(f"📄 Test results saved to: {filename}")
    
    def _display_results(self):
        """Display test results summary"""
        print("\n" + "=" * 60)
        print("🔒 PRIVACY FEATURES TEST SUMMARY")
        print("=" * 60)
        
        summary = self.test_results['summary']
        
        print(f"📊 Total Tests: {summary['total_tests']}")
        print(f"✅ Passed: {summary['passed_tests']}")
        print(f"❌ Failed: {summary['failed_tests']}")
        print(f"💥 Errors: {summary['error_tests']}")
        print(f"📈 Success Rate: {summary['success_rate']}%")
        print(f"⏱️ Total Time: {summary['total_execution_time']}s")
        print(f"🎯 Overall Status: {summary['overall_status']}")
        
        # Security summary
        if self.test_results['security'].get('status') == 'COMPLETED':
            security = self.test_results['security']
            print(f"\n🛡️ Security Risk Level: {security['risk_level']}")
            print(f"🔍 Security Issues Found: {security['total_findings']}")
        
        # Performance summary
        if self.test_results['performance'].get('status') == 'COMPLETED':
            perf = self.test_results['performance']
            print(f"\n⚡ Performance Recommendations: {perf['recommendations_count']}")
            print(f"📊 Active Alerts: {perf['report_summary']['active_alerts']}")
        
        print("\n" + "=" * 60)
        
        if summary['overall_status'] == 'PASSED':
            print("🎉 ALL PRIVACY TESTS PASSED! System is ready for production.")
        else:
            print("⚠️ SOME TESTS FAILED! Please review the issues before deployment.")
        
        print("=" * 60)

def main():
    """Main function to run privacy tests"""
    runner = PrivacyTestRunner()
    runner.run_all_tests()

if __name__ == "__main__":
    main()
