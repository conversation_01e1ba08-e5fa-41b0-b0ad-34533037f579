<!DOCTYPE html>
<html>
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Password Reset Request - Triumph Enterprises</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            line-height: 1.6;
            color: #333;
            max-width: 600px;
            margin: 0 auto;
            padding: 20px;
            background-color: #f4f4f4;
        }
        .container {
            background-color: white;
            padding: 30px;
            border-radius: 10px;
            box-shadow: 0 0 10px rgba(0,0,0,0.1);
        }
        .header {
            text-align: center;
            margin-bottom: 30px;
            padding-bottom: 20px;
            border-bottom: 2px solid #4a5568;
        }
        .logo {
            max-width: 150px;
            margin-bottom: 10px;
        }
        h1 {
            color: #4a5568;
            margin: 0;
            font-size: 24px;
        }
        .content {
            margin-bottom: 30px;
        }
        .reset-button {
            display: inline-block;
            background-color: #4a5568;
            color: white;
            padding: 15px 30px;
            text-decoration: none;
            border-radius: 5px;
            font-weight: bold;
            margin: 20px 0;
            text-align: center;
        }
        .reset-button:hover {
            background-color: #2d3748;
        }
        .security-info {
            background-color: #fff3e0;
            padding: 15px;
            border-left: 4px solid #ff9800;
            margin: 20px 0;
            border-radius: 4px;
        }
        .warning {
            background-color: #ffebee;
            padding: 15px;
            border-left: 4px solid #f44336;
            margin: 20px 0;
            border-radius: 4px;
        }
        .footer {
            margin-top: 30px;
            padding-top: 20px;
            border-top: 1px solid #eee;
            font-size: 12px;
            color: #777;
            text-align: center;
        }
        .token-info {
            background-color: #f8f9fa;
            padding: 10px;
            border-radius: 4px;
            font-family: monospace;
            word-break: break-all;
            margin: 10px 0;
        }
        @media (max-width: 600px) {
            body {
                padding: 10px;
            }
            .container {
                padding: 20px;
            }
            .reset-button {
                display: block;
                width: 100%;
                box-sizing: border-box;
            }
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>🔐 Password Reset Request</h1>
            <p style="margin: 0; color: #666;">{{ site_name }}</p>
        </div>
        
        <div class="content">
            <p>Hello {{ user.name }},</p>
            
            <p>We received a request to reset the password for your account associated with <strong>{{ user.email }}</strong>.</p>
            
            <div class="security-info">
                <strong>🛡️ Security Information:</strong>
                <ul style="margin: 10px 0;">
                    <li>Request made from IP: <code>{{ ip_address }}</code></li>
                    <li>Request time: <strong>{{ expires_at }}</strong></li>
                    <li>This link will expire in <strong>15 minutes</strong></li>
                </ul>
            </div>
            
            <p>If you requested this password reset, click the button below to create a new password:</p>
            
            <div style="text-align: center;">
                <a href="{{ reset_url }}" class="reset-button">Reset My Password</a>
            </div>
            
            <p>Or copy and paste this link into your browser:</p>
            <div class="token-info">{{ reset_url }}</div>
            
            <div class="warning">
                <strong>⚠️ Important Security Notice:</strong>
                <ul style="margin: 10px 0;">
                    <li>If you did not request this password reset, please ignore this email</li>
                    <li>Your current password will remain unchanged</li>
                    <li>Never share this reset link with anyone</li>
                    <li>This link can only be used once</li>
                    <li>For security, this link expires in 15 minutes</li>
                </ul>
            </div>
            
            <p><strong>Password Security Requirements:</strong></p>
            <ul>
                <li>At least 8 characters long</li>
                <li>Must contain uppercase and lowercase letters</li>
                <li>Must contain at least one number</li>
                <li>Must contain at least one special character</li>
                <li>Cannot be one of your last 5 passwords</li>
            </ul>
            
            <p>If you're having trouble with the button above, you can also reset your password by visiting our website and entering this token manually:</p>
            <div class="token-info">{{ token }}</div>
            
            <p>If you have any questions or concerns about your account security, please contact our support team immediately.</p>
            
            <p>Best regards,<br>
            <strong>{{ site_name }} Security Team</strong></p>
        </div>
        
        <div class="footer">
            <p>This is an automated security email from {{ site_name }}.</p>
            <p>If you did not request this password reset, please contact support immediately.</p>
            <p>© 2024 {{ site_name }}. All rights reserved.</p>
        </div>
    </div>
</body>
</html>
