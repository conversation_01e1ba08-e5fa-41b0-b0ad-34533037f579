// import { createContext, useContext, useState, useEffect } from "react";
// import * as SecureStore from "expo-secure-store";
// import axios from "axios";
// import { BaseURL } from "@/constants/ApiEndpoint";
// import { Dispatch, SetStateAction } from "react";

// interface User {
//   email: string;
//   name: string;
// }

// interface AuthContextType {
//   isAuthenticated: boolean;
//   setIsAuthenticated: Dispatch<SetStateAction<boolean>>;
//   user: User | null;
//   login: (email: string, password: string) => Promise<void>;
//   signup: (email: string, password: string) => Promise<void>;
//   logout: () => Promise<void>;
//   requireAuth: (action: string, navigation: any) => boolean;
//   handleProtectedAction: (
//     action: string,
//     navigation: any,
//     callback: () => void
//   ) => void;
//   accessToken: string;
//   refreshToken: string;
//   loading: boolean;
// }

// const AuthContext = createContext<AuthContextType | undefined>(undefined);

// const SECURE_STORE_KEYS = {
//   USER: "user",
//   TOKEN: "token",
//   REFRESH_TOKEN: "refresh_token",
// };

// export function AuthProvider({ children }: { children: React.ReactNode }) {
//   const [isAuthenticated, setIsAuthenticated] = useState(false);
//   const [user, setUser] = useState<User | null>(null);
//   const [loading, setLoading] = useState(true);
//   const [accessToken, setAccessToken] = useState<string>("");
//   const [refreshToken, setRefreshToken] = useState<string>("");

//   useEffect(() => {
//     loadStoredAuth();
//   }, []);
  
//   const loadStoredAuth = async () => {
//     try {
//       const [storedUser, storedToken, storedRefreshToken] = await Promise.all([
//         SecureStore.getItemAsync(SECURE_STORE_KEYS.USER),
//         SecureStore.getItemAsync(SECURE_STORE_KEYS.TOKEN),
//         SecureStore.getItemAsync(SECURE_STORE_KEYS.REFRESH_TOKEN),
//       ]);

//       if (storedUser && storedToken) {
//         setUser(JSON.parse(storedUser));
//         setAccessToken(storedToken);
//         setRefreshToken(storedRefreshToken);
//         setIsAuthenticated(true);
//       }
//     } catch (error) {
//       console.error("Error loading stored auth:", error);
//     } finally {
//       setLoading(false);
//     }
//   };

//   async function refreshAccessToken(token: { refresh: string }) { 
//     try { 
//       const res = await axios.post(`{$BaseURL}/api/v1/users/token/refresh/`, { 
//         refresh: token, 
//       }); 
  
//       return { 
//         ...token, 
//         access: res.data.access, 
//         accessTokenExpires: Date.now() + 60 * 60 * 1000, // 1 hour from now 
//       }; 
//     } catch (error) {
//       console.error("Error refreshing token:", error);
//       // Clear auth state on refresh failure
//       await Promise.all([
//         SecureStore.deleteItemAsync(SECURE_STORE_KEYS.USER),
//         SecureStore.deleteItemAsync(SECURE_STORE_KEYS.TOKEN),
//         SecureStore.deleteItemAsync(SECURE_STORE_KEYS.REFRESH_TOKEN),
//       ]);
      
//       return { error: "RefreshAccessTokenError" }; 
//     } 
//   }

//   const login = async (email: string, password: string) => {
//     try {
//       // Fix the API call - data should be the second parameter
//       const response = await axios.post(
//         `${BaseURL}/api/v1/users/login/`, 
//         { email, password }
//       );
      
//       if (response?.data) {
//         const userData = response.data;
//         const token = response.data.accessToken;
//         const refreshToken = response.data.refreshToken; // Assuming the API returns a refresh token
    
//         // Make sure we have data before setting state
//         if (userData && token) {
//           setUser(userData);
//           setAccessToken(token);
//           setRefreshToken(refreshToken); // Set the refresh token in state
//           // Store in SecureStore
//           await Promise.all([
//             SecureStore.setItemAsync(SECURE_STORE_KEYS.USER, JSON.stringify(userData)),
//             SecureStore.setItemAsync(SECURE_STORE_KEYS.TOKEN, token),
//             SecureStore.setItemAsync(SECURE_STORE_KEYS.REFRESH_TOKEN, refreshToken), // Store the refresh token
//           ]);
          
//           setIsAuthenticated(true);
//         } else {
//           throw new Error("Invalid response format");
//         }
//       }
//     } catch (error) {
//       console.error("Login failed:", error);
//       throw new Error("Login failed");
//     }
//   };


//   const signup = async (email: string, password: string) => {
//     try {
//       // Fix the API call - implement actual signup endpoint
//       const response = await axios.post(
//         `${BaseURL}/api/v1/users/register/`, 
//         { email, password }
//       );
      
//       if (response?.data) {
//         const userData = response.data;
//         const token = response.data.accessToken;
//         const refreshToken = response.data.refreshToken; // Assuming the API returns a refresh token
        
//         if (userData && token) {
//           // Store in SecureStore
//           await Promise.all([
//             SecureStore.setItemAsync(SECURE_STORE_KEYS.USER, JSON.stringify(userData)),
//             SecureStore.setItemAsync(SECURE_STORE_KEYS.TOKEN, token),
//             SecureStore.setItemAsync(SECURE_STORE_KEYS.REFRESH_TOKEN, refreshToken), // Store the refresh token
//           ]);

//           setUser(userData);
//           setAccessToken(token);
//           setRefreshToken(refreshToken); // Set the refresh token in state
//           setIsAuthenticated(true);
//         } else {
//           throw new Error("Invalid response format");
//         }
//       }
//     } catch (error) {
//       console.error("Signup failed:", error);
//       throw new Error("Signup failed");
//     }
//   };

//   const logout = async () => {
//     try {
//       // Clear SecureStore
      
//       const response = await axios.post(`${BaseURL}/api/v1/users/token/blacklist/`,{ refresh: refreshToken })
//       if (response.status === 200) {
//         await Promise.all([
//           SecureStore.deleteItemAsync(SECURE_STORE_KEYS.USER),
//           SecureStore.deleteItemAsync(SECURE_STORE_KEYS.TOKEN),
//           SecureStore.deleteItemAsync(SECURE_STORE_KEYS.REFRESH_TOKEN),
//         ]);
//       }

//       setUser(null);
//       setAccessToken("");
//       setIsAuthenticated(false);
//     } catch (error) {
//       console.error("Logout failed:", error);
//     }
//   };

//   const requireAuth = (action: string, navigation: any) => {
//     if (!isAuthenticated) {
//       navigation?.navigate("Auth", {
//         returnTo: action,
//         returnScreen: navigation?.getCurrentRoute()?.name,
//       });
//       return false;
//     }
//     return true;
//   };

//   const handleProtectedAction = (
//     action: string,
//     navigation: any,
//     callback: () => void
//   ) => {
//     if (isAuthenticated) {
//       callback();
//     } else {
//       navigation?.navigate("Auth", {
//         returnTo: action,
//         returnScreen: navigation?.getCurrentRoute()?.name,
//         onAuthenticated: callback,
//       });
//     }
//   };

//   return (
//     <AuthContext.Provider
//       value={{
//         isAuthenticated,
//         setIsAuthenticated,
//         accessToken,
//         refreshToken,
//         user,
//         login,
//         signup,
//         logout,
//         requireAuth,
//         handleProtectedAction,
//         loading,
//       }}
//     >
//       {children}
//     </AuthContext.Provider>
//   );
// }

// export const useAuth = () => {
//   const context = useContext(AuthContext);
//   if (!context) {
//     throw new Error("useAuth must be used within an AuthProvider");
//   }
//   return context;
// };


import { createContext, useContext, useState, useEffect } from "react";
import * as SecureStore from "expo-secure-store";
import axios from "axios";
import { BaseURL } from "@/constants/ApiEndpoint";
import { Dispatch, SetStateAction } from "react";

interface User {
  email: string;
  name: string;
}

interface AuthContextType {
  isAuthenticated: boolean;
  setIsAuthenticated: Dispatch<SetStateAction<boolean>>;
  user: User | null;
  login: (email: string, password: string) => Promise<void>;
  signup: (email: string, password: string) => Promise<void>;
  logout: () => Promise<void>;
  accessToken: string;
  refreshToken: string;
  loading: boolean;
}

const AuthContext = createContext<AuthContextType | undefined>(undefined);

const SECURE_STORE_KEYS = {
  USER: "user",
  TOKEN: "token",
  REFRESH_TOKEN: "refresh_token",
};

export function AuthProvider({ children }: { children: React.ReactNode }) {
  const [isAuthenticated, setIsAuthenticated] = useState(false);
  const [user, setUser] = useState<User | null>(null);
  const [loading, setLoading] = useState(true);
  const [accessToken, setAccessToken] = useState<string>("");
  const [refreshToken, setRefreshToken] = useState<string>("");

  useEffect(() => {
    loadStoredAuth();
  }, []);

  useEffect(() => {
    if (accessToken) {
      setupAxiosInterceptors();
    }
  }, [accessToken]);

  const loadStoredAuth = async () => {
    try {
      const [storedUser, storedToken, storedRefreshToken] = await Promise.all([
        SecureStore.getItemAsync(SECURE_STORE_KEYS.USER),
        SecureStore.getItemAsync(SECURE_STORE_KEYS.TOKEN),
        SecureStore.getItemAsync(SECURE_STORE_KEYS.REFRESH_TOKEN),
      ]);

      if (storedUser && storedToken && storedRefreshToken) {
        setUser(JSON.parse(storedUser));
        setAccessToken(storedToken);
        setRefreshToken(storedRefreshToken);
        setIsAuthenticated(true);
      }
    } catch (error) {
      console.error("Error loading stored auth:", error);
    } finally {
      setLoading(false);
    }
  };

  const refreshAccessToken = async () => {
    try {
      const res = await axios.post(`${BaseURL}/api/v1/users/token/refresh/`, {
        refresh: refreshToken,
      });

      if (res?.data?.access) {
        const newAccessToken = res.data.access;
        setAccessToken(newAccessToken);
        await SecureStore.setItemAsync(SECURE_STORE_KEYS.TOKEN, newAccessToken);
        return newAccessToken;
      } else {
        throw new Error("Invalid refresh token response");
      }
    } catch (error) {
      console.error("Error refreshing token:", error);
      await logout();
      return null;
    }
  };

  const setupAxiosInterceptors = () => {
    axios.interceptors.request.clear();
    axios.interceptors.response.clear();

    axios.interceptors.request.use(
      async (config) => {
        if (accessToken) {
          config.headers.Authorization = `Bearer ${accessToken}`;
        }
        return config;
      },
      (error) => Promise.reject(error)
    );

    axios.interceptors.response.use(
      (response) => response,
      async (error) => {
        if (error.response?.status === 401 && refreshToken) {
          console.log("401 detected, attempting to refresh token...");
          const newToken = await refreshAccessToken();
          if (newToken) {
            error.config.headers.Authorization = `Bearer ${newToken}`;
            return axios(error.config);
          }
        }
        return Promise.reject(error);
      }
    );
  };

  const login = async (email: string, password: string) => {
    try {
      const response = await axios.post(`${BaseURL}/api/v1/users/login/`, {
        email,
        password,
      });

      if (response?.data) {
        const userData = response.data;
        const token = response.data.accessToken;
        const refreshToken = response.data.refreshToken;

        if (userData && token && refreshToken) {
          setUser(userData);
          setAccessToken(token);
          setRefreshToken(refreshToken);
          await Promise.all([
            SecureStore.setItemAsync(
              SECURE_STORE_KEYS.USER,
              JSON.stringify(userData)
            ),
            SecureStore.setItemAsync(SECURE_STORE_KEYS.TOKEN, token),
            SecureStore.setItemAsync(
              SECURE_STORE_KEYS.REFRESH_TOKEN,
              refreshToken
            ),
          ]);

          setIsAuthenticated(true);
        } else {
          throw new Error("Invalid response format");
        }
      }
    } catch (error) {
      console.error("Login failed:", error);
      throw new Error("Login failed");
    }
  };

  // const signup = async (email: string, password: string) => {
  //   try {
  //     const response = await axios.post(`${BaseURL}/api/v1/users/register/`, {
  //       email,
  //       password,
  //     });

  //     if (response?.data) {
  //       const userData = response.data;
  //       const token = response.data.accessToken;
  //       const refreshToken = response.data.refreshToken;

  //       if (userData && token && refreshToken) {
  //         await Promise.all([
  //           SecureStore.setItemAsync(
  //             SECURE_STORE_KEYS.USER,
  //             JSON.stringify(userData)
  //           ),
  //           SecureStore.setItemAsync(SECURE_STORE_KEYS.TOKEN, token),
  //           SecureStore.setItemAsync(
  //             SECURE_STORE_KEYS.REFRESH_TOKEN,
  //             refreshToken
  //           ),
  //         ]);

  //         setUser(userData);
  //         setAccessToken(token);
  //         setRefreshToken(refreshToken);
  //         setIsAuthenticated(true);
  //       } else {
  //         throw new Error("Invalid response format");
  //       }
  //     }
  //   } catch (error) {
  //     console.error("Signup failed:", error);
  //     throw new Error("Signup failed");
  //   }
  // };


  const signup = async (
    email: string,
    password: string,
    name: string,
    phone_number: string,
    date_of_birth: string
  ) => {
    try {
      const response = await axios.post(`${BaseURL}/api/v1/users/`, {
        email,
        password,
        name,
        phone_number,
        date_of_birth,
      });
  
      if (response?.data) {
        const userData = response.data;
        const token = response.data.accessToken;
        const refreshToken = response.data.refreshToken;
  
        if (userData && token && refreshToken) {
          await Promise.all([
            SecureStore.setItemAsync(
              SECURE_STORE_KEYS.USER,
              JSON.stringify(userData)
            ),
            SecureStore.setItemAsync(SECURE_STORE_KEYS.TOKEN, token),
            SecureStore.setItemAsync(
              SECURE_STORE_KEYS.REFRESH_TOKEN,
              refreshToken
            ),
          ]);
  
          setUser(userData);
          setAccessToken(token);
          setRefreshToken(refreshToken);
          setIsAuthenticated(true);
        } else {
          throw new Error("Invalid response format");
        }
      }
    } catch (error) {
      console.error("Signup failed:", error);
      throw new Error("Signup failed");
    }
  };
  
  const logout = async () => {
    try {
      if (refreshToken) {
        await axios.post(`${BaseURL}/api/v1/users/token/blacklist/`, {
          refresh: refreshToken,
        });
      }

      await Promise.all([
        SecureStore.deleteItemAsync(SECURE_STORE_KEYS.USER),
        SecureStore.deleteItemAsync(SECURE_STORE_KEYS.TOKEN),
        SecureStore.deleteItemAsync(SECURE_STORE_KEYS.REFRESH_TOKEN),
      ]);

      setUser(null);
      setAccessToken("");
      setRefreshToken("");
      setIsAuthenticated(false);
    } catch (error) {
      console.error("Logout failed:", error);
    }
  };

  return (
    <AuthContext.Provider
      value={{
        isAuthenticated,
        setIsAuthenticated,
        accessToken,
        refreshToken,
        user,
        login,
        signup,
        logout,
        loading,
      }}
    >
      {children}
    </AuthContext.Provider>
  );
}

export const useAuth = () => {
  const context = useContext(AuthContext);
  if (!context) {
    throw new Error("useAuth must be used within an AuthProvider");
  }
  return context;
};
