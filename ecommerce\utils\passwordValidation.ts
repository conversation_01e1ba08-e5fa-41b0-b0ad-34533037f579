/**
 * Password validation utilities for enhanced security
 */

export interface PasswordRequirement {
  id: string;
  label: string;
  isValid: boolean;
  description: string;
}

export interface PasswordValidationResult {
  isValid: boolean;
  score: number; // 0-100
  requirements: PasswordRequirement[];
  errors: string[];
}

/**
 * Validate password strength with comprehensive requirements
 */
export function validatePasswordStrength(password: string): PasswordValidationResult {
  const requirements: PasswordRequirement[] = [
    {
      id: 'length',
      label: 'At least 8 characters',
      isValid: password.length >= 8,
      description: 'Password must be at least 8 characters long'
    },
    {
      id: 'uppercase',
      label: 'One uppercase letter',
      isValid: /[A-Z]/.test(password),
      description: 'Password must contain at least one uppercase letter'
    },
    {
      id: 'lowercase',
      label: 'One lowercase letter',
      isValid: /[a-z]/.test(password),
      description: 'Password must contain at least one lowercase letter'
    },
    {
      id: 'number',
      label: 'One number',
      isValid: /\d/.test(password),
      description: 'Password must contain at least one number'
    },
    {
      id: 'special',
      label: 'One special character',
      isValid: /[!@#$%^&*(),.?":{}|<>]/.test(password),
      description: 'Password must contain at least one special character'
    }
  ];

  // Check for weak patterns
  const weakPatterns = [
    { pattern: /123456/, message: 'Contains common sequence "123456"' },
    { pattern: /password/i, message: 'Contains the word "password"' },
    { pattern: /qwerty/i, message: 'Contains keyboard pattern "qwerty"' },
    { pattern: /abc123/i, message: 'Contains common pattern "abc123"' },
    { pattern: /admin/i, message: 'Contains the word "admin"' },
    { pattern: /letmein/i, message: 'Contains common phrase "letmein"' },
    { pattern: /welcome/i, message: 'Contains the word "welcome"' },
    { pattern: /monkey/i, message: 'Contains common word "monkey"' }
  ];

  // Check for sequential patterns
  const sequentialPatterns = [
    { pattern: /(012|123|234|345|456|567|678|789|890)/, message: 'Contains sequential numbers' },
    { pattern: /(abc|bcd|cde|def|efg|fgh|ghi|hij|ijk|jkl|klm|lmn|mno|nop|opq|pqr|qrs|rst|stu|tuv|uvw|vwx|wxy|xyz)/i, message: 'Contains sequential letters' }
  ];

  const errors: string[] = [];
  
  // Check weak patterns
  for (const { pattern, message } of weakPatterns) {
    if (pattern.test(password)) {
      errors.push(message);
      break; // Only show one weak pattern error
    }
  }

  // Check sequential patterns
  for (const { pattern, message } of sequentialPatterns) {
    if (pattern.test(password)) {
      errors.push(message);
      break; // Only show one sequential pattern error
    }
  }

  // Check maximum length
  if (password.length > 128) {
    errors.push('Password must not exceed 128 characters');
  }

  // Calculate score
  const validRequirements = requirements.filter(req => req.isValid).length;
  let score = (validRequirements / requirements.length) * 80; // Base score from requirements

  // Bonus points for length
  if (password.length >= 12) score += 10;
  if (password.length >= 16) score += 5;

  // Penalty for weak patterns
  if (errors.length > 0) score -= 20;

  // Ensure score is between 0-100
  score = Math.max(0, Math.min(100, score));

  const isValid = requirements.every(req => req.isValid) && errors.length === 0;

  return {
    isValid,
    score,
    requirements,
    errors
  };
}

/**
 * Get password strength level based on score
 */
export function getPasswordStrengthLevel(score: number): {
  level: 'weak' | 'fair' | 'good' | 'strong';
  color: string;
  description: string;
} {
  if (score < 30) {
    return {
      level: 'weak',
      color: 'text-red-500',
      description: 'Weak - Please strengthen your password'
    };
  } else if (score < 60) {
    return {
      level: 'fair',
      color: 'text-orange-500',
      description: 'Fair - Consider adding more complexity'
    };
  } else if (score < 80) {
    return {
      level: 'good',
      color: 'text-yellow-500',
      description: 'Good - Almost there!'
    };
  } else {
    return {
      level: 'strong',
      color: 'text-green-500',
      description: 'Strong - Excellent password!'
    };
  }
}

/**
 * Generate password strength color for progress bar
 */
export function getPasswordStrengthColor(score: number): string {
  if (score < 30) return 'bg-red-500';
  if (score < 60) return 'bg-orange-500';
  if (score < 80) return 'bg-yellow-500';
  return 'bg-green-500';
}

/**
 * Validate if passwords match
 */
export function validatePasswordMatch(password: string, confirmPassword: string): {
  isValid: boolean;
  error?: string;
} {
  if (!confirmPassword) {
    return { isValid: false, error: 'Please confirm your password' };
  }
  
  if (password !== confirmPassword) {
    return { isValid: false, error: 'Passwords do not match' };
  }
  
  return { isValid: true };
}

/**
 * Validate email format
 */
export function validateEmail(email: string): {
  isValid: boolean;
  error?: string;
} {
  if (!email) {
    return { isValid: false, error: 'Email is required' };
  }
  
  const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
  if (!emailRegex.test(email)) {
    return { isValid: false, error: 'Please enter a valid email address' };
  }
  
  return { isValid: true };
}
