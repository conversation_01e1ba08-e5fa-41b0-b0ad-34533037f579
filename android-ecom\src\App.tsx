import { Navigation<PERSON>ontainer } from "@react-navigation/native";
import { createBottomTabNavigator } from "@react-navigation/bottom-tabs";
import { createNativeStackNavigator } from "@react-navigation/native-stack";
import { StyleSheet } from "react-native";
import { SafeAreaProvider } from "react-native-safe-area-context";
import { MaterialIcons } from "@expo/vector-icons";
import HomeScreen from "../screens/HomeScreen";
import CartScreen from "../screens/CartScreen";
import ProfileScreen from "../screens/ProfileScreen";
import WishlistScreen from "../screens/WishlistScreen";
import CategoriesScreen from "../screens/CategoriesScreen";
import ProductDetailsScreen from "../screens/ProductDetailsScreen";
import CheckoutScreen from "../screens/CheckoutScreen";
import { AuthProvider } from "@/context/AuthContext";
import AuthScreen from "@/screens/AuthScreen";
import { registerRootComponent } from "expo";
import SearchResultsScreen from "@/screens/SearchResultsScreen";
import OrderDetailsScreen from "@/screens/OrderDetailsScreen";
import OrdersScreen from "@/screens/OrdersScreen";
import ShippingAddressScreen from "@/screens/ShippingAddressScreen";
import CategoriesProductScreen from "@/screens/CategoriesProductScreen";
import OrderConfirmationScreen from "@/screens/OrderConfirmationScreen";
import { ToastProvider } from "@/toast/ToastProvider";
const Stack = createNativeStackNavigator();
const Tab = createBottomTabNavigator();

function HomeStack() {
  return (
    <Stack.Navigator screenOptions={{ headerShown: false }}>
      <Stack.Screen name="Home" component={HomeScreen} />
      <Stack.Screen name="ProductDetails" component={ProductDetailsScreen} />
      <Stack.Screen
        name="CategoriesProduct"
        component={CategoriesProductScreen}
      />
      <Stack.Screen
        name="OrderConfirmation"
        component={OrderConfirmationScreen}
      />
    </Stack.Navigator>
  );
}

function TabNavigator() {
  return (
    <Tab.Navigator
      screenOptions={({ route }) => ({
        tabBarIcon: ({ focused, color, size }) => {
          let iconName;
          switch (route.name) {
            case "HomeTab":
              iconName = "home";
              break;
            case "Categories":
              iconName = "category";
              break;
            case "Cart":
              iconName = "shopping-cart";
              break;
            case "Wishlist":
              iconName = "favorite";
              break;
            case "Profile":
              iconName = "person";
              break;
            default:
              iconName = "home";
          }
          return <MaterialIcons name={iconName} size={size} color={color} />;
        },
        tabBarActiveTintColor: "#2563EB",
        tabBarInactiveTintColor: "gray",
        headerShown: false,
      })}
    >
      <Tab.Screen
        name="HomeTab"
        component={HomeStack}
        options={{ title: "Home" }}
      />
      <Tab.Screen name="Categories" component={CategoriesScreen} />
      <Tab.Screen name="Cart" component={CartScreen} />
      <Tab.Screen name="Wishlist" component={WishlistScreen} />
      <Tab.Screen name="Profile" component={ProfileScreen} />
    </Tab.Navigator>
  );
}

function RootStack() {
  return (
    <Stack.Navigator screenOptions={{ headerShown: false }}>
      <Stack.Screen name="MainTabs" component={TabNavigator} />
      <Stack.Screen name="ProductDetails" component={ProductDetailsScreen} />
      <Stack.Screen name="Checkout" component={CheckoutScreen} />
      <Stack.Screen name="Auth" component={AuthScreen} />
      <Stack.Screen name="SearchResults" component={SearchResultsScreen} />
      <Stack.Screen name="Orders" component={OrdersScreen} />
      <Stack.Screen name="OrderDetails" component={OrderDetailsScreen} />
      <Stack.Screen name="ShippingAddress" component={ShippingAddressScreen} />
      <Stack.Screen name="CategoriesProduct" component={CategoriesProductScreen} />
      <Stack.Screen name="OrderConfirmation" component={OrderConfirmationScreen} />
    </Stack.Navigator>
  );
}

function App() {
  return (
    <NavigationContainer>
      <AuthProvider>
        <RootStack />
        <ToastProvider />
      </AuthProvider>
    </NavigationContainer>
  );
}

export default registerRootComponent(App);
