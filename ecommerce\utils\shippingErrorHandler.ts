/**
 * Shipping Error Handling Utilities
 * Provides centralized error handling for shipping-related operations
 */

import { toast } from '@/components/ui/use-toast';

export interface ShippingError {
  code: string;
  message: string;
  details?: any;
  recoverable: boolean;
  fallbackAction?: () => void;
}

export class ShippingErrorHandler {
  private static instance: ShippingErrorHandler;
  private errorLog: ShippingError[] = [];

  private constructor() {}

  static getInstance(): ShippingErrorHandler {
    if (!ShippingErrorHandler.instance) {
      ShippingErrorHandler.instance = new ShippingErrorHandler();
    }
    return ShippingErrorHandler.instance;
  }

  /**
   * Handle API errors from shipping services
   */
  handleApiError(error: any, context: string = 'shipping'): ShippingError {
    let shippingError: ShippingError;

    if (error?.response) {
      // HTTP error response
      const status = error.response.status;
      const data = error.response.data;

      switch (status) {
        case 400:
          shippingError = {
            code: 'INVALID_REQUEST',
            message: data?.message || 'Invalid shipping request. Please check your input.',
            details: data,
            recoverable: true
          };
          break;

        case 401:
          shippingError = {
            code: 'UNAUTHORIZED',
            message: 'Authentication required. Please log in again.',
            details: data,
            recoverable: true,
            fallbackAction: () => window.location.href = '/auth/login'
          };
          break;

        case 403:
          shippingError = {
            code: 'FORBIDDEN',
            message: 'Access denied to shipping service.',
            details: data,
            recoverable: false
          };
          break;

        case 404:
          shippingError = {
            code: 'NOT_FOUND',
            message: context === 'tracking' 
              ? 'Tracking information not found for this order.'
              : 'Shipping service endpoint not found.',
            details: data,
            recoverable: false
          };
          break;

        case 429:
          shippingError = {
            code: 'RATE_LIMITED',
            message: 'Too many requests. Please wait a moment and try again.',
            details: data,
            recoverable: true
          };
          break;

        case 503:
          shippingError = {
            code: 'SERVICE_UNAVAILABLE',
            message: 'Shipping service is temporarily unavailable. Using standard rates.',
            details: data,
            recoverable: true
          };
          break;

        default:
          shippingError = {
            code: 'API_ERROR',
            message: data?.message || `Shipping service error (${status}). Please try again.`,
            details: data,
            recoverable: true
          };
      }
    } else if (error?.code === 'NETWORK_ERROR' || error?.message?.includes('Network')) {
      shippingError = {
        code: 'NETWORK_ERROR',
        message: 'Network connection issue. Please check your internet connection.',
        details: error,
        recoverable: true
      };
    } else if (error?.name === 'AbortError') {
      shippingError = {
        code: 'REQUEST_CANCELLED',
        message: 'Request was cancelled.',
        details: error,
        recoverable: true
      };
    } else {
      shippingError = {
        code: 'UNKNOWN_ERROR',
        message: error?.message || 'An unexpected error occurred with shipping service.',
        details: error,
        recoverable: true
      };
    }

    this.logError(shippingError, context);
    return shippingError;
  }

  /**
   * Handle validation errors
   */
  handleValidationError(field: string, value: any, rule: string): ShippingError {
    const errorMessages: Record<string, string> = {
      'pincode.required': 'Delivery pincode is required.',
      'pincode.format': 'Pincode must be 6 digits.',
      'pincode.invalid': 'Invalid pincode format.',
      'weight.min': 'Package weight must be at least 0.1 kg.',
      'weight.max': 'Package weight cannot exceed 50 kg.',
      'total_value.min': 'Order total must be greater than 0.',
      'courier.required': 'Please select a shipping method.',
      'address.required': 'Shipping address is required.'
    };

    const key = `${field}.${rule}`;
    const message = errorMessages[key] || `Invalid ${field}: ${rule}`;

    const shippingError: ShippingError = {
      code: 'VALIDATION_ERROR',
      message,
      details: { field, value, rule },
      recoverable: true
    };

    this.logError(shippingError, 'validation');
    return shippingError;
  }

  /**
   * Handle service degradation gracefully
   */
  handleServiceDegradation(service: 'rapidshyp' | 'fallback', reason: string): ShippingError {
    const shippingError: ShippingError = {
      code: 'SERVICE_DEGRADED',
      message: service === 'rapidshyp' 
        ? 'Live shipping rates are temporarily unavailable. Using standard rates.'
        : 'Standard shipping methods are temporarily unavailable.',
      details: { service, reason },
      recoverable: true
    };

    this.logError(shippingError, 'service_degradation');
    return shippingError;
  }

  /**
   * Show user-friendly error message
   */
  showErrorToast(error: ShippingError, showDetails: boolean = false) {
    const description = showDetails && error.details 
      ? `${error.message} (${error.code})`
      : error.message;

    toast({
      title: this.getErrorTitle(error.code),
      description,
      variant: error.recoverable ? 'default' : 'destructive',
      duration: error.recoverable ? 5000 : 8000
    });

    // Execute fallback action if available
    if (error.fallbackAction) {
      setTimeout(error.fallbackAction, 2000);
    }
  }

  /**
   * Get user-friendly error title
   */
  private getErrorTitle(code: string): string {
    const titles: Record<string, string> = {
      'INVALID_REQUEST': 'Invalid Input',
      'UNAUTHORIZED': 'Authentication Required',
      'FORBIDDEN': 'Access Denied',
      'NOT_FOUND': 'Not Found',
      'RATE_LIMITED': 'Too Many Requests',
      'SERVICE_UNAVAILABLE': 'Service Unavailable',
      'NETWORK_ERROR': 'Connection Issue',
      'REQUEST_CANCELLED': 'Request Cancelled',
      'VALIDATION_ERROR': 'Validation Error',
      'SERVICE_DEGRADED': 'Service Notice',
      'API_ERROR': 'Service Error',
      'UNKNOWN_ERROR': 'Unexpected Error'
    };

    return titles[code] || 'Error';
  }

  /**
   * Log error for debugging and monitoring
   */
  private logError(error: ShippingError, context: string) {
    const logEntry = {
      ...error,
      context,
      timestamp: new Date().toISOString(),
      userAgent: navigator.userAgent,
      url: window.location.href
    };

    this.errorLog.push(error);
    
    // Keep only last 50 errors
    if (this.errorLog.length > 50) {
      this.errorLog = this.errorLog.slice(-50);
    }

    // Log to console in development
    if (process.env.NODE_ENV === 'development') {
      console.error('Shipping Error:', logEntry);
    }

    // In production, you might want to send to monitoring service
    // this.sendToMonitoring(logEntry);
  }

  /**
   * Get error statistics for monitoring
   */
  getErrorStats() {
    const stats = this.errorLog.reduce((acc, error) => {
      acc[error.code] = (acc[error.code] || 0) + 1;
      return acc;
    }, {} as Record<string, number>);

    return {
      totalErrors: this.errorLog.length,
      errorsByCode: stats,
      recoverableErrors: this.errorLog.filter(e => e.recoverable).length,
      criticalErrors: this.errorLog.filter(e => !e.recoverable).length
    };
  }

  /**
   * Clear error log
   */
  clearErrorLog() {
    this.errorLog = [];
  }

  /**
   * Check if service should be degraded based on error patterns
   */
  shouldDegradeService(service: string): boolean {
    const recentErrors = this.errorLog
      .filter(e => Date.now() - new Date(e.details?.timestamp || 0).getTime() < 300000) // Last 5 minutes
      .filter(e => e.details?.service === service);

    // Degrade if more than 3 errors in 5 minutes
    return recentErrors.length > 3;
  }
}

// Export singleton instance
export const shippingErrorHandler = ShippingErrorHandler.getInstance();

// Utility functions for common error scenarios
export const handleShippingApiError = (error: any, context?: string) => {
  const shippingError = shippingErrorHandler.handleApiError(error, context);
  shippingErrorHandler.showErrorToast(shippingError);
  return shippingError;
};

export const handleShippingValidationError = (field: string, value: any, rule: string) => {
  const shippingError = shippingErrorHandler.handleValidationError(field, value, rule);
  shippingErrorHandler.showErrorToast(shippingError);
  return shippingError;
};

export const handleServiceDegradation = (service: 'rapidshyp' | 'fallback', reason: string) => {
  const shippingError = shippingErrorHandler.handleServiceDegradation(service, reason);
  shippingErrorHandler.showErrorToast(shippingError);
  return shippingError;
};
