import { useCallback, useRef, useState } from "react";
import { Upload, X, Image<PERSON><PERSON>, Loader2 } from "lucide-react";
import { Button } from "@/components/ui/button";
import { useToast } from "@/components/ui/use-toast";
import { useProducts } from "@/hooks/useProducts";
import Image from "next/image";

interface ImageUploadProps {
  value: string[];
  onChange: (value: string[]) => void;
  maxImages?: number;
}

export const ImageUpload = ({ value, onChange, maxImages = 5 }: ImageUploadProps) => {
  const fileInputRef = useRef<HTMLInputElement>(null);
  const [dragActive, setDragActive] = useState(false);
  const { uploadProductImage, loading } = useProducts();
  const { toast } = useToast();

  const handleFileUpload = useCallback(async (files: FileList | null) => {
    if (!files || files.length === 0) return;

    if (value.length + files.length > maxImages) {
      toast({
        title: "Too many images",
        description: `You can only upload up to ${maxImages} images`,
        variant: "destructive",
      });
      return;
    }

    const uploadPromises = Array.from(files).map(async (file) => {
      if (!file.type.startsWith('image/')) {
        toast({
          title: "Invalid file type",
          description: "Please upload only image files",
          variant: "destructive",
        });
        return null;
      }

      if (file.size > 5 * 1024 * 1024) { // 5MB limit
        toast({
          title: "File too large",
          description: "Please upload images smaller than 5MB",
          variant: "destructive",
        });
        return null;
      }

      try {
        const response = await uploadProductImage(file);
        if (response) {
          return response.image;
        }
        return null;
      } catch (error) {
        console.error('Upload error:', error);
        return null;
      }
    });

    const uploadedUrls = await Promise.all(uploadPromises);
    const validUrls = uploadedUrls.filter((url): url is string => url !== null);

    if (validUrls.length > 0) {
      onChange([...value, ...validUrls]);
      toast({
        title: "Images uploaded",
        description: `Successfully uploaded ${validUrls.length} image(s)`,
      });
    }
  }, [value, onChange, maxImages, uploadProductImage, toast]);

  const handleUpload = useCallback(() => {
    fileInputRef.current?.click();
  }, []);

  const removeImage = (urlToRemove: string) => {
    onChange(value.filter((url) => url !== urlToRemove));
  };

  const handleDrag = useCallback((e: React.DragEvent) => {
    e.preventDefault();
    e.stopPropagation();
    if (e.type === "dragenter" || e.type === "dragover") {
      setDragActive(true);
    } else if (e.type === "dragleave") {
      setDragActive(false);
    }
  }, []);

  const handleDrop = useCallback((e: React.DragEvent) => {
    e.preventDefault();
    e.stopPropagation();
    setDragActive(false);

    if (e.dataTransfer.files && e.dataTransfer.files[0]) {
      handleFileUpload(e.dataTransfer.files);
    }
  }, [handleFileUpload]);

  return (
    <div className="space-y-4">
      {/* Image Grid */}
      <div className="grid grid-cols-2 gap-4 md:grid-cols-4">
        {value.map((url, index) => (
          <div key={url} className="relative aspect-square group">
            <Image
              src={url}
              alt={`Product image ${index + 1}`}
              fill
              className="rounded-lg object-cover transition-all group-hover:brightness-75"
            />
            <Button
              variant="destructive"
              size="icon"
              className="absolute right-2 top-2 h-6 w-6 opacity-0 group-hover:opacity-100 transition-opacity"
              onClick={() => removeImage(url)}
              disabled={loading}
            >
              <X className="h-3 w-3" />
            </Button>
            {index === 0 && (
              <div className="absolute bottom-2 left-2 bg-primary text-primary-foreground text-xs px-2 py-1 rounded">
                Primary
              </div>
            )}
          </div>
        ))}
      </div>

      {/* Upload Area */}
      <div
        className={`border-2 border-dashed rounded-lg p-6 text-center transition-colors ${
          dragActive
            ? "border-primary bg-primary/5"
            : "border-muted-foreground/25 hover:border-primary/50"
        }`}
        onDragEnter={handleDrag}
        onDragLeave={handleDrag}
        onDragOver={handleDrag}
        onDrop={handleDrop}
      >
        <input
          ref={fileInputRef}
          type="file"
          multiple
          accept="image/*"
          onChange={(e) => handleFileUpload(e.target.files)}
          className="hidden"
        />

        <div className="flex flex-col items-center space-y-2">
          {loading ? (
            <Loader2 className="h-8 w-8 animate-spin text-muted-foreground" />
          ) : (
            <ImageIcon className="h-8 w-8 text-muted-foreground" />
          )}

          <div className="space-y-1">
            <p className="text-sm font-medium">
              {loading ? "Uploading..." : "Drop images here or click to upload"}
            </p>
            <p className="text-xs text-muted-foreground">
              PNG, JPG, JPEG up to 5MB each (max {maxImages} images)
            </p>
          </div>

          <Button
            type="button"
            variant="outline"
            onClick={handleUpload}
            disabled={loading || value.length >= maxImages}
          >
            <Upload className="mr-2 h-4 w-4" />
            {loading ? "Uploading..." : "Choose Files"}
          </Button>
        </div>
      </div>

      {value.length > 0 && (
        <p className="text-sm text-muted-foreground">
          {value.length} of {maxImages} images uploaded. First image will be used as primary.
        </p>
      )}
    </div>
  );
};