import { useState, useEffect } from "react";
import { ChevronLeft, ChevronRight } from "lucide-react";
import AnimatedButton from "../ui/AnimatedButton";
import { useRouter } from "next/navigation";
import Image from "next/image";

const slides = [
  {
    image: "/home/<USER>",
    title: "Discover Perfect Style",
    subtitle: "From timeless classics to modern must-haves",
  },
  {
    image: "/home/<USER>",
    title: "Stylish & Catchy",
    subtitle: "Drive Confidently with Qubo Dashcam Pro",
  },
  {
    image: "/home/<USER>",
    title: "Premium Quality",
    subtitle: "Crafted with attention to detail",
  },
  {
    image: "/home/<USER>",
    title: "Perfect Pricing",
    subtitle: "Price that never before",
  },
];

const HeroSlider = () => {
  const [currentSlide, setCurrentSlide] = useState(0);
  const [isAnimating, setIsAnimating] = useState(false);
  const router = useRouter();

  const nextSlide = () => {
    if (isAnimating) return;
    setIsAnimating(true);
    setCurrentSlide((prev) => (prev + 1) % slides.length);
    setTimeout(() => setIsAnimating(false), 600);
  };

  const prevSlide = () => {
    if (isAnimating) return;
    setIsAnimating(true);
    setCurrentSlide((prev) => (prev - 1 + slides.length) % slides.length);
    setTimeout(() => setIsAnimating(false), 600);
  };

  useEffect(() => {
    const timer = setInterval(nextSlide, 5000);
    return () => clearInterval(timer);
  }, []);

  return (
    <div className="relative h-screen w-full overflow-hidden">
      {slides.map((slide, index) => (
        <div
          key={index}
          className={`absolute inset-0 transition-opacity duration-600 ${
            currentSlide === index ? "opacity-100" : "opacity-0"
          }`}
        >
          <Image
            width={100}
            height={100}
            src={slide.image}
            alt={slide.title}
            className="absolute inset-0 w-full h-full object-cover"
            loading="lazy"
          />
          <div className="slide-content">
            <div
              className={`text-center space-y-6 max-w-4xl px-4 ${
                currentSlide === index ? "animate-fade-in" : ""
              }`}
            >
              <h1 className="text-4xl md:text-6xl font-bold tracking-tight text-balance">
                {slide.title}
              </h1>
              <p className="text-xl md:text-2xl text-neutral-200">
                {slide.subtitle}
              </p>
              <div className=" flex flex-col sm:flex-row gap-4 justify-center pt-4">
                <AnimatedButton
                  onClick={() => {
                    router.push("/shop");
                  }}
                >
                  Shop Now
                </AnimatedButton>
                <AnimatedButton
                  onClick={() => {
                    router.push("/shop");
                  }}
                  variant="secondary"
                >
                  View Collections
                </AnimatedButton>
              </div>
            </div>
          </div>
        </div>
      ))}

      <button
        onClick={prevSlide}
        className="absolute left-4 top-1/2 -translate-y-1/2 p-2 rounded-full bg-white/10 backdrop-blur-md hover:bg-white/20 transition-colors"
        aria-label="Previous slide"
      >
        <ChevronLeft className="w-6 h-6 text-white" />
      </button>
      <button
        onClick={nextSlide}
        className="absolute right-4 top-1/2 -translate-y-1/2 p-2 rounded-full bg-white/10 backdrop-blur-md hover:bg-white/20 transition-colors"
        aria-label="Next slide"
      >
        <ChevronRight className="w-6 h-6 text-white" />
      </button>

      <div className="absolute bottom-8 left-1/2 -translate-x-1/2 flex gap-2">
        {slides.map((_, index) => (
          <button
            key={index}
            onClick={() => setCurrentSlide(index)}
            className={`w-2 h-2 rounded-full transition-all duration-300 ${
              currentSlide === index
                ? "bg-white w-8"
                : "bg-white/50 hover:bg-white/75"
            }`}
            aria-label={`Go to slide ${index + 1}`}
          />
        ))}
      </div>
    </div>
  );
};

export default HeroSlider;
