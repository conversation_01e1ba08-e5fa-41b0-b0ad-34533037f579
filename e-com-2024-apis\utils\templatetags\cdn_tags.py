"""
Template tags for CDN integration
"""

from django import template
from django.conf import settings
from backend.cdn_settings import get_cdn_url, USE_CDN

register = template.Library()

@register.simple_tag
def cdn_static(path):
    """
    Template tag to generate a CDN URL for a static file
    
    Usage:
        {% load cdn_tags %}
        <img src="{% cdn_static 'images/logo.png' %}" alt="Logo">
    """
    if path.startswith('/'):
        path = path[1:]
    return get_cdn_url(f'/static/{path}')

@register.simple_tag
def cdn_media(path):
    """
    Template tag to generate a CDN URL for a media file
    
    Usage:
        {% load cdn_tags %}
        <img src="{% cdn_media 'products/product1.jpg' %}" alt="Product">
    """
    if path.startswith('/'):
        path = path[1:]
    return get_cdn_url(f'/media/{path}')

@register.simple_tag
def is_using_cdn():
    """
    Template tag to check if CDN is being used
    
    Usage:
        {% load cdn_tags %}
        {% if is_using_cdn %}
            <!-- CDN-specific content -->
        {% else %}
            <!-- Local content -->
        {% endif %}
    """
    return USE_CDN
