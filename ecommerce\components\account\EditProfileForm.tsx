import React, { use<PERSON><PERSON>back, useState } from "react";
import { Formik, Form, Field, FormikHelpers } from "formik";
import * as Yup from "yup";
import { useDropzone, DropzoneOptions } from "react-dropzone";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Switch } from "@/components/ui/switch";
import { toast } from "@/components/ui/use-toast";
import { X, Upload } from "lucide-react";
import useApi from "@/hooks/useApi";
import { MAIN_URL, PROFILE_UPDATE } from "@/constant/urls";

// Interface for the user data
interface User {
  name: string;
  email: string;
  phone_number?: string;
  date_of_birth?: string;
  display_image_url?: string;
}

// Props interface for the component
interface EditProfileFormProps {
  user: User;
  onClose: () => void;
  setUser: (user: User) => void;
}

// Interface for form values
interface FormValues {
  name: string;
  email: string;
  phone: string;
  dob: string;
  currentPassword: string;
  newPassword: string;
  confirmPassword: string;
  profilePhoto: File | null;
}

// Create separate validation schemas
const profileValidationSchema = Yup.object().shape({
  name: Yup.string()
    .min(2, "Name must be at least 2 characters")
    .required("Name is required"),
  email: Yup.string()
    .email("Invalid email address")
    .required("Email is required"),
  phone: Yup.string()
    .matches(
      /^((\\+[1-9]{1,4}[ -]?)|(\([0-9]{2,3}\)[ -]?)|([0-9]{2,4})[ -]?)*?[0-9]{3,4}[ -]?[0-9]{3,4}$/,
      "Invalid phone number"
    )
    .required("Phone number is required"),
  dob: Yup.date()
    .max(new Date(), "Date of birth cannot be in the future")
    .required("Date of birth is required"),
});

const passwordValidationSchema = Yup.object().shape({
  currentPassword: Yup.string()
    .min(6, "Current password must be at least 6 characters")
    .required("Current password is required"),
  newPassword: Yup.string()
    .min(6, "New password must be at least 6 characters")
    .required("New password is required"),
  confirmPassword: Yup.string()
    .required("Please confirm your new password")
    .oneOf([Yup.ref("newPassword")], "Passwords must match"),
});

const EditProfileForm: React.FC<EditProfileFormProps> = ({
  user,
  onClose,
  setUser,
}) => {
  const [preview, setPreview] = useState<string | null>(
    user?.display_image_url ? MAIN_URL + user?.display_image_url : null
  );
  const [showPasswordSection, setShowPasswordSection] = useState(false);
  const [isPasswordOnly, setIsPasswordOnly] = useState(false);
  const { update, error, loading } = useApi(MAIN_URL);

  const onDrop = useCallback(
    (
      acceptedFiles: File[],
      setFieldValue: (field: string, value: any) => void
    ) => {
      const file = acceptedFiles[0];
      if (file) {
        setFieldValue("profilePhoto", file);
        const reader = new FileReader();
        reader.onloadend = () => {
          setPreview(reader.result as string);
        };
        reader.readAsDataURL(file);
      }
    },
    []
  );

  const dropzoneOptions: DropzoneOptions = {
    accept: {
      "image/*": [".jpeg", ".jpg", ".png", ".gif"],
    },
    maxFiles: 1,
    onDrop: (acceptedFiles) => onDrop(acceptedFiles, () => {}),
  };

  const { getRootProps, getInputProps, isDragActive } =
    useDropzone(dropzoneOptions);

  const handleSubmit = async (
    values: FormValues,
    { setSubmitting }: FormikHelpers<FormValues>
  ) => {
    try {
      const formData = new FormData();

      if (values.name) {
        formData.append("name", values.name);
      }
      if (values.email) {
        formData.append("email", values.email);
      }
      if (values.phone) {
        formData.append("phone_number", values.phone);
      }
      if (values.dob) {
        formData.append("date_of_birth", values.dob);
      }
      if (values.profilePhoto) {
        formData.append("image", values.profilePhoto);
      }

      // Handle password change fields
      if (values.currentPassword) {
        formData.append("current_password", values.currentPassword);
        formData.append("new_password", values.newPassword);
        formData.append("confirm_password", values.confirmPassword);
      }

      const res: any = await update(PROFILE_UPDATE, formData);

      if (Boolean(res?.id)) {
        toast({
          title: "Success",
          description: `${
            isPasswordOnly ? "Password" : "Profile"
          } updated successfully`,
        });
        setUser(res);
        onClose();
      } else {
        toast({
          title: "Error",
          description: Object.values(res),
          variant: "destructive",
        });
      }
    } catch (error) {
      toast({
        title: "Error",
        description: "Failed to update profile",
        variant: "destructive",
      });
    } finally {
      setSubmitting(false);
    }
  };

  const getValidationSchema = () => {
    if (isPasswordOnly) {
      return passwordValidationSchema;
    }
    if (!showPasswordSection) {
      return profileValidationSchema;
    }
    return Yup.object()
      .concat(profileValidationSchema)
      .concat(passwordValidationSchema);
  };

  const initialValues: FormValues = {
    name: isPasswordOnly ? "" : user.name,
    email: isPasswordOnly ? "" : user.email,
    phone: isPasswordOnly ? "" : user.phone_number || "",
    dob: isPasswordOnly ? "" : user.date_of_birth || "",
    currentPassword: "",
    newPassword: "",
    confirmPassword: "",
    profilePhoto: null,
  };

  return (
    <div className="space-y-6">
      {/* Mode Selection */}
      <div className="flex items-center justify-between border-b pb-4">
        <Label htmlFor="password-only">Password Change Only</Label>
        <Switch
          id="password-only"
          checked={isPasswordOnly}
          onCheckedChange={(checked) => {
            setIsPasswordOnly(checked);
            setShowPasswordSection(checked);
          }}
        />
      </div>

      {!isPasswordOnly && (
        <div className="flex items-center justify-between">
          <Label htmlFor="show-password">Include Password Change</Label>
          <Switch
            id="show-password"
            checked={showPasswordSection}
            onCheckedChange={setShowPasswordSection}
          />
        </div>
      )}

      <Formik
        initialValues={initialValues}
        validationSchema={getValidationSchema()}
        onSubmit={handleSubmit}
        enableReinitialize
      >
        {({
          errors,
          touched,
          setFieldValue,
          isSubmitting,
          handleChange,
          values,
        }) => (
          <Form className="space-y-6">
            {!isPasswordOnly && (
              <>
                {/* Profile Photo Upload */}
                <div className="space-y-2">
                  <Label>Profile Photo</Label>
                  <div
                    {...getRootProps()}
                    className={`border-2 border-dashed rounded-lg p-4 text-center cursor-pointer
                      ${
                        isDragActive
                          ? "border-primary bg-primary/10"
                          : "border-gray-300"
                      }`}
                  >
                    <input
                      {...getInputProps()}
                      onChange={(
                        event: React.ChangeEvent<HTMLInputElement>
                      ) => {
                        if (event.target.files) {
                          onDrop([event.target.files[0]], setFieldValue);
                        }
                      }}
                    />
                    {preview ? (
                      <div className="relative w-32 h-32 mx-auto">
                        <img
                          src={preview}
                          alt="Preview"
                          className="w-full h-full object-cover rounded-full"
                        />
                        <button
                          type="button"
                          onClick={(e: React.MouseEvent) => {
                            e.stopPropagation();
                            setPreview(null);
                            setFieldValue("profilePhoto", null);
                          }}
                          className="absolute -top-2 -right-2 bg-destructive text-destructive-foreground rounded-full p-1"
                        >
                          <X className="w-4 h-4" />
                        </button>
                      </div>
                    ) : (
                      <div className="flex flex-col items-center">
                        <Upload className="w-8 h-8 mb-2 text-gray-500" />
                        <p>Drag & drop or click to upload</p>
                      </div>
                    )}
                  </div>
                </div>

                {/* Profile Fields */}
                <div className="space-y-4">
                  <div className="space-y-2">
                    <Label htmlFor="name">Name</Label>
                    <Field name="name">
                      {({ field }: { field: any }) => (
                        <div>
                          <Input
                            {...field}
                            id="name"
                            placeholder="Enter your name"
                          />
                          {errors.name && touched.name && (
                            <p className="text-sm text-destructive mt-1">
                              {errors.name}
                            </p>
                          )}
                        </div>
                      )}
                    </Field>
                  </div>

                  <div className="space-y-2">
                    <Label htmlFor="email">Email</Label>
                    <Field name="email">
                      {({ field }: { field: any }) => (
                        <div>
                          <Input
                            {...field}
                            type="email"
                            id="email"
                            placeholder="Enter your email"
                          />
                          {errors.email && touched.email && (
                            <p className="text-sm text-destructive mt-1">
                              {errors.email}
                            </p>
                          )}
                        </div>
                      )}
                    </Field>
                  </div>

                  <div className="space-y-2">
                    <Label htmlFor="phone">Phone</Label>
                    <Field name="phone">
                      {({ field }: { field: any }) => (
                        <div>
                          <Input
                            {...field}
                            id="phone"
                            placeholder="Enter your phone number"
                          />
                          {errors.phone && touched.phone && (
                            <p className="text-sm text-destructive mt-1">
                              {errors.phone}
                            </p>
                          )}
                        </div>
                      )}
                    </Field>
                  </div>

                  <div className="space-y-2">
                    <Label htmlFor="dob">Date of Birth</Label>
                    <Field name="dob">
                      {({ field }: { field: any }) => (
                        <div>
                          <Input {...field} type="date" id="dob" />
                          {errors.dob && touched.dob && (
                            <p className="text-sm text-destructive mt-1">
                              {errors.dob}
                            </p>
                          )}
                        </div>
                      )}
                    </Field>
                  </div>
                </div>
              </>
            )}

            {/* Password Change Section */}
            {(showPasswordSection || isPasswordOnly) && (
              <div className="space-y-4 border-t pt-4">
                <h3 className="font-medium">Change Password</h3>

                <div className="space-y-2">
                  <Label htmlFor="currentPassword">Current Password</Label>
                  <Field name="currentPassword">
                    {({ field }: { field: any }) => (
                      <div>
                        <Input
                          {...field}
                          type="password"
                          id="currentPassword"
                        />
                        {errors.currentPassword && touched.currentPassword && (
                          <p className="text-sm text-destructive mt-1">
                            {errors.currentPassword}
                          </p>
                        )}
                      </div>
                    )}
                  </Field>
                </div>

                <div className="space-y-2">
                  <Label htmlFor="newPassword">New Password</Label>
                  <Field name="newPassword">
                    {({ field }: { field: any }) => (
                      <div>
                        <Input {...field} type="password" id="newPassword" />
                        {errors.newPassword && touched.newPassword && (
                          <p className="text-sm text-destructive mt-1">
                            {errors.newPassword}
                          </p>
                        )}
                      </div>
                    )}
                  </Field>
                </div>

                <div className="space-y-2">
                  <Label htmlFor="confirmPassword">Confirm New Password</Label>
                  <Field name="confirmPassword">
                    {({ field }: { field: any }) => (
                      <div>
                        <Input
                          {...field}
                          type="password"
                          id="confirmPassword"
                        />
                        {errors.confirmPassword && touched.confirmPassword && (
                          <p className="text-sm text-destructive mt-1">
                            {errors.confirmPassword}
                          </p>
                        )}
                      </div>
                    )}
                  </Field>
                </div>
              </div>
            )}

            {/* Form Actions */}
            <div className="flex justify-end space-x-4 pt-6">
              <Button type="button" variant="outline" onClick={onClose}>
                Cancel
              </Button>
              <Button type="submit" disabled={isSubmitting}>
                {isSubmitting
                  ? "Saving..."
                  : `Save ${isPasswordOnly ? "Password" : "Changes"}`}
              </Button>
            </div>
          </Form>
        )}
      </Formik>
    </div>
  );
};

export default EditProfileForm;
