import json
import os
import requests
import time
from urllib.parse import urljoin, urlparse
from selenium import webdriver
from selenium.webdriver.common.by import By
from selenium.webdriver.support.ui import WebDriverWait
from selenium.webdriver.support import expected_conditions as EC
from selenium.webdriver.chrome.options import Options
from selenium.common.exceptions import TimeoutException, NoSuchElementException
import re
from PIL import Image
import io

class HaierImageScraper:
    def __init__(self):
        self.setup_driver()
        self.base_urls = [
            "https://www.haier.com/in/",
            "https://www.haier.com/global/",
            "https://www.haierappliances.com/",
            "https://www.haierindia.com/"
        ]
        self.hq_images_dir = "haier_product_images_hq"
        self.min_image_size = (800, 600)  # Minimum size for HQ images
        self.session = requests.Session()
        self.session.headers.update({
            'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36'
        })

    def setup_driver(self):
        """Setup Chrome driver with options for web scraping"""
        chrome_options = Options()
        chrome_options.add_argument("--headless")
        chrome_options.add_argument("--no-sandbox")
        chrome_options.add_argument("--disable-dev-shm-usage")
        chrome_options.add_argument("--disable-gpu")
        chrome_options.add_argument("--window-size=1920,1080")
        chrome_options.add_argument("--user-agent=Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36")

        try:
            self.driver = webdriver.Chrome(options=chrome_options)
            self.wait = WebDriverWait(self.driver, 10)
        except Exception as e:
            print(f"Error setting up Chrome driver: {e}")
            print("Please ensure ChromeDriver is installed and in PATH")
            raise

    def search_product_images(self, model_number, product_name):
        """Search for high-quality product images using multiple strategies"""
        print(f"Searching for HQ images for {model_number} - {product_name}")

        images = []

        # Strategy 1: Search on official Haier websites
        images.extend(self.search_official_sites(model_number, product_name))

        # Strategy 2: Google Images search
        images.extend(self.search_google_images(model_number, product_name))

        # Strategy 3: E-commerce sites
        images.extend(self.search_ecommerce_sites(model_number, product_name))

        return self.filter_high_quality_images(images)

    def search_official_sites(self, model_number, product_name):
        """Search official Haier websites for product images"""
        images = []

        for base_url in self.base_urls:
            try:
                # Search for the product on the official site
                search_url = f"{base_url}search?q={model_number}"
                self.driver.get(search_url)
                time.sleep(3)

                # Look for product links
                product_links = self.driver.find_elements(By.CSS_SELECTOR, "a[href*='product'], a[href*='kitchen'], a[href*='appliance']")

                for link in product_links[:3]:  # Check first 3 results
                    try:
                        href = link.get_attribute('href')
                        if href and model_number.lower() in href.lower():
                            images.extend(self.extract_images_from_page(href))
                    except Exception as e:
                        print(f"Error processing link: {e}")
                        continue

            except Exception as e:
                print(f"Error searching {base_url}: {e}")
                continue

        return images

    def search_google_images(self, model_number, product_name):
        """Search Google Images for high-quality product images"""
        images = []

        try:
            # Construct Google Images search query
            query = f"Haier {model_number} {product_name} kitchen appliance high resolution"
            google_url = f"https://www.google.com/search?q={query}&tbm=isch&tbs=isz:l"  # Large images

            self.driver.get(google_url)
            time.sleep(3)

            # Find image elements
            img_elements = self.driver.find_elements(By.CSS_SELECTOR, "img[data-src], img[src]")

            for img in img_elements[:10]:  # First 10 images
                try:
                    img_url = img.get_attribute('data-src') or img.get_attribute('src')
                    if img_url and self.is_valid_image_url(img_url):
                        images.append(img_url)
                except Exception as e:
                    continue

        except Exception as e:
            print(f"Error in Google Images search: {e}")

        return images

    def search_ecommerce_sites(self, model_number, product_name):
        """Search e-commerce sites for product images"""
        images = []
        ecommerce_sites = [
            f"https://www.amazon.in/s?k=Haier+{model_number}",
            f"https://www.flipkart.com/search?q=Haier+{model_number}",
            f"https://www.croma.com/search?q=Haier+{model_number}"
        ]

        for site_url in ecommerce_sites:
            try:
                self.driver.get(site_url)
                time.sleep(3)

                # Look for product images
                img_elements = self.driver.find_elements(By.CSS_SELECTOR, "img[src*='product'], img[data-src*='product'], .product-image img")

                for img in img_elements[:5]:
                    try:
                        img_url = img.get_attribute('data-src') or img.get_attribute('src')
                        if img_url and self.is_valid_image_url(img_url):
                            images.append(img_url)
                    except Exception as e:
                        continue

            except Exception as e:
                print(f"Error searching {site_url}: {e}")
                continue

        return images

    def extract_images_from_page(self, page_url):
        """Extract all images from a product page"""
        images = []

        try:
            self.driver.get(page_url)
            time.sleep(3)

            # Look for various image selectors
            selectors = [
                "img[src*='product']",
                "img[data-src*='product']",
                ".product-gallery img",
                ".product-images img",
                ".gallery img",
                "img[alt*='Haier']",
                "img[src*='haier']"
            ]

            for selector in selectors:
                img_elements = self.driver.find_elements(By.CSS_SELECTOR, selector)
                for img in img_elements:
                    try:
                        img_url = img.get_attribute('data-src') or img.get_attribute('src')
                        if img_url and self.is_valid_image_url(img_url):
                            # Convert relative URLs to absolute
                            img_url = urljoin(page_url, img_url)
                            images.append(img_url)
                    except Exception as e:
                        continue

        except Exception as e:
            print(f"Error extracting images from {page_url}: {e}")

        return images

    def is_valid_image_url(self, url):
        """Check if URL is a valid image URL"""
        if not url or url.startswith('data:'):
            return False

        # Check for image extensions
        image_extensions = ['.jpg', '.jpeg', '.png', '.webp', '.gif']
        url_lower = url.lower()

        return any(ext in url_lower for ext in image_extensions) or 'image' in url_lower

    def filter_high_quality_images(self, image_urls):
        """Filter images to keep only high-quality ones"""
        hq_images = []

        for url in image_urls:
            try:
                # Download image to check quality
                response = self.session.get(url, timeout=10, stream=True)
                if response.status_code == 200:
                    # Check image size
                    img_data = io.BytesIO(response.content)
                    with Image.open(img_data) as img:
                        width, height = img.size

                        # Filter by minimum size and aspect ratio
                        if (width >= self.min_image_size[0] and
                            height >= self.min_image_size[1] and
                            0.5 <= width/height <= 2.0):  # Reasonable aspect ratio
                            hq_images.append(url)

                            if len(hq_images) >= 5:  # Limit to 5 HQ images per product
                                break

            except Exception as e:
                print(f"Error checking image quality for {url}: {e}")
                continue

        return hq_images

    def download_image(self, url, filepath):
        """Download an image from URL to filepath"""
        try:
            response = self.session.get(url, timeout=15)
            if response.status_code == 200:
                os.makedirs(os.path.dirname(filepath), exist_ok=True)
                with open(filepath, 'wb') as f:
                    f.write(response.content)
                return True
        except Exception as e:
            print(f"Error downloading {url}: {e}")
        return False

    def close(self):
        """Close the browser driver"""
        if hasattr(self, 'driver'):
            self.driver.quit()


def scrape_haier_hq_images():
    """Main function to scrape high-quality images for all Haier products"""

    # Load the JSON file
    json_file_path = 'output/json/haier_kitchen_products.json'

    try:
        with open(json_file_path, 'r', encoding='utf-8') as f:
            products = json.load(f)
        print(f"Loaded {len(products)} products from JSON")
    except Exception as e:
        print(f"Error loading JSON: {e}")
        return

    # Initialize scraper
    scraper = HaierImageScraper()

    try:
        updated_products = []

        for i, product in enumerate(products):
            print(f"\nProcessing product {i+1}/{len(products)}: {product.get('model', 'Unknown')}")

            model = product.get('model')
            name = product.get('name', '')

            if not model:
                print("No model number found, skipping...")
                updated_products.append(product)
                continue

            # Check if HQ images already exist
            if product.get('hq_images') and len(product['hq_images']) >= 3:
                print(f"Product {model} already has {len(product['hq_images'])} HQ images, skipping...")
                updated_products.append(product)
                continue

            # Search for high-quality images
            hq_image_urls = scraper.search_product_images(model, name)

            if hq_image_urls:
                print(f"Found {len(hq_image_urls)} high-quality images for {model}")

                # Create directory for this product
                product_dir = os.path.join(scraper.hq_images_dir, f"{model}_{name}")
                os.makedirs(product_dir, exist_ok=True)

                # Download images
                downloaded_paths = []
                for j, img_url in enumerate(hq_image_urls):
                    filename = f"hq_image_{j+1}.jpg"
                    filepath = os.path.join(product_dir, filename)

                    if scraper.download_image(img_url, filepath):
                        relative_path = filepath.replace('\\', '/')
                        downloaded_paths.append(relative_path)
                        print(f"Downloaded: {filename}")

                    time.sleep(1)  # Rate limiting

                # Update product with HQ image paths
                product['hq_images'] = downloaded_paths
                print(f"Updated {model} with {len(downloaded_paths)} HQ images")
            else:
                print(f"No high-quality images found for {model}")
                if 'hq_images' not in product:
                    product['hq_images'] = []

            updated_products.append(product)

            # Save progress every 5 products
            if (i + 1) % 5 == 0:
                save_progress(updated_products, json_file_path)
                print(f"Progress saved after {i+1} products")

        # Save final results
        save_progress(updated_products, json_file_path)
        print(f"\nCompleted! Updated {len(updated_products)} products")

    except KeyboardInterrupt:
        print("\nScraping interrupted by user")
        save_progress(updated_products if 'updated_products' in locals() else products, json_file_path)
    except Exception as e:
        print(f"Error during scraping: {e}")
    finally:
        scraper.close()


def save_progress(products, json_file_path):
    """Save progress to JSON file"""
    try:
        # Create backup
        backup_path = json_file_path.replace('.json', '_backup.json')
        if os.path.exists(json_file_path):
            import shutil
            shutil.copy2(json_file_path, backup_path)

        # Save updated data
        with open(json_file_path, 'w', encoding='utf-8') as f:
            json.dump(products, f, indent=2)
        print(f"Progress saved to {json_file_path}")
    except Exception as e:
        print(f"Error saving progress: {e}")


def install_requirements():
    """Install required packages"""
    packages = [
        'selenium',
        'requests',
        'Pillow',
        'webdriver-manager'
    ]

    import subprocess
    import sys

    for package in packages:
        try:
            subprocess.check_call([sys.executable, '-m', 'pip', 'install', package])
            print(f"Installed {package}")
        except Exception as e:
            print(f"Error installing {package}: {e}")


if __name__ == "__main__":
    print("Haier High-Quality Image Scraper")
    print("=" * 40)

    # Check if user wants to install requirements
    install_deps = input("Install required packages? (y/n): ").lower().strip()
    if install_deps == 'y':
        install_requirements()

    print("\nStarting image scraping...")
    scrape_haier_hq_images()
