import { NextRequest, NextResponse } from 'next/server';
import { withAuth } from "next-auth/middleware";

// This function handles all middleware requests
export function middleware(request: NextRequest) {
  // Check if the request is for sitemap.xml
  if (request.nextUrl.pathname === '/sitemap.xml') {
    // Get the response from the next middleware or route handler
    const response = NextResponse.next();

    // Add the Content-Type header for XML
    response.headers.set('Content-Type', 'application/xml');

    return response;
  }

  // For authenticated routes, use the withAuth middleware
  if (
    request.nextUrl.pathname.startsWith('/account') ||
    request.nextUrl.pathname.startsWith('/cart') ||
    request.nextUrl.pathname.startsWith('/checkout') ||
    request.nextUrl.pathname.startsWith('/order-confirmation') ||
    request.nextUrl.pathname.startsWith('/order-details')
  ) {
    const authMiddleware = withAuth({
      pages: {
        signIn: '/auth/login', // Your custom login page
      },
      callbacks: {
        authorized: ({ token }) => {
          // Check if token exists and has valid access token
          // If token has error or no access token, redirect to login
          if (!token || token.error || !token.access) {
            console.log("Middleware: Token invalid or missing, redirecting to login");
            return false;
          }
          return true;
        },
      },
    });

    return authMiddleware(request);
  }

  // For all other routes, continue normally
  return NextResponse.next();
}

export const config = {
  matcher: [
    '/sitemap.xml',
    '/account/:path*',
    '/cart/:path*',
    '/checkout/:path*',
    '/order-confirmation/:path*',
    '/order-details/:path*'
  ],
};
