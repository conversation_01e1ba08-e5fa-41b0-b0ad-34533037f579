/**
 * Utility functions for token management and validation
 */

/**
 * Decode JWT token payload (without verification)
 * This is for debugging purposes only - never trust client-side JWT decoding for security
 */
// eslint-disable-next-line @typescript-eslint/no-explicit-any
export const decodeJWTPayload = (token: string): any => {
  try {
    const base64Url = token.split('.')[1];
    const base64 = base64Url.replace(/-/g, '+').replace(/_/g, '/');
    const jsonPayload = decodeURIComponent(
      atob(base64)
        .split('')
        .map((c) => '%' + ('00' + c.charCodeAt(0).toString(16)).slice(-2))
        .join('')
    );
    return JSON.parse(jsonPayload);
  } catch (error) {
    console.error('Failed to decode JWT payload:', error);
    return null;
  }
};

/**
 * Check if JWT token is expired (client-side check only)
 */
export const isTokenExpired = (token: string): boolean => {
  try {
    const payload = decodeJWTPayload(token);
    if (!payload || !payload.exp) {
      return true; // If we can't decode or no expiry, consider expired
    }
    
    const currentTime = Math.floor(Date.now() / 1000);
    return payload.exp < currentTime;
  } catch (error) {
    console.error('Failed to check token expiry:', error);
    return true; // If error, consider expired
  }
};

/**
 * Get token expiry time in milliseconds
 */
export const getTokenExpiryTime = (token: string): number | null => {
  try {
    const payload = decodeJWTPayload(token);
    if (!payload || !payload.exp) {
      return null;
    }
    return payload.exp * 1000; // Convert to milliseconds
  } catch (error) {
    console.error('Failed to get token expiry time:', error);
    return null;
  }
};

/**
 * Get time until token expires in milliseconds
 */
export const getTimeUntilExpiry = (token: string): number | null => {
  try {
    const expiryTime = getTokenExpiryTime(token);
    if (!expiryTime) {
      return null;
    }
    return expiryTime - Date.now();
  } catch (error) {
    console.error('Failed to get time until expiry:', error);
    return null;
  }
};

/**
 * Format token for logging (shows first and last few characters)
 */
export const formatTokenForLogging = (token: string): string => {
  if (!token || token.length < 20) {
    return '[INVALID_TOKEN]';
  }
  return `${token.substring(0, 10)}...${token.substring(token.length - 10)}`;
};

/**
 * Debug token information
 */
export const debugToken = (token: string, tokenType: 'access' | 'refresh' = 'access'): void => {
  if (!token) {
    console.log(`🔍 ${tokenType.toUpperCase()} TOKEN DEBUG: No token provided`);
    return;
  }

  console.group(`🔍 ${tokenType.toUpperCase()} TOKEN DEBUG`);
  
  try {
    const payload = decodeJWTPayload(token);
    const isExpired = isTokenExpired(token);
    const expiryTime = getTokenExpiryTime(token);
    const timeUntilExpiry = getTimeUntilExpiry(token);
    
    console.log('Token preview:', formatTokenForLogging(token));
    console.log('Is expired:', isExpired);
    
    if (expiryTime) {
      console.log('Expires at:', new Date(expiryTime).toISOString());
    }
    
    if (timeUntilExpiry !== null) {
      if (timeUntilExpiry > 0) {
        const minutes = Math.floor(timeUntilExpiry / (1000 * 60));
        const seconds = Math.floor((timeUntilExpiry % (1000 * 60)) / 1000);
        console.log(`Time until expiry: ${minutes}m ${seconds}s`);
      } else {
        console.log('Token expired', Math.abs(timeUntilExpiry), 'ms ago');
      }
    }
    
    if (payload) {
      console.log('Payload:', {
        ...payload,
        // Don't log sensitive data
        user_id: payload.user_id || payload.sub,
        exp: payload.exp ? new Date(payload.exp * 1000).toISOString() : undefined,
        iat: payload.iat ? new Date(payload.iat * 1000).toISOString() : undefined,
      });
    }
  } catch (error) {
    console.error('Error debugging token:', error);
  }
  
  console.groupEnd();
};

/**
 * Validate token structure (basic check)
 */
export const isValidJWTStructure = (token: string): boolean => {
  if (!token || typeof token !== 'string') {
    return false;
  }
  
  const parts = token.split('.');
  return parts.length === 3;
};
