import React, { useEffect, useState } from "react";
import {
  View,
  Text,
  StyleSheet,
  ScrollView,
  Image,
  TouchableOpacity,
  ActivityIndicator,
  Alert,
} from "react-native";
import { SafeAreaView } from "react-native-safe-area-context";
import { MaterialIcons } from "@expo/vector-icons";
import axios from "axios";
import { useAuth } from "@/context/AuthContext";
import { BaseURL } from "@/constants/ApiEndpoint";

export default function OrderConfirmationScreen({ route, navigation }) {
  const { orderData } = route.params || {};
  const [order, setOrder] = useState(null);
  const [loading, setLoading] = useState(true);
  const { accessToken } = useAuth();

  useEffect(() => {
    // If orderData is provided directly, use it
    if (orderData) {
      setOrder(orderData);
      setLoading(false);
    } else {
      // Otherwise fetch the order details from the API
      fetchOrderDetails();
    }
  }, []);

  const fetchOrderDetails = async () => {
    try {
      const orderId = route.params?.orderId;
      if (!orderId) {
        Alert.alert("Error", "Order ID not provided");
        setLoading(false);
        return;
      }

      const response = await axios.get(`${BaseURL}/api/v1/orders/${orderId}/`, {
        headers: {
          Authorization: `Bearer ${accessToken}`,
        },
      });

      setOrder(response.data);
      setLoading(false);
    } catch (error) {
      console.error("Error fetching order details:", error);
      Alert.alert("Error", "Failed to load order details");
      setLoading(false);
    }
  };

  const handleTrackOrder = () => {
    // Navigate to order tracking screen
    // This would be implemented in a future feature
    Alert.alert("Track Order", "Order tracking feature coming soon!");
  };

  const handleDownloadReceipt = () => {
    // Download receipt functionality
    // This would be implemented in a future feature
    Alert.alert("Download Receipt", "Receipt download feature coming soon!");
  };

  const handleContinueShopping = () => {
    // Navigate to the home or products screen
    navigation.navigate("Home");
  };

  if (loading) {
    return (
      <SafeAreaView style={styles.container}>
        <View style={styles.loadingContainer}>
          <ActivityIndicator size="large" color="#2563EB" />
          <Text style={styles.loadingText}>Loading order details...</Text>
        </View>
      </SafeAreaView>
    );
  }

  if (!order) {
    return (
      <SafeAreaView style={styles.container}>
        <View style={styles.errorContainer}>
          <MaterialIcons name="error-outline" size={48} color="#EF4444" />
          <Text style={styles.errorText}>Order details not found</Text>
          <TouchableOpacity
            style={styles.button}
            onPress={() => navigation.navigate("Home")}
          >
            <Text style={styles.buttonText}>Go to Home</Text>
          </TouchableOpacity>
        </View>
      </SafeAreaView>
    );
  }

  return (
    <SafeAreaView style={styles.container}>
      <ScrollView style={styles.content}>
        <View style={styles.successContainer}>
          <Text style={styles.successTitle}>Thank You for Your Order!</Text>
          <Text style={styles.orderId}>Order #{order.id}</Text>
          <Text style={styles.orderStatus}>Status: {order.status}</Text>
          <Text style={styles.successMessage}>
            We'll send you an email confirmation with order details and tracking
            information.
          </Text>
        </View>

        <View style={styles.section}>
          <Text style={styles.sectionTitle}>Order Details</Text>

          {order?.items &&
            order?.items.map((item) => (
              <View key={item.id} style={styles.orderItem}>
                <Image
                  source={{ uri: `${BaseURL}${item?.product_image}` }}
                  style={styles.productImage}
                />
                <View style={styles.productDetails}>
                  <Text style={styles.productName}>{item.product_name}</Text>
                  {item.variant_name && (
                    <Text style={styles.variantName}>
                      Variant: {item.variant_name}
                    </Text>
                  )}
                  <Text style={styles.productQuantity}>
                    Quantity: {item.quantity}
                  </Text>
                  <Text style={styles.productPrice}>₹{item.total_price}</Text>
                </View>
              </View>
            ))}

          <View style={styles.addressSection}>
            <Text style={styles.addressTitle}>Shipping Address</Text>
            <Text style={styles.addressText}>
              {order.shipping_address.street_address},{" "}
              {order.shipping_address.apartment}
            </Text>
            <Text style={styles.addressText}>
              {order.shipping_address.city}, {order.shipping_address.state}{" "}
              {order.shipping_address.postal_code}
            </Text>
            <Text style={styles.addressText}>
              Phone: {order.shipping_address.order_user_phone}
            </Text>
            <Text style={styles.addressText}>
              Email: {order.shipping_address.order_user_email}
            </Text>
          </View>

          <View style={styles.orderInfoRow}>
            <Text style={styles.orderInfoLabel}>Shipping Method</Text>
            <Text style={styles.orderInfoValue}>
              {order.shipping_method.name}
            </Text>
          </View>

          <View style={styles.orderInfoRow}>
            <Text style={styles.orderInfoLabel}>Shipping Cost</Text>
            <Text style={styles.orderInfoValue}>₹{order.shipping_cost}</Text>
          </View>

          <View style={styles.orderInfoRow}>
            <Text style={styles.orderInfoLabel}>Estimated Delivery</Text>
            <Text style={styles.orderInfoValue}>
              {order.estimated_delivery_date}
            </Text>
          </View>

          <View style={styles.orderInfoRow}>
            <Text style={styles.orderInfoLabel}>Subtotal</Text>
            <Text style={styles.orderInfoValue}>₹{order.subtotal}</Text>
          </View>

          <View style={styles.orderInfoRow}>
            <Text style={styles.orderInfoLabel}>Total</Text>
            <Text style={[styles.orderInfoValue, styles.totalPrice]}>
              ₹{order.total}
            </Text>
          </View>
        </View>

        {order.tracking_number && (
          <View style={styles.trackingSection}>
            <Text style={styles.sectionTitle}>Tracking Information</Text>
            <Text style={styles.trackingNumber}>
              Tracking Number: {order.tracking_number}
            </Text>
          </View>
        )}

        <View style={styles.actionsContainer}>
          <TouchableOpacity
            style={styles.actionButton}
            onPress={handleTrackOrder}
          >
            <MaterialIcons name="local-shipping" size={20} color="#333" />
            <Text style={styles.actionButtonText}>Track Order</Text>
          </TouchableOpacity>

          <TouchableOpacity
            style={styles.actionButton}
            onPress={handleDownloadReceipt}
          >
            <MaterialIcons name="file-download" size={20} color="#333" />
            <Text style={styles.actionButtonText}>Download Receipt</Text>
          </TouchableOpacity>

          <TouchableOpacity
            style={[styles.actionButton, styles.continueButton]}
            onPress={handleContinueShopping}
          >
            <MaterialIcons name="shopping-bag" size={20} color="#fff" />
            <Text style={styles.continueButtonText}>Continue Shopping</Text>
          </TouchableOpacity>
        </View>
      </ScrollView>
    </SafeAreaView>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: "#f3f4f6",
  },
  header: {
    flexDirection: "row",
    alignItems: "center",
    justifyContent: "space-between",
    padding: 16,
    backgroundColor: "white",
    borderBottomWidth: 1,
    borderBottomColor: "#e5e7eb",
  },
  storeName: {
    fontSize: 22,
    fontWeight: "bold",
    color: "#1F2937",
  },
  headerButtons: {
    flexDirection: "row",
  },
  iconButton: {
    padding: 8,
    marginLeft: 8,
  },
  content: {
    flex: 1,
  },
  loadingContainer: {
    flex: 1,
    justifyContent: "center",
    alignItems: "center",
    padding: 20,
  },
  loadingText: {
    marginTop: 16,
    fontSize: 16,
    color: "#6B7280",
  },
  errorContainer: {
    flex: 1,
    justifyContent: "center",
    alignItems: "center",
    padding: 20,
  },
  errorText: {
    marginTop: 12,
    fontSize: 18,
    color: "#6B7280",
    marginBottom: 20,
  },
  successContainer: {
    padding: 24,
    alignItems: "center",
    backgroundColor: "white",
    borderBottomWidth: 1,
    borderBottomColor: "#e5e7eb",
  },
  successTitle: {
    fontSize: 24,
    fontWeight: "bold",
    color: "#1F2937",
    textAlign: "center",
    marginBottom: 12,
  },
  orderId: {
    fontSize: 16,
    color: "#6B7280",
    marginBottom: 6,
  },
  orderStatus: {
    fontSize: 16,
    fontWeight: "500",
    color: "#2563EB",
    marginBottom: 12,
  },
  successMessage: {
    fontSize: 16,
    color: "#6B7280",
    textAlign: "center",
    lineHeight: 22,
  },
  section: {
    padding: 20,
    backgroundColor: "white",
    marginTop: 16,
    marginHorizontal: 16,
    borderRadius: 12,
    shadowColor: "#000",
    shadowOffset: { width: 0, height: 1 },
    shadowOpacity: 0.05,
    shadowRadius: 2,
    elevation: 2,
  },
  sectionTitle: {
    fontSize: 18,
    fontWeight: "600",
    color: "#1F2937",
    marginBottom: 16,
  },
  orderItem: {
    flexDirection: "row",
    paddingBottom: 16,
    marginBottom: 16,
    borderBottomWidth: 1,
    borderBottomColor: "#e5e7eb",
  },
  productImage: {
    width: 70,
    height: 70,
    borderRadius: 8,
    backgroundColor: "#f3f4f6",
  },
  productDetails: {
    flex: 1,
    paddingLeft: 12,
  },
  productName: {
    fontSize: 16,
    fontWeight: "500",
    color: "#1F2937",
    marginBottom: 4,
  },
  variantName: {
    fontSize: 14,
    color: "#6B7280",
    marginBottom: 4,
  },
  productQuantity: {
    fontSize: 14,
    color: "#6B7280",
    marginBottom: 4,
  },
  productPrice: {
    fontSize: 16,
    fontWeight: "600",
    color: "#2563EB",
  },
  addressSection: {
    marginBottom: 16,
    paddingBottom: 16,
    borderBottomWidth: 1,
    borderBottomColor: "#e5e7eb",
  },
  addressTitle: {
    fontSize: 16,
    fontWeight: "600",
    color: "#1F2937",
    marginBottom: 8,
  },
  addressText: {
    fontSize: 14,
    color: "#6B7280",
    marginBottom: 4,
  },
  orderInfoRow: {
    flexDirection: "row",
    justifyContent: "space-between",
    marginBottom: 12,
  },
  orderInfoLabel: {
    fontSize: 16,
    color: "#6B7280",
  },
  orderInfoValue: {
    fontSize: 16,
    fontWeight: "500",
    color: "#1F2937",
  },
  totalPrice: {
    fontWeight: "bold",
    fontSize: 18,
    color: "#2563EB",
  },
  trackingSection: {
    padding: 20,
    backgroundColor: "white",
    marginTop: 16,
    marginHorizontal: 16,
    borderRadius: 12,
    shadowColor: "#000",
    shadowOffset: { width: 0, height: 1 },
    shadowOpacity: 0.05,
    shadowRadius: 2,
    elevation: 2,
  },
  trackingNumber: {
    fontSize: 16,
    color: "#1F2937",
  },
  actionsContainer: {
    padding: 20,
    backgroundColor: "white",
    marginTop: 16,
    marginHorizontal: 16,
    borderRadius: 12,
    shadowColor: "#000",
    shadowOffset: { width: 0, height: 1 },
    shadowOpacity: 0.05,
    shadowRadius: 2,
    elevation: 2,
    marginBottom: 30,
  },
  actionButton: {
    flexDirection: "row",
    alignItems: "center",
    justifyContent: "center",
    padding: 14,
    marginBottom: 12,
    borderRadius: 8,
    borderWidth: 1,
    borderColor: "#d1d5db",
    backgroundColor: "white",
  },
  actionButtonText: {
    fontSize: 16,
    fontWeight: "500",
    marginLeft: 8,
  },
  continueButton: {
    backgroundColor: "#2563EB",
    borderColor: "#2563EB",
  },
  continueButtonText: {
    color: "white",
    fontSize: 16,
    fontWeight: "500",
    marginLeft: 8,
  },
  button: {
    backgroundColor: "#2563EB",
    paddingVertical: 12,
    paddingHorizontal: 24,
    borderRadius: 8,
    marginTop: 16,
  },
  buttonText: {
    color: "white",
    fontSize: 16,
    fontWeight: "500",
  },
});
