"""
Django management command for security data cleanup and maintenance.
This command should be run regularly (e.g., daily via cron) to maintain security logs
and enforce data retention policies.
"""

from django.core.management.base import BaseCommand
from django.utils import timezone
from datetime import timedelta
from backend.security_monitoring import SecurityEvent, FailedLoginAttempt, cleanup_old_security_events
from users.models import DataDeletionRequest
import logging

logger = logging.getLogger('security')

class Command(BaseCommand):
    help = 'Cleanup old security events and maintain security data'
    
    def add_arguments(self, parser):
        parser.add_argument(
            '--days',
            type=int,
            default=90,
            help='Number of days to retain security events (default: 90)'
        )
        parser.add_argument(
            '--dry-run',
            action='store_true',
            help='Show what would be deleted without actually deleting'
        )
        parser.add_argument(
            '--process-deletions',
            action='store_true',
            help='Process pending data deletion requests'
        )
    
    def handle(self, *args, **options):
        days = options['days']
        dry_run = options['dry_run']
        process_deletions = options['process_deletions']
        
        self.stdout.write(
            self.style.SUCCESS(f'Starting security cleanup (retention: {days} days)')
        )
        
        if dry_run:
            self.stdout.write(
                self.style.WARNING('DRY RUN MODE - No data will be deleted')
            )
        
        # Cleanup old security events
        self._cleanup_security_events(days, dry_run)
        
        # Cleanup old failed login attempts
        self._cleanup_failed_attempts(days, dry_run)
        
        # Process data deletion requests if requested
        if process_deletions:
            self._process_deletion_requests(dry_run)
        
        self.stdout.write(
            self.style.SUCCESS('Security cleanup completed successfully')
        )
    
    def _cleanup_security_events(self, days, dry_run):
        """Cleanup old security events"""
        cutoff_date = timezone.now() - timedelta(days=days)
        
        old_events = SecurityEvent.objects.filter(timestamp__lt=cutoff_date)
        count = old_events.count()
        
        if count > 0:
            if dry_run:
                self.stdout.write(
                    f'Would delete {count} security events older than {days} days'
                )
            else:
                deleted = old_events.delete()
                self.stdout.write(
                    self.style.SUCCESS(f'Deleted {deleted[0]} security events')
                )
                logger.info(f'Cleaned up {deleted[0]} old security events')
        else:
            self.stdout.write('No old security events to cleanup')
    
    def _cleanup_failed_attempts(self, days, dry_run):
        """Cleanup old failed login attempts"""
        cutoff_date = timezone.now() - timedelta(days=days)
        
        old_attempts = FailedLoginAttempt.objects.filter(timestamp__lt=cutoff_date)
        count = old_attempts.count()
        
        if count > 0:
            if dry_run:
                self.stdout.write(
                    f'Would delete {count} failed login attempts older than {days} days'
                )
            else:
                deleted = old_attempts.delete()
                self.stdout.write(
                    self.style.SUCCESS(f'Deleted {deleted[0]} failed login attempts')
                )
                logger.info(f'Cleaned up {deleted[0]} old failed login attempts')
        else:
            self.stdout.write('No old failed login attempts to cleanup')
    
    def _process_deletion_requests(self, dry_run):
        """Process pending data deletion requests"""
        from users.models import Customer
        from backend.encryption_utils import anonymize_email, anonymize_name, anonymize_phone
        
        # Get pending deletion requests older than 7 days (grace period)
        grace_period = timezone.now() - timedelta(days=7)
        pending_requests = DataDeletionRequest.objects.filter(
            status='PENDING',
            request_date__lt=grace_period
        )
        
        count = pending_requests.count()
        
        if count > 0:
            if dry_run:
                self.stdout.write(
                    f'Would process {count} pending deletion requests'
                )
                for request in pending_requests:
                    self.stdout.write(
                        f'  - User: {request.user.email}, Requested: {request.request_date}'
                    )
            else:
                processed = 0
                for request in pending_requests:
                    try:
                        user = request.user
                        
                        # Anonymize user data instead of hard delete
                        original_email = user.email
                        user.email = anonymize_email(user.email)
                        user.name = anonymize_name(user.name)
                        if hasattr(user, 'phone_number'):
                            user.phone_number = anonymize_phone(user.phone_number)
                        user.is_active = False
                        user.save()
                        
                        # Anonymize addresses
                        for address in user.addresses.all():
                            address.street_address = "Anonymized Address"
                            address.city = "Anonymized"
                            address.state = "Anonymized"
                            address.postal_code = "000000"
                            address.save()
                        
                        # Mark request as completed (this will automatically send email)
                        request.complete_deletion(
                            processed_by=None,
                            notes=f'Automated anonymization completed. Original email: {original_email}'
                        )
                        
                        processed += 1
                        logger.info(f'Processed deletion request for user {original_email}')
                        
                    except Exception as e:
                        logger.error(f'Error processing deletion request {request.id}: {e}')
                        request.status = 'REJECTED'
                        request.processing_notes = f'Error during processing: {str(e)}'
                        request.save()
                
                self.stdout.write(
                    self.style.SUCCESS(f'Processed {processed} deletion requests')
                )
        else:
            self.stdout.write('No pending deletion requests to process')
    
    def _get_security_stats(self):
        """Get security statistics for reporting"""
        now = timezone.now()
        last_24h = now - timedelta(hours=24)
        last_7d = now - timedelta(days=7)
        last_30d = now - timedelta(days=30)
        
        stats = {
            'total_events': SecurityEvent.objects.count(),
            'events_24h': SecurityEvent.objects.filter(timestamp__gte=last_24h).count(),
            'events_7d': SecurityEvent.objects.filter(timestamp__gte=last_7d).count(),
            'events_30d': SecurityEvent.objects.filter(timestamp__gte=last_30d).count(),
            'failed_logins_24h': SecurityEvent.objects.filter(
                event_type='LOGIN_FAILED',
                timestamp__gte=last_24h
            ).count(),
            'successful_logins_24h': SecurityEvent.objects.filter(
                event_type='LOGIN_SUCCESS',
                timestamp__gte=last_24h
            ).count(),
        }
        
        return stats
