import {
  Sheet,
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
} from "@/components/ui/sheet";
import { But<PERSON> } from "@/components/ui/button";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import { Separator } from "@/components/ui/separator";
import { Badge } from "@/components/ui/badge";
import { Order } from "@/hooks/useOrders";
import { ORDER_STATUS_CHOICES } from "@/constant/urls";
import { getOrderStatusColor, canRefundOrder } from "@/lib/orderUtils";

interface OrderQuickViewProps {
  order: Order | null;
  onClose: () => void;
  onStatusUpdate: (orderId: string, status: string) => void;
  onRefund: (orderId: string) => void;
}

export const OrderQuickView = ({
  order,
  onClose,
  onStatusUpdate,
  onRefund,
}: OrderQuickViewProps) => {
  if (!order) return null;

  const formatCustomerName = () => {
    return order.user.name || order.user.email;
  };

  console.log(order, "order>>>>>>>>>>>>>>>>>>");

  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString('en-US', {
      year: 'numeric',
      month: 'long',
      day: 'numeric',
      hour: '2-digit',
      minute: '2-digit'
    });
  };

  return (
    <Sheet open={Boolean(order)} onOpenChange={onClose}>
      <SheetContent className="w-[400px] sm:w-[540px]">
        <SheetHeader>
          <SheetTitle>Order {order.id.slice(0, 8)}...</SheetTitle>
        </SheetHeader>
        <div className="mt-6 space-y-6">
          <div className="space-y-3">
            <h3 className="text-sm font-medium">Customer Details</h3>
            <div className="space-y-1">
              <p className="text-sm font-medium">{formatCustomerName()}</p>
              <p className="text-sm text-muted-foreground">{order.user.email}</p>
              <p className="text-xs text-muted-foreground">
                Order placed: {formatDate(order.created_at)}
              </p>
            </div>
          </div>

          <Separator />

          <div className="space-y-4">
            <div className="flex items-center justify-between">
              <h3 className="text-sm font-medium">Status</h3>
              <Badge className={getOrderStatusColor(order.status)}>
                {order.status}
              </Badge>
            </div>
            <Select
              defaultValue={order.status}
              onValueChange={(value) => onStatusUpdate(order.id, value)}
            >
              <SelectTrigger className="w-full">
                <SelectValue placeholder="Update status" />
              </SelectTrigger>
              <SelectContent>
                {ORDER_STATUS_CHOICES.map((status) => (
                  <SelectItem key={status.value} value={status.value}>
                    {status.label}
                  </SelectItem>
                ))}
              </SelectContent>
            </Select>
          </div>

          <Separator />

          <div className="space-y-4">
            <h3 className="text-sm font-medium">Order Items</h3>
            <div className="space-y-3">
              {order.items.map((item, index) => (
                <div key={index} className="flex justify-between items-start text-sm">
                  <div className="flex-1">
                    <p className="font-medium">{item.product_name}</p>
                    <p className="text-muted-foreground">Qty: {item.quantity}</p>
                  </div>
                  <div className="text-right">
                    <p className="font-medium">₹{Number(item?.unit_price).toFixed(2)}</p>
                    <p className="text-xs text-muted-foreground">
                      ₹{Number(item?.total_price).toFixed(2)} each
                    </p>
                  </div>
                </div>
              ))}
            </div>

            <Separator />

            <div className="space-y-2">
              <div className="flex justify-between text-sm">
                <span>Subtotal</span>
                <span>₹{Number(order?.subtotal).toFixed(2)}</span>
              </div>
              <div className="flex justify-between text-sm">
                <span>GST</span>
                <span>₹{Number(order?.gst_amount).toFixed(2)}</span>
              </div>
              <div className="flex justify-between text-sm">
                <span>Shipping</span>
                <span>₹{Number(order?.shipping_cost).toFixed(2)}</span>
              </div>
              <div className="flex justify-between font-medium">
                <span>Total</span>
                <span>₹{Number(order?.total).toFixed(2)}</span>
              </div>
            </div>
          </div>

          <Separator />

          <div className="space-y-3">
            <Button
              variant="outline"
              className="w-full"
              onClick={() => onRefund(order.id)}
              disabled={!canRefundOrder(order.status)}
            >
              Process Refund
            </Button>

            {order.tracking_number && (
              <div className="text-sm">
                <span className="font-medium">Tracking: </span>
                <span className="text-muted-foreground">{order.tracking_number}</span>
              </div>
            )}

            {order.notes && (
              <div className="text-sm">
                <span className="font-medium">Notes: </span>
                <span className="text-muted-foreground">{order.notes}</span>
              </div>
            )}
          </div>
        </div>
      </SheetContent>
    </Sheet>
  );
};

