'use client';

import { useState, useEffect } from 'react';
import { useAuthSession } from '@/hooks/useAuthSession';
import { debugToken, isTokenExpired, getTimeUntilExpiry } from '@/lib/tokenUtils';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { RefreshCw, Eye, EyeOff, AlertTriangle, CheckCircle, Minimize2, Maximize2 } from 'lucide-react';

/**
 * Debug component to monitor token status and refresh process
 * Only use in development!
 */
type TokenInfo = {
  hasAccessToken: boolean;
  hasRefreshToken: boolean;
  isExpired: boolean;
  timeUntilExpiry: number | null;
  accessTokenPreview: string | null;
  refreshTokenPreview: string | null;
};

export const TokenDebugger = () => {
  const {
    status,
    isAuthenticated,
    hasRefreshError,
    getAccessToken,
    getRefreshToken,
    refreshSession
  } = useAuthSession();

  const [showTokens, setShowTokens] = useState(false);
  const [tokenInfo, setTokenInfo] = useState<TokenInfo | null>(null);
  const [isRefreshing, setIsRefreshing] = useState(false);
  const [isMinimized, setIsMinimized] = useState(false);

  // Update token info periodically
  useEffect(() => {
    const updateTokenInfo = () => {
      const accessToken = getAccessToken();
      const refreshToken = getRefreshToken();
      
      if (accessToken) {
        const isExpired = isTokenExpired(accessToken);
        const timeUntilExpiry = getTimeUntilExpiry(accessToken);
        
        setTokenInfo({
          hasAccessToken: !!accessToken,
          hasRefreshToken: !!refreshToken,
          isExpired,
          timeUntilExpiry,
          accessTokenPreview: accessToken ? `${accessToken.substring(0, 20)}...` : null,
          refreshTokenPreview: refreshToken ? `${refreshToken.substring(0, 20)}...` : null,
        });
      } else {
        setTokenInfo({
          hasAccessToken: false,
          hasRefreshToken: !!refreshToken,
          isExpired: true,
          timeUntilExpiry: null,
          accessTokenPreview: null,
          refreshTokenPreview: refreshToken ? `${refreshToken.substring(0, 20)}...` : null,
        });
      }
    };

    updateTokenInfo();
    const interval = setInterval(updateTokenInfo, 5000); // Update every 5 seconds
    
    return () => clearInterval(interval);
  }, [getAccessToken, getRefreshToken]);

  const handleRefreshSession = async () => {
    setIsRefreshing(true);
    try {
      console.log('🔄 Manual session refresh triggered');
      await refreshSession();
      console.log('✅ Manual session refresh completed');
    } catch (error) {
      console.error('❌ Manual session refresh failed:', error);
    } finally {
      setIsRefreshing(false);
    }
  };

  const handleDebugTokens = () => {
    const accessToken = getAccessToken();
    const refreshToken = getRefreshToken();
    
    console.group('🔍 TOKEN DEBUG INFO');
    if (accessToken) {
      debugToken(accessToken, 'access');
    } else {
      console.log('❌ No access token available');
    }
    
    if (refreshToken) {
      debugToken(refreshToken, 'refresh');
    } else {
      console.log('❌ No refresh token available');
    }
    console.groupEnd();
  };

  const formatTimeUntilExpiry = (timeMs: number | null) => {
    if (!timeMs) return 'Unknown';
    if (timeMs <= 0) return 'Expired';
    
    const minutes = Math.floor(timeMs / (1000 * 60));
    const seconds = Math.floor((timeMs % (1000 * 60)) / 1000);
    return `${minutes}m ${seconds}s`;
  };

  // Only show in development or when explicitly enabled
  const shouldShow = false || process.env.NEXT_PUBLIC_SHOW_TOKEN_DEBUGGER === 'true';

  if (!shouldShow) {
    return null;
  }

  return (
    <Card className={`fixed bottom-4 right-4 z-50 bg-white shadow-lg border transition-all duration-300 ${
      isMinimized ? 'w-48' : 'w-80'
    }`}>
      <CardHeader className="pb-2">
        <CardTitle className="text-sm flex items-center justify-between">
          <div className="flex items-center gap-2">
            🔧 Token Debugger
            <Badge variant={isAuthenticated ? "default" : "destructive"} className="text-xs">
              {status}
            </Badge>
          </div>
          <Button
            size="sm"
            variant="ghost"
            onClick={() => setIsMinimized(!isMinimized)}
            className="h-6 w-6 p-0 hover:bg-gray-100"
          >
            {isMinimized ? (
              <Maximize2 className="h-3 w-3" />
            ) : (
              <Minimize2 className="h-3 w-3" />
            )}
          </Button>
        </CardTitle>
      </CardHeader>
      {!isMinimized && (
        <CardContent className="space-y-3">
          {/* Session Status */}
          <div className="flex items-center gap-2 text-sm">
            {isAuthenticated ? (
              <CheckCircle className="h-4 w-4 text-green-500" />
            ) : (
              <AlertTriangle className="h-4 w-4 text-red-500" />
            )}
            <span>Authenticated: {isAuthenticated ? 'Yes' : 'No'}</span>
          </div>

        {hasRefreshError && (
          <div className="flex items-center gap-2 text-sm text-red-600">
            <AlertTriangle className="h-4 w-4" />
            <span>Refresh Error Detected</span>
          </div>
        )}

        {/* Token Info */}
        {tokenInfo && (
          <div className="space-y-2 text-xs">
            <div className="flex justify-between">
              <span>Access Token:</span>
              <Badge variant={tokenInfo.hasAccessToken ? "default" : "destructive"}>
                {tokenInfo.hasAccessToken ? 'Present' : 'Missing'}
              </Badge>
            </div>
            
            <div className="flex justify-between">
              <span>Refresh Token:</span>
              <Badge variant={tokenInfo.hasRefreshToken ? "default" : "destructive"}>
                {tokenInfo.hasRefreshToken ? 'Present' : 'Missing'}
              </Badge>
            </div>
            
            {tokenInfo.hasAccessToken && (
              <>
                <div className="flex justify-between">
                  <span>Token Status:</span>
                  <Badge variant={tokenInfo.isExpired ? "destructive" : "default"}>
                    {tokenInfo.isExpired ? 'Expired' : 'Valid'}
                  </Badge>
                </div>
                
                <div className="flex justify-between">
                  <span>Expires In:</span>
                  <span className={tokenInfo.isExpired ? 'text-red-600' : 'text-green-600'}>
                    {formatTimeUntilExpiry(tokenInfo.timeUntilExpiry)}
                  </span>
                </div>
              </>
            )}

            {showTokens && (
              <div className="space-y-1 p-2 bg-gray-50 rounded text-xs font-mono">
                {tokenInfo.accessTokenPreview && (
                  <div>
                    <div className="font-semibold">Access:</div>
                    <div className="break-all">{tokenInfo.accessTokenPreview}</div>
                  </div>
                )}
                {tokenInfo.refreshTokenPreview && (
                  <div>
                    <div className="font-semibold">Refresh:</div>
                    <div className="break-all">{tokenInfo.refreshTokenPreview}</div>
                  </div>
                )}
              </div>
            )}
          </div>
        )}

        {/* Actions */}
        <div className="flex gap-2">
          <Button
            size="sm"
            variant="outline"
            onClick={() => setShowTokens(!showTokens)}
            className="text-xs"
          >
            {showTokens ? <EyeOff className="h-3 w-3" /> : <Eye className="h-3 w-3" />}
          </Button>
          
          <Button
            size="sm"
            variant="outline"
            onClick={handleDebugTokens}
            className="text-xs"
          >
            Debug
          </Button>
          
          <Button
            size="sm"
            variant="outline"
            onClick={handleRefreshSession}
            disabled={isRefreshing}
            className="text-xs"
          >
            {isRefreshing ? (
              <RefreshCw className="h-3 w-3 animate-spin" />
            ) : (
              <RefreshCw className="h-3 w-3" />
            )}
          </Button>
          </div>
        </CardContent>
      )}

      {/* Minimized state - show just a compact status */}
      {isMinimized && (
        <CardContent className="py-2">
          <div className="flex items-center justify-between text-xs">
            <div className="flex items-center gap-1">
              {isAuthenticated ? (
                <CheckCircle className="h-3 w-3 text-green-500" />
              ) : (
                <AlertTriangle className="h-3 w-3 text-red-500" />
              )}
              <span>{isAuthenticated ? 'Auth OK' : 'Auth Failed'}</span>
            </div>
            {hasRefreshError && (
              <AlertTriangle className="h-3 w-3 text-red-500" />
            )}
          </div>
        </CardContent>
      )}
    </Card>
  );
};

export default TokenDebugger;

