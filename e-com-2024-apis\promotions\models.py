# from django.db import models
# from django.core.validators import MinValueValidator, MaxValueValidator
# from django.utils import timezone

# class Promotion(models.Model):
#     DISCOUNT_TYPE_CHOICES = [
#         ('PERCENTAGE', 'Percentage'),
#         ('FIXED', 'Fixed Amount'),
#     ]

#     code = models.CharField(max_length=50, unique=True)
#     description = models.TextField()
#     discount_type = models.CharField(max_length=10, choices=DISCOUNT_TYPE_CHOICES)
#     discount_value = models.DecimalField(
#         max_digits=10,
#         decimal_places=2,
#         validators=[MinValueValidator(0)]
#     )
#     min_purchase_amount = models.DecimalField(
#         max_digits=10,
#         decimal_places=2,
#         default=0,
#         validators=[MinValueValidator(0)]
#     )
#     max_discount_amount = models.DecimalField(
#         max_digits=10,
#         decimal_places=2,
#         null=True,
#         blank=True,
#         validators=[MinValueValidator(0)]
#     )
#     start_date = models.DateTimeField()
#     end_date = models.DateTimeField()
#     usage_limit = models.PositiveIntegerField(null=True, blank=True)
#     times_used = models.PositiveIntegerField(default=0)
#     is_active = models.BooleanField(default=True)
#     created_at = models.DateTimeField(auto_now_add=True)
#     updated_at = models.DateTimeField(auto_now=True)

#     class Meta:
#         ordering = ['-created_at']

#     def __str__(self):
#         return self.code

#     @property
#     def is_valid(self):
#         now = timezone.now()
#         if not self.is_active:
#             return False
#         if now < self.start_date or now > self.end_date:
#             return False
#         if self.usage_limit and self.times_used >= self.usage_limit:
#             return False
#         return True

#     def calculate_discount(self, subtotal):
#         if not self.is_valid or subtotal < self.min_purchase_amount:
#             return 0

#         if self.discount_type == 'PERCENTAGE':
#             discount = subtotal * (self.discount_value / 100)
#         else:  # FIXED
#             discount = self.discount_value

#         if self.max_discount_amount:
#             discount = min(discount, self.max_discount_amount)

#         return discount

# class PromotionUsage(models.Model):
#     promotion = models.ForeignKey(Promotion, on_delete=models.CASCADE, related_name='usages')
#     user = models.ForeignKey('users.Customer', on_delete=models.CASCADE)
#     order = models.ForeignKey('orders.Order', on_delete=models.CASCADE)
#     discount_amount = models.DecimalField(max_digits=10, decimal_places=2)
#     created_at = models.DateTimeField(auto_now_add=True)

#     class Meta:
#         unique_together = ('promotion', 'order')

#     def __str__(self):
#         return f"{self.promotion.code} used by {self.user.email} on order #{self.order.id}"


# serializers.py
from django.db import models
from django.core.validators import MinValueValidator, MaxValueValidator
from django.utils import timezone
from decimal import Decimal

class Promotion(models.Model):
    DISCOUNT_TYPE_CHOICES = [
        ('PERCENTAGE', 'Percentage'),
        ('FIXED', 'Fixed Amount'),
    ]
    
    code = models.CharField(max_length=50, unique=True, db_index=True)
    description = models.TextField()
    discount_type = models.CharField(
        max_length=10,
        choices=DISCOUNT_TYPE_CHOICES,
        default='FIXED'
    )
    discount_value = models.DecimalField(
        max_digits=10,
        decimal_places=2,
        validators=[MinValueValidator(Decimal('0.00'))]
    )
    min_purchase_amount = models.DecimalField(
        max_digits=10,
        decimal_places=2,
        default=Decimal('0.00'),
        validators=[MinValueValidator(Decimal('0.00'))]
    )
    max_discount_amount = models.DecimalField(
        max_digits=10,
        decimal_places=2,
        null=True,
        blank=True,
        validators=[MinValueValidator(Decimal('0.00'))]
    )
    start_date = models.DateTimeField(db_index=True)
    end_date = models.DateTimeField(db_index=True)
    usage_limit = models.PositiveIntegerField(null=True, blank=True)
    times_used = models.PositiveIntegerField(default=0)
    is_active = models.BooleanField(default=True, db_index=True)
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)

    class Meta:
        ordering = ['-created_at']
        indexes = [
            models.Index(fields=['code', 'is_active', 'start_date', 'end_date'])
        ]

    def __str__(self):
        return f"{self.code} ({self.discount_type}: {self.discount_value})"

    @property
    def is_valid(self):
        now = timezone.now()
        return (
            self.is_active and
            self.start_date <= now <= self.end_date and
            (self.usage_limit is None or self.times_used < self.usage_limit)
        )

    def calculate_discount(self, subtotal):
        if not self.is_valid or subtotal < self.min_purchase_amount:
            return Decimal('0.00')

        if self.discount_type == 'PERCENTAGE':
            discount = Decimal(str(subtotal)) * (Decimal(str(self.discount_value)) / Decimal('100'))
        else:  # FIXED
            discount = min(Decimal(str(self.discount_value)), Decimal(str(subtotal)))

        if self.max_discount_amount:
            discount = min(discount, self.max_discount_amount)

        return Decimal(str(discount)).quantize(Decimal('0.01'))

class PromotionUsage(models.Model):
    promotion = models.ForeignKey(
        Promotion,
        on_delete=models.CASCADE,
        related_name='usages'
    )
    user = models.ForeignKey(
        'users.Customer',
        on_delete=models.CASCADE,
        related_name='promotion_usages'
    )
    order = models.ForeignKey(
        'orders.Order',
        on_delete=models.CASCADE,
        related_name='promotion_usages'
    )
    discount_amount = models.DecimalField(max_digits=10, decimal_places=2)
    created_at = models.DateTimeField(auto_now_add=True)

    class Meta:
        unique_together = ('promotion', 'order')
        indexes = [
            models.Index(fields=['user', 'promotion', 'order']),
            models.Index(fields=['created_at'])
        ]

    def __str__(self):
        return f"{self.promotion.code} - {self.user.email} - Order #{self.order.id}"