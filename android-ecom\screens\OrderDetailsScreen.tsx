import {
  View,
  Text,
  StyleSheet,
  ScrollView,
  Image,
  TouchableOpacity,
  ActivityIndicator,
} from "react-native";
import { SafeAreaView } from "react-native-safe-area-context";
import { MaterialIcons } from "@expo/vector-icons";
import { useAuth } from "@/context/AuthContext";
import React from "react";
import { useOrderById } from "@/hooks/useUserDetailHook";
import { BaseURL } from "@/constants/ApiEndpoint";

export default function OrderDetailsScreen({ route, navigation }) {
  const { isAuthenticated } = useAuth();
  const orderId = route?.params?.order?.id;
  const orderStatus = route?.params?.order?.status;

  const { order, loading } = useOrderById(orderId);

  if (!isAuthenticated) {
    // Redirect to auth screen if not authenticated
    React.useEffect(() => {
      navigation.replace("Auth", {
        returnTo: "Orders",
      });
    }, []);
    return null;
  }

  const renderStatusTimeline = () => {
    const statuses = ["Processing", "Shipped", "Delivered"];
    const currentIndex = statuses.indexOf(orderStatus);

    return (
      <View style={styles.timeline}>
        {statuses.map((status, index) => (
          <View key={status} style={styles.timelineItem}>
            <View
              style={[
                styles.timelineDot,
                {
                  backgroundColor:
                    index <= currentIndex
                      ? getStatusColor(orderStatus)
                      : "#E5E7EB",
                },
              ]}
            />
            <Text
              style={[
                styles.timelineText,
                {
                  color:
                    index <= currentIndex
                      ? getStatusColor(orderStatus)
                      : "#6B7280",
                },
              ]}
            >
              {status}
            </Text>
            {index < statuses.length - 1 && (
              <View
                style={[
                  styles.timelineLine,
                  {
                    backgroundColor:
                      index < currentIndex
                        ? getStatusColor(orderStatus)
                        : "#E5E7EB",
                  },
                ]}
              />
            )}
          </View>
        ))}
      </View>
    );
  };

  return (
    <SafeAreaView style={styles.container}>
      <View style={styles.header}>
        <TouchableOpacity
          style={styles.backButton}
          onPress={() => navigation.goBack()}
        >
          <MaterialIcons name="arrow-back" size={24} color="#333" />
        </TouchableOpacity>
        <Text style={styles.title}>Order Details</Text>
        <View style={{ width: 24 }} />
      </View>
{loading ? (
        <View style={styles.loadingContainer}>
          <ActivityIndicator size="large" color="#2563EB" />
          <Text>Loading orders...</Text>
        </View>
      ) :
      <ScrollView style={styles.content}>
        <View style={styles.section}>
          <Text style={styles.sectionTitle}>Order Status</Text>
          {renderStatusTimeline()}
        </View>

        <View style={styles.section}>
          <Text style={styles.sectionTitle}>Order Information</Text>
          <View style={styles.infoCard}>
            <View style={styles.infoRow}>
              <Text style={styles.infoLabel}>Order ID</Text>
              <Text style={styles.infoValue}>{order?.id}</Text>
            </View>
            <View style={styles.infoRow}>
              <Text style={styles.infoLabel}>Order Date</Text>
              <Text style={styles.infoValue}>
                {new Date(order?.created_at).toLocaleDateString("en-US", {
                  year: "numeric",
                  month: "long",
                  day: "numeric",
                })}
              </Text>
            </View>
            <View style={styles.infoRow}>
              <Text style={styles.infoLabel}>Total Amount</Text>
              <Text style={styles.infoValue}>
                ${Number(order?.total).toFixed(2)}
              </Text>
            </View>
          </View>
        </View>

        <View style={styles.section}>
          <Text style={styles.sectionTitle}>Items</Text>
          {order?.items.map((item) => (
            <View key={item.id} style={styles.itemCard}>
              <Image
                source={{ uri: BaseURL + item?.product_image }}
                style={styles.itemImage}
              />
              <View style={styles.itemInfo}>
                <Text style={styles.itemName}>{item?.product_name}</Text>
                <Text style={styles.itemQuantity}>
                  Quantity: {item.quantity}
                </Text>
                <Text style={styles.itemPrice}>
                  ${Number(item?.total_price).toFixed(2)}
                </Text>
              </View>
            </View>
          ))}
        </View>

        <View style={styles.section}>
          <Text style={styles.sectionTitle}>Shipping Address</Text>
          <View style={styles.addressCard}>
            <Text style={styles.addressText}>
              {order?.shipping_address?.apartment},{" "}
              {order?.shipping_address?.street_address},{"\n"}
              {order?.shipping_address?.city}, {order?.shipping_address?.state}{" "}
              - {order?.shipping_address?.postal_code},{"\n"}
              {order?.shipping_address?.country || "Country not specified"}
            </Text>
            <Text style={styles.addressText}>
              Contact: {order?.shipping_address?.order_user_phone}
            </Text>
          </View>
        </View>

        <View style={styles.section}>
          <Text style={styles.sectionTitle}>Payment Information</Text>
          <View style={styles.paymentCard}>
            <MaterialIcons name="credit-card" size={24} color="#666" />
            <Text style={styles.paymentText}>Visa ending in 4242</Text>
          </View>
        </View>
      </ScrollView>}
    </SafeAreaView>
  );
}

const getStatusColor = (status: string) => {
  switch (status) {
    case "Processing":
      return "#EAB308";
    case "Shipped":
      return "#2563EB";
    case "Delivered":
      return "#16A34A";
    default:
      return "#6B7280";
  }
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: "#ffffff",
  },
  loadingContainer: {
    flex: 1,
    alignItems: 'center',
    justifyContent: 'center',
  },
  header: {
    flexDirection: "row",
    alignItems: "center",
    justifyContent: "space-between",
    padding: 16,
    borderBottomWidth: 1,
    borderBottomColor: "#f3f4f6",
  },
  backButton: {
    padding: 8,
  },
  title: {
    fontSize: 20,
    fontWeight: "bold",
  },
  content: {
    flex: 1,
  },
  section: {
    padding: 16,
    borderBottomWidth: 1,
    borderBottomColor: "#f3f4f6",
  },
  sectionTitle: {
    fontSize: 18,
    fontWeight: "600",
    marginBottom: 16,
  },
  timeline: {
    flexDirection: "row",
    justifyContent: "space-between",
    alignItems: "center",
    paddingHorizontal: 16,
  },
  timelineItem: {
    alignItems: "center",
    flex: 1,
  },
  timelineDot: {
    width: 12,
    height: 12,
    borderRadius: 6,
    marginBottom: 8,
  },
  timelineText: {
    fontSize: 12,
    fontWeight: "500",
  },
  timelineLine: {
    position: "absolute",
    top: 6,
    left: "50%",
    width: "100%",
    height: 2,
  },
  infoCard: {
    backgroundColor: "white",
    borderRadius: 12,
    padding: 16,
    shadowColor: "#000",
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 8,
    elevation: 3,
  },
  infoRow: {
    flexDirection: "row",
    justifyContent: "space-between",
    marginBottom: 12,
  },
  infoLabel: {
    fontSize: 14,
    color: "#666",
  },
  infoValue: {
    fontSize: 14,
    fontWeight: "600",
  },
  itemCard: {
    flexDirection: "row",
    backgroundColor: "white",
    borderRadius: 12,
    padding: 12,
    marginBottom: 12,
    shadowColor: "#000",
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 8,
    elevation: 3,
  },
  itemImage: {
    width: 80,
    height: 80,
    borderRadius: 8,
  },
  itemInfo: {
    flex: 1,
    marginLeft: 12,
  },
  itemName: {
    fontSize: 16,
    fontWeight: "600",
    marginBottom: 4,
  },
  itemQuantity: {
    fontSize: 14,
    color: "#666",
    marginBottom: 4,
  },
  itemPrice: {
    fontSize: 16,
    fontWeight: "600",
    color: "#2563EB",
  },
  addressCard: {
    backgroundColor: "white",
    borderRadius: 12,
    padding: 16,
    shadowColor: "#000",
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 8,
    elevation: 3,
  },
  addressText: {
    fontSize: 14,
    lineHeight: 22,
    color: "#333",
  },
  paymentCard: {
    flexDirection: "row",
    alignItems: "center",
    backgroundColor: "white",
    borderRadius: 12,
    padding: 16,
    shadowColor: "#000",
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 8,
    elevation: 3,
  },
  paymentText: {
    marginLeft: 12,
    fontSize: 14,
    color: "#333",
  },
});
