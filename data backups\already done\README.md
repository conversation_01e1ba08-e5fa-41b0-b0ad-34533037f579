# Haier Product Image Scraper

A robust Python script to find and download Haier product images from multiple sources including Google, Bing, Amazon, Flipkart, and the Haier official website. The script uses Selenium with multithreading to search for product images and updates a JSON file with the local image paths.

## Features

- Searches multiple sources for Haier product images:
  - Google Images
  - Bing Images
  - Amazon
  - Flipkart
  - Haier official website
- Uses Selenium for dynamic web scraping
- Implements multithreading for efficient processing
- Downloads images and organizes them by product model
- Updates JSON file with local image paths
- <PERSON><PERSON> pagination and multiple images per product
- Avoids duplicate images using image hashing
- Robust error handling and logging

## Requirements

- Python 3.6+
- Selenium
- Pillow (PIL)
- tqdm
- requests
- A web browser (Chrome, Firefox, or Edge)

## Installation

1. Clone this repository or download the script
2. Install the required packages:

```bash
pip install selenium pillow tqdm requests webdriver-manager
```

3. Make sure you have a compatible web browser installed (Chrome, Firefox, or Edge)

## Usage

Basic usage:

```bash
python haier_image_scraper.py
```

This will process all products in the `haier_products_updated.json` file.

### Command Line Arguments

The script supports several command line arguments for more control:

- `--test`: Run in test mode with only 3 products
- `--start INDEX`: Start processing from the specified index
- `--count N`: Process only N products starting from the start index
- `--model MODEL`: Process only the product with the specified model number
- `--category CATEGORY`: Process only products in the specified category
- `--json-file FILE`: Use a different JSON file instead of the default
- `--max-images N`: Set the maximum number of images per product (default: 5)

### Examples

Process only a specific model:

```bash
python haier_image_scraper.py --model HIH-G90HM-DG
```

Process only products in the "Hood" category:

```bash
python haier_image_scraper.py --category Hood
```

Process 5 products starting from index 10:

```bash
python haier_image_scraper.py --start 10 --count 5
```

Run in test mode with only 3 products:

```bash
python haier_image_scraper.py --test
```

Use a different JSON file and set maximum images to 3:

```bash
python haier_image_scraper.py --json-file other_products.json --max-images 3
```

## Output

The script creates a directory structure for storing images:

```
haier_product_images/
├── MODEL1/
│   ├── image_0.jpg
│   ├── image_1.jpg
│   └── ...
├── MODEL2/
│   ├── image_0.jpg
│   ├── image_1.jpg
│   └── ...
└── ...
```

The JSON file is updated with the local image paths:

```json
{
  "name": "Haier Product Name",
  "model": "MODEL1",
  "images": [
    "haier_product_images\\MODEL1\\image_0.jpg",
    "haier_product_images\\MODEL1\\image_1.jpg",
    ...
  ],
  ...
}
```

## Logging

The script logs detailed information to both the console and a log file (`haier_image_scraper.log`). This includes:

- Progress information
- Errors and warnings
- Image download status
- Search results from different sources

## Troubleshooting

If you encounter issues with the web drivers:

1. Make sure you have a compatible browser installed
2. Try using a different browser by modifying the `create_driver` method
3. Check the log file for detailed error messages
4. Ensure you have the latest version of Selenium and the browser drivers

## License

This project is licensed under the MIT License - see the LICENSE file for details.
