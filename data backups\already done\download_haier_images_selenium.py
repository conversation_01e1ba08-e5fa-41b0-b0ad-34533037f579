import json
import os
import time
import re
from selenium import webdriver
from selenium.webdriver.chrome.service import Service
from selenium.webdriver.chrome.options import Options
from webdriver_manager.chrome import ChromeDriverManager
from selenium.webdriver.common.by import By
from selenium.webdriver.support.ui import WebDriverWait
from selenium.webdriver.support import expected_conditions as EC
from selenium.common.exceptions import TimeoutException, NoSuchElementException
from PIL import Image
from io import BytesIO
import base64

# Configuration
JSON_FILE_PATH = 'output/json/haier_kitchen_products.json'
OUTPUT_DIR = 'haier_product_images_selenium'
WAIT_TIME = 10  # seconds to wait for page elements to load

def create_folder(folder_path):
    """Create folder if it doesn't exist"""
    if not os.path.exists(folder_path):
        os.makedirs(folder_path)
        print(f"Created folder: {folder_path}")

def sanitize_filename(filename):
    """Remove invalid characters from filename"""
    return re.sub(r'[\\/*?:"<>|]', "", filename)

def setup_driver():
    """Set up and return a configured Chrome WebDriver using webdriver-manager."""
    chrome_options = Options()
    # Uncomment the line below to run in headless mode (no GUI)
    # chrome_options.add_argument("--headless")
    chrome_options.add_argument("--disable-gpu")
    chrome_options.add_argument("--window-size=1920,1080")
    chrome_options.add_argument("--disable-extensions")
    chrome_options.add_argument("--no-sandbox")
    chrome_options.add_argument("--disable-dev-shm-usage")
    chrome_options.add_argument("user-agent=Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36")
    
    # Initialize the Chrome WebDriver with webdriver-manager
    service = Service(ChromeDriverManager().install())
    driver = webdriver.Chrome(service=service, options=chrome_options)
    return driver

def capture_element_screenshot(driver, element, save_path):
    """Capture a screenshot of a specific element and save it."""
    try:
        # Scroll element into view
        driver.execute_script("arguments[0].scrollIntoView(true);", element)
        time.sleep(0.5)  # Allow time for any animations to complete
        
        # Get the element's location and size
        location = element.location
        size = element.size
        
        # Take a screenshot of the entire page
        screenshot = driver.get_screenshot_as_png()
        image = Image.open(BytesIO(screenshot))
        
        # Calculate the element's coordinates
        left = location['x']
        top = location['y']
        right = location['x'] + size['width']
        bottom = location['y'] + size['height']
        
        # Crop the image to the element's dimensions
        image = image.crop((left, top, right, bottom))
        
        # Save the cropped image
        image.save(save_path)
        print(f"Saved screenshot: {save_path}")
        return True
    except Exception as e:
        print(f"Error capturing screenshot: {e}")
        return False

def process_product_page(driver, product_url, product_model, product_name, product_dir):
    """Visit product page and capture images using Selenium."""
    try:
        print(f"Visiting: {product_url}")
        driver.get(product_url)
        
        # Wait for the page to load
        wait = WebDriverWait(driver, WAIT_TIME)
        wait.until(EC.presence_of_element_located((By.TAG_NAME, "img")))
        
        # Look for image elements - adjust selectors based on the actual website structure
        image_elements = []
        
        # Try different possible image containers
        try:
            # Product gallery images
            gallery_images = driver.find_elements(By.CSS_SELECTOR, '.product-gallery img, .product-images img, .gallery img')
            if gallery_images:
                image_elements.extend(gallery_images)
            
            # Main product image
            main_image = driver.find_elements(By.CSS_SELECTOR, '.product-image img, .main-image img, .product-img img')
            if main_image:
                image_elements.extend(main_image)
            
            # Banner images that might contain product
            banner_images = driver.find_elements(By.CSS_SELECTOR, '.product-banner img')
            if banner_images:
                image_elements.extend(banner_images)
                
            # If still no images found, try a more generic approach
            if not image_elements:
                # Look for any img with src containing the product model or with alt text containing product name
                all_images = driver.find_elements(By.TAG_NAME, 'img')
                for img in all_images:
                    src = img.get_attribute('src') or ''
                    alt = img.get_attribute('alt') or ''
                    if product_model.lower() in src.lower() or product_model.lower() in alt.lower() or \
                       product_name.lower() in alt.lower():
                        image_elements.append(img)
            
            # If still no images, get all images from the page that are reasonably sized
            if not image_elements:
                all_images = driver.find_elements(By.TAG_NAME, 'img')
                for img in all_images:
                    width = img.get_attribute('width')
                    height = img.get_attribute('height')
                    if width and height and int(width) > 100 and int(height) > 100:
                        image_elements.append(img)
            
            # Capture screenshots of each image element
            successful_captures = 0
            for i, img in enumerate(image_elements):
                filename = f"{product_model}_{i+1}.png"
                save_path = os.path.join(product_dir, filename)
                
                if capture_element_screenshot(driver, img, save_path):
                    successful_captures += 1
            
            print(f"Captured {successful_captures} images for {product_name} ({product_model})")
            return successful_captures
            
        except NoSuchElementException as e:
            print(f"Element not found: {e}")
            return 0
    
    except TimeoutException:
        print(f"Timeout while loading {product_url}")
        return 0
    except Exception as e:
        print(f"Error processing {product_url}: {e}")
        return 0

def main():
    # Create base output directory
    create_folder(OUTPUT_DIR)
    
    # Load JSON file
    with open(JSON_FILE_PATH, 'r') as f:
        products = json.load(f)
    
    print(f"Found {len(products)} products in the JSON file")
    
    # Initialize the Selenium WebDriver
    driver = setup_driver()
    
    try:
        # Process each product
        for i, product in enumerate(products):
            product_name = product.get('name', 'Unknown')
            product_model = product.get('model', 'Unknown')
            
            # Create sanitized folder name
            folder_name = sanitize_filename(f"{product_model}_{product_name}")
            product_dir = os.path.join(OUTPUT_DIR, folder_name)
            create_folder(product_dir)
            
            # Get product URL from images array
            product_urls = product.get('images', [])
            if not product_urls:
                print(f"No URL found for product: {product_name} ({product_model})")
                continue
            
            # Process each URL for the product
            for product_url in product_urls:
                # Skip empty URLs
                if not product_url:
                    continue
                    
                # Process the product page and capture images
                process_product_page(driver, product_url, product_model, product_name, product_dir)
                
                # Add a small delay to avoid overwhelming the server
                time.sleep(2)
            
            print(f"Completed {i+1}/{len(products)}: {product_name}")
    
    finally:
        # Always close the driver to free resources
        driver.quit()
        print("WebDriver closed")

if __name__ == "__main__":
    main()
