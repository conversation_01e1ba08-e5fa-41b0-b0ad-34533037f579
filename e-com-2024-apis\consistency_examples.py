#!/usr/bin/env python3
"""
Database Consistency Examples for E-commerce Operations

This file demonstrates how to handle read-after-write consistency
for critical e-commerce operations like user registration, cart management,
and order processing.
"""

import os
import django

# Setup Django environment
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'backend.settings')
django.setup()

from django.db import transaction
from django.contrib.auth import get_user_model
from backend.db_utils import ensure_read_after_write_consistency, ensure_primary_db
from products.models import Product
from orders.models import Cart, CartItem, Order

User = get_user_model()


class EcommerceConsistencyExamples:
    """Examples of handling consistency in e-commerce operations"""
    
    @staticmethod
    @transaction.atomic
    def user_registration_and_login(email, password, name):
        """
        Example: User registration followed by immediate login
        
        Problem: User creates account, then immediately tries to login
        Solution: Use transaction to ensure consistency
        """
        print("🔐 User Registration with Immediate Login")
        
        # Step 1: Create user (goes to primary DB)
        user = User.objects.create_user(
            email=email,
            password=password,
            name=name
        )
        print(f"   ✅ User created: {user.email}")
        
        # Step 2: Immediately verify user exists (stays in primary DB due to transaction)
        user_check = User.objects.get(email=email)
        print(f"   ✅ User verified: {user_check.email}")
        
        # Step 3: Any other operations in this transaction use primary DB
        user_check.is_verified = True
        user_check.save()
        print(f"   ✅ User verified and updated")
        
        return user_check
    
    @staticmethod
    def cart_operations_with_consistency(user_email, product_id, quantity):
        """
        Example: Cart operations that need immediate consistency
        
        Problem: Add item to cart, then immediately view cart
        Solution: Use primary DB for cart operations after writes
        """
        print("🛒 Cart Operations with Consistency")
        
        # Get user (can use replica for this)
        user = User.objects.get(email=user_email)
        
        # Get or create cart (write operation - goes to primary)
        cart, created = Cart.objects.get_or_create(user=user)
        if created:
            print(f"   ✅ New cart created for {user.email}")
        
        # Add item to cart (write operation - goes to primary)
        product = Product.objects.get(id=product_id)
        cart_item, created = CartItem.objects.get_or_create(
            cart=cart,
            product=product,
            defaults={'quantity': quantity}
        )
        
        if not created:
            cart_item.quantity += quantity
            cart_item.save()
        
        print(f"   ✅ Added {quantity} x {product.name} to cart")
        
        # Immediately get updated cart (use primary for consistency)
        updated_cart = ensure_read_after_write_consistency(
            Cart, 
            user=user
        ).prefetch_related('items__product').first()
        
        total_items = sum(item.quantity for item in updated_cart.items.all())
        print(f"   ✅ Cart now has {total_items} total items")
        
        return updated_cart
    
    @staticmethod
    @transaction.atomic
    def order_creation_and_confirmation(user_email, cart_items):
        """
        Example: Order creation with immediate confirmation
        
        Problem: Create order, then immediately show order confirmation
        Solution: Use transaction for entire order process
        """
        print("📦 Order Creation with Immediate Confirmation")
        
        # Get user
        user = User.objects.get(email=user_email)
        
        # Create order (all operations in this transaction use primary DB)
        order = Order.objects.create(
            user=user,
            status='PENDING',
            total=0  # Will calculate below
        )
        print(f"   ✅ Order created: {order.id}")
        
        # Add order items
        total_amount = 0
        for item_data in cart_items:
            product = Product.objects.get(id=item_data['product_id'])
            quantity = item_data['quantity']
            unit_price = product.price
            
            OrderItem.objects.create(
                order=order,
                product=product,
                quantity=quantity,
                unit_price=unit_price,
                total_price=unit_price * quantity,
                product_name=product.name
            )
            
            total_amount += unit_price * quantity
        
        # Update order total
        order.total = total_amount
        order.save()
        print(f"   ✅ Order total: ${total_amount}")
        
        # Immediately get order details (stays in primary due to transaction)
        order_with_items = Order.objects.select_related('user').prefetch_related(
            'items__product'
        ).get(id=order.id)
        
        print(f"   ✅ Order confirmation ready: {len(order_with_items.items.all())} items")
        
        return order_with_items
    
    @staticmethod
    def safe_user_profile_update(user_email, profile_data):
        """
        Example: Profile update with immediate display
        
        Problem: Update profile, then immediately show updated profile
        Solution: Use primary DB for reads after profile updates
        """
        print("👤 Profile Update with Immediate Display")
        
        # Update user profile (goes to primary)
        user = User.objects.get(email=user_email)
        for field, value in profile_data.items():
            setattr(user, field, value)
        user.save()
        print(f"   ✅ Profile updated for {user.email}")
        
        # Immediately get updated profile (use primary for consistency)
        updated_user = ensure_read_after_write_consistency(
            User, 
            email=user_email
        ).first()
        
        print(f"   ✅ Updated profile retrieved: {updated_user.name}")
        
        return updated_user
    
    @staticmethod
    def payment_processing_with_verification(order_id, payment_data):
        """
        Example: Payment processing with immediate verification
        
        Problem: Process payment, then immediately verify payment status
        Solution: Use transaction for payment operations
        """
        print("💳 Payment Processing with Verification")
        
        with transaction.atomic():
            # Get order (uses primary due to transaction)
            order = Order.objects.get(id=order_id)
            
            # Process payment (simulate payment processing)
            order.status = 'PAID'
            order.stripe_payment_intent_id = payment_data.get('payment_intent_id')
            order.save()
            print(f"   ✅ Payment processed for order {order.id}")
            
            # Immediately verify payment (stays in primary due to transaction)
            verified_order = Order.objects.get(id=order_id)
            
            if verified_order.status == 'PAID':
                print(f"   ✅ Payment verified successfully")
                return True
            else:
                print(f"   ❌ Payment verification failed")
                return False


def demonstrate_consistency_scenarios():
    """Demonstrate various consistency scenarios"""
    print("🎯 E-COMMERCE CONSISTENCY SCENARIOS")
    print("=" * 50)
    
    examples = EcommerceConsistencyExamples()
    
    try:
        # Scenario 1: User Registration
        print("\n" + "="*50)
        user = examples.user_registration_and_login(
            email="<EMAIL>",
            password="testpass123",
            name="Test User"
        )
        
        # Scenario 2: Cart Operations
        print("\n" + "="*50)
        # Note: This would need actual product IDs from your database
        # cart = examples.cart_operations_with_consistency(
        #     user_email="<EMAIL>",
        #     product_id=1,
        #     quantity=2
        # )
        
        # Scenario 3: Profile Update
        print("\n" + "="*50)
        updated_user = examples.safe_user_profile_update(
            user_email="<EMAIL>",
            profile_data={"name": "Updated Test User"}
        )
        
        print("\n✅ All consistency scenarios completed successfully!")
        
    except Exception as e:
        print(f"\n❌ Error in demonstration: {e}")
        print("Note: Some scenarios require existing data in your database")


def show_consistency_best_practices():
    """Show best practices for maintaining consistency"""
    print("\n💡 CONSISTENCY BEST PRACTICES")
    print("=" * 50)
    
    practices = [
        "🔐 Use @transaction.atomic for multi-step operations",
        "📝 Use primary DB for reads immediately after writes",
        "⏱️ Keep transactions short to avoid blocking",
        "🎯 Use replicas for read-only operations (reports, listings)",
        "🔄 Handle replica lag gracefully in non-critical operations",
        "📊 Monitor replica lag in production",
        "🚨 Use primary DB for authentication and authorization",
        "💳 Use transactions for payment and order processing",
        "🛒 Use primary DB for cart operations after modifications",
        "👤 Use primary DB for profile reads after updates"
    ]
    
    for i, practice in enumerate(practices, 1):
        print(f"{i:2}. {practice}")


if __name__ == "__main__":
    print("Database Consistency Examples for E-commerce")
    print("=" * 50)
    
    # Show best practices
    show_consistency_best_practices()
    
    # Demonstrate scenarios (commented out to avoid database dependencies)
    # demonstrate_consistency_scenarios()
    
    print("\n🎯 Key Takeaway:")
    print("The system automatically handles most consistency issues,")
    print("but you can use the provided utilities for critical operations!")
