import { NextAuthOptions } from "next-auth";
import GoogleProvider from "next-auth/providers/google";
import Credentials<PERSON>rovider from "next-auth/providers/credentials";
import { MAIN_URL, TOKEN_REFFRESH, USER_SOCIAL_LOGIN } from "../constant/urls";
import axios from "axios";
import { authUtils, tokenManager } from "./secureStorage";

export const authOptions: NextAuthOptions = {
  providers: [
    GoogleProvider({
      clientId: process.env.NEXT_PUBLIC_GOOGLE_CLIENT_ID || process.env.GOOGLE_CLIENT_ID || "",
      clientSecret: process.env.NEXT_PUBLIC_GOOGLE_CLIENT_SECRET || process.env.GOOGLE_CLIENT_SECRET || "",
    }),
    CredentialsProvider({
      // Configure to use the custom API response
      name: "Credentials",
      credentials: {},
      async authorize(credentials) {
        try {
          const data: any = { ...credentials };
          if (data) {
            const user = {
              id: data.id || "",
              email: data.email || "",
              name: data.name || "",
              accessToken: data.accessToken || "",  // Changed to match JWT callback
              refreshToken: data.refreshToken || "", // Changed to match JWT callback
              access: data.accessToken || "",        // Keep for backward compatibility
              refresh: data.refreshToken || "",      // Keep for backward compatibility
              phone: data.phone_number,
              dob: data.date_of_birth,
            };
            return user;
          }
        } catch (error) {
          console.error("Error in authorize:", error);
          return null;
        }
        return null;
      },
    }),
  ],
  callbacks: {
    async signIn({ user, account, profile }: any) {
      if (account?.provider === "google") {
        try {
          // Show loading state in the console
          console.log("Google sign-in in progress...");
          console.log("Google profile data:", profile);

          // Send Google user data to your backend API
          const res = await axios.post(`${MAIN_URL}${USER_SOCIAL_LOGIN}`, {
            email: user.email,
            name: user.name,
            image_url: user.image || profile?.picture,
            provider: "google",
          }, {
            headers: { "Content-Type": "application/json" }
          });

          if (res.status === 200) {
            const data = res.data;
            console.log("Social login success response:", data);

            // Attach custom tokens to user object
            user.accessToken = data.accessToken;  // Changed to match JWT callback
            user.refreshToken = data.refreshToken; // Changed to match JWT callback
            user.access = data.accessToken;        // Keep for backward compatibility
            user.refresh = data.refreshToken;      // Keep for backward compatibility
            user.phone = data.phone_number;
            user.dob = data.date_of_birth;
            user.id = Number(data.id);
            user.email = data.email;
            user.name = data.name;

            console.log("Google sign-in successful, redirecting...");
            return true;
          } else {
            console.error("Backend login failed with status:", res.status);
            return false;
          }
        } catch (error) {
          console.error("Error during Google sign-in:", error);
          if (axios.isAxiosError(error)) {
            console.error("Axios error details:", error.response?.data);
          }
          return false;
        }
      }
      return true;
    },
    async jwt({ token, user }: any) {
      if (user) {
        console.log("=== JWT CALLBACK DEBUG ===");
        console.log("User object:", user);
        console.log("user.accessToken:", user.accessToken);
        console.log("user.access:", user.access);
        console.log("user.refreshToken:", user.refreshToken);
        console.log("user.refresh:", user.refresh);

        // Store tokens securely using our secure storage
        if (user.accessToken && user.refreshToken) {
          authUtils.storeAuthResponse({
            accessToken: user.accessToken,
            refreshToken: user.refreshToken,
            ...user
          });
        }

        token.access = user.accessToken || user.access;
        token.refresh = user.refreshToken || user.refresh;
        token.phone = user.phone;
        token.dob = user.dob;
        token.id = user.id;
        token.email = user.email;
        token.name = user.name;
        token.accessTokenExpires = Date.now() + 15 * 60 * 1000; // 15 minutes as per backend config

        console.log("Final token.access:", token.access);
        console.log("Final token.refresh:", token.refresh);
        console.log("=== END JWT CALLBACK DEBUG ===");
      }

      // Check if token is still valid
      if (Date.now() < token.accessTokenExpires) {
        return token;
      }

      // Check if we have a refresh token before attempting refresh
      if (!token.refresh) {
        console.log("No refresh token available, forcing re-login");
        return {
          ...token,
          access: null,
          refresh: null,
          error: "NoRefreshToken"
        };
      }

      // Access token expired, try to update it
      return refreshAccessToken(token);
    },
    async session({ session, token }: any) {
      // If there's a token error, don't include access token in session
      // This will force the frontend to redirect to login
      if (token.error) {
        console.log("Token error detected, session will not include access token:", token.error);
        session.user = {
          ...session.user,
          access: null,
          refresh: null,
          phone: token.phone,
          dob: token.dob,
          id: token.id,
          email: token.email ?? "",
          name: token.name ?? "",
          error: token.error,
        };
      } else {
        session.user = {
          ...session.user,
          access: token.access,
          refresh: token.refresh,
          phone: token.phone,
          dob: token.dob,
          id: token.id,
          email: token.email ?? "",
          name: token.name ?? "",
        };
      }
      return session;
    },
  },
  pages: {
    signIn: "/auth/login",
  },
};

async function refreshAccessToken(token: any) {
  try {
    console.log("=== TOKEN REFRESH DEBUG ===");
    console.log("Attempting to refresh token...");
    console.log("Refresh token exists:", !!token.refresh);
    console.log("Refresh token preview:", token.refresh?.substring(0, 50) + "...");
    console.log("Refresh URL:", MAIN_URL + TOKEN_REFFRESH);

    const res = await axios.post(MAIN_URL + TOKEN_REFFRESH,
      { refresh: token.refresh },
      { headers: { "Content-Type": "application/json" } }
    );

    const refreshedTokens = res.data;
    console.log("Token refresh successful!");
    console.log("New access token preview:", refreshedTokens.access?.substring(0, 50) + "...");

    return {
      ...token,
      access: refreshedTokens.access,
      accessTokenExpires: Date.now() + 15 * 60 * 1000, // 15 minutes from now (matching Django config)
    };
  } catch (error) {
    console.error("=== TOKEN REFRESH FAILED ===");
    console.error("Error refreshing access token:", error);
    if (axios.isAxiosError(error)) {
      console.error("Response status:", error.response?.status);
      console.error("Response data:", error.response?.data);
      console.error("Request data:", { refresh: token.refresh });
    }
    console.error("=== END TOKEN REFRESH DEBUG ===");

    // Instead of returning an error token, return the original token without access
    // This will force a re-login
    return {
      ...token,
      access: null,
      refresh: null,
      error: "RefreshAccessTokenError"
    };
  }
}
