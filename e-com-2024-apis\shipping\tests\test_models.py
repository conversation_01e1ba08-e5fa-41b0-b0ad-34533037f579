"""
Tests for shipping models
"""

from django.test import TestCase
from django.contrib.auth import get_user_model
from django.core.exceptions import ValidationError
from decimal import Decimal
from datetime import datetime, timedelta
from django.utils import timezone

from orders.models import Order, ShippingMethod
from users.models import Address
from shipping.models import (
    RapidshypConfiguration,
    RapidshypShipment,
    ShippingRateCache,
    TrackingEvent,
    RapidshypAPILog
)

User = get_user_model()


class RapidshypConfigurationTests(TestCase):
    def setUp(self):
        self.config_data = {
            'store_name': 'DEFAULT',
            'pickup_address_name': 'Main Warehouse',
            'default_pickup_pincode': '110001',
            'contact_name': 'Test Contact',
            'contact_phone': '9876543210',
            'contact_email': '<EMAIL>',
            'address_line_1': 'Test Address Line 1',
        }
    
    def test_create_configuration(self):
        """Test creating a Rapidshyp configuration"""
        config = RapidshypConfiguration.objects.create(**self.config_data)
        
        self.assertEqual(config.store_name, 'DEFAULT')
        self.assertEqual(config.pickup_address_name, 'Main Warehouse')
        self.assertTrue(config.is_active)
        self.assertIsNotNone(config.created_at)
    
    def test_configuration_str_method(self):
        """Test string representation of configuration"""
        config = RapidshypConfiguration.objects.create(**self.config_data)
        expected_str = f"{config.store_name} - {config.pickup_address_name}"
        self.assertEqual(str(config), expected_str)


class RapidshypShipmentTests(TestCase):
    def setUp(self):
        # Create test user
        self.user = User.objects.create_user(
            email='<EMAIL>',
            password='testpass123',
            first_name='Test',
            last_name='User'
        )
        
        # Create test addresses
        self.shipping_address = Address.objects.create(
            user=self.user,
            street_address='123 Test Street',
            city='Test City',
            state='Test State',
            postal_code='110001',
            address_type='SHIPPING'
        )
        
        # Create test shipping method
        self.shipping_method = ShippingMethod.objects.create(
            name='Standard Shipping',
            price=Decimal('50.00'),
            estimated_days=5,
            is_active=True
        )
        
        # Create test order
        self.order = Order.objects.create(
            user=self.user,
            shipping_address=self.shipping_address,
            billing_address=self.shipping_address,
            shipping_method=self.shipping_method,
            subtotal=Decimal('100.00'),
            total=Decimal('150.00')
        )
    
    def test_create_rapidshyp_shipment(self):
        """Test creating a Rapidshyp shipment"""
        shipment = RapidshypShipment.objects.create(
            order=self.order,
            rapidshyp_order_id='TEST123',
            courier_code='6001',
            courier_name='BlueDart Express',
            total_freight=Decimal('85.50')
        )
        
        self.assertEqual(shipment.order, self.order)
        self.assertEqual(shipment.rapidshyp_order_id, 'TEST123')
        self.assertEqual(shipment.current_status, 'SCB')  # Default status
        self.assertFalse(shipment.pickup_scheduled)
    
    def test_shipment_properties(self):
        """Test shipment property methods"""
        shipment = RapidshypShipment.objects.create(
            order=self.order,
            rapidshyp_order_id='TEST123',
            courier_code='6001',
            courier_name='BlueDart Express',
            total_freight=Decimal('85.50'),
            current_status='DEL'
        )
        
        self.assertTrue(shipment.is_delivered)
        self.assertFalse(shipment.is_in_transit)
        self.assertFalse(shipment.is_returned)
        
        # Test in transit status
        shipment.current_status = 'INT'
        shipment.save()
        self.assertTrue(shipment.is_in_transit)
        self.assertFalse(shipment.is_delivered)
        
        # Test RTO status
        shipment.current_status = 'RTO_DEL'
        shipment.save()
        self.assertTrue(shipment.is_returned)


class ShippingRateCacheTests(TestCase):
    def test_create_rate_cache(self):
        """Test creating a shipping rate cache entry"""
        expires_at = timezone.now() + timedelta(minutes=5)
        
        cache_entry = ShippingRateCache.objects.create(
            pickup_pincode='110001',
            delivery_pincode='400001',
            weight=Decimal('1.0'),
            cod=False,
            rates_data={'test': 'data'},
            is_serviceable=True,
            expires_at=expires_at
        )
        
        self.assertEqual(cache_entry.pickup_pincode, '110001')
        self.assertEqual(cache_entry.delivery_pincode, '400001')
        self.assertTrue(cache_entry.is_serviceable)
        self.assertFalse(cache_entry.is_expired)
    
    def test_cache_expiry(self):
        """Test cache expiry functionality"""
        # Create expired cache entry
        expires_at = timezone.now() - timedelta(minutes=5)
        
        cache_entry = ShippingRateCache.objects.create(
            pickup_pincode='110001',
            delivery_pincode='400001',
            weight=Decimal('1.0'),
            cod=False,
            rates_data={'test': 'data'},
            is_serviceable=True,
            expires_at=expires_at
        )
        
        self.assertTrue(cache_entry.is_expired)


class TrackingEventTests(TestCase):
    def setUp(self):
        # Create test data (reuse from RapidshypShipmentTests)
        self.user = User.objects.create_user(
            email='<EMAIL>',
            password='testpass123'
        )
        
        self.shipping_address = Address.objects.create(
            user=self.user,
            street_address='123 Test Street',
            city='Test City',
            state='Test State',
            postal_code='110001',
            address_type='SHIPPING'
        )
        
        self.shipping_method = ShippingMethod.objects.create(
            name='Standard Shipping',
            price=Decimal('50.00'),
            estimated_days=5,
            is_active=True
        )
        
        self.order = Order.objects.create(
            user=self.user,
            shipping_address=self.shipping_address,
            billing_address=self.shipping_address,
            shipping_method=self.shipping_method,
            subtotal=Decimal('100.00'),
            total=Decimal('150.00')
        )
        
        self.shipment = RapidshypShipment.objects.create(
            order=self.order,
            rapidshyp_order_id='TEST123',
            courier_code='6001',
            courier_name='BlueDart Express',
            total_freight=Decimal('85.50')
        )
    
    def test_create_tracking_event(self):
        """Test creating a tracking event"""
        event = TrackingEvent.objects.create(
            shipment=self.shipment,
            status='PUC',
            status_description='Pickup Completed',
            location='Delhi Hub',
            event_timestamp=timezone.now()
        )
        
        self.assertEqual(event.shipment, self.shipment)
        self.assertEqual(event.status, 'PUC')
        self.assertEqual(event.location, 'Delhi Hub')


class RapidshypAPILogTests(TestCase):
    def test_create_api_log(self):
        """Test creating an API log entry"""
        log_entry = RapidshypAPILog.objects.create(
            method='serviceability_check',
            endpoint='/serviceabilty_check',
            request_data={'test': 'request'},
            response_data={'test': 'response'},
            response_status_code=200,
            response_time_ms=150,
            is_success=True
        )
        
        self.assertEqual(log_entry.method, 'serviceability_check')
        self.assertTrue(log_entry.is_success)
        self.assertEqual(log_entry.response_status_code, 200)
    
    def test_api_log_str_method(self):
        """Test string representation of API log"""
        log_entry = RapidshypAPILog.objects.create(
            method='serviceability_check',
            endpoint='/serviceabilty_check',
            request_data={'test': 'request'},
            is_success=True
        )
        
        str_repr = str(log_entry)
        self.assertIn('✓', str_repr)  # Success symbol
        self.assertIn('serviceability_check', str_repr)
