<!DOCTYPE html>
<html>
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Administrative Action Alert - Security Notification</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            line-height: 1.6;
            color: #333;
            max-width: 600px;
            margin: 0 auto;
            padding: 20px;
            background-color: #f5f5f5;
        }
        .container {
            background-color: #ffffff;
            border-radius: 8px;
            padding: 30px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        .header {
            text-align: center;
            margin-bottom: 30px;
            border-bottom: 2px solid #f39c12;
            padding-bottom: 20px;
        }
        .priority-medium {
            background-color: #f39c12;
            color: white;
            padding: 8px 16px;
            border-radius: 20px;
            font-weight: bold;
            display: inline-block;
            margin-bottom: 15px;
        }
        .alert-icon {
            font-size: 48px;
            color: #f39c12;
            margin-bottom: 10px;
        }
        h1 {
            color: #2c3e50;
            margin: 0;
            font-size: 24px;
        }
        .admin-action {
            background-color: #fff3e0;
            border: 2px solid #f39c12;
            border-radius: 8px;
            padding: 20px;
            margin: 20px 0;
            text-align: center;
        }
        .action-type {
            font-size: 20px;
            font-weight: bold;
            color: #f39c12;
            margin: 10px 0;
            padding: 10px;
            background-color: #fff;
            border-radius: 5px;
        }
        .event-details {
            background-color: #f8f9fa;
            border-left: 4px solid #f39c12;
            padding: 20px;
            margin: 20px 0;
            border-radius: 0 5px 5px 0;
        }
        .detail-row {
            display: flex;
            justify-content: space-between;
            margin-bottom: 10px;
            padding: 8px 0;
            border-bottom: 1px solid #ecf0f1;
        }
        .detail-label {
            font-weight: bold;
            color: #34495e;
            min-width: 120px;
        }
        .detail-value {
            color: #2c3e50;
            word-break: break-all;
        }
        .timestamp {
            background-color: #ecf0f1;
            padding: 15px;
            border-radius: 5px;
            text-align: center;
            margin: 20px 0;
            font-family: monospace;
            color: #7f8c8d;
        }
        .action-section {
            background-color: #e8f5e8;
            border: 1px solid #27ae60;
            border-radius: 5px;
            padding: 15px;
            margin: 20px 0;
        }
        .action-title {
            color: #27ae60;
            font-weight: bold;
            margin-bottom: 10px;
        }
        .info-notice {
            background-color: #e3f2fd;
            border: 1px solid #2196f3;
            border-radius: 5px;
            padding: 15px;
            margin: 20px 0;
            color: #1565c0;
        }
        .footer {
            margin-top: 30px;
            padding-top: 20px;
            border-top: 1px solid #ecf0f1;
            font-size: 12px;
            color: #7f8c8d;
            text-align: center;
        }
        .admin-highlight {
            background-color: #fff;
            padding: 5px 10px;
            border-radius: 3px;
            font-family: monospace;
            border: 1px solid #ddd;
            color: #f39c12;
            font-weight: bold;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <div class="alert-icon">👨‍💼</div>
            <div class="priority-medium">MEDIUM PRIORITY</div>
            <h1>Administrative Action Alert</h1>
            <p style="margin: 10px 0 0 0; color: #7f8c8d;">{{ site_name }} Security Monitoring</p>
        </div>
        
        <div class="admin-action">
            <h3 style="margin-top: 0; color: #f39c12;">🔧 ADMIN ACTION PERFORMED</h3>
            <div class="action-type">{{ event_data.action|default:"Administrative Action" }}</div>
            <p style="margin-bottom: 0; color: #666;">Administrative action has been logged for security audit</p>
        </div>
        
        <div class="event-details">
            <h3 style="margin-top: 0; color: #f39c12;">Action Details</h3>
            
            <div class="detail-row">
                <span class="detail-label">Administrator:</span>
                <span class="detail-value">
                    <span class="admin-highlight">{{ event_data.admin_user|default:user_email }}</span>
                </span>
            </div>
            
            <div class="detail-row">
                <span class="detail-label">Action Performed:</span>
                <span class="detail-value">{{ event_data.action|default:"Unspecified Action" }}</span>
            </div>
            
            <div class="detail-row">
                <span class="detail-label">Target/Resource:</span>
                <span class="detail-value">{{ event_data.target|default:"Not specified" }}</span>
            </div>
            
            <div class="detail-row">
                <span class="detail-label">Source IP:</span>
                <span class="detail-value">{{ ip_address }}</span>
            </div>
            
            <div class="detail-row">
                <span class="detail-label">User Agent:</span>
                <span class="detail-value">{{ event_data.user_agent|default:"Unknown" }}</span>
            </div>
        </div>
        
        <div class="timestamp">
            <strong>Action Timestamp:</strong> {{ timestamp|date:"F d, Y, g:i A T" }}
        </div>
        
        <div class="action-section">
            <div class="action-title">📋 Audit Trail</div>
            <p>{{ event_data.action_taken|default:"Administrative action has been logged in the security audit trail for compliance and monitoring purposes." }}</p>
        </div>
        
        <div class="info-notice">
            <strong>📊 Security Audit Information:</strong>
            <ul style="margin: 10px 0;">
                <li><strong>Compliance:</strong> This action is logged for regulatory compliance</li>
                <li><strong>Audit Trail:</strong> Full details are available in the security logs</li>
                <li><strong>Verification:</strong> Confirm this action was authorized and legitimate</li>
                <li><strong>Monitoring:</strong> Administrative actions are continuously monitored</li>
            </ul>
        </div>
        
        <div style="background-color: #f3e5f5; border: 1px solid #9c27b0; border-radius: 5px; padding: 15px; margin: 20px 0;">
            <h4 style="margin-top: 0; color: #7b1fa2;">🔍 Action Summary</h4>
            <p><strong>Administrator:</strong> {{ event_data.admin_user|default:user_email }}</p>
            <p><strong>Action:</strong> {{ event_data.action|default:"Administrative Action" }}</p>
            <p><strong>Target:</strong> {{ event_data.target|default:"System" }}</p>
            <p style="margin-bottom: 0;"><strong>Status:</strong> Logged and monitored</p>
        </div>
        
        <div style="background-color: #fff3e0; border: 1px solid #ff9800; border-radius: 5px; padding: 15px; margin: 20px 0;">
            <strong>⚠️ Security Note:</strong>
            <p style="margin: 10px 0 0 0;">If this administrative action was not performed by you or an authorized administrator, please investigate immediately and take appropriate security measures.</p>
        </div>
        
        <div class="footer">
            <p>This is an automated security notification from {{ site_name }}.</p>
            <p>Action logged at {{ timestamp|date:"F d, Y, g:i A T" }}</p>
            <p>Administrative actions are monitored for security and compliance purposes.</p>
        </div>
    </div>
</body>
</html>
