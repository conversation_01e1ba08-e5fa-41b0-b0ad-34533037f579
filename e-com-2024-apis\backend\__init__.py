"""
Backend Django app initialization.

This module handles the initialization of the backend app,
including database synchronization signal registration.
"""

def setup_database_sync():
    """Setup database synchronization signals if replicas are configured."""
    from django.conf import settings

    # Only setup sync if replicas are configured
    if hasattr(settings, 'DATABASES') and len(settings.DATABASES) > 1:
        try:
            # Import db_sync module to register signals
            from . import db_sync  # noqa: F401 - Import for signal registration
            return True
        except ImportError:
            return False
    return False


# Setup sync when the app is imported
_sync_enabled = setup_database_sync()