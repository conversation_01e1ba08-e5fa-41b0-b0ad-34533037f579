import { useState, useCallback } from "react";
import axios, { AxiosInstance, AxiosError, AxiosRequestConfig } from "axios";
import { useRouter } from "next/navigation";
import { useAuthSession } from "./useAuthSession";

// Extend AxiosRequestConfig to include retry flag
declare module "axios" {
  interface AxiosRequestConfig {
    _retry?: boolean;
  }
}

interface ApiResponse<T> {
  data: T | null;
  loading: boolean;
  error: string | null;
}

interface UseApiReturn<T> extends ApiResponse<T> {
  create: (endpoint: string, body: unknown) => Promise<T | string | undefined>;
  read: (endpoint: string) => Promise<T | string | undefined>;
  update: (endpoint: string, body: unknown) => Promise<T | string | undefined>;
  remove: (endpoint: string) => Promise<T | string | undefined>;
}

const useApi = <T>(baseUrl: string): UseApiReturn<T> => {
  const [data, setData] = useState<T | null>(null);
  const [loading, setLoading] = useState<boolean>(false);
  const [error, setError] = useState<string | null>(null);
  const {
    isAuthenticated,
    hasRefreshError,
    signOutWithCleanup,
    getAccessToken,
    refreshSession
  } = useAuthSession();
  const router = useRouter();

  // Create axios instance with interceptors
  const createAxiosInstance = useCallback((): AxiosInstance => {
    const instance = axios.create({
      baseURL: baseUrl,
      timeout: 30000,
      headers: {
        'Content-Type': 'application/json',
      },
    });

    // Request interceptor to add auth token
    instance.interceptors.request.use(
      (config) => {
        const accessToken = getAccessToken();
        if (accessToken) {
          config.headers.Authorization = `Bearer ${accessToken}`;
          // Debug token usage (only log first few characters for security)
          console.log(`🔑 Using token for ${config.method?.toUpperCase()} ${config.url}: ${accessToken.substring(0, 20)}...`);
        } else {
          console.warn(`⚠️ No access token available for ${config.method?.toUpperCase()} ${config.url}`);
        }
        return config;
      },
      (error) => {
        return Promise.reject(error);
      }
    );

    // Response interceptor to handle errors and redirects
    instance.interceptors.response.use(
      (response) => {
        return response;
      },
      async (error: AxiosError) => {
        const originalRequest = error.config;

        // Handle 401 Unauthorized
        if (error.response?.status === 401) {
          console.warn('🔒 401 Unauthorized - Token may be expired');

          // Check if session has a critical refresh error (token refresh failed in NextAuth)
          if (hasRefreshError) {
            console.warn('🚨 Session has critical refresh error');
            // Don't automatically sign out - show error to user instead
            return Promise.reject(new Error('Your session has expired. Please refresh the page or log in again.'));
          }

          // If we have a valid session but got 401, the token might be refreshing
          if (isAuthenticated && originalRequest && !originalRequest._retry) {
            console.log('🔄 Token may be expired, letting NextAuth handle refresh on next request');

            // Mark this request as retried to prevent infinite loops
            originalRequest._retry = true;

            try {
              // Force NextAuth to refresh the session
              console.log('🔄 Forcing session refresh...');
              await refreshSession();

              // Wait a moment for the session to update
              await new Promise(resolve => setTimeout(resolve, 500));

              // Get the new token
              const newToken = getAccessToken();
              if (newToken) {
                console.log('✅ Got new token, retrying original request');
                // Update the authorization header with the new token
                originalRequest.headers = originalRequest.headers || {};
                originalRequest.headers.Authorization = `Bearer ${newToken}`;

                // Retry the original request
                return instance(originalRequest);
              } else {
                console.warn('❌ No new token available after refresh');
                return Promise.reject(new Error('Unable to refresh session. Please try again or log in.'));
              }
            } catch (refreshError) {
              console.error('❌ Session refresh failed:', refreshError);
              // Don't automatically sign out - let user handle it
              return Promise.reject(new Error('Session refresh failed. Please refresh the page or log in again.'));
            }
          }

          // No valid session - don't automatically sign out
          console.warn('❌ No valid session available');
          return Promise.reject(new Error('Please log in to continue.'));
        }

        // Handle 403 Forbidden
        if (error.response?.status === 403) {
          console.warn('🚫 403 Forbidden - Insufficient permissions');
          router.push('/unauthorized');
          return Promise.reject(new Error('You do not have permission to access this resource.'));
        }

        // Handle network errors
        if (!error.response) {
          console.error('🌐 Network error occurred');
          return Promise.reject(new Error('Network error. Please check your connection.'));
        }

        return Promise.reject(error);
      }
    );

    return instance;
  }, [baseUrl, isAuthenticated, hasRefreshError, signOutWithCleanup, getAccessToken, refreshSession, router]);

  // Helper function to handle API responses and errors
  const handleApiCall = useCallback(async <R>(apiCall: () => Promise<R>): Promise<R | string | undefined> => {
    setLoading(true);
    setError(null);

    try {
      const result = await apiCall();
      setData(result as unknown as T);
      return result;
    } catch (err) {
      let errorMessage = "Something went wrong";

      if (axios.isAxiosError(err)) {
        if (err.response?.data) {
          const responseData = err.response.data;
          errorMessage =
            responseData.message ||
            responseData.detail ||
            (responseData.email ? responseData.email[0] : null) ||
            JSON.stringify(responseData) ||
            `HTTP ${err.response.status}: ${err.response.statusText}`;
        } else if (err.message) {
          errorMessage = err.message;
        }
      } else if (err instanceof Error) {
        errorMessage = err.message;
      }

      setError(errorMessage);
      return errorMessage;
    } finally {
      setLoading(false);
    }
  }, []);

  // Create (POST)
  const create = useCallback(
    async (endpoint: string, body: unknown): Promise<T | string | undefined> => {
      const axiosInstance = createAxiosInstance();
      return handleApiCall(async () => {
        const response = await axiosInstance.post(endpoint, body);
        return response.data;
      });
    },
    [createAxiosInstance, handleApiCall]
  );

  // Read (GET)
  const read = useCallback(
    async (endpoint: string): Promise<T | string | undefined> => {
      const axiosInstance = createAxiosInstance();
      return handleApiCall(async () => {
        const response = await axiosInstance.get(endpoint);
        return response.data;
      });
    },
    [createAxiosInstance, handleApiCall]
  );

  // Update (PUT)
  const update = useCallback(
    async (endpoint: string, body: unknown): Promise<T | string | undefined> => {
      const axiosInstance = createAxiosInstance();
      return handleApiCall(async () => {
        const response = await axiosInstance.put(endpoint, body);
        return response.data;
      });
    },
    [createAxiosInstance, handleApiCall]
  );

  // Delete (DELETE)
  const remove = useCallback(
    async (endpoint: string): Promise<T | string | undefined> => {
      const axiosInstance = createAxiosInstance();
      return handleApiCall(async () => {
        const response = await axiosInstance.delete(endpoint);
        return response.data;
      });
    },
    [createAxiosInstance, handleApiCall]
  );

  return {
    data,
    loading,
    error,
    create,
    read,
    update,
    remove,
  };
};

export default useApi;
