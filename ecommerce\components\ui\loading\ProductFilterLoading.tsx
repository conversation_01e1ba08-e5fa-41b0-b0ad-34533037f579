import React from "react";

export const ProductFilterLoading = () => {
  return (
    <div id="webcrumbs">
      <div className="w-full bg-white shadow-lg rounded-lg p-4">
        <div className="flex justify-between items-center">
          <div className="h-[20px] w-[100px] bg-neutral-300 rounded-md animate-pulse"></div>
          <div className="h-[20px] w-[20px] bg-neutral-300 rounded-full animate-pulse"></div>
        </div>
        <div className="flex justify-between items-center mt-4 mb-2">
          <div className="h-[20px] w-[20px] bg-neutral-300 rounded-md animate-pulse"></div>
          <div className="h-[20px] w-[40px] bg-neutral-300 rounded-md animate-pulse"></div>
        </div>
        <div className="mb-4">
          <div className="h-[12px] w-full bg-neutral-300 rounded-md animate-pulse"></div>
        </div>
        <div className="h-[20px] w-[100px] bg-neutral-300 rounded-md animate-pulse mb-4"></div>
        <div className="h-[200px] overflow-y-auto">
          <div className="mt-2 space-y-2">
            {Array(6)
              .fill(0)
              .map((_, i) => (
                <div key={i} className="flex items-center space-x-2">
                  <div className="h-[16px] w-[16px] bg-neutral-300 rounded-md animate-pulse"></div>
                  <div className="h-[16px] w-[150px] bg-neutral-300 rounded-md animate-pulse"></div>
                </div>
              ))}
          </div>
        </div>
        <div className="h-[20px] w-[100px] bg-neutral-300 rounded-md animate-pulse mt-4"></div>
        <div className="mt-2 space-y-2">
          {Array(3)
            .fill(0)
            .map((_, i) => (
              <div key={i} className="flex items-center space-x-2">
                <div className="h-[16px] w-[16px] bg-neutral-300 rounded-md animate-pulse"></div>
                <div className="h-[16px] w-[100px] bg-neutral-300 rounded-md animate-pulse"></div>
              </div>
            ))}
        </div>
        <div className="h-[20px] w-[100px] bg-neutral-300 rounded-md animate-pulse mt-4"></div>
        <div className="mt-2 space-y-2">
          {Array(4)
            .fill(0)
            .map((_, i) => (
              <div key={i} className="flex items-center space-x-2">
                <div className="h-[16px] w-[16px] bg-neutral-300 rounded-md animate-pulse"></div>
                <div className="h-[14px] w-[90px] bg-neutral-300 rounded-md animate-pulse"></div>
                <div className="h-[16px] w-[70px] bg-neutral-300 rounded-md animate-pulse"></div>
              </div>
            ))}
        </div>
        <div className="h-[40px] w-full bg-neutral-300 rounded-md mt-4 animate-pulse"></div>
      </div>
    </div>
  );
};
