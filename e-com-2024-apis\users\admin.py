from products.models import SubCategorie, Category, Product, ProductImage, ProductVariant, Review, Brand, GST
from orders.models import Cart, CartItem, Order, OrderItem, ShippingMethod, Payment, Invoice
from users.models import Address, PaymentMethod, Wishlist, Customer, ContactMessage, UserConsent, DataDeletionRequest
from promotions.models import Promotion, PromotionUsage
from django.contrib import admin
from django.utils.html import format_html, mark_safe
from django.db.models import Count, Q
from django.http import HttpResponse
from backend.minio_settings import get_minio_media_url
import csv
from datetime import datetime
from backend.security_monitoring import SecurityEvent, FailedLoginAttempt

admin.site.site_title = "E-Commerce Admin"
admin.site.site_header = "E-Commerce Admin"


class HasProductsFilter(admin.SimpleListFilter):
    title = 'has products'
    parameter_name = 'has_products'

    def lookups(self, request, model_admin):
        return (
            ('yes', 'Has Products'),
            ('no', 'No Products'),
        )

    def queryset(self, request, queryset):
        if self.value() == 'yes':
            return queryset.filter(products__isnull=False, products__is_active=True).distinct()
        if self.value() == 'no':
            return queryset.filter(
                Q(products__isnull=True) |
                ~Q(products__is_active=True)
            ).distinct()
        return queryset


class SubCategoryHasProductsFilter(admin.SimpleListFilter):
    title = 'has products'
    parameter_name = 'has_products'

    def lookups(self, request, model_admin):
        return (
            ('yes', 'Has Products'),
            ('no', 'No Products'),
        )

    def queryset(self, request, queryset):
        if self.value() == 'yes':
            return queryset.filter(products__isnull=False, products__is_active=True).distinct()
        if self.value() == 'no':
            return queryset.filter(
                Q(products__isnull=True) |
                ~Q(products__is_active=True)
            ).distinct()
        return queryset


# admin.site.register(Product)
admin.site.register(ProductImage)
admin.site.register(ProductVariant)
admin.site.register(Review)
admin.site.register(Cart)
admin.site.register(CartItem)
admin.site.register(Order)
admin.site.register(OrderItem)
admin.site.register(ShippingMethod)
admin.site.register(Payment)
admin.site.register(Address)
admin.site.register(PaymentMethod)
admin.site.register(Wishlist)
admin.site.register(Promotion)
admin.site.register(PromotionUsage)
admin.site.register(Brand)
admin.site.register(Customer)

admin.site.register(UserConsent)
admin.site.register(DataDeletionRequest)


class CategoryAdmin(admin.ModelAdmin):
    list_display = ('name', 'slug', 'product_count', 'is_active', 'image_thumbnail', 'created_at', 'updated_at')
    list_filter = ('is_active', 'created_at', HasProductsFilter)
    search_fields = ('name', 'slug', 'description')
    readonly_fields = ('slug', 'created_at', 'updated_at', 'image_preview')
    ordering = ('name',)

    fieldsets = (
        ('Basic Information', {
            'fields': ('name', 'slug', 'description', 'is_active')
        }),
        ('Image', {
            'fields': ('image', 'image_preview'),
            'description': 'Upload a category image. Recommended size: 400x400 pixels.'
        }),
        ('Timestamps', {
            'fields': ('created_at', 'updated_at'),
            'classes': ('collapse',)
        }),
    )

    def image_thumbnail(self, obj):
        """Display a small thumbnail in the list view"""
        if obj.image:
            try:
                # Use get_minio_media_url to get the correct URL
                image_url = get_minio_media_url(obj.image.name)
                return mark_safe(f'<img src="{image_url}" width="50" height="50" style="object-fit: cover; border-radius: 4px;" />')
            except Exception:
                # Fallback to direct image URL if MinIO function fails
                return mark_safe(f'<img src="{obj.image.url}" width="50" height="50" style="object-fit: cover; border-radius: 4px;" />')
        return "No Image"

    image_thumbnail.short_description = 'Image'

    def image_preview(self, obj):
        """Display a larger image preview in the detail view"""
        if obj.image:
            try:
                # Use get_minio_media_url to get the correct URL
                image_url = get_minio_media_url(obj.image.name)
                return mark_safe(f'''
                    <div style="margin: 10px 0;">
                        <img src="{image_url}" width="200" height="200" style="object-fit: cover; border: 1px solid #ddd; border-radius: 8px;" />
                        <p style="margin-top: 10px; font-size: 12px; color: #666;">
                            <strong>Image URL:</strong> {image_url}
                        </p>
                    </div>
                ''')
            except Exception:
                # Fallback to direct image URL if MinIO function fails
                return mark_safe(f'''
                    <div style="margin: 10px 0;">
                        <img src="{obj.image.url}" width="200" height="200" style="object-fit: cover; border: 1px solid #ddd; border-radius: 8px;" />
                        <p style="margin-top: 10px; font-size: 12px; color: #666;">
                            <strong>Image URL:</strong> {obj.image.url}
                        </p>
                    </div>
                ''')
        return "No image uploaded"

    image_preview.short_description = 'Image Preview'

    def product_count(self, obj):
        """Display the number of products in this category"""
        count = obj.products.filter(is_active=True).count()
        if count > 0:
            return format_html(
                '<span style="color: green; font-weight: bold;">{}</span>',
                count
            )
        return format_html('<span style="color: #999;">0</span>')

    product_count.short_description = 'Active Products'
    product_count.admin_order_field = 'products__count'

    def get_queryset(self, request):
        """Optimize queryset with product count annotation"""
        queryset = super().get_queryset(request)
        return queryset.annotate(
            products_count=Count('products', filter=Q(products__is_active=True))
        ).prefetch_related('products')

admin.site.register(Category, CategoryAdmin)


class SubCategoryAdmin(admin.ModelAdmin):
    list_display = ('name', 'category', 'slug', 'product_count', 'is_active', 'image_thumbnail', 'created_at')
    list_filter = ('is_active', 'category', 'created_at', SubCategoryHasProductsFilter)
    search_fields = ('name', 'slug', 'description', 'category__name')
    readonly_fields = ('slug', 'created_at', 'updated_at', 'image_preview')
    ordering = ('category__name', 'name')

    fieldsets = (
        ('Basic Information', {
            'fields': ('name', 'slug', 'description', 'category', 'is_active')
        }),
        ('Image', {
            'fields': ('image', 'image_preview'),
            'description': 'Upload a subcategory image. Recommended size: 400x400 pixels.'
        }),
        ('Timestamps', {
            'fields': ('created_at', 'updated_at'),
            'classes': ('collapse',)
        }),
    )

    def image_thumbnail(self, obj):
        """Display a small thumbnail in the list view"""
        if obj.image:
            try:
                # Use get_minio_media_url to get the correct URL
                image_url = get_minio_media_url(obj.image.name)
                return mark_safe(f'<img src="{image_url}" width="50" height="50" style="object-fit: cover; border-radius: 4px;" />')
            except Exception:
                # Fallback to direct image URL if MinIO function fails
                return mark_safe(f'<img src="{obj.image.url}" width="50" height="50" style="object-fit: cover; border-radius: 4px;" />')
        return "No Image"

    image_thumbnail.short_description = 'Image'

    def image_preview(self, obj):
        """Display a larger image preview in the detail view"""
        if obj.image:
            try:
                # Use get_minio_media_url to get the correct URL
                image_url = get_minio_media_url(obj.image.name)
                return mark_safe(f'''
                    <div style="margin: 10px 0;">
                        <img src="{image_url}" width="200" height="200" style="object-fit: cover; border: 1px solid #ddd; border-radius: 8px;" />
                        <p style="margin-top: 10px; font-size: 12px; color: #666;">
                            <strong>Image URL:</strong> {image_url}
                        </p>
                    </div>
                ''')
            except Exception:
                # Fallback to direct image URL if MinIO function fails
                return mark_safe(f'''
                    <div style="margin: 10px 0;">
                        <img src="{obj.image.url}" width="200" height="200" style="object-fit: cover; border: 1px solid #ddd; border-radius: 8px;" />
                        <p style="margin-top: 10px; font-size: 12px; color: #666;">
                            <strong>Image URL:</strong> {obj.image.url}
                        </p>
                    </div>
                ''')
        return "No image uploaded"

    image_preview.short_description = 'Image Preview'

    def product_count(self, obj):
        """Display the number of products in this subcategory"""
        count = obj.products.filter(is_active=True).count()
        if count > 0:
            return format_html(
                '<span style="color: green; font-weight: bold;">{}</span>',
                count
            )
        return format_html('<span style="color: #999;">0</span>')

    product_count.short_description = 'Active Products'

admin.site.register(SubCategorie, SubCategoryAdmin)


class GSTAdmin(admin.ModelAdmin):
    list_display = ('name', 'rate', 'cgst_rate', 'sgst_rate', 'igst_rate', 'hsn_code', 'is_active', 'created_at')
    list_filter = ('is_active', 'rate')
    search_fields = ('name', 'hsn_code')
    ordering = ('name',)
    fieldsets = (
        ('Basic Information', {
            'fields': ('name', 'hsn_code', 'is_active')
        }),
        ('Tax Rates', {
            'fields': ('rate', 'cgst_rate', 'sgst_rate', 'igst_rate'),
            'description': 'Note: CGST + SGST should equal the total GST rate'
        }),
        ('Timestamps', {
            'fields': ('created_at', 'updated_at'),
            'classes': ('collapse',)
        }),
    )
    readonly_fields = ('created_at', 'updated_at')

admin.site.register(GST, GSTAdmin)


class InvoiceAdmin(admin.ModelAdmin):
    list_display = ('invoice_number', 'order', 'generated_at', 'company_name')
    list_filter = ('generated_at', 'company_name')
    search_fields = ('invoice_number', 'order__id', 'order__user__email')
    readonly_fields = ('invoice_number', 'generated_at', 'order')
    ordering = ('-generated_at',)

    fieldsets = (
        ('Invoice Information', {
            'fields': ('invoice_number', 'order', 'generated_at', 'pdf_file')
        }),
        ('Company Details', {
            'fields': ('company_name', 'company_address', 'company_email', 'company_phone', 'gst_number')
        }),
    )

admin.site.register(Invoice, InvoiceAdmin)


def export_selected_products_csv(modeladmin, request, queryset):
    """
    Export selected products to CSV file
    """
    # Create the HttpResponse object with CSV header
    response = HttpResponse(content_type='text/csv')
    timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
    response['Content-Disposition'] = f'attachment; filename="products_export_{timestamp}.csv"'

    # Create CSV writer
    writer = csv.writer(response)

    # Write CSV header
    writer.writerow([
        'ID',
        'Name',
        'Slug',
        'Description',
        'Category',
        'Subcategory',
        'Brand',
        'Price',
        'Stock',
        'Is Active',
        'Average Rating',
        'Review Count',
        'Total Images',
        'Total Variants',
        'Created At',
        'Updated At',
    ])

    # Write product data
    for product in queryset.select_related('category', 'subcategory', 'brand').prefetch_related('images', 'variants', 'reviews'):
        # Calculate average rating
        reviews = product.reviews.all()
        avg_rating = sum(review.rating for review in reviews) / len(reviews) if reviews else 0

        # Get counts
        review_count = len(reviews)
        image_count = product.images.count()
        variant_count = product.variants.count()

        # Format description (truncate if too long and remove newlines)
        description = product.description or ''
        if len(description) > 200:
            description = description[:200] + '...'
        description = description.replace('\n', ' ').replace('\r', ' ')

        writer.writerow([
            product.id,
            product.name,
            product.slug or '',
            description,
            product.category.name if product.category else '',
            product.subcategory.name if product.subcategory else '',
            product.brand.name if product.brand else '',
            str(product.price),
            product.stock,
            'Yes' if product.is_active else 'No',
            f'{avg_rating:.2f}' if avg_rating > 0 else '0.00',
            review_count,
            image_count,
            variant_count,
            product.created_at.strftime('%Y-%m-%d %H:%M:%S'),
            product.updated_at.strftime('%Y-%m-%d %H:%M:%S'),
        ])

    return response

export_selected_products_csv.short_description = "Export selected products to CSV"


class ContactMessageAdmin(admin.ModelAdmin):
    list_display = ('name', 'email', 'subject', 'created_at', 'is_read')
    list_filter = ('is_read', 'created_at')
    search_fields = ('name', 'email', 'subject', 'message')
    readonly_fields = ('name', 'email', 'subject', 'message', 'created_at')
    date_hierarchy = 'created_at'
    ordering = ('-created_at',)

    def has_add_permission(self, request):
        # Disable the ability to add new contact messages from the admin
        return False

    def has_delete_permission(self, request, obj=None):
        # Allow deletion of contact messages
        return True

    def save_model(self, request, obj, form, change):
        # Mark the message as read when viewed/saved in the admin
        if not obj.is_read:
            obj.is_read = True
        super().save_model(request, obj, form, change)

admin.site.register(ContactMessage, ContactMessageAdmin)


class ProductImageInline(admin.TabularInline):
    model = ProductImage
    extra = 1
    fields = ('image', 'is_primary', 'image_preview')
    readonly_fields = ('image_preview',)

    def image_preview(self, obj):
        if obj.image:
            return mark_safe(f'<img src="{obj.image.url}" width="150" height="auto" />')
        return "No Image"

    image_preview.short_description = 'Image Preview'


class ProductAdmin(admin.ModelAdmin):
    # Add filters for category and brand
    list_filter = ('category', 'brand', 'is_active')

    # Add search functionality for specific fields
    search_fields = ('name', 'description', 'category__name', 'brand__name')

    # Display specific fields in the admin list view
    list_display = ('name', 'category', 'brand', 'price', 'stock', 'is_active', 'created_at', 'updated_at', 'thumbnail')

    # Order by latest updated products by default
    ordering = ('-updated_at',)

    # Add inlines for related models
    inlines = [ProductImageInline]

    # Add custom actions
    actions = [export_selected_products_csv]

    # Define fieldsets for better organization
    fieldsets = (
        ('Basic Information', {
            'fields': ('name', 'slug', 'description', 'price', 'stock', 'is_active')
        }),
        ('Categories', {
            'fields': ('category', 'subcategory', 'brand')
        }),
        ('Tax Information', {
            'fields': ('gst',),
            'description': 'Select GST rate for this product. If not selected, default 18% will be applied.'
        }),
        ('Timestamps', {
            'fields': ('created_at', 'updated_at'),
            'classes': ('collapse',)
        }),
        ('Product Images', {
            'fields': ('product_images',),
        }),
    )

    readonly_fields = ('slug', 'created_at', 'updated_at', 'product_images')

    def thumbnail(self, obj):
        """Display a thumbnail of the primary product image in the list view"""
        primary_image = obj.images.filter(is_primary=True).first()
        if not primary_image:
            primary_image = obj.images.first()

        if primary_image:
            return mark_safe(f'<img src="{primary_image.image.url}" width="50" height="auto" />')
        return "No Image"

    thumbnail.short_description = 'Thumbnail'

    def product_images(self, obj):
        """Display all product images in the detail view"""
        images = obj.images.all()
        if not images:
            return "No images available"

        html = '<div style="display: flex; flex-wrap: wrap; gap: 10px;">'
        for img in images:
            primary_badge = '<span style="position: absolute; background-color: #007bff; color: white; padding: 2px 5px; border-radius: 3px;">Primary</span>' if img.is_primary else ''
            html += f'''
            <div style="position: relative; margin-bottom: 15px;">
                {primary_badge}
                <img src="{img.image.url}" width="200" height="auto" style="object-fit: contain; border: 1px solid #ddd; padding: 5px;" />
                <p style="margin-top: 5px; font-size: 12px;">{"Primary" if img.is_primary else ""}</p>
            </div>
            '''
        html += '</div>'
        return mark_safe(html)

    product_images.short_description = 'Product Images'

# Register the Product model with the customized admin class
admin.site.register(Product, ProductAdmin)


admin.site.register(SecurityEvent)
admin.site.register(FailedLoginAttempt)
