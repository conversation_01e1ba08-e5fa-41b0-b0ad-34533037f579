"use client"
import { useState, useRef } from "react";
import { Plus } from "lucide-react";
import { Button } from "@/components/ui/button";
import { CategoryList, CategoryListRef } from "@/components/categories/CategoryList";
import { CategoryDialog } from "@/components/categories/CategoryDialog";
import { useToast } from "@/components/ui/use-toast";
import useApi from "@/hooks/useApi";
import { CATEGORY_CREATE, CATEGORY_UPDATE, MAIN_URL } from "@/constant/urls";

// TypeScript interface for category data matching API response
interface Category {
  id: number;
  name: string;
  description?: string;
  display_order: number;
  product_count?: number;
}

interface CategoryFormData {
  name: string;
  description?: string;
  displayOrder: string;
}

const Categories = () => {
  const [isDialogOpen, setIsDialogOpen] = useState(false);
  const [editingCategory, setEditingCategory] = useState<Category | null>(null);
  const { toast } = useToast();
  const { create, update, loading } = useApi(MAIN_URL);
  const categoryListRef = useRef<CategoryListRef>(null);

  const handleEdit = (category: Category) => {
    setEditingCategory(category);
    setIsDialogOpen(true);
  };

  const handleClose = () => {
    setEditingCategory(null);
    setIsDialogOpen(false);
  };

  const handleSave = async (categoryData: CategoryFormData) => {
    try {
      // Prepare the data for API call
      const apiData = {
        name: categoryData.name,
        description: categoryData.description || "",
        // Note: display_order is auto-generated by the backend (uses ID)
      };

      let result;
      if (editingCategory) {
        // Update existing category
        result = await update(CATEGORY_UPDATE(editingCategory.id), apiData);
      } else {
        // Create new category
        result = await create(CATEGORY_CREATE, apiData);
      }

      // Check if the result is an error string
      if (typeof result === 'string') {
        throw new Error(result);
      }

      toast({
        title: `Category ${editingCategory ? "updated" : "created"} successfully`,
        description: categoryData.name,
      });

      handleClose();

      // Refresh the category list
      if (categoryListRef.current) {
        categoryListRef.current.refreshCategories();
      }
    } catch (error) {
      console.error('Error saving category:', error);
      toast({
        variant: "destructive",
        title: "Error",
        description: error instanceof Error ? error.message : "Failed to save category",
      });
    }
  };

  return (
    <div className="space-y-6">
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-3xl font-bold tracking-tight">Categories</h1>
          <p className="text-muted-foreground">
            Manage your product categories and assignments
          </p>
        </div>
        <Button onClick={() => setIsDialogOpen(true)} disabled={loading}>
          <Plus className="mr-2 h-4 w-4" />
          Add Category
        </Button>
      </div>

      <CategoryList ref={categoryListRef} onEdit={handleEdit} />

      <CategoryDialog
        open={isDialogOpen}
        onOpenChange={setIsDialogOpen}
        category={editingCategory}
        onClose={handleClose}
        onSave={handleSave}
        loading={loading}
      />
    </div>
  );
};

export default Categories;