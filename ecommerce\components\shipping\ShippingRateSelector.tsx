/**
 * Shipping Rate Selector Component for displaying and selecting shipping options
 */

import React from 'react';
import { Card, CardContent } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Alert, AlertDescription } from '@/components/ui/alert';
import { Truck, Clock, Zap, AlertTriangle, CheckCircle } from 'lucide-react';
import { cn } from '@/lib/utils';
import type { ShippingRateSelectorProps, CourierRate } from '@/types/shipping';

export const ShippingRateSelector: React.FC<ShippingRateSelectorProps> = ({
  rates,
  selectedRate,
  onRateSelect,
  loading = false,
  error = null,
  source = 'fallback',
  className
}) => {
  const formatCurrency = (amount: number) => {
    return new Intl.NumberFormat('en-IN', {
      style: 'currency',
      currency: 'INR',
      minimumFractionDigits: 0,
      maximumFractionDigits: 2
    }).format(amount);
  };

  const formatDeliveryTime = (days?: number) => {
    if (!days) return 'Standard delivery';
    if (days === 1) return '1 business day';
    return `${days} business days`;
  };

  const getRateTypeIcon = (rate: CourierRate) => {
    if (rate.is_existing_method) {
      return <Truck className="h-4 w-4 text-blue-500" />;
    }
    return <Zap className="h-4 w-4 text-green-500" />;
  };

  const getRateTypeBadge = (rate: CourierRate) => {
    if (rate.is_existing_method) {
      return (
        <Badge variant="secondary" className="text-xs">
          Standard
        </Badge>
      );
    }
    return (
      <Badge variant="default" className="text-xs bg-green-100 text-green-700 border-green-200">
        <Zap className="h-3 w-3 mr-1" />
        Live Rate
      </Badge>
    );
  };

  const getSourceMessage = () => {
    if (source === 'rapidshyp') {
      return {
        icon: <CheckCircle className="h-4 w-4 text-green-500" />,
        message: 'Live shipping rates from multiple couriers',
        variant: 'default' as const,
        className: 'border-green-200 bg-green-50'
      };
    }
    return {
      icon: <AlertTriangle className="h-4 w-4 text-orange-500" />,
      message: 'Using standard shipping rates. Live rates temporarily unavailable.',
      variant: 'default' as const,
      className: 'border-orange-200 bg-orange-50'
    };
  };

  if (loading) {
    return (
      <div className={cn("space-y-4", className)}>
        <div className="flex items-center justify-center py-8">
          <div className="animate-spin rounded-full h-6 w-6 border-b-2 border-primary"></div>
          <span className="ml-2 text-sm text-gray-600">Calculating shipping rates...</span>
        </div>
      </div>
    );
  }

  if (error) {
    return (
      <div className={cn("space-y-4", className)}>
        <Alert variant="destructive">
          <AlertTriangle className="h-4 w-4" />
          <AlertDescription>{error}</AlertDescription>
        </Alert>
      </div>
    );
  }

  if (!rates || rates.length === 0) {
    return (
      <div className={cn("space-y-4", className)}>
        <Alert>
          <AlertTriangle className="h-4 w-4" />
          <AlertDescription>
            No shipping options available. Please check your delivery pincode.
          </AlertDescription>
        </Alert>
      </div>
    );
  }

  const sourceInfo = getSourceMessage();

  return (
    <div className={cn("space-y-4", className)}>
      {/* Source information */}
      <Alert variant={sourceInfo.variant} className={sourceInfo.className}>
        <div className="flex items-center">
          {sourceInfo.icon}
          <AlertDescription className="ml-2">
            {sourceInfo.message}
          </AlertDescription>
        </div>
      </Alert>

      {/* Shipping rates */}
      <div className="space-y-3">
        <h3 className="text-sm font-medium text-gray-900">
          Choose Shipping Method ({rates.length} option{rates.length !== 1 ? 's' : ''})
        </h3>
        
        <div className="grid gap-3">
          {rates.map((rate, index) => (
            <Card
              key={`${rate.courier_code}-${index}`}
              className={cn(
                "cursor-pointer transition-all duration-200 hover:shadow-md",
                selectedRate?.courier_code === rate.courier_code
                  ? "ring-2 ring-primary border-primary bg-primary/5"
                  : "border-gray-200 hover:border-gray-300"
              )}
              onClick={() => onRateSelect(rate)}
            >
              <CardContent className="p-4">
                <div className="flex items-center justify-between">
                  <div className="flex items-center space-x-3 flex-1">
                    {getRateTypeIcon(rate)}
                    
                    <div className="flex-1">
                      <div className="flex items-center gap-2 mb-1">
                        <h4 className="font-medium text-sm text-gray-900">
                          {rate.courier_name}
                        </h4>
                        {getRateTypeBadge(rate)}
                      </div>
                      
                      <div className="flex items-center text-xs text-gray-500 space-x-4">
                        <div className="flex items-center">
                          <Clock className="h-3 w-3 mr-1" />
                          {formatDeliveryTime(rate.estimated_days)}
                        </div>
                        
                        {rate.freight_mode && (
                          <div className="flex items-center">
                            <span className="capitalize">{rate.freight_mode}</span>
                          </div>
                        )}
                        
                        {rate.cutoff_time && (
                          <div className="flex items-center">
                            <span>Order by {rate.cutoff_time}</span>
                          </div>
                        )}
                      </div>
                      
                      {rate.description && (
                        <p className="text-xs text-gray-500 mt-1">
                          {rate.description}
                        </p>
                      )}
                    </div>
                  </div>
                  
                  <div className="text-right ml-4">
                    <div className="font-semibold text-lg text-gray-900">
                      {formatCurrency(rate.total_freight)}
                    </div>
                    
                    {rate.parent_courier_name && rate.parent_courier_name !== rate.courier_name && (
                      <div className="text-xs text-gray-500">
                        via {rate.parent_courier_name}
                      </div>
                    )}
                  </div>
                </div>
                
                {/* Selection indicator */}
                {selectedRate?.courier_code === rate.courier_code && (
                  <div className="mt-3 pt-3 border-t border-primary/20">
                    <div className="flex items-center text-xs text-primary">
                      <CheckCircle className="h-3 w-3 mr-1" />
                      Selected shipping method
                    </div>
                  </div>
                )}
              </CardContent>
            </Card>
          ))}
        </div>
      </div>
      
      {/* Summary */}
      {selectedRate && (
        <div className="mt-4 p-3 bg-gray-50 rounded-lg">
          <div className="flex justify-between items-center text-sm">
            <span className="text-gray-600">Selected shipping:</span>
            <span className="font-medium">
              {selectedRate.courier_name} - {formatCurrency(selectedRate.total_freight)}
            </span>
          </div>
          
          {selectedRate.estimated_days && (
            <div className="flex justify-between items-center text-xs text-gray-500 mt-1">
              <span>Estimated delivery:</span>
              <span>{formatDeliveryTime(selectedRate.estimated_days)}</span>
            </div>
          )}
        </div>
      )}
    </div>
  );
};

export default ShippingRateSelector;
