import { View, Text, StyleSheet, TouchableOpacity, ScrollView } from "react-native";
import { SafeAreaView } from "react-native-safe-area-context";
import { MaterialIcons } from "@expo/vector-icons";
import { NavigationProp } from "@react-navigation/native";
import { useAuth } from "../context/AuthContext";

export default function ProfileScreen({ navigation }: { navigation: NavigationProp }) {
  const { user, isAuthenticated, logout } = useAuth();

  return (
    <SafeAreaView style={styles.container}>
      <ScrollView>
        <View style={styles.header}>
          <Text style={styles.title}>Profile</Text>
        </View>

        <View style={styles.profileInfo}>
          <View style={styles.avatar}>
            <MaterialIcons name="person" size={40} color="#9ca3af" />
          </View>
          <Text style={styles.name}>{isAuthenticated ? user?.name : "Guest User"}</Text>

          {!isAuthenticated ? (
            <TouchableOpacity onPress={() => navigation.navigate("Auth", { returnScreen: "Profile" })} style={styles.loginButton}>
              <Text style={styles.loginButtonText}>Login / Register</Text>
            </TouchableOpacity>
          ) : (
            <TouchableOpacity onPress={logout} style={styles.logoutButton}>
              <Text style={styles.logoutButtonText}>Logout</Text>
            </TouchableOpacity>
          )}
        </View>

        <View style={styles.section}>
          <TouchableOpacity onPress={() => navigation.navigate("Orders")} style={styles.menuItem}>
            <MaterialIcons name="shopping-bag" size={24} color="black" />
            <Text style={styles.menuText}>My Orders</Text>
          </TouchableOpacity>

          <TouchableOpacity style={styles.menuItem} onPress={() => navigation.navigate("ShippingAddress")}>
            <MaterialIcons name="location-on" size={24} color="black" />
            <Text style={styles.menuText}>Shipping Addresses</Text>
          </TouchableOpacity>

          <TouchableOpacity style={styles.menuItem}>
            <MaterialIcons name="credit-card" size={24} color="black" />
            <Text style={styles.menuText}>Payment Methods</Text>
          </TouchableOpacity>
        </View>
      </ScrollView>
    </SafeAreaView>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#ffffff',
  },
  header: {
    padding: 16,
  },
  title: {
    fontSize: 24,
    fontWeight: 'bold',
  },
  profileInfo: {
    alignItems: 'center',
    padding: 20,
  },
  avatar: {
    width: 80,
    height: 80,
    borderRadius: 40,
    backgroundColor: '#f3f4f6',
    justifyContent: 'center',
    alignItems: 'center',
  },
  name: {
    fontSize: 18,
    fontWeight: '600',
    marginTop: 12,
  },
  loginButton: {
    marginTop: 12,
    padding: 12,
    backgroundColor: '#2563EB',
    borderRadius: 8,
  },
  loginButtonText: {
    color: 'white',
    fontWeight: '600',
  },
  section: {
    marginTop: 20,
  },
  menuItem: {
    flexDirection: 'row',
    alignItems: 'center',
    padding: 16,
    borderBottomWidth: 1,
    borderBottomColor: '#f3f4f6',
  },
  menuText: {
    flex: 1,
    marginLeft: 12,
    fontSize: 16,
  },
});