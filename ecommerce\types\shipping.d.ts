/**
 * TypeScript definitions for Rapidshyp shipping integration
 */

export interface CourierRate {
  courier_code: string;
  courier_name: string;
  parent_courier_name?: string;
  total_freight: number;
  estimated_days?: number;
  freight_mode?: string;
  cutoff_time?: string;
  max_weight?: number;
  min_weight?: number;
  is_existing_method?: boolean;
  description?: string;
}

export interface ShippingRateRequest {
  pickup_pincode?: string;
  delivery_pincode: string;
  weight?: number;
  cod?: boolean;
  total_value?: number;
}

export interface ShippingRateResponse {
  success: boolean;
  rapidshyp_rates: CourierRate[];
  existing_methods: CourierRate[];
  source: 'rapidshyp' | 'fallback';
  message: string;
  minimum_rate?: CourierRate;
  pickup_pincode: string;
  delivery_pincode: string;
  weight: number;
  cod: boolean;
  total_value: number;
}

export interface TrackingEvent {
  status: string;
  status_description: string;
  location?: string;
  remarks?: string;
  event_timestamp: string;
  created_at: string;
}

export interface OrderTrackingResponse {
  success: boolean;
  tracking_available: boolean;
  source: 'rapidshyp' | 'rapidshyp_basic' | 'standard';
  
  // Rapidshyp tracking data
  shipment_id?: string;
  rapidshyp_order_id?: string;
  awb_number?: string;
  courier_name?: string;
  current_status?: string;
  status_description?: string;
  tracking_url?: string;
  expected_delivery_date?: string;
  actual_delivery_date?: string;
  tracking_events?: TrackingEvent[];
  
  // Standard tracking data
  tracking_number?: string;
  order_status?: string;
  
  // Error information
  error?: string;
}

export interface PincodeValidationRequest {
  pincode: string;
}

export interface PincodeValidationResponse {
  pincode: string;
  is_valid: boolean;
  is_serviceable: boolean;
  message: string;
}

export interface ServiceStatus {
  rapidshyp_enabled: boolean;
  rapidshyp_available: boolean;
  rapidshyp_api_key_configured: boolean;
  fallback_available: boolean;
  existing_methods_count: number;
  service_mode: 'rapidshyp' | 'fallback';
}

export interface BulkTrackingRequest {
  order_ids: string[];
}

export interface BulkTrackingResponse {
  success: boolean;
  tracking_data: Record<string, any>;
  errors: Record<string, string>;
  processed_count: number;
  error_count: number;
}

// Enhanced shipping method interface that extends existing
export interface EnhancedShippingMethod {
  id: string;
  name: string;
  price: number;
  description?: string;
  estimated_days?: number;
  
  // Rapidshyp specific fields
  courier_code?: string;
  courier_name?: string;
  parent_courier_name?: string;
  freight_mode?: string;
  cutoff_time?: string;
  is_rapidshyp?: boolean;
  is_existing_method?: boolean;
  
  // UI state
  selected?: boolean;
  loading?: boolean;
}

// Shipping calculator state
export interface ShippingCalculatorState {
  deliveryPincode: string;
  isPincodeValid: boolean;
  isPincodeServiceable: boolean;
  rates: CourierRate[];
  selectedRate?: CourierRate;
  loading: boolean;
  error: string | null;
  source: 'rapidshyp' | 'fallback' | null;
  minimumRate?: CourierRate;
}

// Pincode validation state
export interface PincodeValidationState {
  pincode: string;
  isValid: boolean;
  isServiceable: boolean;
  isValidating: boolean;
  message: string;
  error: string | null;
}

// Order tracking state
export interface OrderTrackingState {
  orderId: string;
  trackingData?: OrderTrackingResponse;
  loading: boolean;
  error: string | null;
  lastUpdated?: Date;
}

// Shipping service configuration
export interface ShippingConfig {
  enableRapidshyp: boolean;
  enableFallback: boolean;
  defaultPickupPincode: string;
  cacheTimeout: number;
  retryAttempts: number;
  apiTimeout: number;
}

// API error response
export interface ShippingApiError {
  success: false;
  error: string;
  message?: string;
  errors?: Record<string, string[]>;
}

// Generic API response wrapper
export type ShippingApiResponse<T> = T | ShippingApiError;

// Hook return types
export interface UseShippingRatesReturn {
  rates: CourierRate[];
  loading: boolean;
  error: string | null;
  source: 'rapidshyp' | 'fallback' | null;
  minimumRate?: CourierRate;
  calculateRates: (request: ShippingRateRequest) => Promise<void>;
  clearRates: () => void;
}

export interface UsePincodeValidationReturn {
  isValid: boolean;
  isServiceable: boolean;
  isValidating: boolean;
  message: string;
  error: string | null;
  validatePincode: (pincode: string) => Promise<void>;
  clearValidation: () => void;
}

export interface UseOrderTrackingReturn {
  trackingData?: OrderTrackingResponse;
  loading: boolean;
  error: string | null;
  trackOrder: (orderId: string) => Promise<void>;
  clearTracking: () => void;
}

// Component prop types
export interface RapidshypRateCalculatorProps {
  deliveryPincode: string;
  orderWeight?: number;
  isCOD?: boolean;
  orderTotal?: number;
  onRateSelect: (rate: CourierRate) => void;
  selectedRate?: CourierRate;
  className?: string;
}

export interface PincodeValidatorProps {
  value: string;
  onChange: (pincode: string) => void;
  onValidationChange: (isValid: boolean, isServiceable: boolean) => void;
  label?: string;
  placeholder?: string;
  className?: string;
}

export interface ShippingRateSelectorProps {
  rates: CourierRate[];
  selectedRate?: CourierRate;
  onRateSelect: (rate: CourierRate) => void;
  loading?: boolean;
  error?: string | null;
  source?: 'rapidshyp' | 'fallback';
  className?: string;
}

export interface OrderTrackingInterfaceProps {
  orderId: string;
  className?: string;
  autoRefresh?: boolean;
  refreshInterval?: number;
}
