import { useState, useCallback, useEffect } from 'react';
import { useSession } from 'next-auth/react';
import useApi from './useApi';
import { MAIN_URL, CUSTOMERS_LIST } from '@/constant/urls';
import { Customer, CustomersListResponse } from '@/types/customer';

interface UseCustomersReturn {
  customers: Customer[];
  loading: boolean;
  error: string | null;
  fetchCustomers: () => Promise<void>;
  searchCustomers: (query: string, status?: string) => Customer[];
}



export const useCustomers = (): UseCustomersReturn => {
  const [customers, setCustomers] = useState<Customer[]>([]);
  const [isInitialized, setIsInitialized] = useState(false);
  const { data: session, status } = useSession();
  const { read, loading, error } = useApi<CustomersListResponse>(MAIN_URL);

  const fetchCustomers = useCallback(async () => {
    // Only fetch if user is authenticated
    if (status !== 'authenticated' || !session?.user) {
      console.log('User not authenticated, skipping customer fetch');
      return;
    }

    console.log('🚀 fetchCustomers called for authenticated user');
    console.log('Making API call to:', CUSTOMERS_LIST);

    try {
      const result = await read(CUSTOMERS_LIST);

      console.log('Raw API Response:', result);

      if (typeof result === 'string') {
        console.error('API returned error string:', result);
        // Don't throw error immediately, let the component handle it
        return;
      }

      if (result) {
        let customersData: Customer[] = [];

        // Handle the specific CustomerListView response structure
        if (
          result &&
          typeof result === 'object' &&
          result !== null &&
          'results' in result &&
          result.results &&
          typeof result.results === 'object' &&
          !Array.isArray(result.results) &&
          'users' in (result.results as unknown as Record<string, unknown>) &&
          Array.isArray((result.results as unknown as Record<string, unknown>).users)
        ) {
          console.log('Found paginated users array');
          customersData = (result.results as unknown as { users: Customer[] }).users;
        } else if (
          result &&
          typeof result === 'object' &&
          result !== null &&
          'users' in (result as unknown as Record<string, unknown>) &&
          Array.isArray((result as unknown as Record<string, unknown>).users)
        ) {
          console.log('Found direct users array');
          customersData = (result as unknown as { users: Customer[] }).users;
        } else if (Array.isArray(result)) {
          console.log('Found direct array response');
          customersData = result;
        } else if (
          result &&
          typeof result === 'object' &&
          result !== null &&
          'results' in (result as unknown as Record<string, unknown>) &&
          Array.isArray((result as unknown as Record<string, unknown>).results)
        ) {
          console.log('Found standard paginated results');
          customersData = (result as unknown as { results: Customer[] }).results;
        } else if (
          result &&
          typeof result === 'object' &&
          result !== null &&
          'data' in (result as unknown as Record<string, unknown>) &&
          Array.isArray((result as unknown as Record<string, unknown>).data)
        ) {
          console.log('Found data wrapped array');
          customersData = (result as unknown as { data: Customer[] }).data;
        } else {
          console.warn('Unexpected API response structure:', result);
          console.warn('Available keys:', Object.keys(result));
          return;
        }

        console.log('Customers data found:', customersData.length, 'items');

        // Transform backend data to match frontend expectations
        const transformedCustomers = customersData.map((customer: unknown) => {
          if (typeof customer !== 'object' || customer === null) return {} as Customer;
          const c = customer as Record<string, unknown>;
          return {
            ...c,
            status: c.is_active ? 'active' : 'inactive',
            totalOrders: c.total_orders || c.order_count || 0,
            totalSpent: c.total_spent || 0,
            lastPurchase: c.last_purchase || c.last_order_date || c.last_login,
          } as Customer;
        });

        console.log('Transformed customers:', transformedCustomers);
        setCustomers(transformedCustomers);
        setIsInitialized(true);
      }
    } catch (err) {
      console.error('Error fetching customers:', err);
    }
  }, [read, status, session]);

  const searchCustomers = useCallback((query: string, statusFilter?: string): Customer[] => {
    return customers.filter((customer) => {
      const matchesSearch =
        customer.name.toLowerCase().includes(query.toLowerCase()) ||
        customer.email.toLowerCase().includes(query.toLowerCase());

      const matchesStatus =
        !statusFilter || statusFilter === 'all' || customer.status === statusFilter;

      return matchesSearch && matchesStatus;
    });
  }, [customers]);

  // Fetch customers when session becomes authenticated
  useEffect(() => {
    if (status === 'authenticated' && session?.user && !isInitialized) {
      console.log('Session authenticated, fetching customers');
      fetchCustomers();
    }
  }, [status, session?.user, fetchCustomers, isInitialized]); // Include fetchCustomers but it's stable due to useCallback

  return {
    customers,
    loading,
    error,
    fetchCustomers,
    searchCustomers,
  };
};

// For now, we'll create a simple function to get customer by ID from the list
export const useCustomerById = (customerId: string | undefined, customers: Customer[]) => {
  return customers.find(customer => Number(customer.id) === Number(customerId)) || null;
};
