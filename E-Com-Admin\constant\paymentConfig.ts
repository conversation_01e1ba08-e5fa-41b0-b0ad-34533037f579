// Payment Configuration Constants
// This file centralizes all payment-related configuration to avoid hardcoding

// Payment Status Constants - Must match backend PAYMENT_STATUS_CHOICES
export const PAYMENT_STATUS = {
  PENDING: 'PENDING',
  PROCESSING: 'PROCESSING', 
  COMPLETED: 'COMPLETED',
  PAID: 'PAID',
  SUCCESS: 'SUCCESS',
  FAILED: 'FAILED',
  CANCELLED: 'CANCELLED',
  REFUNDED: 'REFUNDED',
} as const;

export const PAYMENT_STATUS_CHOICES = [
  { value: PAYMENT_STATUS.PENDING, label: 'Pending' },
  { value: PAYMENT_STATUS.PROCESSING, label: 'Processing' },
  { value: PAYMENT_STATUS.COMPLETED, label: 'Completed' },
  { value: PAYMENT_STATUS.PAID, label: 'Paid' },
  { value: PAYMENT_STATUS.SUCCESS, label: 'Success' },
  { value: PAYMENT_STATUS.FAILED, label: 'Failed' },
  { value: PAYMENT_STATUS.CANCELLED, label: 'Cancelled' },
  { value: PAYMENT_STATUS.REFUNDED, label: 'Refunded' },
] as const;

export type PaymentStatusType = typeof PAYMENT_STATUS[keyof typeof PAYMENT_STATUS];

// Payment Method Constants
export const PAYMENT_METHODS = {
  CARD: 'CARD',
  STRIPE: 'STRIPE',
  PHONEPE: 'PHONEPE',
  COD: 'COD',
  UNKNOWN: 'UNKNOWN',
} as const;

export const PAYMENT_METHOD_CHOICES = [
  { value: PAYMENT_METHODS.CARD, label: 'Credit/Debit Card' },
  { value: PAYMENT_METHODS.STRIPE, label: 'Stripe' },
  { value: PAYMENT_METHODS.PHONEPE, label: 'PhonePe' },
  { value: PAYMENT_METHODS.COD, label: 'Cash on Delivery' },
  { value: PAYMENT_METHODS.UNKNOWN, label: 'Unknown' },
] as const;

export type PaymentMethodType = typeof PAYMENT_METHODS[keyof typeof PAYMENT_METHODS];

// Payment Status Colors Configuration
export const PAYMENT_STATUS_COLORS = {
  [PAYMENT_STATUS.PENDING]: 'bg-yellow-100 text-yellow-800 border-yellow-200',
  [PAYMENT_STATUS.PROCESSING]: 'bg-blue-100 text-blue-800 border-blue-200',
  [PAYMENT_STATUS.COMPLETED]: 'bg-green-100 text-green-800 border-green-200',
  [PAYMENT_STATUS.PAID]: 'bg-green-100 text-green-800 border-green-200',
  [PAYMENT_STATUS.SUCCESS]: 'bg-green-100 text-green-800 border-green-200',
  [PAYMENT_STATUS.FAILED]: 'bg-red-100 text-red-800 border-red-200',
  [PAYMENT_STATUS.CANCELLED]: 'bg-red-100 text-red-800 border-red-200',
  [PAYMENT_STATUS.REFUNDED]: 'bg-purple-100 text-purple-800 border-purple-200',
} as const;

// Chart Colors Configuration
export const CHART_COLORS = {
  SUCCESS: '#10B981',
  PENDING: '#F59E0B', 
  FAILED: '#EF4444',
  PRIMARY: '#3B82F6',
  SECONDARY: '#8B5CF6',
  ACCENT: '#06B6D4',
} as const;

// Payment Dashboard Configuration
export const PAYMENT_CONFIG = {
  // API Configuration
  DEFAULT_PAGE_SIZE: parseInt(process.env.NEXT_PUBLIC_PAYMENT_PAGE_SIZE || '50'),
  DEFAULT_TABLE_LIMIT: parseInt(process.env.NEXT_PUBLIC_PAYMENT_TABLE_LIMIT || '10'),
  RECENT_TRANSACTIONS_LIMIT: parseInt(process.env.NEXT_PUBLIC_RECENT_TRANSACTIONS_LIMIT || '7'),
  
  // Refresh Configuration
  AUTO_REFRESH_INTERVAL: parseInt(process.env.NEXT_PUBLIC_PAYMENT_REFRESH_INTERVAL || '30000'), // 30 seconds
  
  // Currency Configuration
  CURRENCY_SYMBOL: process.env.NEXT_PUBLIC_CURRENCY_SYMBOL || '$',
  CURRENCY_CODE: process.env.NEXT_PUBLIC_CURRENCY_CODE || 'USD',
  
  // Display Configuration
  DECIMAL_PLACES: parseInt(process.env.NEXT_PUBLIC_DECIMAL_PLACES || '2'),
  
  // Filter Configuration
  DEFAULT_FILTER: 'all',
  AVAILABLE_FILTERS: ['all', 'paid', 'pending', 'failed', 'completed', 'success'] as const,
} as const;

// Utility Functions
export function getPaymentStatusColor(status: string): string {
  const normalizedStatus = status.toUpperCase() as keyof typeof PAYMENT_STATUS_COLORS;
  return PAYMENT_STATUS_COLORS[normalizedStatus] || 'bg-gray-100 text-gray-800 border-gray-200';
}

export function getPaymentMethodLabel(method: string): string {
  const normalizedMethod = method.toUpperCase() as keyof typeof PAYMENT_METHODS;
  const methodChoice = PAYMENT_METHOD_CHOICES.find(choice => choice.value === normalizedMethod);
  return methodChoice?.label || 'Unknown';
}

export function formatCurrency(amount: number): string {
  return `${PAYMENT_CONFIG.CURRENCY_SYMBOL}${Number(amount).toFixed(PAYMENT_CONFIG.DECIMAL_PLACES)}`;
}

export function isSuccessfulPayment(status: string): boolean {
  const successStatuses: string[] = [PAYMENT_STATUS.PAID, PAYMENT_STATUS.COMPLETED, PAYMENT_STATUS.SUCCESS];
  return successStatuses.includes(status.toUpperCase());
}

export function isPendingPayment(status: string): boolean {
  return status.toUpperCase() === PAYMENT_STATUS.PENDING;
}

export function isFailedPayment(status: string): boolean {
  const failedStatuses: string[] = [PAYMENT_STATUS.FAILED, PAYMENT_STATUS.CANCELLED];
  return failedStatuses.includes(status);
}
