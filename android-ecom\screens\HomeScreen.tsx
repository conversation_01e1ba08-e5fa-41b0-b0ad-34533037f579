import { useEffect, useState } from "react";
import {
  View,
  StyleSheet,
  FlatList,
  Image,
  ActivityIndicator,
  Text,
} from "react-native";
import { SafeAreaView } from "react-native-safe-area-context";
import SearchBar from "../components/components/SearchBar";
import CategoryFilter from "../components/components/CategoryFilter";
import ProductCard from "../components/components/ProductCard";
import axios from "axios";
import { BaseURL } from "@/constants/ApiEndpoint";
import { useAuth } from "@/context/AuthContext";
import { addToCart } from "@/hooks/API";

// Mock data
const PRODUCTS = [
  {
    id: "1",
    name: "Wireless Noise Cancelling Headphones",
    price: 299.99,
    image:
      "https://api.a0.dev/assets/image?text=premium%20wireless%20headphones%20product%20photo&aspect=1:1",
    rating: 4.5,
  },
  {
    id: "2",
    name: "Smart Watch Series 7",
    price: 399.99,
    image:
      "https://api.a0.dev/assets/image?text=modern%20smartwatch%20product%20photo&aspect=1:1",
    rating: 4.8,
  },
  {
    id: "3",
    name: "Premium Laptop Stand",
    price: 59.99,
    image:
      "https://api.a0.dev/assets/image?text=sleek%20laptop%20stand%20product%20photo&aspect=1:1",
    rating: 4.3,
  },
  {
    id: "4",
    name: "Wireless Charging Pad",
    price: 39.99,
    image:
      "https://api.a0.dev/assets/image?text=minimalist%20wireless%20charger%20product%20photo&aspect=1:1",
    rating: 4.6,
  },
];

const BANNERS = [
  "https://api.a0.dev/assets/image?text=modern%20ecommerce%20banner%20with%20products&aspect=16:9",
  "https://api.a0.dev/assets/image?text=special%20sale%20ecommerce%20banner&aspect=16:9",
];

export default function HomeScreen({ navigation }) {
  const [searchQuery, setSearchQuery] = useState("");
  const [selectedCategory, setSelectedCategory] = useState("All");
  const [product, setProduct] = useState([]);
  const [isNext, setIsNext] = useState<boolean>(false);
  const [page, setPage] = useState(1);
  const [loading, setLoading] = useState<boolean>(false);
  const { accessToken, isAuthenticated } = useAuth();
  const renderBanner = () => (
    <View style={styles.bannerContainer}>
      <Image source={{ uri: BANNERS[0] }} style={styles.banner} />
    </View>
  );

  const fetchProductDetails = async () => {
    setLoading(true);
    try {
      const response = await axios.get(
        `${BaseURL}/api/v1/products/?page=${page}`
      );
      if (response.data.count > 0) {
        setProduct((prev) => {
          if (prev) {
            return [...prev, ...response.data.results];
          }
          return [];
        });

        setIsNext(response.data.next ? true : false);
      }
      // setProduct();
    } catch (error) {
      console.log("error fetching product", error);
    } finally {
      setLoading(false);
    }
  };

  const handleNext = () => {
    if (isNext) {
      setPage(page + 1);
    }
  };

  useEffect(() => {
    fetchProductDetails();
  }, [page]);
  const handleAddToCart = async (
    productId: any,
    quantity: number,
    accessToken: string
  ) => {
    if (!isAuthenticated) {
      navigation.navigate("Auth", {
        returnTo: "Cart",
      });
      return;
    }
    try {
      const response = await addToCart(productId, quantity, accessToken);
      return response;
    } catch (error) {
      console.log("Error adding to cart", error);
    }
  };

  const renderFooter = () => {
    return loading ? (
      <View
        style={{
          padding: 10,
          justifyContent: "center",
          alignItems: "center",
        }}
      >
        <ActivityIndicator size="large" color="#0000ff" />
        <Text style={{ marginTop: 5 }}>Loading more...</Text>
      </View>
    ) : null;
  };
  const handleSearch = () => {
    navigation.navigate("SearchResults", { searchQuery });
  };
  return (
    <SafeAreaView style={styles.container}>
      <View style={styles.header}>
        <SearchBar
          value={searchQuery}
          onChangeText={setSearchQuery}
          onSubmit={handleSearch}
        />
        <CategoryFilter
          selectedCategory={selectedCategory}
          onSelectCategory={setSelectedCategory}
          navigation={navigation}
        />
      </View>
      <FlatList
        ListHeaderComponent={renderBanner}
        data={product}
        renderItem={({ item }) => (
          <ProductCard
            {...item}
            onPress={() =>
              navigation.navigate("ProductDetails", { product: item })
            }
            onAddToCart={() => handleAddToCart(item?.id, 1, accessToken)}
            onWishlist={() => {}}
          />
        )}
        numColumns={2}
        keyExtractor={(item) => item?.id}
        showsVerticalScrollIndicator={false}
        contentContainerStyle={styles.productList}
        columnWrapperStyle={styles.productRow}
        scrollEventThrottle={16}
        ListEmptyComponent={null}
        onEndReachedThreshold={0.5}
        onEndReached={handleNext}
        ListFooterComponent={renderFooter}
      />
    </SafeAreaView>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: "#ffffff",
  },
  header: {
    paddingTop: 8,
  },
  bannerContainer: {
    height: 200,
    marginHorizontal: 16,
    marginVertical: 16,
    borderRadius: 12,
    overflow: "hidden",
  },
  banner: {
    width: "100%",
    height: "100%",
    resizeMode: "cover",
  },
  productList: {
    paddingHorizontal: 8,
    paddingBottom: 24,
  },
  productRow: {
    justifyContent: "space-evenly",
  },
});
