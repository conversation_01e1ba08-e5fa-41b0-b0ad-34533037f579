/**
 * This script generates a static sitemap.xml file for better SEO
 * Run with: node scripts/generate-sitemap.js
 *
 * This follows Google's guidelines for sitemap structure and content
 * to ensure proper indexing by search engines
 */

const fs = require('fs');
const path = require('path');
const axios = require('axios');

// Define the output directory and file
const outputDir = path.join(__dirname, '../public');
const outputFile = path.join(outputDir, 'sitemap.xml');

// Get the base URL from environment or use default
const baseUrl = process.env.NEXT_PUBLIC_SITE_URL || 'https://trio.net.in';
const apiUrl = process.env.NEXT_PUBLIC_API_URL || 'http://api-ecom.trio.net.in';

// Get current date in YYYY-MM-DD format
const currentDate = new Date().toISOString().split('T')[0];

// Define static routes - include all crawlable and indexable pages
const staticRoutes = [
  // Core pages - highest priority
  {
    url: '/',
    lastModified: currentDate,
    changeFrequency: 'daily',
    priority: 1,
  },
  {
    url: '/shop',
    lastModified: currentDate,
    changeFrequency: 'daily',
    priority: 0.9,
  },
  // Informational pages - medium priority
  {
    url: '/contact-us',
    lastModified: currentDate,
    changeFrequency: 'monthly',
    priority: 0.7,
  },
  {
    url: '/about',
    lastModified: currentDate,
    changeFrequency: 'monthly',
    priority: 0.7,
  },
  // Policy pages - lower priority
  {
    url: '/privacy-policy',
    lastModified: currentDate,
    changeFrequency: 'yearly',
    priority: 0.5,
  },
  {
    url: '/terms-and-conditions',
    lastModified: currentDate,
    changeFrequency: 'yearly',
    priority: 0.5,
  },
  {
    url: '/shipping-policy',
    lastModified: currentDate,
    changeFrequency: 'yearly',
    priority: 0.5,
  },
  {
    url: '/return-policy',
    lastModified: currentDate,
    changeFrequency: 'yearly',
    priority: 0.5,
  },
  {
    url: '/refund-policy',
    lastModified: currentDate,
    changeFrequency: 'yearly',
    priority: 0.5,
  },
];

// Function to generate sitemap XML
function generateSitemapXml(routes) {
  let xml = '<?xml version="1.0" encoding="UTF-8"?>\n';
  xml += '<urlset xmlns="http://www.sitemaps.org/schemas/sitemap/0.9">\n';

  routes.forEach(route => {
    // Ensure the URL is absolute (Google requires absolute URLs)
    const url = route.url.startsWith('http') ? route.url : `${baseUrl}${route.url}`;

    xml += '  <url>\n';
    xml += `    <loc>${url}</loc>\n`;
    xml += `    <lastmod>${route.lastModified}</lastmod>\n`;
    xml += `    <changefreq>${route.changeFrequency}</changefreq>\n`;
    xml += `    <priority>${route.priority}</priority>\n`;
    xml += '  </url>\n';
  });

  xml += '</urlset>';
  return xml;
}

// Main function to fetch dynamic routes and generate sitemap
async function generateSitemap() {
  try {
    console.log('Generating sitemap.xml...');

    // Initialize arrays for dynamic routes
    let productRoutes = [];
    let categoryRoutes = [];
    // Removed brandRoutes as requested

    try {
      // Fetch products
      console.log('Fetching products from API...');
      const productsResponse = await axios.get(`${apiUrl}/api/v1/products/`);
      if (productsResponse.data && productsResponse.data.results) {
        productRoutes = productsResponse.data.results.map(product => ({
          url: `/product/${product.slug}`,
          lastModified: product.updated_at || currentDate,
          changeFrequency: 'weekly',
          priority: 0.8,
        }));
        console.log(`Found ${productRoutes.length} products`);
      }

      // Fetch categories
      console.log('Fetching categories from API...');
      const categoriesResponse = await axios.get(`${apiUrl}/api/v1/products/categories/`);
      if (categoriesResponse.data && categoriesResponse.data.results) {
        categoryRoutes = categoriesResponse.data.results.map(category => ({
          url: `/shop/category/${category.slug}`,
          lastModified: currentDate,
          changeFrequency: 'weekly',
          priority: 0.7,
        }));
        console.log(`Found ${categoryRoutes.length} categories`);
      }

      // Brand routes have been removed as requested
    } catch (error) {
      console.error('Error fetching dynamic routes:', error.message);
      console.log('Continuing with static routes only...');
    }

    // Combine all routes (without brandRoutes)
    const allRoutes = [...staticRoutes, ...categoryRoutes, ...productRoutes];
    console.log(`Total routes for sitemap: ${allRoutes.length}`);

    // Filter out any routes that should not be indexed
    const filteredRoutes = allRoutes.filter(route => {
      const url = route.url.toLowerCase();
      // Exclude routes that shouldn't be indexed
      return !url.includes('/api/') &&
             !url.includes('/admin/') &&
             !url.includes('/auth/') &&
             !url.includes('/checkout/') &&
             !url.includes('/account/') &&
             !url.includes('/cart');
    });
    console.log(`After filtering, ${filteredRoutes.length} routes remain in sitemap`);

    // Generate sitemap XML
    const sitemapXml = generateSitemapXml(filteredRoutes);

    // Write to file
    fs.writeFileSync(outputFile, sitemapXml);
    console.log(`Sitemap generated at: ${outputFile}`);

    // Also update robots.txt to ensure it references the sitemap
    const robotsFile = path.join(outputDir, 'robots.txt');
    const robotsTxt = `# Allow all web crawlers
User-agent: *
Allow: /

# Disallow admin and private areas
Disallow: /admin/
Disallow: /api/
Disallow: /private/
Disallow: /auth/
Disallow: /checkout/
Disallow: /account/
Disallow: /cart/

# Sitemap location
Sitemap: ${baseUrl}/sitemap.xml
`;
    fs.writeFileSync(robotsFile, robotsTxt);
    console.log(`Robots.txt updated at: ${robotsFile}`);

    // Validate the sitemap
    try {
      const sitemapContent = fs.readFileSync(outputFile, 'utf-8');
      if (sitemapContent.includes('<?xml') && sitemapContent.includes('<urlset') && sitemapContent.includes('</urlset>')) {
        console.log('Sitemap validation passed: XML structure looks valid');
      } else {
        console.warn('Sitemap validation warning: XML structure may not be valid');
      }
    } catch (validationError) {
      console.error('Error validating sitemap:', validationError);
    }

    console.log('Sitemap generation completed successfully!');
  } catch (error) {
    console.error('Error generating sitemap:', error);
    process.exit(1);
  }
}

// Run the function
generateSitemap();
