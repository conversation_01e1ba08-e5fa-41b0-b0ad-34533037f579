"use client";
import React, { useEffect, useState } from "react";
import { ChevronDownIcon, ChevronUpIcon, SearchIcon } from "lucide-react";
import { But<PERSON> } from "../ui/button";
import useApi from "../../hooks/useApi";
import { CATEGORIES, MAIN_URL, PRODUCTS } from "../../constant/urls";
import { Swiper, SwiperSlide } from "swiper/react";
import { EffectCoverflow, Pagination } from "swiper/modules";
// Import Swiper styles
import "swiper/css";
import "swiper/css/effect-coverflow";
import "swiper/css/pagination";
import Product from "../product/Product";

import { CategoryLoading } from "../ui/loading/CategoryLoading";

const CategoriesMenu = () => {
  const [selectedCategory, setSelectedCategory] = useState({
    name: "All categories",
    id: [0],
  });
  const [searchQuery, setSearchQuery] = useState("");
  const [showCategoryTable, setShowCategoryTable] = useState<any>();
  const [page, setPage] = useState<number>(1);
  const [products, setProducts] = useState<any[]>([]);
  const { data, loading, read } = useApi(MAIN_URL || '');
  const { loading: searchLoading, read: searchRead } = useApi(MAIN_URL || '');

  useEffect(() => {
    read(CATEGORIES);
  }, []);

  const handleProductSearch = async () => {
    let url = `${PRODUCTS}?search=${searchQuery}&page=${page}`;
    if (showCategoryTable?.id > 0) {
      url += `&category=${JSON.stringify(showCategoryTable.id)}`;
    }
    const response: any = await searchRead(url);
    if (Boolean(response?.results?.length > 0)) {
      if (page === 1) {
        // Replace products for first page
        setProducts(response.results);
      } else {
        // Append products for pagination, avoiding duplicates
        setProducts((prev) => {
          // Create a Set of existing product IDs to avoid duplicates
          const existingIds = new Set(prev.map((product: any) => product.id));
          // Filter out any products that already exist in the array
          const newProducts = response.results.filter((product: any) => !existingIds.has(product.id));
          return [...prev, ...newProducts];
        });
      }
    } else if (page === 1) {
      // Clear products if no results for first page
      setProducts([]);
    }
  };

  useEffect(() => {
    // If query is empty, clear results and exit
    if (!searchQuery) {
      setProducts([]);
      setPage(1);
      return;
    }

    // Set a delay for the debounce
    const handler = setTimeout(handleProductSearch, 500); // Adjust debounce delay as needed

    // Clean up the timeout if the query changes before delay completes
    return () => clearTimeout(handler);
  }, [searchQuery, showCategoryTable, page]);

  const toggleCategoryTable = () => {
    setShowCategoryTable(!showCategoryTable);
  };

  const handleSwiperIndexChange = (e: any) => {
    const index = e.activeIndex;

    if (Boolean(products?.length % 10 === 0)) {
      if (index % 9 === 0) {
        setPage(page + 1);
      }
    }
  };

  return (
    <div className="z-50 relative">
      <div className="flex items-center justify-between">
        <div className="relative">
          <input
            type="text"
            className="w-full md:w-72 border-[1px] rounded-md py-2 px-4 focus:outline-none"
            placeholder="search..."
            value={searchQuery}
            onChange={(e) => setSearchQuery(e.target.value)}
          />
          <SearchIcon className="absolute right-2 top-1/2 transform -translate-y-1/2 w-5 h-5 text-gray-400" />
        </div>
        <Button
          variant="outline"
          className="ml-4 rounded-md py-2 px-4 flex items-center"
          onClick={toggleCategoryTable}
        >
          <span className="mr-2">{selectedCategory.name}</span>
          {showCategoryTable ? (
            <ChevronUpIcon className="w-5 h-5" />
          ) : (
            <ChevronDownIcon className="w-5 h-5" />
          )}
        </Button>
      </div>

      {loading && showCategoryTable && (
        <div className="w-48 z-50 absolute bg-slate-50 rounded-md top-16 translate-x-1/4 right-0 ">
          <CategoryLoading />
        </div>
      )}

      {!loading && showCategoryTable && (
        <div
          onMouseLeave={toggleCategoryTable}
          className="w-48 z-50 h-[450px] overflow-y-scroll absolute bg-slate-50 rounded-md top-16 translate-x-1/4 right-0 "
        >
          <table className="table w-full">
            <thead>
              <tr className="border-b">
                <th className="py-3 font-medium">Categories</th>
              </tr>
            </thead>
            <tbody>
              {Array.isArray(data) && data.length > 0 ? (
                [
                  {
                    id: 0,
                    name: "All categories",
                  },
                  ...data,
                ].map(({ name, id }: { name: string; id: number }) => (
                  <tr
                    key={id}
                    className="border-b pb-4 transition-colors duration-300 cursor-pointer"
                    onClick={() => {
                      setSelectedCategory({
                        name: name,
                        id: [id],
                      });
                      setShowCategoryTable(false);
                    }}
                  >
                    <td className="py-3 pl-4">{name}</td>
                  </tr>
                ))
              ) : (
                <tr>
                  <td className="py-3 pl-4 text-center">No categories available</td>
                </tr>
              )}
            </tbody>
          </table>
        </div>
      )}

      {Array.isArray(products) && products.length > 0 && searchQuery.trim() !== "" && (
        <div className=" w-full absolute top-12 left-0 backdrop-blur-lg rounded-lg">
          <Swiper
            effect={"coverflow"}
            grabCursor={true}
            centeredSlides={true}
            slidesPerView={"auto"}
            coverflowEffect={{
              rotate: 50,
              stretch: 0,
              depth: 100,
              modifier: 1,
              slideShadows: true,
            }}
            // pagination={true}
            modules={[EffectCoverflow, Pagination]}
            className="w-full  py-12"
            onActiveIndexChange={handleSwiperIndexChange}
          >
            {products?.map((product: any) => (
              <SwiperSlide
                style={{ width: "250px" }}
                className=""
                key={product.id}
              >
                <div className="w-54 h-54">
                  <Product {...product} />
                </div>
              </SwiperSlide>
            ))}
            {searchLoading && (
              <SwiperSlide style={{ width: "250px" }}>
                <div className="w-54 h-54">Loading...</div>
              </SwiperSlide>
            )}
          </Swiper>
        </div>
      )}
    </div>
  );
};

export default CategoriesMenu;
