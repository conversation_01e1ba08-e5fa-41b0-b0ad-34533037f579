import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import { ORDER_STATUS_CHOICES } from "@/constant/urls";

interface OrderStatusSelectProps {
  currentStatus: string;
  onStatusChange: (status: string) => void;
}

export const OrderStatusSelect = ({
  currentStatus,
  onStatusChange,
}: OrderStatusSelectProps) => {
  return (
    <Select defaultValue={currentStatus} onValueChange={onStatusChange}>
      <SelectTrigger className="w-[180px]">
        <SelectValue />
      </SelectTrigger>
      <SelectContent>
        {ORDER_STATUS_CHOICES.map((status) => (
          <SelectItem key={status.value} value={status.value}>
            {status.label}
          </SelectItem>
        ))}
      </SelectContent>
    </Select>
  );
};