"use client";
import MainHOF from "../../layout/MainHOF";
import Link from "next/link";

const ReturnPolicy = () => {
  return (
    <MainHOF>
      <div className="container mx-auto px-4 py-12">
        <div className="max-w-4xl mx-auto">
          <h1 className="text-3xl font-bold mb-8">Return Policy</h1>

          <div className="prose prose-slate max-w-none">
            <section className="mb-8">
              <p className="mb-4">
                We offer refund / exchange within first 7 days from the date of your purchase. If 7 days have passed
                since your purchase, you will not be offered a return, exchange or refund of any kind. In order to become
                eligible for a return or an exchange, (i) the purchased item should be unused and in the same condition as
                you received it, (ii) the item must have original packaging, (iii) if the item that you purchased on a sale,
                then the item may not be eligible for a return / exchange. Further, only such items are replaced by us
                (based on an exchange request), if such items are found defective or damaged.
              </p>
              <p className="mb-4">
                You agree that there may be a certain category of products / items that are exempted from returns or
                refunds. Such categories of the products would be identified to you at the item of purchase. For exchange
                / return accepted request(s) (as applicable), once your returned product / item is received and inspected
                by us, we will send you an email to notify you about receipt of the returned / exchanged product. Further.
                If the same has been approved after the quality check at our end, your request (i.e. return / exchange) will
                be processed in accordance with our policies.
              </p>
              <p className="mb-4">
                Return / Exchange products will be delivered within 3 days to the given address.
              </p>
            </section>

            <section className="mb-8">
              <h2 className="text-2xl font-semibold mb-4">Shipping Policy</h2>
              <p className="mb-4">
                The orders for the user are shipped through registered domestic courier companies and/or speed post
                only. Orders are shipped within 10 days from the date of the order and/or payment or as per the delivery
                date agreed at the time of order confirmation and delivering of the shipment, subject to courier company /
                post office norms. Platform Owner shall not be liable for any delay in delivery by the courier company /
                postal authority. Delivery of all orders will be made to the address provided by the buyer at the time of
                purchase. Delivery of our services will be confirmed on your email ID as specified at the time of
                registration. If there are any shipping cost(s) levied by the seller or the Platform Owner (as the case be),
                the same is not refundable.
              </p>
            </section>

            <section>
              <h2 className="text-2xl font-semibold mb-4">Contact Us</h2>
              <p className="mb-4">
                If you have any questions about our return policy, please contact our customer service team:
              </p>
              <ul className="list-none space-y-2">
                <li><strong>Email:</strong> <EMAIL></li>
                <li><strong>Phone:</strong> +91 9848486452</li>
                <li><strong>Hours:</strong> Monday to Friday, 9am to 6pm IST</li>
              </ul>
              <p className="mt-4">
                For more information about refunds, please see our <Link href="/refund-policy" className="text-primary hover:underline">Refund Policy</Link>.
              </p>
            </section>
          </div>

          <div className="mt-8 p-4 bg-amber-50 border border-amber-200 rounded-lg">
            <p className="font-medium text-amber-800 mb-2">Important Notice:</p>
            <p className="text-amber-700 mb-2">Return policy is subject to change without prior notice.</p>
            <p className="text-amber-700 italic">*Please note: Our policies may be updated occasionally. We recommend reviewing this page periodically to stay informed of any changes.</p>
          </div>

          <div className="mt-4 text-sm text-muted-foreground">
            <p>Last updated: June 1, 2024</p>
          </div>
        </div>
      </div>
    </MainHOF>
  );
};

export default ReturnPolicy;
