import { BaseURL } from "@/constants/ApiEndpoint";
import { showSuccessToast } from "@/toast/ToastProvider";
import axios from "axios";
import * as SecureStore from "expo-secure-store";
import Toast from "react-native-toast-message";
export const addToCart = async (
  productId: string,
  quantity: number,
  accessToken: string
) => {
  try {
    if (!accessToken) {
      throw new Error("User is not authenticated");
    }
    const response = await axios.post(
      `${BaseURL}/api/v1/orders/cart/add-item/`,
      {
        product_id: productId,
        quantity: quantity,
      },
      {
        headers: {
          Authorization: `Bearer ${accessToken}`,
        },
      }
    );

    if (response.data) {
      showSuccessToast(
        "Product added to cart successfully" +
          response.data.items[0].product.name
      );
    }
  } catch (error) {
    console.error("Error adding to cart:", error);
  }
};

const WISHLIST_STORAGE_KEY = "wishlistIds";
const ENDPOINTS = {
  ADD_TO_WISHLIST: `${BaseURL}/api/v1/users/wishlist/`,
  REMOVE_FROM_WISHLIST: `${BaseURL}/api/v1/users/remove/wishlist/`,
};

// Interface for wishlist operations
interface WishlistResponse {
  id?: number;
  status?: boolean;
  error?: string;
}

// Wishlist manager
export const WishlistService = {
  // Get wishlist IDs from secure storage
  async getWishlistIds(): Promise<number[]> {
    try {
      const idsString = await SecureStore.getItemAsync(WISHLIST_STORAGE_KEY);
      return idsString ? JSON.parse(idsString) : [];
    } catch (error) {
      console.error("Error getting wishlist IDs:", error);
      return [];
    }
  },

  // Check if product is in wishlist (local storage)
  async isInWishlist(productId: number): Promise<boolean> {
    const ids = await this.getWishlistIds();
    return ids.includes(productId);
  },

  // Update wishlist IDs in secure storage
  async updateWishlistIds(ids: number[]): Promise<void> {
    try {
      await SecureStore.setItemAsync(WISHLIST_STORAGE_KEY, JSON.stringify(ids));
    } catch (error) {
      console.error("Error updating wishlist IDs:", error);
    }
  },

  // Add to both local storage and backend
  async addToWishlist(productId: number, productName: string): Promise<boolean> {
    try {
      // Add to backend
      const response = await axios.post(ENDPOINTS.ADD_TO_WISHLIST, {
        product_id: productId,
      });
      
      if (response.data?.id) {
        // Add to local storage
        const currentIds = await this.getWishlistIds();
        if (!currentIds.includes(productId)) {
          await this.updateWishlistIds([...currentIds, productId]);
        }
        showSuccessToast(`${productName} added to wishlist`);
        return true;
      }
      return false;
    } catch (error) {
      console.error("Add to wishlist error:", error);
      return false;
    }
  },

  // Remove from both local storage and backend
  async removeFromWishlist(productId: number, productName: string): Promise<boolean> {
    try {
      // Remove from backend
      const url = `${ENDPOINTS.REMOVE_FROM_WISHLIST}?wishlist_id=${productId}`;
      const response = await axios.delete(url);
      
      if (response.data?.status) {
        // Remove from local storage
        const currentIds = await this.getWishlistIds();
        await this.updateWishlistIds(currentIds.filter(id => id !== productId));
        showSuccessToast(`${productName} removed from wishlist`);
        return true;
      }
      return false;
    } catch (error) {
      console.error("Remove from wishlist error:", error);
      return false;
    }
  },

  // Toggle wishlist status - handles both add and remove
  async toggleWishlist(productId: number, productName: string): Promise<boolean> {
    const isInWishlist = await this.isInWishlist(productId);
    
    if (isInWishlist) {
      // If in local storage, remove from both
      return await this.removeFromWishlist(productId, productName);
    } else {
      // If not in local storage, add to both
      return await this.addToWishlist(productId, productName);
    }
  }
};
