#!/bin/bash
# Zero-downtime encryption deployment script
# This script safely deploys database encryption without service interruption

set -e  # Exit on any error

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Logging function
log() {
    echo -e "${BLUE}[$(date +'%Y-%m-%d %H:%M:%S')]${NC} $1"
}

success() {
    echo -e "${GREEN}[$(date +'%Y-%m-%d %H:%M:%S')] ✅ $1${NC}"
}

warning() {
    echo -e "${YELLOW}[$(date +'%Y-%m-%d %H:%M:%S')] ⚠️  $1${NC}"
}

error() {
    echo -e "${RED}[$(date +'%Y-%m-%d %H:%M:%S')] ❌ $1${NC}"
}

# Configuration
BACKUP_DIR="./backups"
TIMESTAMP=$(date +%Y%m%d_%H%M%S)
BACKUP_FILE="${BACKUP_DIR}/backup_before_encryption_${TIMESTAMP}.sql"

# Check if running in production
if [[ "${ENVIRONMENT}" == "production" ]]; then
    warning "Running in PRODUCTION environment"
    read -p "Are you sure you want to proceed? (yes/no): " confirm
    if [[ $confirm != "yes" ]]; then
        error "Deployment cancelled by user"
        exit 1
    fi
fi

log "🚀 Starting zero-downtime encryption deployment..."

# Step 1: Pre-deployment checks
log "📋 Running pre-deployment checks..."

# Check if encryption key is set
if [[ -z "${ENCRYPTION_KEY}" ]]; then
    error "ENCRYPTION_KEY environment variable is not set"
    exit 1
fi

# Check if database is accessible
log "🔍 Checking database connectivity..."
python manage.py check --database default
if [[ $? -ne 0 ]]; then
    error "Database connectivity check failed"
    exit 1
fi

success "Pre-deployment checks passed"

# Step 2: Create backup
log "💾 Creating database backup..."
mkdir -p "${BACKUP_DIR}"

# PostgreSQL backup
if [[ "${DB_ENGINE}" == *"postgresql"* ]]; then
    pg_dump "${DATABASE_URL}" > "${BACKUP_FILE}"
    if [[ $? -eq 0 ]]; then
        success "Database backup created: ${BACKUP_FILE}"
    else
        error "Database backup failed"
        exit 1
    fi
else
    warning "Non-PostgreSQL database detected. Manual backup recommended."
fi

# Step 3: Test encryption functionality
log "🔐 Testing encryption functionality..."
python manage.py validate_encryption
if [[ $? -ne 0 ]]; then
    error "Encryption validation failed"
    exit 1
fi

success "Encryption functionality validated"

# Step 4: Create and run migrations
log "📝 Creating migrations for encrypted fields..."
python manage.py makemigrations --name add_encrypted_fields
if [[ $? -ne 0 ]]; then
    error "Failed to create migrations"
    exit 1
fi

log "🔄 Running migrations..."
python manage.py migrate
if [[ $? -ne 0 ]]; then
    error "Migration failed"
    exit 1
fi

success "Migrations completed successfully"

# Step 5: Migrate data to encrypted fields
log "🔐 Migrating existing data to encrypted fields..."
log "This may take some time depending on data volume..."

# Run migration with progress tracking
python manage.py migrate_to_encryption --batch-size=500
if [[ $? -ne 0 ]]; then
    error "Data migration to encrypted fields failed"
    log "🔄 Attempting rollback..."
    python manage.py migrate users $(python manage.py showmigrations users | grep -B1 "add_encrypted_fields" | head -1 | cut -d' ' -f4 | tr -d '[]')
    exit 1
fi

success "Data migration to encrypted fields completed"

# Step 6: Validate encryption after migration
log "✅ Validating encryption after migration..."
python manage.py validate_encryption --data-integrity --sample-size=20
if [[ $? -ne 0 ]]; then
    error "Post-migration encryption validation failed"
    exit 1
fi

success "Post-migration validation passed"

# Step 7: Performance testing
log "⚡ Running performance tests..."
python manage.py validate_encryption --performance
if [[ $? -ne 0 ]]; then
    warning "Performance tests showed potential issues"
else
    success "Performance tests passed"
fi

# Step 8: Final health check
log "🏥 Running final health check..."
python manage.py check
if [[ $? -ne 0 ]]; then
    error "Final health check failed"
    exit 1
fi

success "Final health check passed"

# Step 9: Cleanup (optional)
log "🧹 Cleaning up temporary files..."
# Remove old migration files if needed
# find . -name "*.pyc" -delete

success "🎉 Encryption deployment completed successfully!"

log "📊 Deployment Summary:"
log "   - Backup created: ${BACKUP_FILE}"
log "   - Migrations applied successfully"
log "   - Data migrated to encrypted fields"
log "   - All validations passed"
log "   - System is ready for production use"

warning "📝 Post-deployment tasks:"
warning "   1. Monitor application logs for any issues"
warning "   2. Run additional performance tests if needed"
warning "   3. Update monitoring dashboards"
warning "   4. Notify team of successful deployment"

log "🔐 Database encryption is now active and protecting sensitive data!"
