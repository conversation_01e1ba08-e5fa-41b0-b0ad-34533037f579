import { Dimensions, PixelRatio, Platform } from 'react-native';

// Get device dimensions
const { width: SCREEN_WIDTH, height: SCREEN_HEIGHT } = Dimensions.get('window');

// Base dimensions that you're designing for
// These values should match your design specs
const BASE_WIDTH = 375; // iPhone X width, common design reference
const BASE_HEIGHT = 812; // iPhone X height, common design reference

// Determine scaling ratio based on screen width
const widthScale = SCREEN_WIDTH / BASE_WIDTH;
const heightScale = SCREEN_HEIGHT / BASE_HEIGHT;

/**
 * Normalizes sizes across different devices
 * @param {number} size - The size to normalize
 * @param {boolean} based0nHeight - Whether to use height as the scaling factor
 * @returns {number} The normalized size
 */
export const normalize = (size, basedOnHeight = false) => {
  // Choose scaling factor based on parameter
  const scale = basedOnHeight ? heightScale : widthScale;
  
  // Calculate normalized size
  const newSize = size * scale;
  
  // For iOS, use PixelRatio to ensure the correct amount of pixels
  // For Android, round to nearest pixel
  if (Platform.OS === 'ios') {
    return Math.round(PixelRatio.roundToNearestPixel(newSize));
  } else {
    return Math.round(PixelRatio.roundToNearestPixel(newSize)) - 2; // Small adjustment for Android
  }
};

/**
 * Normalize horizontal sizes (widths, paddings, margins)
 * @param {number} size - The size to normalize
 * @returns {number} The normalized horizontal size
 */
export const horizontalScale = (size) => {
  return normalize(size, false);
};

/**
 * Normalize vertical sizes (heights, paddings, margins)
 * @param {number} size - The size to normalize
 * @returns {number} The normalized vertical size
 */
export const verticalScale = (size) => {
  return normalize(size, true);
};

/**
 * Scale fonts by a factor slightly different from the screen scale
 * to prevent excessively large fonts on larger devices
 * @param {number} size - The font size to normalize
 * @param {number} factor - Optional adjustment factor (default: 0.5)
 * @returns {number} The normalized font size
 */
export const moderateScale = (size, factor = 0.5) => {
  // Use a more moderate scaling factor for fonts
  const scale = widthScale * (1 + (widthScale - 1) * factor);
  return Math.round(size * scale);
};

// Export default functions for convenience
export default {
  normalize,
  horizontalScale,
  verticalScale,
  moderateScale,
  screenWidth: SCREEN_WIDTH,
  screenHeight: SCREEN_HEIGHT
};