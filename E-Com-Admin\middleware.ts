import { withAuth } from "next-auth/middleware";
import { NextResponse } from "next/server";

export default withAuth(
  function middleware(req) {
    const { pathname } = req.nextUrl;
    const token = req.nextauth.token;

    // Allow access to login page and public assets
    if (
      pathname.startsWith("/login") ||
      pathname.startsWith("/api/auth") ||
      pathname.startsWith("/_next") ||
      pathname.startsWith("/favicon.ico") ||
      pathname.startsWith("/assets")
    ) {
      return NextResponse.next();
    }

    // Allow access to dashboard and analytics without strict authentication (demo mode)
    if (pathname === "/" || pathname === "/analytics") {
      return NextResponse.next();
    }

    // Protect admin routes - require some form of authentication
    const protectedRoutes = [
      "/privacy",
      "/payments",
      "/security",
      "/products",
      "/categories",
      "/orders",
      "/customers",
      "/promotions",
      "/settings"
    ];

    const isProtectedRoute = protectedRoutes.some(route =>
      pathname.startsWith(route)
    );

    // Only redirect if no token at all, or if token has critical error
    if (isProtectedRoute) {
      if (!token) {
        const loginUrl = new URL("/login", req.url);
        loginUrl.searchParams.set("callbackUrl", pathname);
        return NextResponse.redirect(loginUrl);
      }

      // If token has critical error (both access and refresh failed), redirect
      if (token.error === "RefreshAccessTokenError" && !token.access && !token.refresh) {
        const loginUrl = new URL("/login", req.url);
        loginUrl.searchParams.set("callbackUrl", pathname);
        return NextResponse.redirect(loginUrl);
      }
    }

    // For non-protected routes, redirect to login only if no token at all
    if (!token && pathname !== "/") {
      const loginUrl = new URL("/login", req.url);
      loginUrl.searchParams.set("callbackUrl", pathname);
      return NextResponse.redirect(loginUrl);
    }

    return NextResponse.next();
  },
  {
    callbacks: {
      authorized: ({ token, req }) => {
        const { pathname } = req.nextUrl;
        
        // Always allow access to login and public routes
        if (
          pathname.startsWith("/login") ||
          pathname.startsWith("/api/auth") ||
          pathname.startsWith("/_next") ||
          pathname.startsWith("/favicon.ico") ||
          pathname.startsWith("/assets")
        ) {
          return true;
        }

        // Allow dashboard access without authentication (demo mode)
        if (pathname === "/" || pathname === "/analytics") {
          return true;
        }

        // For other routes, require authentication
        return !!token;
      },
    },
  }
);

export const config = {
  matcher: [
    /*
     * Match all request paths except for the ones starting with:
     * - api/auth (NextAuth.js routes)
     * - _next/static (static files)
     * - _next/image (image optimization files)
     * - favicon.ico (favicon file)
     * - public assets
     */
    "/((?!api/auth|_next/static|_next/image|favicon.ico|assets).*)",
  ],
};
