import os
import django
import json
import requests
from decimal import Decimal

os.environ.setdefault("DJANGO_SETTINGS_MODULE", "backend.settings")  # Update 'backend' with your project name
django.setup()

from products.models import Category, Brand, Product, ProductImage  # Update 'products' with your app name

# Function to load product data from JSON file
# 1. response_tehus.json complete
# 2. response_rehau.json complete
# 3. response_qubo.json complete

def load_qubo():
    with open("./data/response_qubo.json", "r") as f:
        data = json.load(f)
    return data['products']

# Function to check and update duplicate product names
def get_unique_product_name(name, category):
    original_name = name
    counter = 1

    # Check if the product name already exists in the same category
    while Product.objects.filter(name=name, category=category).exists():
        # Append a counter to the name to make it unique
        name = f"{original_name} ({counter})"
        counter += 1

    return name

# Main data loading function
def load_data():
    for product_data in load_qubo():
        try:
            # Handle Category
            category_name = product_data['category']
            category, _ = Category.objects.get_or_create(name=category_name)

            # Handle Brand
            brand_name = product_data['brand']
            brand, _ = Brand.objects.get_or_create(name=brand_name)

            # Generate a unique name for the product
            unique_name = get_unique_product_name(product_data['name'], category)

            # Create Product
            product = Product.objects.create(
                name=unique_name,
                description=product_data['description'],
                category=category,
                brand=brand,
                price=Decimal(product_data['sell_price']) if product_data['sell_price'] else None,
                stock=10,  # Assign default stock, update as needed
                is_active=True
            )

            # Download and associate images if provided
            for image_url in product_data['images']:
                try:
                    # Sanitize the file name by removing invalid characters
                    image_name = os.path.basename(image_url).split("?")[0]  # Strip off query parameters
                    image_path = os.path.join("media/products", image_name)

                    # Download and save the image file
                    response = requests.get(image_url)
                    if response.status_code == 200:
                        os.makedirs(os.path.dirname(image_path), exist_ok=True)
                        with open(image_path, 'wb') as f:
                            f.write(response.content)

                        ProductImage.objects.create(product=product, image=image_path)
                except Exception as img_error:
                    print(f"Error downloading or saving image {image_url}: {img_error}")

            print(f"Product created: {product.name}")

        except Exception as e:
            # Log the error and continue with the next product
            print(f"Error processing product: {product_data.get('name')}. Error: {e}")

if __name__ == "__main__":
    load_data()
