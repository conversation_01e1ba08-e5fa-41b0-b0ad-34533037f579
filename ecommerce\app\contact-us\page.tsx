"use client";
import { useState } from "react";
import <PERSON>HO<PERSON> from "../../layout/MainHOF";
import { Button } from "../../components/ui/button";
import { Input } from "../../components/ui/input";
import { Textarea } from "../../components/ui/textarea";
import { useToast } from "../../components/ui/use-toast";
import { Mail, Phone, MapPin } from "lucide-react";
import { MAIN_URL, CONTACT_FORM } from "../../constant/urls";
import axios from "axios";

const ContactUs = () => {
  const { toast } = useToast();
  const [formData, setFormData] = useState({
    name: "",
    email: "",
    subject: "",
    message: "",
  });
  const [isSubmitting, setIsSubmitting] = useState(false);

  const handleChange = (
    e: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement>
  ) => {
    const { name, value } = e.target;
    setFormData((prev) => ({ ...prev, [name]: value }));
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    setIsSubmitting(true);
console.log(CONTACT_FORM, "CONTACT_FORM");
    try {
      console.log('MAIN_URL:', MAIN_URL);
      console.log('CONTACT_FORM:', CONTACT_FORM);

      // Make sure we have a valid URL by checking both parts
      if (!MAIN_URL || !CONTACT_FORM) {
        throw new Error('Invalid API URL configuration');
      }

      // Ensure MAIN_URL ends with a slash if CONTACT_FORM doesn't start with one
      let baseUrl = MAIN_URL;
      if (!MAIN_URL.endsWith('/') && !CONTACT_FORM.startsWith('/')) {
        baseUrl = `${MAIN_URL}/`;
      }

      const apiUrl = `${baseUrl}${CONTACT_FORM}`;
      console.log('Sending request to:', apiUrl);

      const response = await axios.post(apiUrl, formData, {
        headers: { 'Content-Type': 'application/json' }
      });

      const data = response.data;

      if (response.status === 201 && data.success) {
        toast({
          variant: "success",
          title: "Success",
          description: data.message || "We'll get back to you as soon as possible.",
        });
        setFormData({
          name: "",
          email: "",
          subject: "",
          message: "",
        });
      } else {
        toast({
          title: "Error",
          description: data.message || "There was an error sending your message. Please try again.",
          variant: "destructive",
        });
      }
    } catch (error) {
      console.error("Error sending contact form:", error);
      let errorMessage = "There was an error sending your message. Please try again.";

      if (axios.isAxiosError(error)) {
        console.error("Axios error details:", error.response?.data);
        errorMessage = error.response?.data?.message || errorMessage;
      }

      toast({
        title: "Error",
        description: errorMessage,
        variant: "destructive",
      });
    } finally {
      setIsSubmitting(false);
    }
  };

  return (
    <MainHOF>
      <div className="container mx-auto px-4 py-12">
        <h1 className="text-3xl font-bold mb-8">Contact Us</h1>

        <div className="grid grid-cols-1 lg:grid-cols-2 gap-12">
          {/* Contact Form */}
          <div className="bg-white p-6 rounded-lg shadow-md">
            <h2 className="text-xl font-semibold mb-6">Send Us a Message</h2>
            <form onSubmit={handleSubmit} className="space-y-4">
              <div>
                <label htmlFor="name" className="block text-sm font-medium mb-1">
                  Your Name
                </label>
                <Input
                  id="name"
                  name="name"
                  value={formData.name}
                  onChange={handleChange}
                  placeholder="John Doe"
                  required
                />
              </div>

              <div>
                <label htmlFor="email" className="block text-sm font-medium mb-1">
                  Email Address
                </label>
                <Input
                  id="email"
                  name="email"
                  type="email"
                  value={formData.email}
                  onChange={handleChange}
                  placeholder="<EMAIL>"
                  required
                />
              </div>

              <div>
                <label htmlFor="subject" className="block text-sm font-medium mb-1">
                  Subject
                </label>
                <Input
                  id="subject"
                  name="subject"
                  value={formData.subject}
                  onChange={handleChange}
                  placeholder="How can we help you?"
                  required
                />
              </div>

              <div>
                <label htmlFor="message" className="block text-sm font-medium mb-1">
                  Message
                </label>
                <Textarea
                  id="message"
                  name="message"
                  value={formData.message}
                  onChange={handleChange}
                  placeholder="Your message here..."
                  rows={5}
                  required
                />
              </div>

              <Button
                type="submit"
                className="w-full"
                disabled={isSubmitting}
              >
                {isSubmitting ? "Sending..." : "Send Message"}
              </Button>
            </form>
          </div>

          {/* Contact Information */}
          <div>
            <h2 className="text-xl font-semibold mb-6">Contact Information</h2>
            <div className="space-y-6">
              <div className="flex items-start space-x-4">
                <MapPin className="w-5 h-5 mt-1 text-primary" />
                <div>
                  <h3 className="font-medium">Our Location</h3>
                  <p className="text-muted-foreground mt-1">
                    D.No. 5-5-190/65A, Ehata Nooruddin Shah Qadri<br />
                    Patel Nagar, Darussalam <br/>
                    Hyderabad, Telangana 500001<br />
                    India
                  </p>
                </div>
              </div>

              <div className="flex items-start space-x-4">
                <Phone className="w-5 h-5 mt-1 text-primary" />
                <div>
                  <h3 className="font-medium">Phone Number</h3>
                  <p className="text-muted-foreground mt-1">+91 9848486452</p>
                  <p className="text-muted-foreground">Monday to Friday, 9am to 6pm IST</p>
                </div>
              </div>

              <div className="flex items-start space-x-4">
                <Mail className="w-5 h-5 mt-1 text-primary" />
                <div>
                  <h3 className="font-medium">Email Address</h3>
                  <p className="text-muted-foreground mt-1"><EMAIL></p>
                  <p className="text-muted-foreground">We aim to respond within 24 hours</p>
                </div>
              </div>
            </div>

            <div className="mt-8 p-6 bg-muted rounded-lg">
              <h3 className="font-medium mb-2">Business Hours</h3>
              <ul className="space-y-2 text-muted-foreground">
                <li className="flex justify-between">
                  <span>Monday - Friday:</span>
                  <span>9:00 AM - 6:00 PM</span>
                </li>
                <li className="flex justify-between">
                  <span>Saturday:</span>
                  <span>10:00 AM - 4:00 PM</span>
                </li>
                <li className="flex justify-between">
                  <span>Sunday:</span>
                  <span>Closed</span>
                </li>
              </ul>
            </div>
          </div>
        </div>
      </div>
    </MainHOF>
  );
};

export default ContactUs;
