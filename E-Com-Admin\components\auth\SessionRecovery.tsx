'use client';

import { useEffect, useState } from 'react';
import { useAuthSession } from '@/hooks/useAuthSession';
import { Button } from '@/components/ui/button';
import { Alert, AlertDescription } from '@/components/ui/alert';
import { RefreshCw, AlertTriangle, CheckCircle } from 'lucide-react';

/**
 * Component that handles session recovery and provides user feedback
 * for token refresh issues without automatically signing out
 */
export const SessionRecovery = () => {
  const { hasRefreshError, refreshSession, signOutWithCleanup } = useAuthSession();
  const [isRecovering, setIsRecovering] = useState(false);
  const [recoveryAttempts, setRecoveryAttempts] = useState(0);
  const [showRecovery, setShowRecovery] = useState(false);
  const [recoverySuccess, setRecoverySuccess] = useState(false);

  useEffect(() => {
    if (hasRefreshError && recoveryAttempts === 0) {
      setShowRecovery(true);
    }
  }, [hasRefreshError, recoveryAttempts]);

  const handleRecovery = async () => {
    setIsRecovering(true);
    setRecoveryAttempts(prev => prev + 1);
    
    try {
      console.log('🔄 Attempting session recovery...');
      await refreshSession();
      
      // Wait a moment to see if the error clears
      await new Promise(resolve => setTimeout(resolve, 1000));
      
      if (!hasRefreshError) {
        console.log('✅ Session recovery successful');
        setRecoverySuccess(true);
        setShowRecovery(false);
        
        // Hide success message after 3 seconds
        setTimeout(() => setRecoverySuccess(false), 3000);
      } else {
        console.warn('⚠️ Session recovery failed, error persists');
      }
    } catch (error) {
      console.error('❌ Session recovery failed:', error);
    } finally {
      setIsRecovering(false);
    }
  };

  const handleManualSignOut = async () => {
    console.log('🚪 User manually requested sign out');
    await signOutWithCleanup();
  };

  // Show success message
  if (recoverySuccess) {
    return (
      <div className="fixed top-4 right-4 z-50 max-w-md">
        <Alert className="border-green-200 bg-green-50">
          <CheckCircle className="h-4 w-4 text-green-600" />
          <AlertDescription className="text-green-800">
            Session recovered successfully! You can continue using the application.
          </AlertDescription>
        </Alert>
      </div>
    );
  }

  // Don't show if no error or if user dismissed
  if (!showRecovery || !hasRefreshError) {
    return null;
  }

  return (
    <div className="fixed top-4 right-4 z-50 max-w-md">
      <Alert variant="destructive" className="border-orange-200 bg-orange-50">
        <AlertTriangle className="h-4 w-4 text-orange-600" />
        <AlertDescription className="mt-2">
          <div className="space-y-3">
            <p className="text-sm font-medium text-orange-800">
              Session Issue Detected
            </p>
            <p className="text-xs text-orange-700">
              Your session needs to be refreshed. You can try to recover it or sign in again.
            </p>
            <div className="flex gap-2 flex-wrap">
              <Button
                size="sm"
                variant="outline"
                onClick={handleRecovery}
                disabled={isRecovering}
                className="text-xs border-orange-300 text-orange-800 hover:bg-orange-100"
              >
                {isRecovering ? (
                  <>
                    <RefreshCw className="h-3 w-3 mr-1 animate-spin" />
                    Recovering...
                  </>
                ) : (
                  <>
                    <RefreshCw className="h-3 w-3 mr-1" />
                    Try Recovery
                  </>
                )}
              </Button>
              
              {recoveryAttempts > 0 && (
                <Button
                  size="sm"
                  variant="outline"
                  onClick={handleManualSignOut}
                  className="text-xs border-red-300 text-red-800 hover:bg-red-100"
                >
                  Sign Out
                </Button>
              )}
              
              <Button
                size="sm"
                variant="ghost"
                onClick={() => setShowRecovery(false)}
                className="text-xs text-orange-700 hover:bg-orange-100"
              >
                Dismiss
              </Button>
            </div>
            
            {recoveryAttempts > 0 && (
              <p className="text-xs text-orange-600">
                Recovery attempts: {recoveryAttempts}
                {recoveryAttempts >= 2 && " - Consider signing out if issues persist"}
              </p>
            )}
          </div>
        </AlertDescription>
      </Alert>
    </div>
  );
};

export default SessionRecovery;
