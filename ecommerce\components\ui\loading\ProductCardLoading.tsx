import React from "react";

const ProductCardLoading = () => {
  return (
    <div className="w-full bg-white shadow rounded-lg p-4 border border-neutral-300 animate-pulse">
      <div className="w-full h-[200px] bg-gray-200 rounded-md"></div>
      <div className="mt-4 text-left">
        <div className="h-4 bg-gray-200 rounded w-3/4 mt-2"></div>
        <div className="flex justify-between items-center mt-2">
          <div className="h-4 bg-gray-200 rounded w-1/4"></div>
          <div className="h-4 bg-gray-200 rounded w-1/4"></div>
        </div>
        <div className="mt-4 border border-neutral-300 rounded-xs px-4 py-4 w-full text-center ">
          <div className="h-4 bg-gray-200 rounded"></div>
        </div>
      </div>
    </div>
  );
};

export default ProductCardLoading;
