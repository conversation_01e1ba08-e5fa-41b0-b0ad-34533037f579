import { MAIN_URL, PRODUCTS } from "@/constant/urls";
import { useNewApi } from "@/hooks/useNewApi";
import { Search } from "lucide-react";
import React, { useEffect, useRef, useState } from "react";
import ProductInfiniteScrolling from "../product/ProductInfiniteScrolling";
import { useInfiniteScroll } from "@/hooks/useInfiniteScroll";

export const SearchBtn = () => {
  const [searchQuery, setSearchQuery] = useState("");
  const [currentQuery, setCurrentQuery] = useState("");
  const { page, loading, setLoading, setHasMore } =
    useInfiniteScroll();

  const [products, setProducts] = useState<any[]>([]);
  const { get } = useNewApi({
    baseURL: MAIN_URL,
  });
  const observerTarget = useRef<HTMLDivElement>(null);

  const handleProductSearch = async (isNewSearch = false) => {
    setLoading(true);
    let url = `${PRODUCTS}?search=${currentQuery}&page=${page}`;
    const response: any = await get(url);
    if (response.data) {
      const data = response.data;
      if (data?.next) {
        setHasMore(true);
      } else {
        setHasMore(false);
      }
      if (Boolean(data?.results?.length > 0)) {
        if (isNewSearch) {
          // Replace products for new searches
          setProducts(data?.results);
        } else {
          // Append products for pagination
          setProducts((prev) => {
            // Create a Set of existing product IDs to avoid duplicates
            const existingIds = new Set(prev.map((product: any) => product.id));
            // Filter out any products that already exist in the array
            const newProducts = data?.results.filter((product: any) => !existingIds.has(product.id));
            return [...prev, ...newProducts];
          });
        }
      } else if (isNewSearch) {
        // Clear products if no results for a new search
        setProducts([]);
      }

      setLoading(false);
    }
    if (response.error) {
      // Handle error
      setLoading(false);
    }
  };

  // Effect for handling search query changes
  useEffect(() => {
    // If query is empty, clear results and exit
    if (!searchQuery) {
      setProducts([]);
      setCurrentQuery("");
      return;
    }

    // Set a delay for the debounce
    const handler = setTimeout(() => {
      if (searchQuery !== currentQuery) {
        setCurrentQuery(searchQuery);
      }
    }, 500);

    // Clean up the timeout if the query changes before delay completes
    return () => clearTimeout(handler);
  }, [searchQuery]);

  // Effect for handling actual API calls
  useEffect(() => {
    if (!currentQuery) return;

    // If the query changed, reset page to 1 and treat as new search
    const isNewSearch = page === 1;
    handleProductSearch(isNewSearch);
  }, [currentQuery, page]);

  return (
    <div className="rounded-2xl p-4 md:p-6 h-full w-full max-w-4xl mx-auto">
      {/* Search Header */}
      <div className="text-center mb-6">
        <h2 className="text-2xl md:text-3xl font-bold text-white mb-2">Search Products</h2>
        <p className="text-gray-300 text-sm md:text-base">Find exactly what you're looking for</p>
      </div>

      <form
        className="w-full max-w-2xl mx-auto flex items-center gap-3 bg-white/95 backdrop-blur-sm border border-gray-200 rounded-2xl px-4 md:px-6 py-3 md:py-4 hover:shadow-2xl transition-all duration-300 focus-within:ring-2 focus-within:ring-theme-accent-primary/50"
        onSubmit={(e) => {
          e.preventDefault();
          if (searchQuery.trim()) {
            setCurrentQuery(searchQuery.trim());
          }
        }}
      >
        <Search className="h-5 w-5 md:h-6 md:w-6 text-gray-400 flex-shrink-0" />
        <input
          type="text"
          placeholder="Search for products, brands, categories..."
          value={searchQuery}
          onChange={(e) => setSearchQuery(e.target.value)}
          className="flex-1 px-2 py-1 text-gray-800 bg-transparent focus:outline-none text-base md:text-lg placeholder-gray-500"
          autoFocus
        />
        <button
          type="submit"
          className="flex items-center justify-center h-10 w-10 md:h-12 md:w-12 bg-gradient-to-r from-theme-accent-primary to-theme-accent-hover text-white rounded-xl shadow-lg hover:shadow-xl transition-all duration-200 active:scale-95 flex-shrink-0"
          aria-label="Search"
        >
          <Search className="h-5 w-5 md:h-6 md:w-6" strokeWidth={2.5} />
        </button>
      </form>

      <div className="mt-6">
        {searchQuery.trim() !== "" && (
          <div className="bg-white/10 backdrop-blur-sm rounded-2xl p-4 max-h-[60vh] overflow-y-auto">
            <ProductInfiniteScrolling
              products={products}
              isLoading={loading}
              observerTarget={observerTarget}
            />
          </div>
        )}
      </div>
    </div>
  );
};
