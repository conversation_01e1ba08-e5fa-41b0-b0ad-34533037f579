import pytest
import json
from decimal import Decimal
from django.urls import reverse, NoReverseMatch
from rest_framework import status
from orders.models import Order, OrderItem

# Remove the skip marker to enable the tests
# pytestmark = pytest.mark.skip(reason="URL names don't match the actual implementation")

class TestOrderProcessing:
    """Tests for order processing."""

    @pytest.mark.django_db
    def test_create_order(self, authenticated_client, create_address, create_shipping_method, create_product):
        """Test creating an order."""
        client, user = authenticated_client
        shipping_address = create_address(user)
        billing_address = create_address(user, is_default=False)
        shipping_method = create_shipping_method()
        product = create_product(price=Decimal('100.00'))

        url = reverse('order-list')
        data = {
            'shipping_address_id': shipping_address.id,
            'billing_address_id': billing_address.id,
            'shipping_method_id': shipping_method.id,
            'items': [
                {
                    'product_id': product.id,
                    'quantity': 2
                }
            ],
            'payment_method': 'PHONEPE'
        }

        response = client.post(url, data=json.dumps(data), content_type='application/json')

        assert response.status_code == status.HTTP_201_CREATED
        assert 'id' in response.data

        # Check that order was created
        order = Order.objects.get(id=response.data['id'])
        assert order.user == user
        assert order.status == 'PENDING'
        assert order.shipping_address == shipping_address
        assert order.billing_address == billing_address
        assert order.shipping_method == shipping_method
        # With GST implementation, subtotal is base price (GST exclusive)
        # MRP = 100.00 per item, Base price = 100/1.18 = 84.75 per item
        # For 2 items: 2 * 84.75 = 169.49 (rounded)
        expected_base_price = Decimal('100.00') / Decimal('1.18') * 2
        assert order.subtotal == round(expected_base_price, 2)
        assert order.shipping_cost == shipping_method.price
        # Total includes subtotal + GST + shipping
        assert order.total == order.subtotal + order.gst_amount + order.shipping_cost

        # Check that order items were created
        order_items = OrderItem.objects.filter(order=order)
        assert order_items.count() == 1
        assert order_items[0].product == product
        assert order_items[0].quantity == 2
        # unit_price and total_price store MRP (GST inclusive)
        assert order_items[0].unit_price == product.price  # MRP
        assert order_items[0].total_price == product.price * 2  # Total MRP

    @pytest.mark.django_db
    def test_create_order_minimum_value(self, authenticated_client, create_address, create_shipping_method, create_product):
        """Test creating an order with value below minimum."""
        client, user = authenticated_client
        shipping_address = create_address(user)
        billing_address = create_address(user, is_default=False)
        shipping_method = create_shipping_method()
        product = create_product(price=Decimal('50.00'))

        url = reverse('order-list')
        data = {
            'shipping_address_id': shipping_address.id,
            'billing_address_id': billing_address.id,
            'shipping_method_id': shipping_method.id,
            'items': [
                {
                    'product_id': product.id,
                    'quantity': 1
                }
            ],
            'payment_method': 'PHONEPE'
        }

        response = client.post(url, data=json.dumps(data), content_type='application/json')

        # Should fail because total is less than minimum (150)
        assert response.status_code == status.HTTP_400_BAD_REQUEST
        # Handle both string and list response formats
        detail = response.data.get('detail', '')
        if isinstance(detail, list):
            detail = detail[0] if detail else ''
        assert 'minimum order value' in detail.lower()

    @pytest.mark.django_db
    def test_get_order_details(self, authenticated_client, create_address, create_shipping_method, create_order):
        """Test getting order details."""
        client, user = authenticated_client
        shipping_address = create_address(user)
        billing_address = create_address(user, is_default=False)
        shipping_method = create_shipping_method()
        order = create_order(user, shipping_address, billing_address, shipping_method)

        url = reverse('order-detail', kwargs={'pk': str(order.id)})
        response = client.get(url)

        assert response.status_code == status.HTTP_200_OK
        assert response.data['id'] == str(order.id)
        assert response.data['status'] == order.status
        assert Decimal(response.data['total']) == order.total

    @pytest.mark.django_db
    def test_update_order_status(self, admin_client, create_address, create_shipping_method, create_order):
        """Test updating order status (admin only)."""
        client, admin_user = admin_client
        shipping_address = create_address(admin_user)
        billing_address = create_address(admin_user, is_default=False)
        shipping_method = create_shipping_method()
        order = create_order(admin_user, shipping_address, billing_address, shipping_method)

        url = reverse('order-detail', kwargs={'pk': str(order.id)})
        data = {
            'status': 'PROCESSING'
        }

        response = client.patch(url, data=json.dumps(data), content_type='application/json')

        assert response.status_code == status.HTTP_200_OK

        # Check that order was updated
        order.refresh_from_db()
        assert order.status == 'PROCESSING'

    @pytest.mark.django_db
    def test_cancel_order(self, authenticated_client, create_address, create_shipping_method, create_order):
        """Test cancelling an order."""
        client, user = authenticated_client
        shipping_address = create_address(user)
        billing_address = create_address(user, is_default=False)
        shipping_method = create_shipping_method()
        order = create_order(user, shipping_address, billing_address, shipping_method)

        url = reverse('order-cancel', kwargs={'pk': str(order.id)})
        response = client.post(url)

        assert response.status_code == status.HTTP_200_OK

        # Check that order was updated
        order.refresh_from_db()
        assert order.status == 'CANCELLED'

    @pytest.mark.django_db
    def test_list_user_orders(self, authenticated_client, create_address, create_shipping_method, create_order):
        """Test listing user orders."""
        client, user = authenticated_client
        shipping_address = create_address(user)
        billing_address = create_address(user, is_default=False)
        shipping_method = create_shipping_method()

        # Create multiple orders
        order1 = create_order(user, shipping_address, billing_address, shipping_method)
        order2 = create_order(user, shipping_address, billing_address, shipping_method)

        url = reverse('order-list')
        response = client.get(url)

        assert response.status_code == status.HTTP_200_OK
        assert len(response.data['results']) == 2
        assert response.data['count'] == 2
