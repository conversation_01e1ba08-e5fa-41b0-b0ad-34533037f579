/* Privacy Component Animations */

/* Slide up animation for consent banner */
@keyframes slideUp {
  from {
    transform: translateY(100%);
    opacity: 0;
  }
  to {
    transform: translateY(0);
    opacity: 1;
  }
}

@keyframes slideDown {
  from {
    transform: translateY(0);
    opacity: 1;
  }
  to {
    transform: translateY(100%);
    opacity: 0;
  }
}

/* Fade in animation for privacy settings */
@keyframes fadeIn {
  from {
    opacity: 0;
    transform: translateY(20px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

/* Pulse animation for important buttons */
@keyframes pulse {
  0% {
    box-shadow: 0 0 0 0 rgba(59, 130, 246, 0.7);
  }
  70% {
    box-shadow: 0 0 0 10px rgba(59, 130, 246, 0);
  }
  100% {
    box-shadow: 0 0 0 0 rgba(59, 130, 246, 0);
  }
}

/* Smooth toggle animation */
@keyframes toggleOn {
  from {
    background-color: #e5e7eb;
  }
  to {
    background-color: #2563eb;
  }
}

@keyframes toggleOff {
  from {
    background-color: #2563eb;
  }
  to {
    background-color: #e5e7eb;
  }
}

/* Loading spinner for export/deletion operations */
@keyframes spin {
  from {
    transform: rotate(0deg);
  }
  to {
    transform: rotate(360deg);
  }
}

/* Shake animation for errors */
@keyframes shake {
  0%, 100% {
    transform: translateX(0);
  }
  10%, 30%, 50%, 70%, 90% {
    transform: translateX(-5px);
  }
  20%, 40%, 60%, 80% {
    transform: translateX(5px);
  }
}

/* Success checkmark animation */
@keyframes checkmark {
  0% {
    stroke-dasharray: 0 100;
  }
  100% {
    stroke-dasharray: 100 0;
  }
}

/* Privacy banner classes */
.animate-slide-up {
  animation: slideUp 0.5s ease-out forwards;
}

.animate-slide-down {
  animation: slideDown 0.3s ease-in forwards;
}

.animate-fade-in {
  animation: fadeIn 0.3s ease-out forwards;
}

.animate-pulse-blue {
  animation: pulse 2s infinite;
}

.animate-spin {
  animation: spin 1s linear infinite;
}

.animate-shake {
  animation: shake 0.5s ease-in-out;
}

/* Toggle switch animations */
.toggle-switch {
  transition: all 0.3s ease;
}

.toggle-switch.on {
  animation: toggleOn 0.3s ease forwards;
}

.toggle-switch.off {
  animation: toggleOff 0.3s ease forwards;
}

/* Button hover effects */
.privacy-button {
  transition: all 0.2s ease;
  position: relative;
  overflow: hidden;
}

.privacy-button:hover {
  transform: translateY(-1px);
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
}

.privacy-button:active {
  transform: translateY(0);
}

/* Ripple effect for buttons */
.privacy-button::before {
  content: '';
  position: absolute;
  top: 50%;
  left: 50%;
  width: 0;
  height: 0;
  border-radius: 50%;
  background: rgba(255, 255, 255, 0.3);
  transform: translate(-50%, -50%);
  transition: width 0.3s, height 0.3s;
}

.privacy-button:active::before {
  width: 300px;
  height: 300px;
}

/* Smooth transitions for consent cards */
.consent-card {
  transition: all 0.3s ease;
  border: 2px solid transparent;
}

.consent-card:hover {
  transform: translateY(-2px);
  box-shadow: 0 8px 25px rgba(0, 0, 0, 0.1);
  border-color: #e5e7eb;
}

.consent-card.granted {
  border-color: #10b981;
  background: linear-gradient(135deg, #f0fdf4 0%, #ffffff 100%);
}

.consent-card.denied {
  border-color: #ef4444;
  background: linear-gradient(135deg, #fef2f2 0%, #ffffff 100%);
}

/* Loading states */
.loading-overlay {
  position: relative;
}

.loading-overlay::after {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(255, 255, 255, 0.8);
  display: flex;
  align-items: center;
  justify-content: center;
  opacity: 0;
  visibility: hidden;
  transition: all 0.3s ease;
}

.loading-overlay.loading::after {
  opacity: 1;
  visibility: visible;
}

/* Success/Error states */
.success-state {
  background: linear-gradient(135deg, #10b981 0%, #059669 100%);
  color: white;
  animation: fadeIn 0.5s ease;
}

.error-state {
  background: linear-gradient(135deg, #ef4444 0%, #dc2626 100%);
  color: white;
  animation: shake 0.5s ease;
}

/* Mobile-specific animations */
@media (max-width: 640px) {
  .animate-slide-up {
    animation: slideUp 0.3s ease-out forwards;
  }
  
  .privacy-button:hover {
    transform: none; /* Disable hover effects on mobile */
  }
  
  .consent-card:hover {
    transform: none;
  }
}

/* Reduced motion preferences */
@media (prefers-reduced-motion: reduce) {
  .animate-slide-up,
  .animate-slide-down,
  .animate-fade-in,
  .animate-pulse-blue,
  .animate-spin,
  .animate-shake {
    animation: none;
  }
  
  .privacy-button,
  .consent-card,
  .toggle-switch {
    transition: none;
  }
}

/* High contrast mode support */
@media (prefers-contrast: high) {
  .consent-card.granted {
    border-color: #000000;
    background: #ffffff;
  }
  
  .consent-card.denied {
    border-color: #000000;
    background: #ffffff;
  }
}

/* Dark mode support */
@media (prefers-color-scheme: dark) {
  .consent-card {
    background: #1f2937;
    border-color: #374151;
  }
  
  .consent-card:hover {
    border-color: #6b7280;
  }
  
  .consent-card.granted {
    border-color: #10b981;
    background: linear-gradient(135deg, #064e3b 0%, #1f2937 100%);
  }
  
  .consent-card.denied {
    border-color: #ef4444;
    background: linear-gradient(135deg, #7f1d1d 0%, #1f2937 100%);
  }
}

/* Focus indicators for accessibility */
.privacy-button:focus,
.toggle-switch:focus {
  outline: 2px solid #2563eb;
  outline-offset: 2px;
}

/* Custom scrollbar for privacy settings */
.privacy-settings-container::-webkit-scrollbar {
  width: 8px;
}

.privacy-settings-container::-webkit-scrollbar-track {
  background: #f1f5f9;
  border-radius: 4px;
}

.privacy-settings-container::-webkit-scrollbar-thumb {
  background: #cbd5e1;
  border-radius: 4px;
}

.privacy-settings-container::-webkit-scrollbar-thumb:hover {
  background: #94a3b8;
}

/* Tooltip animations */
.privacy-tooltip {
  opacity: 0;
  transform: translateY(10px);
  transition: all 0.2s ease;
  pointer-events: none;
}

.privacy-tooltip.show {
  opacity: 1;
  transform: translateY(0);
  pointer-events: auto;
}
