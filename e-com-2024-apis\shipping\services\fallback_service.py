"""
Fallback shipping service that uses existing shipping methods
when Rapidshyp is unavailable or fails
"""

import logging
from typing import Dict, List, Optional
from decimal import Decimal
from django.conf import settings

from orders.models import ShippingMethod
from ..exceptions import FallbackShippingException
from ..utils import format_currency


logger = logging.getLogger(__name__)


class FallbackShippingService:
    """
    Fallback shipping service that provides existing shipping methods
    when Rapidshyp integration is unavailable
    """

    def __init__(self):
        self.logger = logger

    def get_existing_methods(self) -> List[Dict]:
        """
        Get all active existing shipping methods

        Returns:
            List[Dict]: List of existing shipping methods formatted for compatibility
        """
        try:
            shipping_methods = ShippingMethod.objects.filter(is_active=True).order_by('price')

            methods = []
            for method in shipping_methods:
                methods.append({
                    'courier_code': f'existing_{method.id}',
                    'courier_name': method.name,
                    'parent_courier_name': 'Standard Shipping',
                    'total_freight': float(method.price),
                    'estimated_days': getattr(method, 'estimated_days', 5),
                    'freight_mode': 'Standard',
                    'cutoff_time': '',
                    'max_weight': 50.0,  # Default max weight
                    'min_weight': 0.1,   # Default min weight
                    'is_existing_method': True,
                    'description': getattr(method, 'description', ''),
                })

            self.logger.info(f"Retrieved {len(methods)} existing shipping methods")
            return methods

        except Exception as e:
            self.logger.error(f"Failed to retrieve existing shipping methods: {e}")
            raise FallbackShippingException(f"Failed to retrieve shipping methods: {e}")

    def get_cheapest_method(self) -> Optional[Dict]:
        """
        Get the cheapest existing shipping method

        Returns:
            Dict or None: Cheapest shipping method
        """
        try:
            methods = self.get_existing_methods()
            if not methods:
                return None

            cheapest = min(methods, key=lambda m: m['total_freight'])
            self.logger.debug(f"Cheapest method: {cheapest['courier_name']} - {format_currency(cheapest['total_freight'])}")
            return cheapest

        except Exception as e:
            self.logger.error(f"Failed to get cheapest method: {e}")
            return None

    def get_fastest_method(self) -> Optional[Dict]:
        """
        Get the fastest existing shipping method

        Returns:
            Dict or None: Fastest shipping method
        """
        try:
            methods = self.get_existing_methods()
            if not methods:
                return None

            fastest = min(methods, key=lambda m: m['estimated_days'])
            self.logger.debug(f"Fastest method: {fastest['courier_name']} - {fastest['estimated_days']} days")
            return fastest

        except Exception as e:
            self.logger.error(f"Failed to get fastest method: {e}")
            return None

    def get_method_by_id(self, method_id: int) -> Optional[Dict]:
        """
        Get specific shipping method by ID

        Args:
            method_id: Shipping method ID

        Returns:
            Dict or None: Shipping method details
        """
        try:
            method = ShippingMethod.objects.get(id=method_id, is_active=True)

            return {
                'courier_code': f'existing_{method.id}',
                'courier_name': method.name,
                'parent_courier_name': 'Standard Shipping',
                'total_freight': float(method.price),
                'estimated_days': getattr(method, 'estimated_days', 5),
                'freight_mode': 'Standard',
                'cutoff_time': '',
                'max_weight': 50.0,
                'min_weight': 0.1,
                'is_existing_method': True,
                'description': getattr(method, 'description', ''),
            }

        except ShippingMethod.DoesNotExist:
            self.logger.warning(f"Shipping method {method_id} not found")
            return None
        except Exception as e:
            self.logger.error(f"Failed to get shipping method {method_id}: {e}")
            return None

    def calculate_shipping_cost(self, method_id: int, weight: float = 1.0,
                              distance: float = 0) -> Decimal:
        """
        Calculate shipping cost for existing method

        Args:
            method_id: Shipping method ID
            weight: Package weight (not used in basic calculation)
            distance: Distance (not used in basic calculation)

        Returns:
            Decimal: Shipping cost
        """
        try:
            method = ShippingMethod.objects.get(id=method_id, is_active=True)

            # Basic calculation - can be enhanced with weight/distance logic
            base_cost = method.price

            # Add weight-based calculation if needed
            if weight > 5.0:  # Over 5kg
                additional_cost = (weight - 5.0) * Decimal('10.00')  # ₹10 per additional kg
                base_cost += additional_cost

            self.logger.debug(f"Calculated cost for method {method_id}: {format_currency(base_cost)}")
            return base_cost

        except ShippingMethod.DoesNotExist:
            self.logger.warning(f"Shipping method {method_id} not found for cost calculation")
            return Decimal('0.00')
        except Exception as e:
            self.logger.error(f"Failed to calculate shipping cost: {e}")
            return Decimal('0.00')

    def get_fallback_response(self, pickup_pincode: str = '', delivery_pincode: str = '',
                            weight: float = 1.0, cod: bool = False) -> Dict:
        """
        Get fallback response in Rapidshyp-compatible format

        Args:
            pickup_pincode: Pickup pincode (not used in fallback)
            delivery_pincode: Delivery pincode (not used in fallback)
            weight: Package weight
            cod: Cash on delivery flag

        Returns:
            Dict: Fallback response in compatible format
        """
        try:
            methods = self.get_existing_methods()

            # Adjust pricing based on weight if needed
            for method in methods:
                if weight > 5.0:
                    additional_cost = (weight - 5.0) * 10.0  # ₹10 per additional kg
                    method['total_freight'] += additional_cost

                # Add COD charges if applicable
                if cod and method['total_freight'] > 0:
                    cod_charge = max(20.0, method['total_freight'] * 0.02)  # 2% or ₹20, whichever is higher
                    method['total_freight'] += cod_charge
                    method['courier_name'] += ' (COD)'

            response = {
                'status': True,
                'message': 'Using standard shipping methods',
                'serviceable_courier_list': methods,
                'source': 'fallback',
                'pickup_pincode': pickup_pincode,
                'delivery_pincode': delivery_pincode,
                'weight': weight,
                'cod': cod
            }

            self.logger.info(f"Fallback response generated with {len(methods)} methods")
            return response

        except Exception as e:
            self.logger.error(f"Failed to generate fallback response: {e}")
            raise FallbackShippingException(f"Fallback service failed: {e}")

    def is_pincode_serviceable(self, pincode: str) -> bool:
        """
        Check if pincode is serviceable (always True for fallback)

        Args:
            pincode: Pincode to check

        Returns:
            bool: Always True for fallback service
        """
        # For fallback, we assume all pincodes are serviceable
        # This can be enhanced with actual pincode validation logic
        return len(pincode) == 6 and pincode.isdigit()

    def get_delivery_estimate(self, method_id: int, pickup_pincode: str = '',
                            delivery_pincode: str = '') -> int:
        """
        Get delivery time estimate for existing method

        Args:
            method_id: Shipping method ID
            pickup_pincode: Pickup pincode (not used in basic calculation)
            delivery_pincode: Delivery pincode (not used in basic calculation)

        Returns:
            int: Estimated delivery days
        """
        try:
            method = ShippingMethod.objects.get(id=method_id, is_active=True)
            return getattr(method, 'estimated_days', 5)

        except ShippingMethod.DoesNotExist:
            self.logger.warning(f"Shipping method {method_id} not found for delivery estimate")
            return 7  # Default 7 days
        except Exception as e:
            self.logger.error(f"Failed to get delivery estimate: {e}")
            return 7  # Default 7 days
