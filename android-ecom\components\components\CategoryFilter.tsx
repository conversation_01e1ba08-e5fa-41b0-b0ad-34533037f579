import CategoriesProductScreen from '@/screens/CategoriesProductScreen';
import axios from 'axios';
import { useEffect, useState } from 'react';
import { ScrollView, TouchableOpacity, Text, StyleSheet } from 'react-native';

// const categories = [
//   'All', 'Electronics', 'Fashion', 'Home', 'Beauty', 'Sports', 'Books'
// ];



interface CategoryFilterProps {
  selectedCategory: string;
  onSelectCategory: (category: string) => void;
  navigation:any
}

export default function CategoryFilter({ selectedCategory, onSelectCategory, navigation }: CategoryFilterProps) {
  const [categories, setCategories] = useState([]);

  
  const fetchCategories = async () => {
  try {
    const response = await axios.get('https://api-e-com.wesolves.in/api/v1/products/categories/');


    if(response){

      setCategories(response.data);
    }
  } catch (error) {
    console.error('Error fetching categories:', error);
  }
}


  useEffect(() => {
    fetchCategories();
  },[])

  const ToggleCategories = (categoryId:any, slug:string)=>{
    onSelectCategory(categoryId)
    navigation.navigate('CategoriesProduct', {category: { slug: slug }})
  }
  return (
    <ScrollView
      horizontal
      showsHorizontalScrollIndicator={false}
      contentContainerStyle={styles.container}
    >
      {Array.isArray(categories) && categories.map((category) => (
        <TouchableOpacity
          key={category?.id}
          style={[
            styles.categoryButton,
            selectedCategory === category?.id && styles.selectedCategory,
          ]}
          onPress={() => ToggleCategories(category?.id, category.slug)}
        >
          <Text
            style={[
              styles.categoryText,
              selectedCategory === category?.id && styles.selectedCategoryText,
            ]}
          >
            {category?.name}
          </Text>
        </TouchableOpacity>
      ))}
    </ScrollView>
  );
}

const styles = StyleSheet.create({
  container: {
    paddingHorizontal: 16,
    paddingVertical: 12,
    flexGrow: 0,
  },
  categoryButton: {
    paddingHorizontal: 16,
    paddingVertical: 8,
    borderRadius: 20,
    backgroundColor: '#f3f4f6',
    marginRight: 8,
    minHeight: 36,
    justifyContent: 'center',
  },
  selectedCategory: {
    backgroundColor: '#2563EB',
  },
  categoryText: {
    fontSize: 14,
    color: '#4b5563',
    textAlign: 'center',
  },
  selectedCategoryText: {
    color: 'white',
    fontWeight: '500',
  },
});