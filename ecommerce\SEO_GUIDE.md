# Triumph Enterprises SEO Guide

This document provides comprehensive guidance on SEO best practices for the Triumph Enterprises ecommerce application.

## Table of Contents

1. [Environment Setup](#environment-setup)
2. [Logo and Brand Assets](#logo-and-brand-assets)
3. [Metadata Configuration](#metadata-configuration)
4. [Sitemap and Robots.txt](#sitemap-and-robotstxt)
5. [Structured Data](#structured-data)
6. [URL Structure](#url-structure)
7. [Performance Optimization](#performance-optimization)
8. [Testing and Monitoring](#testing-and-monitoring)

## Environment Setup

### Required Environment Variables

For proper SEO functionality, set the following environment variables:

```
NEXT_PUBLIC_SITE_URL=https://your-actual-domain.com
NEXT_PUBLIC_API_URL=https://your-api-domain.com
```

### Development Environment

Create a `.env.local` file in the root of your project with:

```
NEXT_PUBLIC_SITE_URL=http://localhost:3000
NEXT_PUBLIC_API_URL=http://localhost:8000
```

### Production Environment

Set these environment variables in your hosting platform (Vercel, <PERSON>lify, etc.).

## Logo and Brand Assets

### Logo Generation

We've implemented a script to generate multiple sizes of the logo for different platforms:

```bash
npm run generate-logo-sizes
```

This script creates various sizes of the logo in the `/public/logo/` directory:

- Favicons (16x16, 32x32, 48x48, 64x64, 96x96, 128x128)
- App icons (192x192, 256x256, 384x384, 512x512, 1024x1024)
- Apple Touch Icon (180x180)
- Android Chrome icons (192x192, 512x512)
- Open Graph image (1200x630)
- Twitter Card image (1024x512)

### Using Logo Assets

- For favicons: Use the appropriate size from `/logo/favicon-{size}.png`
- For social media: Use `/logo/og-image.png` for Open Graph and `/logo/twitter-card.png` for Twitter
- For app icons: Use the appropriate size from `/logo/logo-{size}.png`

## Metadata Configuration

### Global Metadata

Global metadata is configured in `app/layout.tsx`. This includes:

- Page title and description
- Open Graph tags
- Twitter Card tags
- Favicon and icon configuration
- Robots directives

### Page-specific Metadata

For product pages, we use the `ProductMetadata` component in `app/product/[slug]/ProductMetadata.tsx`.

To add metadata to other pages, create a similar component or use Next.js metadata API:

```tsx
export const metadata: Metadata = {
  title: "Page Title | Triumph Enterprises",
  description: "Page description goes here.",
  // Other metadata properties
};
```

## Sitemap and Robots.txt

### Sitemap

The sitemap is generated dynamically in `app/sitemap.ts`. It includes:

- Static routes (homepage, shop, about, policies)
- Dynamic product routes fetched from the API
- Dynamic category routes fetched from the API

The sitemap is accessible at `/sitemap.xml`.

### Robots.txt

The robots.txt file is generated in `app/robots.ts`. It:

- Allows all web crawlers to access most pages
- Disallows access to admin, API, and private areas
- Points to the sitemap location

The robots.txt file is accessible at `/robots.txt`.

## Structured Data

### JSON-LD Implementation

We use JSON-LD for structured data, implemented in:

- `components/utils/JsonLd.tsx` for global structured data
- `app/product/[slug]/ProductMetadata.tsx` for product-specific structured data

### Types of Structured Data

- Organization: Company information, social media links, contact details
- Website: Site name, description, search functionality
- BreadcrumbList: Navigation path for the current page
- Product: Product details, pricing, availability, images

## URL Structure

Follow these URL structure guidelines:

- Product pages: `/product/[slug]`
- Category pages: `/shop/category/[slug]`
- Static pages: `/[page-name]` (e.g., `/about`, `/contact-us`)
- Blog posts: `/blog/[slug]`

Use kebab-case for all slugs (e.g., `digital-door-lock` instead of `DigitalDoorLock`).

## Performance Optimization

### Image Optimization

- Use Next.js Image component for automatic optimization
- Provide appropriate `width` and `height` attributes
- Use responsive images with the `sizes` attribute
- Use the `priority` attribute for above-the-fold images

### Core Web Vitals

- Minimize Largest Contentful Paint (LCP) by optimizing hero images
- Reduce Cumulative Layout Shift (CLS) by specifying image dimensions
- Improve First Input Delay (FID) by minimizing JavaScript execution time

## Testing and Monitoring

### SEO Testing Tools

- [Google Search Console](https://search.google.com/search-console)
- [Google PageSpeed Insights](https://pagespeed.web.dev/)
- [Google Rich Results Test](https://search.google.com/test/rich-results)
- [Facebook Sharing Debugger](https://developers.facebook.com/tools/debug/)
- [Twitter Card Validator](https://cards-dev.twitter.com/validator)
- [LinkedIn Post Inspector](https://www.linkedin.com/post-inspector/)

### Regular Monitoring

- Monitor search rankings for key terms
- Check Google Search Console for indexing issues
- Verify structured data with Google's Rich Results Test
- Test social media sharing regularly

## Additional Resources

- [Google SEO Starter Guide](https://developers.google.com/search/docs/fundamentals/seo-starter-guide)
- [Next.js SEO Documentation](https://nextjs.org/docs/app/building-your-application/optimizing/metadata)
- [Schema.org Documentation](https://schema.org/docs/schemas.html)
- [Open Graph Protocol](https://ogp.me/)
- [Twitter Card Documentation](https://developer.twitter.com/en/docs/twitter-for-websites/cards/overview/abouts-cards)
