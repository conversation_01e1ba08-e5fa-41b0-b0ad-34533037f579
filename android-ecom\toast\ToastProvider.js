// ToastProvider.js
import React from 'react';
import Toast, { BaseToast, ErrorToast } from 'react-native-toast-message';

export const ToastProvider = () => {
  return <Toast config={toastConfig} />;
};

// ===== Reusable Toast Functions =====
export const showSuccessToast = (message, title = 'Success') => {
  Toast.show({
    type: 'success',
    text1: title,
    text2: message,
    position: 'top',
    visibilityTime: 3000,
    topOffset: 60,
  });
};

export const showErrorToast = (message, title = 'Error') => {
  Toast.show({
    type: 'error',
    text1: title,
    text2: message,
    position: 'top',
    visibilityTime: 3000,
    topOffset: 60,
  });
};

export const showInfoToast = (message, title = 'Info') => {
  Toast.show({
    type: 'info',
    text1: title,
    text2: message,
    position: 'top',
    visibilityTime: 3000,
    topOffset: 60,
  });
};

// ===== Custom Toast Design =====
const toastConfig = {
  success: (props) => (
    <BaseToast
      {...props}
      style={{
        borderLeftColor: '#4CAF50',
        backgroundColor: '#e6ffe6',
        borderRadius: 10,
        elevation: 5,
      }}
      contentContainerStyle={{ paddingHorizontal: 15 }}
      text1Style={{
        fontSize: 16,
        fontWeight: '600',
        color: '#2e7d32',
      }}
      text2Style={{
        fontSize: 14,
        color: '#1b5e20',
      }}
    />
  ),
  error: (props) => (
    <ErrorToast
      {...props}
      style={{
        borderLeftColor: '#f44336',
        backgroundColor: '#ffe6e6',
        borderRadius: 10,
        elevation: 5,
      }}
      text1Style={{
        fontSize: 16,
        fontWeight: '600',
        color: '#b71c1c',
      }}
      text2Style={{
        fontSize: 14,
        color: '#b71c1c',
      }}
    />
  ),
  info: (props) => (
    <BaseToast
      {...props}
      style={{
        borderLeftColor: '#2196f3',
        backgroundColor: '#e3f2fd',
        borderRadius: 10,
        elevation: 5,
      }}
      contentContainerStyle={{ paddingHorizontal: 15 }}
      text1Style={{
        fontSize: 16,
        fontWeight: '600',
        color: '#0d47a1',
      }}
      text2Style={{
        fontSize: 14,
        color: '#1565c0',
      }}
    />
  ),
};
