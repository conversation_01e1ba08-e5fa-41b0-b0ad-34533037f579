import {
  View,
  Text,
  StyleSheet,
  FlatList,
  Image,
  TouchableOpacity,
  ActivityIndicator,
  Alert,
} from "react-native";
import { SafeAreaView } from "react-native-safe-area-context";
import { MaterialIcons } from "@expo/vector-icons";
import axios from "axios";
import { BaseURL } from "@/constants/ApiEndpoint";
import { useAuth } from "@/context/AuthContext";
import { useEffect, useState } from "react";

export default function CartScreen({ navigation }) {
  const { isAuthenticated, accessToken } = useAuth();
  const [cartItems, setCartItems] = useState([]);
  const [subtotal, setSubtotal] = useState(0);
  const shipping = 10;
  const total = subtotal + shipping;
  const [loading, setLoading] = useState(true);
  const [updating, setUpdating] = useState(false);

  const fetchCartItems = async () => {
    try {
      const response = await axios.get(`${BaseURL}/api/v1/orders/cart/`, {
        headers: { Authorization: `Bearer ${accessToken}` },
      });
      
      const cartData = response?.data;
      setCartItems(cartData?.items || []);
      setSubtotal(parseFloat(cartData?.subtotal || 0));
      setLoading(false);
    } catch (error) {
      console.error("Error fetching cart items:", error);
      Alert.alert("Error", "Failed to load your cart. Please try again.");
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    if (isAuthenticated) {
      fetchCartItems();
    }
  }, [isAuthenticated, cartItems]);

  const handleAddOrRemoveToCart = async (itemId:any, action:any) => {
    if (updating) return;
    
    setUpdating(true);
    try {
      const response = await axios.put(
        `${BaseURL}/api/v1/orders/cart/update-item/`,
        {
          item_id: itemId,
          action,
        },
        {
          headers: { Authorization: `Bearer ${accessToken}` },
        }
      );

      if (response.data) {
        // Update cart items locally
        if (action === "delete") {
          setCartItems(cartItems.filter(item => item.id !== itemId));
          // Recalculate subtotal
          const newSubtotal = cartItems
            .filter(item => item.id !== itemId)
            .reduce((sum, item) => sum + parseFloat(item.product.price) * item.quantity, 0);
          setSubtotal(newSubtotal);
        } else {
          const updatedItems = cartItems.map(item => {
            if (item.id === itemId) {
              const newQuantity = 
                action === "add" ? item.quantity + 1 : Math.max(1, item.quantity - 1);
              
              // Update subtotal
              const priceDifference = parseFloat(item.product.price) * (newQuantity - item.quantity);
              setSubtotal(prevSubtotal => prevSubtotal + priceDifference);
              
              return {
                ...item,
                quantity: newQuantity,
              };
            }
            return item;
          });
          setCartItems(updatedItems);
        }
      }
    } catch (error) {
      console.error("Error updating cart:", error);
      Alert.alert("Error", "Failed to update your cart. Please try again.");
    } finally {
      setUpdating(false);
    }
  };

  

  
    if (!isAuthenticated) {
      return (
        <SafeAreaView style={styles.container}>
          <View style={styles.header}>
            <Text style={styles.title}>My Cart</Text>
          </View>
          <View style={styles.authRequired}>
            <MaterialIcons name="lock" size={48} color="#666" />
            <Text style={styles.authTitle}>Authentication Required</Text>
            <Text style={styles.authText}>Please login to view your orders</Text>
            <TouchableOpacity
              style={styles.loginButton}
              onPress={() => navigation.navigate('Auth', {
                returnTo: 'Cart'
              })}
            >
              <Text style={styles.loginButtonText}>Login</Text>
            </TouchableOpacity>
          </View>
        </SafeAreaView>
      );
    }

  const renderItem = ({ item }) => (
    <View style={styles.cartItem}>
      <Image source={{ uri: item.product.images.image }} style={styles.itemImage} />
      <View style={styles.itemDetails}>
        <Text style={styles.itemName} numberOfLines={2}>
          {item.product.name}
        </Text>
        <Text style={styles.itemPrice}>
        ₹{parseFloat(item.product.price).toFixed(2)}
        </Text>
        <View style={styles.quantityContainer}>
          <TouchableOpacity 
            style={styles.quantityButton}
            onPress={() => handleAddOrRemoveToCart(item.id, "remove")}
            disabled={item.quantity <= 1 || updating}
          >
            <MaterialIcons name="remove" size={20} color={item.quantity <= 1 ? "#ccc" : "#666"} />
          </TouchableOpacity>
          <Text style={styles.quantity}>{item.quantity}</Text>
          <TouchableOpacity 
            style={styles.quantityButton}
            onPress={() => handleAddOrRemoveToCart(item.id, "add")}
            disabled={updating}
          >
            <MaterialIcons name="add" size={20} color="#666" />
          </TouchableOpacity>
        </View>
      </View>
      <TouchableOpacity 
        style={styles.removeButton}
        onPress={() => handleAddOrRemoveToCart(item.id, "delete")}
        disabled={updating}
      >
        <MaterialIcons name="delete-outline" size={24} color="#666" />
      </TouchableOpacity>
    </View>
  );

  return (
    <SafeAreaView style={styles.container}>
      <View style={styles.header}>
        <Text style={styles.title}>Shopping Cart</Text>
      </View>
      {cartItems.length === 0 ? (
        <View style={styles.emptyCartContainer}>
          <MaterialIcons name="shopping-cart" size={64} color="#d1d5db" />
          <Text style={styles.emptyCartText}>Your cart is empty</Text>
          <TouchableOpacity
            style={styles.continueShoppingButton}
            onPress={() => navigation.navigate("HomeTab")}
          >
            <Text style={styles.continueShoppingText}>Continue Shopping</Text>
          </TouchableOpacity>
        </View>
      ) : (
        <>
          <FlatList
            data={cartItems}
            renderItem={renderItem}
            keyExtractor={(item) => item.id.toString()}
            contentContainerStyle={styles.cartList}
             ListEmptyComponent={
                        <View style={styles.emptyState}>
                          <Text style={styles.emptyStateText}>Your wishlist is empty</Text>
                        </View>
                      }
          />
          <View style={styles.summary}>
            <View style={styles.summaryRow}>
              <Text style={styles.summaryLabel}>Subtotal</Text>
              <Text style={styles.summaryValue}>₹{subtotal.toFixed(2)}</Text>
            </View>
            <View style={styles.summaryRow}>
              <Text style={styles.summaryLabel}>Shipping</Text>
              <Text style={styles.summaryValue}>₹{shipping.toFixed(2)}</Text>
            </View>
            <View style={[styles.summaryRow, styles.totalRow]}>
              <Text style={styles.totalLabel}>Total</Text>
              <Text style={styles.totalValue}>₹{total.toFixed(2)}</Text>
            </View>
            <TouchableOpacity
              style={styles.checkoutButton}
              onPress={() => navigation.navigate("Checkout")}
              disabled={updating}
            >
              <Text style={styles.checkoutButtonText}>
                {updating ? "Updating..." : "Proceed to Checkout"}
              </Text>
            </TouchableOpacity>
          </View>
        </>
      )}
    </SafeAreaView>
  );
}

const styles = StyleSheet.create({
  container: { flex: 1, backgroundColor: "#ffffff" },
  header: { padding: 16, borderBottomWidth: 1, borderBottomColor: "#f3f4f6" },
  title: { fontSize: 24, fontWeight: "bold" },
  cartList: { padding: 16 },
  cartItem: {
    flexDirection: "row",
    padding: 12,
    marginBottom: 16,
    backgroundColor: "#fff",
    borderRadius: 12,
    shadowColor: "#000",
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 8,
    elevation: 3,
  },
  loadingContainer: {
    flex: 1,
    alignItems: "center",
    justifyContent: "center",
  },
  authRequired: {
    flex: 1,
    alignItems: 'center',
    justifyContent: 'center',
    padding: 16,
  },
  authTitle: {
    fontSize: 20,
    fontWeight: 'bold',
    marginTop: 16,
    marginBottom: 8,
  },
  emptyState: {
    flex: 1,
    alignItems: "center",
    justifyContent: "center",
    paddingVertical: 32,
  },
  emptyStateText: {
    fontSize: 16,
    color: "#666",
  },
  authText: {
    fontSize: 16,
    color: '#666',
    textAlign: 'center',
    marginBottom: 24,
  },
  loginButton: {
    backgroundColor: '#2563EB',
    paddingHorizontal: 32,
    paddingVertical: 12,
    borderRadius: 8,
  },
  loginButtonText: {
    color: 'white',
    fontSize: 16,
    fontWeight: '600',
  },
  emptyCartContainer: {
    flex: 1,
    alignItems: "center",
    justifyContent: "center",
    padding: 20,
  },
  emptyCartText: {
    fontSize: 18,
    color: "#666",
    marginTop: 16,
    marginBottom: 24,
  },
  continueShoppingButton: {
    backgroundColor: "#2563EB",
    paddingVertical: 12,
    paddingHorizontal: 20,
    borderRadius: 8,
  },
  continueShoppingText: {
    color: "white",
    fontSize: 16,
    fontWeight: "500",
  },
  itemImage: { width: 80, height: 80, borderRadius: 8 },
  itemDetails: { flex: 1, marginLeft: 12 },
  itemName: { fontSize: 16, fontWeight: "600", marginBottom: 4 },
  itemPrice: { fontSize: 16, color: "#2563EB", fontWeight: "600" },
  quantityContainer: {
    flexDirection: "row",
    alignItems: "center",
    marginTop: 8,
  },
  quantityButton: { padding: 4, backgroundColor: "#f3f4f6", borderRadius: 6 },
  quantity: { marginHorizontal: 16, fontSize: 16, fontWeight: "600" },
  removeButton: { padding: 4 },
  summary: {
    padding: 16,
    backgroundColor: "#fff",
    borderTopWidth: 1,
    borderTopColor: "#f3f4f6",
  },
  summaryRow: {
    flexDirection: "row",
    justifyContent: "space-between",
    marginBottom: 8,
  },
  summaryLabel: { fontSize: 16, color: "#666" },
  summaryValue: { fontSize: 16, fontWeight: "600" },
  totalRow: {
    marginTop: 8,
    paddingTop: 8,
    borderTopWidth: 1,
    borderTopColor: "#f3f4f6",
  },
  totalLabel: { fontSize: 18, fontWeight: "bold" },
  totalValue: { fontSize: 18, fontWeight: "bold", color: "#2563EB" },
  checkoutButton: {
    backgroundColor: "#2563EB",
    padding: 16,
    borderRadius: 12,
    alignItems: "center",
    marginTop: 16,
  },
  checkoutButtonText: { color: "white", fontSize: 16, fontWeight: "600" },
});