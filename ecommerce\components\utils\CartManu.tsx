"use client";

import { MAIN_URL, UPDATE_CART, USER_CART } from "../../constant/urls";
import useApi from "../../hooks/useApi";
import { Minus, Plus, Trash2 } from "lucide-react";
import Link from "next/link";
import { useEffect, useMemo, useState } from "react";
import AddOrRemoveBtn from "./AddOrRemoveBtn";
import { CartMenuLoading } from "../ui/loading/CartMenuLoading";
import { useSession } from "next-auth/react";

const CartMenu = () => {
  // Use a single instance of useApi to maintain consistent state
  const {
    read,
    update,
    loading,
    error
  }: any = useApi(MAIN_URL);

  const [data, setData] = useState<any>({});
  const { status } = useSession();

  const totalPayment = useMemo(() => {
    if (!Array.isArray(data?.items) || data?.items.length === 0) {
      return 0;
    }
    return data?.items
      .reduce((total: number, item: any) => {
        const itemPrice = parseFloat(item.product.price);
        return total + itemPrice * item.quantity;
      }, 0)
      .toFixed(2);
  }, [data?.items]);

  const getUserCart = async () => {
    try {
      const response = await read(USER_CART);
      // Always update data with the response, even if empty
      setData(response || { items: [] });
      console.log("Cart data fetched:", response);
    } catch (error) {
      console.error("Error fetching cart:", error);
      setData({ items: [] });
    }
  };

  // Fetch cart when component mounts and when authentication status changes
  useEffect(() => {
    if (status === "authenticated") {
      getUserCart();
    }
  }, [status]);

  const handleAddOrRemoveToCart = async (itemId: number, action: string) => {
    try {
      const res = await update(UPDATE_CART, {
        item_id: itemId,
        action,
      });

      if (res) {
        // After successful update, refresh the cart data
        getUserCart();
      }
    } catch (error) {
      console.error("Error updating cart:", error);
    }
  };

  if (loading) {
    return <CartMenuLoading />;
  }
  return (
    <div className="bg-white w-full md:min-w-[350px] p-4 md:p-6 rounded-lg shadow-xl border border-gray-200 mx-auto md:mx-0 max-w-[95vw] md:max-w-md">
      <h2 className="text-xl md:text-2xl font-bold mb-4 text-theme-text-primary">Your shopping cart</h2>
      {Array.isArray(data?.items) && data.items.length > 0 ? (
        <>
          <div className="w-full overflow-x-auto">
            <table className="w-full min-w-[300px]">
              <thead>
                <tr className="border-b border-gray-300">
                  <th className="p-2 text-left text-sm md:text-base font-semibold text-theme-text-primary">Product</th>
                  <th className="p-2 text-center text-sm md:text-base font-semibold text-theme-text-primary">Qty</th>
                  <th className="p-2 text-right text-sm md:text-base font-semibold text-theme-text-primary">Price</th>
                </tr>
              </thead>
              <tbody>
                {data.items.map((item: any) => (
                  <tr key={item.id} className="border-b border-gray-200 hover:bg-gray-50">
                    <td className="p-2">
                      <span className="text-sm md:text-base line-clamp-2 text-theme-text-primary font-medium">{item.product.name}</span>
                    </td>
                    <td className="p-2 text-center">
                      <AddOrRemoveBtn
                        quantity={item.quantity}
                        itemId={item.id}
                        data={data}
                        handleAddOrRemoveToCart={handleAddOrRemoveToCart}
                      />
                    </td>
                    <td className="p-2 text-right">
                      <span className="text-sm md:text-base font-semibold text-theme-accent-primary">₹{item.product.price * item.quantity}</span>
                    </td>
                  </tr>
                ))}
              </tbody>
            </table>
          </div>
          <div className="mt-4 border-t border-gray-300 pt-4">
            <div className="my-3 md:my-4 flex justify-between items-center">
              <span className="font-bold text-sm md:text-base text-theme-text-primary">Total payment</span>
              <span className="text-xl md:text-2xl font-bold text-theme-accent-primary">
                ₹{Number(totalPayment).toLocaleString()}
              </span>
            </div>
            <div className="w-full flex justify-center">
              <Link
                href={"/cart"}
                className="bg-theme-accent-primary text-white mx-auto py-2 px-4 rounded-md text-sm md:text-base hover:bg-theme-accent-hover transition-colors shadow-md"
              >
                See your cart
              </Link>
            </div>
          </div>
        </>
      ) : (
        <div className="py-6 text-center">
          <p className="text-theme-text-primary mb-4">Your cart is empty.</p>
          <Link
            href={"/shop"}
            className="bg-theme-accent-primary text-white py-2 px-4 rounded-md text-sm md:text-base hover:bg-theme-accent-hover transition-colors shadow-md"
          >
            Continue Shopping
          </Link>
        </div>
      )}
    </div>
  );
};

export default CartMenu;
