<!DOCTYPE html>
<html>
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Security Alert - {{ event_type|title }}</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            line-height: 1.6;
            color: #333;
            max-width: 600px;
            margin: 0 auto;
            padding: 20px;
            background-color: #f5f5f5;
        }
        .container {
            background-color: #ffffff;
            border-radius: 8px;
            padding: 30px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        .header {
            text-align: center;
            margin-bottom: 30px;
            border-bottom: 2px solid #e74c3c;
            padding-bottom: 20px;
        }
        .priority-high {
            background-color: #e74c3c;
            color: white;
            padding: 8px 16px;
            border-radius: 20px;
            font-weight: bold;
            display: inline-block;
            margin-bottom: 15px;
        }
        .priority-medium {
            background-color: #f39c12;
            color: white;
            padding: 8px 16px;
            border-radius: 20px;
            font-weight: bold;
            display: inline-block;
            margin-bottom: 15px;
        }
        .alert-icon {
            font-size: 48px;
            color: #e74c3c;
            margin-bottom: 10px;
        }
        h1 {
            color: #2c3e50;
            margin: 0;
            font-size: 24px;
        }
        .event-details {
            background-color: #f8f9fa;
            border-left: 4px solid #e74c3c;
            padding: 20px;
            margin: 20px 0;
            border-radius: 0 5px 5px 0;
        }
        .detail-row {
            display: flex;
            justify-content: space-between;
            margin-bottom: 10px;
            padding: 8px 0;
            border-bottom: 1px solid #ecf0f1;
        }
        .detail-label {
            font-weight: bold;
            color: #34495e;
            min-width: 120px;
        }
        .detail-value {
            color: #2c3e50;
            word-break: break-all;
        }
        .timestamp {
            background-color: #ecf0f1;
            padding: 15px;
            border-radius: 5px;
            text-align: center;
            margin: 20px 0;
            font-family: monospace;
            color: #7f8c8d;
        }
        .action-section {
            background-color: #e8f5e8;
            border: 1px solid #27ae60;
            border-radius: 5px;
            padding: 15px;
            margin: 20px 0;
        }
        .action-title {
            color: #27ae60;
            font-weight: bold;
            margin-bottom: 10px;
        }
        .footer {
            margin-top: 30px;
            padding-top: 20px;
            border-top: 1px solid #ecf0f1;
            font-size: 12px;
            color: #7f8c8d;
            text-align: center;
        }
        .logo {
            max-width: 150px;
            margin-bottom: 15px;
        }
        .urgent-notice {
            background-color: #fff3cd;
            border: 1px solid #ffeaa7;
            border-radius: 5px;
            padding: 15px;
            margin: 20px 0;
            color: #856404;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <div class="alert-icon">⚠️</div>
            <div class="priority-{{ priority|lower }}">{{ priority }} PRIORITY</div>
            <h1>Security Alert</h1>
            <p style="margin: 10px 0 0 0; color: #7f8c8d;">{{ site_name }} Security Monitoring System</p>
        </div>
        
        <div class="event-details">
            <h3 style="margin-top: 0; color: #e74c3c;">Security Event Detected</h3>
            
            <div class="detail-row">
                <span class="detail-label">Event Type:</span>
                <span class="detail-value">{{ event_type|title }}</span>
            </div>
            
            <div class="detail-row">
                <span class="detail-label">IP Address:</span>
                <span class="detail-value">{{ ip_address }}</span>
            </div>
            
            <div class="detail-row">
                <span class="detail-label">User:</span>
                <span class="detail-value">{{ user_email }}</span>
            </div>
            
            {% if event_data %}
                {% for key, value in event_data.items %}
                <div class="detail-row">
                    <span class="detail-label">{{ key|title }}:</span>
                    <span class="detail-value">{{ value }}</span>
                </div>
                {% endfor %}
            {% endif %}
        </div>
        
        <div class="timestamp">
            <strong>Event Timestamp:</strong> {{ timestamp|date:"F d, Y, g:i A T" }}
        </div>
        
        {% if event_data.action_taken %}
        <div class="action-section">
            <div class="action-title">🛡️ Action Taken</div>
            <p>{{ event_data.action_taken }}</p>
        </div>
        {% endif %}
        
        <div class="urgent-notice">
            <strong>⚡ Immediate Action Required:</strong>
            <ul style="margin: 10px 0;">
                <li>Review the security logs for additional details</li>
                <li>Verify if this activity is legitimate</li>
                <li>Take appropriate action if suspicious activity is confirmed</li>
                <li>Monitor for similar patterns in the coming hours</li>
            </ul>
        </div>
        
        <div class="footer">
            <p>This is an automated security alert from {{ site_name }}.</p>
            <p>Generated at {{ timestamp|date:"F d, Y, g:i A T" }}</p>
            <p>If you believe this alert was triggered in error, please review your security monitoring configuration.</p>
        </div>
    </div>
</body>
</html>
