/**
 * Utility functions for handling image URLs with MinIO integration
 */

import { MAIN_URL } from "@/constant/urls";

/**
 * Get the correct image URL based on the source path
 * This function handles different image sources:
 * - Full URLs (starting with http/https)
 * - MinIO URLs (media paths)
 * - Local assets
 * - Relative paths
 * 
 * @param src The image source path
 * @param fallbackImage Optional fallback image if src is invalid
 * @returns The complete image URL
 */
export function getImageUrl(src: string | undefined | null, fallbackImage: string = '/assets/products/product-placeholder.svg'): string {
  // If src is undefined, null, or empty, return fallback image
  if (!src) {
    return fallbackImage;
  }

  // If it's already a full URL, return it as is
  if (src.startsWith('http://') || src.startsWith('https://')) {
    return src;
  }

  // If it's a local asset, return it as is
  if (src.startsWith('/assets/')) {
    return src;
  }

  // Get MinIO URL from environment variables
  const minioUrl = process.env.NEXT_PUBLIC_MINIO_URL;

  // If it's a media path and MinIO URL is configured, use MinIO
  if ((src.startsWith('/media/') || src.startsWith('media/')) && minioUrl) {
    // Clean the path (remove leading slash if present)
    const cleanPath = src.startsWith('/') ? src.substring(1) : src;
    return `${minioUrl}/${cleanPath}`;
  }

  // Otherwise, use the main API URL
  if (MAIN_URL) {
    // Ensure the path is properly formatted
    const path = src.startsWith('/') ? src : `/${src}`;
    return `${MAIN_URL}${path}`;
  }

  // If all else fails, return the fallback image
  return fallbackImage;
}

/**
 * Get the correct image URL for a product image
 * This is a specialized version of getImageUrl for product images
 * 
 * @param product The product object
 * @param index The index of the image to get (defaults to 0 for primary image)
 * @param fallbackImage Optional fallback image if product has no images
 * @returns The complete image URL
 */
export function getProductImageUrl(
  product: any, 
  index: number = 0, 
  fallbackImage: string = '/assets/products/product-placeholder.svg'
): string {
  if (!product || !product.images || product.images.length === 0) {
    return fallbackImage;
  }

  // Get the image at the specified index, or the first image if index is out of bounds
  const imageObj = product.images[index < product.images.length ? index : 0];
  
  // If the image object exists and has an image property, get its URL
  if (imageObj && imageObj.image) {
    return getImageUrl(imageObj.image, fallbackImage);
  }

  return fallbackImage;
}
