#!/usr/bin/env python3
"""
Debug script to test database routing behavior for category creation.
This script will help identify why categories might be going to replica instead of primary.
"""

import os
import django

# Setup Django environment
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'backend.settings')
django.setup()

from django.conf import settings
from django.db import connections
from products.models import Category
from backend.db_router import DatabaseRouter


def check_database_configuration():
    """Check current database configuration"""
    print("🔍 DATABASE CONFIGURATION")
    print("=" * 50)
    
    print(f"Total databases configured: {len(settings.DATABASES)}")
    for db_name, db_config in settings.DATABASES.items():
        print(f"\n{db_name}:")
        print(f"  Host: {db_config.get('HOST', 'Not specified')}")
        print(f"  Name: {db_config.get('NAME', 'Not specified')}")
        print(f"  Engine: {db_config.get('ENGINE', 'Not specified')}")
        
        if db_name == 'default':
            print(f"  Role: PRIMARY (read/write)")
        else:
            print(f"  Role: REPLICA (read-only)")
    
    # Check if DATABASE_ROUTERS is configured
    routers = getattr(settings, 'DATABASE_ROUTERS', [])
    print(f"\nDatabase routers configured: {routers}")


def test_database_router():
    """Test the database router behavior"""
    print("\n🧪 DATABASE ROUTER TESTING")
    print("=" * 50)
    
    router = DatabaseRouter()
    
    print(f"Write database: {router.write_database}")
    print(f"Read databases: {router.read_databases}")
    
    # Test routing for Category model
    write_db = router.db_for_write(Category)
    read_db = router.db_for_read(Category)
    
    print(f"\nCategory model routing:")
    print(f"  Write operations → {write_db}")
    print(f"  Read operations → {read_db}")
    
    # Test migration permissions
    for db_name in settings.DATABASES.keys():
        can_migrate = router.allow_migrate(db_name, 'products', 'Category')
        print(f"  Migrations allowed on {db_name}: {can_migrate}")


def test_category_creation():
    """Test actual category creation and see which database it goes to"""
    print("\n📝 CATEGORY CREATION TEST")
    print("=" * 50)
    
    # Create a test category
    test_category_name = "TEST_ROUTING_CATEGORY"
    
    # First, clean up any existing test category
    Category.objects.filter(name=test_category_name).delete()
    
    print(f"Creating category: {test_category_name}")
    
    # Create the category
    category = Category.objects.create(name=test_category_name)
    
    print(f"Category created with ID: {category.id}")
    print(f"Category database state: {category._state.db}")
    
    # Check which databases have this category
    print("\nChecking category presence in databases:")
    
    for db_name in settings.DATABASES.keys():
        try:
            count = Category.objects.using(db_name).filter(name=test_category_name).count()
            print(f"  {db_name}: {count} records")
            
            if count > 0:
                cat = Category.objects.using(db_name).get(name=test_category_name)
                print(f"    ID: {cat.id}, Name: {cat.name}")
        except Exception as e:
            print(f"  {db_name}: ERROR - {e}")
    
    # Clean up
    Category.objects.filter(name=test_category_name).delete()
    print(f"\nTest category cleaned up.")


def check_database_connections():
    """Check if all databases are accessible"""
    print("\n🔗 DATABASE CONNECTION TEST")
    print("=" * 50)
    
    for db_name in settings.DATABASES.keys():
        try:
            connection = connections[db_name]
            with connection.cursor() as cursor:
                cursor.execute("SELECT 1")
                result = cursor.fetchone()
            print(f"  {db_name}: ✅ Connected (result: {result})")
        except Exception as e:
            print(f"  {db_name}: ❌ Connection failed - {e}")


def check_environment_variables():
    """Check relevant environment variables"""
    print("\n🌍 ENVIRONMENT VARIABLES")
    print("=" * 50)
    
    env_vars = [
        'DB_HOST', 'DB_PORT', 'DB_NAME', 'DB_USER',
        'DB_READ_HOST', 'DB_READ_PORT', 'DB_READ_NAME', 'DB_READ_USER',
        'DEVELOPMENT', 'TESTING'
    ]
    
    for var in env_vars:
        value = os.getenv(var, 'Not set')
        # Hide passwords for security
        if 'PASSWORD' in var:
            value = '***' if value != 'Not set' else 'Not set'
        print(f"  {var}: {value}")


def main():
    """Run all diagnostic tests"""
    print("🔧 DATABASE ROUTING DIAGNOSTIC TOOL")
    print("=" * 60)
    
    check_environment_variables()
    check_database_configuration()
    check_database_connections()
    test_database_router()
    test_category_creation()
    
    print("\n✅ DIAGNOSTIC COMPLETED!")
    print("\nIf categories are being created in replica instead of primary:")
    print("1. Check if DATABASE_ROUTERS is properly configured")
    print("2. Verify that db_for_write() returns 'default'")
    print("3. Check if there are any .using() calls in your code")
    print("4. Ensure no custom managers are overriding database routing")


if __name__ == "__main__":
    main()
