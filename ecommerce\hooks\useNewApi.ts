import { useState, useCallback } from 'react';
import axios, { AxiosRequestConfig, AxiosResponse, AxiosError } from 'axios';

// Define the types for our hook's parameters and return values
interface UseApiOptions {
  baseURL?: string;
  token?: string;
}

interface ApiResponse<T> {
  data: T | null;
  error: string | null;
  loading: boolean;
}

/**
 * Custom hook for making API requests with flexible configuration
 *
 * @param options Configuration options for the API hook
 * @returns An object with methods to make different types of requests
 */
export const useNewApi = (options: UseApiOptions = {}) => {
  const { baseURL, token } = options;

  // State to track the current request's status
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);

  /**
   * Prepares default Axios configuration with optional token
   *
   * @param config Additional Axios request configuration
   * @returns Fully configured AxiosRequestConfig
   */
  const prepareConfig = useCallback((config: AxiosRequestConfig = {}) => {
    // Make sure baseURL doesn't end with null
    const cleanBaseURL = baseURL ? baseURL.replace(/null$/, '') : '';

    const defaultConfig: AxiosRequestConfig = {
      baseURL: cleanBaseURL,
      headers: {
        ...config.headers,
        ...(token && { Authorization: `Bearer ${token}` }),
      },
    };

    return { ...defaultConfig, ...config };
  }, [baseURL, token]);

  /**
   * Generic method to make API requests with error handling
   *
   * @param requestFn Axios request function
   * @returns Promise resolving to the API response
   */
  const makeRequest = useCallback(async <T>(
    requestFn: (config: AxiosRequestConfig) => Promise<AxiosResponse<T>>
  ): Promise<ApiResponse<T>> => {
    setLoading(true);
    setError(null);

    try {
      const config = prepareConfig();
      const response = await requestFn(config);
      return { data: response.data, error: null, loading: false };
    } catch (err) {
      const axiosError = err as AxiosError;
      const errorMessage = axiosError.response?.data
        ? JSON.stringify(axiosError.response.data)
        : axiosError.message;

      setError(errorMessage);
      return { data: null, error: errorMessage, loading: false };
    } finally {
      setLoading(false);
    }
  }, [prepareConfig]);

  /**
   * Method to make GET requests
   *
   * @param url The endpoint URL
   * @param config Optional Axios configuration
   * @returns Promise with API response
   */
  const get = useCallback(<T>(url: string, config?: AxiosRequestConfig) => {
    return makeRequest<T>((axiosConfig) =>
      axios.get(url, { ...axiosConfig, ...config })
    );
  }, [makeRequest]);

  /**
   * Method to make POST requests
   *
   * @param url The endpoint URL
   * @param data Request payload
   * @param config Optional Axios configuration
   * @returns Promise with API response
   */
  const post = useCallback(<T>(
    url: string,
    data?: any,
    config?: AxiosRequestConfig
  ) => {
    return makeRequest<T>((axiosConfig) =>
      axios.post(url, data, { ...axiosConfig, ...config })
    );
  }, [makeRequest]);

  /**
   * Method to make PUT requests
   *
   * @param url The endpoint URL
   * @param data Request payload
   * @param config Optional Axios configuration
   * @returns Promise with API response
   */
  const put = useCallback(<T>(
    url: string,
    data?: any,
    config?: AxiosRequestConfig
  ) => {
    return makeRequest<T>((axiosConfig) =>
      axios.put(url, data, { ...axiosConfig, ...config })
    );
  }, [makeRequest]);

  /**
   * Method to make PATCH requests
   *
   * @param url The endpoint URL
   * @param data Request payload (partial update)
   * @param config Optional Axios configuration
   * @returns Promise with API response
   */
  const patch = useCallback(<T>(
    url: string,
    data?: any,
    config?: AxiosRequestConfig
  ) => {
    return makeRequest<T>((axiosConfig) =>
      axios.patch(url, data, { ...axiosConfig, ...config })
    );
  }, [makeRequest]);

  /**
   * Method to make DELETE requests
   *
   * @param url The endpoint URL
   * @param config Optional Axios configuration
   * @returns Promise with API response
   */
  const del = useCallback(<T>(
    url: string,
    config?: AxiosRequestConfig
  ) => {
    return makeRequest<T>((axiosConfig) =>
      axios.delete(url, { ...axiosConfig, ...config })
    );
  }, [makeRequest]);

  /**
   * Method to make file upload requests
   *
   * @param url The endpoint URL
   * @param file File to upload
   * @param fieldName Form field name for the file
   * @param additionalData Additional form data
   * @returns Promise with API response
   */
  const uploadFile = useCallback(<T>(
    url: string,
    file: File,
    fieldName: string = 'file',
    additionalData: Record<string, any> = {}
  ) => {
    const formData = new FormData();
    formData.append(fieldName, file);

    // Append additional data if provided
    Object.entries(additionalData).forEach(([key, value]) => {
      formData.append(key, value);
    });

    return makeRequest<T>((axiosConfig) =>
      axios.post(url, formData, {
        ...axiosConfig,
        headers: {
          'Content-Type': 'multipart/form-data',
          ...axiosConfig.headers,
        },
      })
    );
  }, [makeRequest]);

  // Expose methods and loading/error states
  return {
    get,
    post,
    put,    // Added PUT method
    patch,  // Added PATCH method
    del,    // Added DELETE method (named 'del' to avoid reserved keyword conflict)
    uploadFile,
    loading,
    error,
  };
};



// Example usage:

// // Basic GET request
// const { get, loading, error } = useApi({
//   baseURL: 'https://api.example.com',
//   token: 'your-auth-token'
// });

// // Fetch user data
// const fetchUsers = async () => {
//   const response = await get<User[]>('/users');
//   if (response.data) {
//     // Handle successful response
//   }
//   if (response.error) {
//     // Handle error
//   }
// };

// // POST request
// const { post } = useApi();
// const createUser = async (userData) => {
//   const response = await post<User>('/users', userData);
// };

// // File upload
// const { uploadFile } = useApi({ token: 'your-token' });
// const handleFileUpload = async (file: File) => {
//   const response = await uploadFile('/upload', file, 'avatar', {
//     userId: '123'  // Additional form data
//   });
// };

// // PUT Request - Full Resource Update
// const updateUser = async (userId: string, userData: Partial<User>) => {
//   const response = await put<User>(`/users/${userId}`, userData);
//   // Replaces entire user resource with new data
// };

// // PATCH Request - Partial Update
// const partialUserUpdate = async (userId: string, updates: Partial<User>) => {
//   const response = await patch<User>(`/users/${userId}`, updates);
//   // Updates only the specified fields
// };

// // DELETE Request
// const deleteUser = async (userId: string) => {
//   const response = await del<void>(`/users/${userId}`);
//   // Removes the specified user resource
// };