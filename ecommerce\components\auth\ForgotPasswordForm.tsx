"use client";

import React, { useState } from 'react';
import { motion } from 'framer-motion';
import { AtSign, ArrowLeft, Mail, Shield } from 'lucide-react';
import { Formik, Form, Field, ErrorMessage, FormikHelpers } from 'formik';
import * as Yup from 'yup';
import Link from 'next/link';
import { useToast } from '../../hooks/use-toast';
import { validateEmail } from '../../utils/passwordValidation';
import AuthSpinner from '../ui/loading/AuthSpinner';
import useApi from '../../hooks/useApi';
import { MAIN_URL, FORGOT_PASSWORD } from '../../constant/urls';

interface ForgotPasswordFormValues {
  email: string;
}

const validationSchema: Yup.Schema<ForgotPasswordFormValues> = Yup.object({
  email: Yup.string()
    .email('Enter a valid email address')
    .required('Email is required'),
});

const ForgotPasswordForm: React.FC = () => {
  const [isSubmitted, setIsSubmitted] = useState(false);
  const [submittedEmail, setSubmittedEmail] = useState('');
  const { create, error } = useApi(MAIN_URL);
  const { toast } = useToast();

  const handleSubmit = async (
    values: ForgotPasswordFormValues,
    { setSubmitting }: FormikHelpers<ForgotPasswordFormValues>
  ) => {
    setSubmitting(true);
    
    try {
      const response = await create(FORGOT_PASSWORD, values);
      
      // Always show success message for security (no email enumeration)
      setSubmittedEmail(values.email);
      setIsSubmitted(true);
      
      toast({
        variant: "success",
        title: "Reset link sent",
        description: "If an account exists with this email, you'll receive a reset link shortly.",
      });
      
    } catch (err) {
      console.error('Forgot password error:', err);
      
      // Still show success for security, but log the error
      setSubmittedEmail(values.email);
      setIsSubmitted(true);
      
      toast({
        variant: "success",
        title: "Reset link sent",
        description: "If an account exists with this email, you'll receive a reset link shortly.",
      });
    } finally {
      setSubmitting(false);
    }
  };

  if (isSubmitted) {
    return (
      <motion.div
        initial={{ opacity: 0, y: 20 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ duration: 0.5 }}
        className="text-center space-y-6"
      >
        <div className="flex justify-center">
          <div className="p-4 bg-green-50 rounded-full">
            <Mail className="w-8 h-8 text-green-600" />
          </div>
        </div>
        
        <div className="space-y-2">
          <h2 className="text-2xl font-bold text-gray-800">
            Check your email
          </h2>
          <p className="text-gray-600">
            We've sent a password reset link to
          </p>
          <p className="font-medium text-gray-800">
            {submittedEmail}
          </p>
        </div>
        
        <div className="bg-blue-50 border border-blue-200 rounded-lg p-4 text-left">
          <div className="flex items-start gap-3">
            <Shield className="w-5 h-5 text-blue-600 mt-0.5" />
            <div className="space-y-2 text-sm text-blue-800">
              <p className="font-medium">Security Information:</p>
              <ul className="space-y-1 text-blue-700">
                <li>• The reset link will expire in 15 minutes</li>
                <li>• Check your spam folder if you don't see the email</li>
                <li>• The link can only be used once</li>
                <li>• If you didn't request this, you can safely ignore this email</li>
              </ul>
            </div>
          </div>
        </div>
        
        <div className="space-y-3">
          <p className="text-sm text-gray-600">
            Didn't receive the email?
          </p>
          <button
            onClick={() => {
              setIsSubmitted(false);
              setSubmittedEmail('');
            }}
            className="text-blue-600 hover:text-blue-800 font-medium text-sm transition-colors duration-300"
          >
            Try again
          </button>
        </div>
        
        <div className="pt-4">
          <Link
            href="/auth/login"
            className="inline-flex items-center gap-2 text-gray-600 hover:text-gray-800 transition-colors duration-300"
          >
            <ArrowLeft className="w-4 h-4" />
            Back to login
          </Link>
        </div>
      </motion.div>
    );
  }

  return (
    <motion.div
      initial={{ opacity: 0, y: 20 }}
      animate={{ opacity: 1, y: 0 }}
      transition={{ duration: 0.5 }}
      className="space-y-6"
    >
      <div className="text-center space-y-2">
        <h2 className="text-2xl font-bold text-gray-800">
          Forgot your password?
        </h2>
        <p className="text-gray-600">
          No worries! Enter your email address and we'll send you a link to reset your password.
        </p>
      </div>

      <Formik<ForgotPasswordFormValues>
        initialValues={{ email: '' }}
        validationSchema={validationSchema}
        onSubmit={handleSubmit}
      >
        {({ isSubmitting, values, setFieldError }) => (
          <Form className="space-y-6">
            <div>
              <label
                htmlFor="email"
                className="block mb-2 text-sm font-medium text-gray-700"
              >
                Email Address
              </label>
              <div className="relative">
                <span className="absolute inset-y-0 left-0 flex items-center p-1 pl-3 text-gray-400">
                  <AtSign className="w-5 h-5" />
                </span>
                <Field
                  type="email"
                  name="email"
                  className="pl-12 w-full bg-gray-50 text-gray-600 border border-gray-300 sm:text-sm rounded-lg focus:ring-2 focus:outline-none focus:ring-blue-300 focus:border-blue-500 p-2.5 py-3 px-4 transition-all duration-300"
                  placeholder="Enter your email address"
                  onBlur={(e: React.FocusEvent<HTMLInputElement>) => {
                    const emailValidation = validateEmail(e.target.value);
                    if (!emailValidation.isValid && e.target.value) {
                      setFieldError('email', emailValidation.error);
                    }
                  }}
                />
                <ErrorMessage
                  name="email"
                  component="p"
                  className="text-red-500 text-xs mt-1"
                />
              </div>
            </div>

            <motion.button
              type="submit"
              className="w-full text-white bg-blue-600 hover:bg-blue-700 focus:ring-4 focus:outline-none focus:ring-blue-300 font-medium rounded-lg text-sm px-5 py-3 text-center transition-all duration-300 shadow-md hover:shadow-lg flex justify-center items-center gap-2"
              disabled={isSubmitting}
              whileHover={{ scale: 1.02 }}
              whileTap={{ scale: 0.98 }}
            >
              {isSubmitting ? (
                <>
                  <AuthSpinner size="sm" color="border-t-white" />
                  <span>Sending reset link...</span>
                </>
              ) : (
                <>
                  <Mail className="w-4 h-4" />
                  <span>Send reset link</span>
                </>
              )}
            </motion.button>
          </Form>
        )}
      </Formik>

      <div className="text-center">
        <Link
          href="/auth/login"
          className="inline-flex items-center gap-2 text-gray-600 hover:text-gray-800 transition-colors duration-300"
        >
          <ArrowLeft className="w-4 h-4" />
          Back to login
        </Link>
      </div>
    </motion.div>
  );
};

export default ForgotPasswordForm;
