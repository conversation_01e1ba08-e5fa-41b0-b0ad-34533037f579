@tailwind base;
@tailwind components;
@tailwind utilities;

#root {
  width: 100%;
  max-width: 100%;
  margin: 0 auto;
  padding: 0;
  text-align: center;
}

.logo {
  height: 6em;
  padding: 1.5em;
  will-change: filter;
  transition: filter 300ms;
}
.logo:hover {
  filter: drop-shadow(0 0 2em #646cffaa);
}
.logo.react:hover {
  filter: drop-shadow(0 0 2em #61dafbaa);
}

@keyframes logo-spin {
  from {
    transform: rotate(0deg);
  }
  to {
    transform: rotate(360deg);
  }
}

@media (prefers-reduced-motion: no-preference) {
  a:nth-of-type(2) .logo {
    animation: logo-spin infinite 20s linear;
  }
}

.card {
  padding: 2em;
}

.read-the-docs {
  color: #888;
}

@layer base {
  :root {
    --background: 0 0% 100%;
    --foreground: 0 0% 3.9%;

    --card: 0 0% 100%;
    --card-foreground: 0 0% 3.9%;

    --popover: 0 0% 100%;
    --popover-foreground: 0 0% 3.9%;

    --primary: 0 0% 9%;
    --primary-foreground: 0 0% 98%;

    --secondary: 0 0% 96.1%;
    --secondary-foreground: 0 0% 9%;

    --muted: 0 0% 96.1%;
    --muted-foreground: 0 0% 45.1%;

    --accent: 0 0% 96.1%;
    --accent-foreground: 0 0% 9%;

    --destructive: 0 84.2% 60.2%;
    --destructive-foreground: 0 0% 98%;

    --border: 0 0% 89.8%;
    --input: 0 0% 89.8%;
    --ring: 0 0% 3.9%;

    --radius: 0.5rem;

    --chart-1: 12 76% 61%;
    --chart-2: 173 58% 39%;
    --chart-3: 197 37% 24%;
    --chart-4: 43 74% 66%;
    --chart-5: 27 87% 67%;

    /* Smart Home Theme Colors */
    --theme-header: 222 30% 16%; /* #1A1F36 Midnight Blue */
    --theme-footer: 222 30% 16%; /* #1A1F36 Midnight Blue */
    --theme-homepage: 0 0% 98%; /* #FAFAFA Pearl White */
    --theme-text-primary: 210 29% 24%; /* #2C3E50 Charcoal */
    --theme-text-secondary: 204 8% 76%; /* #BDC3C7 Light Gray */
    --theme-accent-primary: 145 63% 49%; /* #2ECC71 Emerald Green */
    --theme-accent-hover: 145 65% 42%; /* #27AE60 Darker Green */
    --theme-accent-secondary: 37 87% 69%; /* #F5C469 Soft Gold */
    --theme-out-of-stock: 6 78% 57%; /* #E74C3C Coral Red */
  }

  .dark {
    --background: 0 0% 3.9%;
    --foreground: 0 0% 98%;

    --card: 0 0% 3.9%;
    --card-foreground: 0 0% 98%;

    --popover: 0 0% 3.9%;
    --popover-foreground: 0 0% 98%;

    --primary: 0 0% 98%;
    --primary-foreground: 0 0% 9%;

    --secondary: 0 0% 14.9%;
    --secondary-foreground: 0 0% 98%;

    --muted: 0 0% 14.9%;
    --muted-foreground: 0 0% 63.9%;

    --accent: 0 0% 14.9%;
    --accent-foreground: 0 0% 98%;

    --destructive: 0 62.8% 30.6%;
    --destructive-foreground: 0 0% 98%;

    --border: 0 0% 14.9%;
    --input: 0 0% 14.9%;
    --ring: 0 0% 83.1%;
    --chart-1: 220 70% 50%;
    --chart-2: 160 60% 45%;
    --chart-3: 30 80% 55%;
    --chart-4: 280 65% 60%;
    --chart-5: 340 75% 55%;
  }
}

@layer base {
  * {
    @apply border-border;
  }

  body {
    @apply bg-background text-foreground w-full max-w-full overflow-x-hidden min-h-screen;
    text-rendering: optimizeLegibility;
    -webkit-font-smoothing: antialiased;
    -moz-osx-font-smoothing: grayscale;
  }

  html {
    @apply w-full max-w-full overflow-x-hidden scroll-smooth;
  }

  /* Responsive typography */
  h1 {
    @apply text-2xl xs:text-2xl sm:text-3xl md:text-4xl font-bold;
  }

  h2 {
    @apply text-xl xs:text-xl sm:text-2xl md:text-3xl font-bold;
  }

  h3 {
    @apply text-lg xs:text-lg sm:text-xl md:text-2xl font-semibold;
  }

  p {
    @apply text-sm xs:text-sm sm:text-base;
  }
}

/* Responsive utilities */
@layer utilities {
  .text-shadow-sm {
    text-shadow: 0 1px 2px rgba(0, 0, 0, 0.1);
  }

  .text-shadow {
    text-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
  }

  .text-shadow-md {
    text-shadow: 0 4px 8px rgba(0, 0, 0, 0.12);
  }

  .text-shadow-lg {
    text-shadow: 0 8px 16px rgba(0, 0, 0, 0.15);
  }

  .text-shadow-none {
    text-shadow: none;
  }

  /* Responsive padding utilities */
  .responsive-x-padding {
    @apply px-4 sm:px-6 md:px-8 lg:px-10;
  }

  .responsive-y-padding {
    @apply py-4 sm:py-6 md:py-8 lg:py-10;
  }

  /* Scrollbar utilities */
  .scrollbar-hide {
    -ms-overflow-style: none;  /* Internet Explorer 10+ */
    scrollbar-width: none;  /* Firefox */
  }

  .scrollbar-hide::-webkit-scrollbar {
    display: none;  /* Safari and Chrome */
  }

  /* Professional category navigation animations */
  .category-card-professional {
    position: relative;
    transition: all 0.5s cubic-bezier(0.4, 0, 0.2, 1);
  }

  .category-card-professional::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: linear-gradient(135deg, rgba(255,255,255,0.1) 0%, rgba(255,255,255,0.05) 100%);
    border-radius: inherit;
    opacity: 0;
    transition: opacity 0.3s ease;
  }

  .category-card-professional:hover::before {
    opacity: 1;
  }

  /* Smooth lift animation */
  .category-lift {
    transform: translateY(0) scale(1);
    transition: all 0.4s cubic-bezier(0.175, 0.885, 0.32, 1.275);
  }

  .category-lift:hover {
    transform: translateY(-8px) scale(1.02);
  }

  /* Professional shadow effects */
  .shadow-professional {
    box-shadow:
      0 4px 6px -1px rgba(0, 0, 0, 0.1),
      0 2px 4px -1px rgba(0, 0, 0, 0.06),
      0 0 0 1px rgba(255, 255, 255, 0.05) inset;
  }

  .shadow-professional-hover {
    box-shadow:
      0 20px 25px -5px rgba(0, 0, 0, 0.1),
      0 10px 10px -5px rgba(0, 0, 0, 0.04),
      0 0 0 1px rgba(255, 255, 255, 0.05) inset;
  }

  /* Gradient text effect */
  .text-gradient-professional {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
  }

  /* Professional skeleton animation */
  .skeleton-professional {
    background: linear-gradient(90deg, #f0f0f0 25%, #e0e0e0 50%, #f0f0f0 75%);
    background-size: 200% 100%;
    animation: shimmer-professional 2s infinite;
  }

  @keyframes shimmer-professional {
    0% {
      background-position: -200% 0;
    }
    100% {
      background-position: 200% 0;
    }
  }

  /* Smooth fade-in animation */
  .fade-in-professional {
    animation: fadeInProfessional 0.6s ease-out forwards;
  }

  @keyframes fadeInProfessional {
    from {
      opacity: 0;
      transform: translateY(20px);
    }
    to {
      opacity: 1;
      transform: translateY(0);
    }
  }
}

/* scroll bar style  */
/* Style the entire scrollbar */
::-webkit-scrollbar {
  width: 6px; /* Width of the scrollbar */
  height: 6px; /* Height for horizontal scrollbars */
}

/* Style the scrollbar track (background) */
::-webkit-scrollbar-track {
 display: none;
}

/* Style the scrollbar thumb (draggable handle) */
::-webkit-scrollbar-thumb {
  background: #888; /* Darker gray handle */
  border-radius: 10px; /* Rounded corners */
  border: 2px solid #f0f0f0; /* Adds padding and matches track background */
}

/* Hover effect on the scrollbar thumb */
::-webkit-scrollbar-thumb:hover {
  background: #555; /* Darker gray when hovered */
}


/* Original glass morphism - keeping for compatibility */
.glass-morphism-original {
  @apply bg-white/10 backdrop-blur-md border border-white/20;
}

.neo-button {
  @apply relative overflow-hidden transition-all duration-300
         before:absolute before:inset-0 before:bg-black/5
         before:transition-transform before:duration-300
         hover:before:scale-105 active:scale-95;
}

.text-balance {
  text-wrap: balance;
}

.slide-content {
  @apply absolute inset-0 flex flex-col items-center justify-center text-white;
  background: linear-gradient(to bottom, transparent 0%, rgba(0, 0, 0, 0.5) 100%);
}

/* Custom animations */
@keyframes float {
  0% {
    transform: translateY(0px);
  }
  50% {
    transform: translateY(-10px);
  }
  100% {
    transform: translateY(0px);
  }
}

@keyframes pulse-soft {
  0% {
    box-shadow: 0 0 0 0 rgba(99, 102, 241, 0.4);
  }
  70% {
    box-shadow: 0 0 0 10px rgba(99, 102, 241, 0);
  }
  100% {
    box-shadow: 0 0 0 0 rgba(99, 102, 241, 0);
  }
}

@keyframes shine {
  to {
    background-position: 200% center;
  }
}

.animate-float {
  animation: float 6s ease-in-out infinite;
}

.animate-pulse-soft {
  animation: pulse-soft 2s infinite;
}

.animate-shine {
  background: linear-gradient(to right, #f6f7f8 0%, #edeef1 20%, #f6f7f8 40%, #f6f7f8 100%);
  background-size: 200% auto;
  animation: shine 1.5s linear infinite;
}

/* Glass morphism */
.glass-morphism {
  background: rgba(255, 255, 255, 0.7);
  backdrop-filter: blur(10px);
  -webkit-backdrop-filter: blur(10px);
  border: 1px solid rgba(255, 255, 255, 0.18);
  box-shadow: 0 8px 32px 0 rgba(31, 38, 135, 0.07);
}

/* Gradient text */
.gradient-text {
  @apply bg-clip-text text-transparent bg-gradient-to-r from-indigo-600 to-violet-600;
}

/* Mobile Navigation Animations */
@keyframes slide-in-mobile {
  from {
    opacity: 0;
    transform: translateY(-20px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

@keyframes slide-out-mobile {
  from {
    opacity: 1;
    transform: translateY(0);
  }
  to {
    opacity: 0;
    transform: translateY(-20px);
  }
}

@keyframes mobile-menu-slide-down {
  from {
    transform: translateY(-100%);
    opacity: 0;
  }
  to {
    transform: translateY(0);
    opacity: 1;
  }
}

@keyframes mobile-menu-slide-up {
  from {
    transform: translateY(0);
    opacity: 1;
  }
  to {
    transform: translateY(-100%);
    opacity: 0;
  }
}

.animate-slide-in-mobile {
  animation: slide-in-mobile 0.4s ease-out forwards;
}

.animate-slide-out-mobile {
  animation: slide-out-mobile 0.3s ease-in forwards;
}

.animate-mobile-menu-slide-down {
  animation: mobile-menu-slide-down 0.3s ease-out forwards;
}

.animate-mobile-menu-slide-up {
  animation: mobile-menu-slide-up 0.3s ease-in forwards;
}

/* Enhanced mobile touch targets */
@media (max-width: 768px) {
  .mobile-touch-target {
    min-height: 44px;
    min-width: 44px;
  }

  .mobile-menu-item {
    padding: 16px;
    font-size: 16px;
    line-height: 1.5;
  }
}

/* Smooth transitions for mobile interactions */
.mobile-transition {
  transition: all 0.2s cubic-bezier(0.4, 0, 0.2, 1);
}

.mobile-transition:active {
  transform: scale(0.95);
}

/* Mobile-specific hover states (for devices that support hover) */
@media (hover: hover) and (pointer: fine) {
  .mobile-hover:hover {
    background-color: rgba(255, 255, 255, 0.1);
  }
}

/* Mobile-specific focus states for accessibility */
.mobile-focus:focus-visible {
  outline: 2px solid #2ECC71;
  outline-offset: 2px;
}

/* Enhanced mobile header styles */
@media (max-width: 768px) {
  .mobile-header-close-btn {
    min-height: 44px;
    min-width: 44px;
    display: flex;
    align-items: center;
    justify-content: center;
    border-radius: 50%;
    transition: all 0.2s ease;
  }

  .mobile-header-close-btn:active {
    transform: scale(0.95);
  }

  .mobile-menu-item-enhanced {
    backdrop-filter: blur(10px);
    border: 1px solid rgba(255, 255, 255, 0.1);
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  }

  .mobile-menu-item-enhanced:hover {
    backdrop-filter: blur(15px);
    border-color: rgba(255, 255, 255, 0.2);
    transform: translateY(-2px);
    box-shadow: 0 10px 25px rgba(0, 0, 0, 0.2);
  }

  .mobile-search-container {
    backdrop-filter: blur(20px);
    border: 1px solid rgba(255, 255, 255, 0.1);
  }

  .mobile-search-input {
    background: rgba(255, 255, 255, 0.95);
    backdrop-filter: blur(10px);
  }

  .mobile-gradient-bg {
    background: linear-gradient(135deg, rgba(255, 255, 255, 0.1) 0%, rgba(255, 255, 255, 0.05) 100%);
  }
}

/* Smooth animations for mobile interactions */
@keyframes mobile-slide-up {
  from {
    opacity: 0;
    transform: translateY(20px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

.animate-mobile-slide-up {
  animation: mobile-slide-up 0.3s ease-out forwards;
}

/* Enhanced backdrop blur for mobile overlays */
.mobile-backdrop-enhanced {
  backdrop-filter: blur(8px);
  background: rgba(0, 0, 0, 0.6);
  transition: all 0.3s ease;
}

/* Mobile-optimized button styles */
.mobile-btn-primary {
  background: linear-gradient(135deg, #2ECC71 0%, #27AE60 100%);
  box-shadow: 0 4px 15px rgba(46, 204, 113, 0.3);
  transition: all 0.2s ease;
}

.mobile-btn-primary:hover {
  box-shadow: 0 6px 20px rgba(46, 204, 113, 0.4);
  transform: translateY(-1px);
}

.mobile-btn-primary:active {
  transform: translateY(0);
  box-shadow: 0 2px 10px rgba(46, 204, 113, 0.3);
}

/* Product page mobile improvements */
@media (max-width: 1023px) {
  .product-layout {
    display: flex;
    flex-direction: column;
    gap: 1rem;
  }

  .product-carousel-container {
    position: relative !important;
    top: auto !important;
    margin-bottom: 1rem;
  }

  .product-info-container {
    margin-top: 0;
    padding-top: 0;
  }
}

/* Ensure proper spacing on mobile devices */
@media (max-width: 640px) {
  .product-carousel-container {
    padding: 0.75rem;
  }

  .product-info-container {
    padding: 1rem 0;
  }

  /* Prevent carousel from being too tall on small screens */
  .mobile-carousel-height {
    max-height: 300px;
  }
}

/* Smooth transitions for product page elements */
.product-transition {
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

/* Ensure images don't overflow on mobile */
.mobile-image-container {
  overflow: hidden;
  border-radius: 0.5rem;
}

.mobile-image-container img {
  max-width: 100%;
  height: auto;
  object-fit: contain;
}