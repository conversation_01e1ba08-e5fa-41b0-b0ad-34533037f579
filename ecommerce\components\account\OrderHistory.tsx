import Link from "next/link";
import {
  Accordion,
  AccordionContent,
  AccordionItem,
  AccordionTrigger,
} from "../../components/ui/accordion";
import { Badge } from "../../components/ui/badge";

export const OrderHistory = ({ orders }: any) => {
  const formatDate = (dateString: string) => {
    const date = new Date(dateString);
    return date.toLocaleDateString('en-US', {
      year: 'numeric',
      month: 'short',
      day: 'numeric'
    });
  };

  const getStatusColor = (status: string) => {
    switch(status) {
      case "DELIVERED":
      case "Delivered":
        return "bg-green-100 text-green-800 border-green-200";
      case "SHIPPED":
      case "Shipped":
        return "bg-blue-100 text-blue-800 border-blue-200";
      case "PAID":
      case "Paid":
        return "bg-emerald-100 text-emerald-800 border-emerald-200";
      case "PROCESSING":
      case "Processing":
        return "bg-yellow-100 text-yellow-800 border-yellow-200";
      case "PENDING":
      case "Pending":
        return "bg-orange-100 text-orange-800 border-orange-200";
      case "CANCELLED":
      case "Cancelled":
        return "bg-red-100 text-red-800 border-red-200";
      case "REFUNDED":
      case "Refunded":
        return "bg-purple-100 text-purple-800 border-purple-200";
      default:
        return "bg-gray-100 text-gray-800 border-gray-200";
    }
  };

  return (
    <div className="space-y-6">
      <h2 className="text-lg sm:text-xl md:text-2xl font-bold text-gray-800 mb-6">Order History</h2>

      {(!Array.isArray(orders) || orders.length === 0) && (
        <div className="text-center py-12 bg-gray-50 rounded-xl border border-gray-100">
          <svg xmlns="http://www.w3.org/2000/svg" className="h-12 w-12 mx-auto text-gray-400 mb-4" fill="none" viewBox="0 0 24 24" stroke="currentColor">
            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={1.5} d="M19 11H5m14 0a2 2 0 012 2v6a2 2 0 01-2 2H5a2 2 0 01-2-2v-6a2 2 0 012-2m14 0V9a2 2 0 00-2-2M5 11V9a2 2 0 012-2m0 0V5a2 2 0 012-2h6a2 2 0 012 2v2M7 7h10" />
          </svg>
          <p className="text-gray-600 font-medium">No orders found</p>
          <p className="text-gray-500 text-sm mt-1">Your order history will appear here</p>
        </div>
      )}

      <Accordion type="single" collapsible className="w-full space-y-4">
        {Array.isArray(orders) &&
          orders.map((order) => (
            <AccordionItem
              key={order.id}
              value={order.id}
              className="border border-gray-200 rounded-xl overflow-hidden shadow-sm hover:shadow-md transition-all duration-300 bg-white"
            >
              <AccordionTrigger className="hover:no-underline px-6 py-4">
                <div className="flex flex-col sm:flex-row sm:items-center justify-between w-full gap-3">
                  <div className="flex items-center gap-3">
                    <div className="bg-gray-100 p-2 rounded-full">
                      <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5 text-gray-600" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={1.5} d="M16 11V7a4 4 0 00-8 0v4M5 9h14l1 12H4L5 9z" />
                      </svg>
                    </div>
                    <span className="font-medium text-gray-800 text-sm md:text-base">Order #{order.id.substring(0, 8)}...</span>
                  </div>
                  <div className="flex flex-wrap items-center gap-3 sm:gap-4">
                    <Badge
                      className={`px-3 py-1 rounded-full text-xs font-medium border ${getStatusColor(order.status)}`}
                    >
                      {order.status}
                    </Badge>
                    <span className="text-gray-500 text-xs md:text-sm">
                      {formatDate(order.created_at)}
                    </span>
                  </div>
                </div>
              </AccordionTrigger>
              <AccordionContent>
                <div className="border-t border-gray-100">
                  <div className="space-y-4 p-6">
                    <div className="grid gap-4">
                      {order.items.map((item: any) => (
                        <div key={item?.id} className="flex flex-col sm:flex-row justify-between gap-2 pb-3 border-b border-gray-100">
                          <div>
                            <p className="font-medium text-gray-800 text-sm md:text-base">{item.product_name}</p>
                            <p className="text-xs md:text-sm text-gray-500 mt-1">
                              Quantity: {item.quantity}
                            </p>
                          </div>
                          <p className="font-medium text-gray-800 text-sm md:text-base">
                            ₹{typeof order.total === "string" &&
                              Number(item.total_price).toFixed(2)}
                          </p>
                        </div>
                      ))}
                    </div>

                    <div className="flex justify-between pt-4 border-t border-gray-100">
                      <span className="font-bold text-gray-800 text-sm md:text-base">Total</span>
                      <span className="font-bold text-gray-800 text-sm md:text-base">
                        ₹{typeof order.total === "string" &&
                          Number(order.total).toFixed(2)}
                      </span>
                    </div>

                    <div className="w-full flex justify-between items-center mt-4">
                      <div className="flex items-center gap-2">
                        {order.status === "PAID" && (
                          <span className="inline-flex items-center gap-1 text-xs text-green-700 bg-green-50 px-2 py-1 rounded-full border border-green-200">
                            <svg xmlns="http://www.w3.org/2000/svg" className="h-3 w-3" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z" />
                            </svg>
                            Invoice Available
                          </span>
                        )}
                      </div>
                      <Link
                        className="inline-flex items-center gap-2 bg-blue-50 text-blue-700 hover:bg-blue-100 transition-colors px-5 py-2.5 rounded-lg font-medium border border-blue-200 text-xs md:text-sm"
                        href={`/order-details?order_id=${order.id}`}
                      >
                        <svg xmlns="http://www.w3.org/2000/svg" className="h-4 w-4" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M15 12a3 3 0 11-6 0 3 3 0 016 0z" />
                          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M2.458 12C3.732 7.943 7.523 5 12 5c4.478 0 8.268 2.943 9.542 7-1.274 4.057-5.064 7-9.542 7-4.477 0-8.268-2.943-9.542-7z" />
                        </svg>
                        View Order
                      </Link>
                    </div>
                  </div>
                </div>
              </AccordionContent>
            </AccordionItem>
          ))}
      </Accordion>
    </div>
  );
};
