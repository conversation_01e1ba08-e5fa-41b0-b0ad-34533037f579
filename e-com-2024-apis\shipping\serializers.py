"""
Shipping API serializers for Rapidshyp integration
"""

from rest_framework import serializers
from decimal import Decimal
from django.core.validators import RegexValidator
from .models import RapidshypShipment, RapidshypConfiguration, TrackingEvent, ShippingRateCache
from .utils import validate_pincode, validate_weight, validate_phone_number


class ShippingRateRequestSerializer(serializers.Serializer):
    """Serializer for shipping rate calculation request"""

    pickup_pincode = serializers.CharField(
        max_length=6,
        required=False,
        help_text="Pickup pincode (6 digits)",
        validators=[RegexValidator(r'^\d{6}$', 'Pincode must be 6 digits')]
    )
    delivery_pincode = serializers.CharField(
        max_length=6,
        required=True,
        help_text="Delivery pincode (6 digits)",
        validators=[RegexValidator(r'^\d{6}$', 'Pincode must be 6 digits')]
    )
    weight = serializers.DecimalField(
        max_digits=8,
        decimal_places=2,
        default=1.0,
        help_text="Package weight in kg"
    )
    cod = serializers.BooleanField(
        default=False,
        help_text="Cash on delivery flag"
    )
    total_value = serializers.DecimalField(
        max_digits=10,
        decimal_places=2,
        default=0,
        help_text="Total order value"
    )

    def validate_weight(self, value):
        """Validate package weight"""
        if not validate_weight(value):
            raise serializers.ValidationError("Weight must be between 0.1kg and 50kg")
        return value

    def validate_delivery_pincode(self, value):
        """Validate delivery pincode"""
        if not validate_pincode(value):
            raise serializers.ValidationError("Invalid pincode format")
        return value

    def validate_pickup_pincode(self, value):
        """Validate pickup pincode"""
        if value and not validate_pincode(value):
            raise serializers.ValidationError("Invalid pincode format")
        return value


class CourierRateSerializer(serializers.Serializer):
    """Serializer for individual courier rate"""

    courier_code = serializers.CharField(max_length=20)
    courier_name = serializers.CharField(max_length=100)
    parent_courier_name = serializers.CharField(max_length=100, required=False)
    total_freight = serializers.DecimalField(max_digits=10, decimal_places=2)
    estimated_days = serializers.IntegerField(required=False)
    freight_mode = serializers.CharField(max_length=50, required=False)
    cutoff_time = serializers.CharField(max_length=10, required=False)
    max_weight = serializers.DecimalField(max_digits=8, decimal_places=2, required=False)
    min_weight = serializers.DecimalField(max_digits=8, decimal_places=2, required=False)
    is_existing_method = serializers.BooleanField(default=False)
    description = serializers.CharField(required=False)


class ShippingRateResponseSerializer(serializers.Serializer):
    """Serializer for shipping rate calculation response"""

    success = serializers.BooleanField()
    rapidshyp_rates = CourierRateSerializer(many=True)
    existing_methods = CourierRateSerializer(many=True)
    source = serializers.CharField(max_length=20)
    message = serializers.CharField()
    minimum_rate = CourierRateSerializer(required=False, allow_null=True)
    pickup_pincode = serializers.CharField(max_length=6)
    delivery_pincode = serializers.CharField(max_length=6)
    weight = serializers.DecimalField(max_digits=8, decimal_places=2)
    cod = serializers.BooleanField()
    total_value = serializers.DecimalField(max_digits=10, decimal_places=2)


class TrackingEventSerializer(serializers.ModelSerializer):
    """Serializer for tracking events"""

    class Meta:
        model = TrackingEvent
        fields = [
            'status', 'status_description', 'location', 'remarks',
            'event_timestamp', 'created_at'
        ]
        read_only_fields = ['created_at']


class RapidshypShipmentSerializer(serializers.ModelSerializer):
    """Serializer for Rapidshyp shipments"""

    tracking_events = TrackingEventSerializer(many=True, read_only=True)
    order_id = serializers.CharField(source='order.id', read_only=True)
    is_delivered = serializers.BooleanField(read_only=True)
    is_in_transit = serializers.BooleanField(read_only=True)
    is_returned = serializers.BooleanField(read_only=True)

    class Meta:
        model = RapidshypShipment
        fields = [
            'id', 'order_id', 'rapidshyp_order_id', 'shipment_id', 'awb_number',
            'courier_code', 'courier_name', 'parent_courier_name',
            'current_status', 'status_description', 'pickup_scheduled',
            'pickup_date', 'expected_delivery_date', 'actual_delivery_date',
            'freight_mode', 'total_freight', 'cutoff_time',
            'label_url', 'invoice_url', 'manifest_url', 'tracking_url',
            'is_delivered', 'is_in_transit', 'is_returned',
            'tracking_events', 'created_at', 'updated_at'
        ]
        read_only_fields = [
            'id', 'order_id', 'rapidshyp_order_id', 'shipment_id', 'awb_number',
            'is_delivered', 'is_in_transit', 'is_returned',
            'tracking_events', 'created_at', 'updated_at'
        ]


class OrderTrackingRequestSerializer(serializers.Serializer):
    """Serializer for order tracking request"""

    order_id = serializers.UUIDField(
        help_text="Order ID to track"
    )


class OrderTrackingResponseSerializer(serializers.Serializer):
    """Serializer for order tracking response"""

    success = serializers.BooleanField()
    tracking_available = serializers.BooleanField()
    source = serializers.CharField(max_length=20)

    # Rapidshyp tracking data
    shipment_id = serializers.CharField(required=False)
    rapidshyp_order_id = serializers.CharField(required=False)
    awb_number = serializers.CharField(required=False)
    courier_name = serializers.CharField(required=False)
    current_status = serializers.CharField(required=False)
    status_description = serializers.CharField(required=False)
    tracking_url = serializers.URLField(required=False)
    expected_delivery_date = serializers.DateField(required=False)
    actual_delivery_date = serializers.DateTimeField(required=False)
    tracking_events = TrackingEventSerializer(many=True, required=False)

    # Fallback tracking data
    tracking_number = serializers.CharField(required=False)
    order_status = serializers.CharField(required=False)

    # Error information
    error = serializers.CharField(required=False)


class RapidshypConfigurationSerializer(serializers.ModelSerializer):
    """Serializer for Rapidshyp configuration"""

    class Meta:
        model = RapidshypConfiguration
        fields = [
            'id', 'store_name', 'pickup_address_name', 'default_pickup_pincode',
            'contact_name', 'contact_phone', 'contact_email',
            'address_line_1', 'address_line_2', 'is_active',
            'created_at', 'updated_at'
        ]
        read_only_fields = ['id', 'created_at', 'updated_at']

    def validate_contact_phone(self, value):
        """Validate contact phone number"""
        if not validate_phone_number(value):
            raise serializers.ValidationError("Invalid phone number format")
        return value

    def validate_default_pickup_pincode(self, value):
        """Validate pickup pincode"""
        if not validate_pincode(value):
            raise serializers.ValidationError("Invalid pincode format")
        return value


class ServiceStatusSerializer(serializers.Serializer):
    """Serializer for service status response"""

    rapidshyp_enabled = serializers.BooleanField()
    rapidshyp_available = serializers.BooleanField()
    rapidshyp_api_key_configured = serializers.BooleanField()
    fallback_available = serializers.BooleanField()
    existing_methods_count = serializers.IntegerField()
    service_mode = serializers.CharField(max_length=20)


class PincodeValidationSerializer(serializers.Serializer):
    """Serializer for pincode validation"""

    pincode = serializers.CharField(
        max_length=6,
        validators=[RegexValidator(r'^\d{6}$', 'Pincode must be 6 digits')]
    )


class PincodeValidationResponseSerializer(serializers.Serializer):
    """Serializer for pincode validation response"""

    pincode = serializers.CharField(max_length=6)
    is_valid = serializers.BooleanField()
    is_serviceable = serializers.BooleanField()
    message = serializers.CharField()


class BulkTrackingRequestSerializer(serializers.Serializer):
    """Serializer for bulk tracking request"""

    order_ids = serializers.ListField(
        child=serializers.UUIDField(),
        max_length=50,
        help_text="List of order IDs to track (max 50)"
    )


class BulkTrackingResponseSerializer(serializers.Serializer):
    """Serializer for bulk tracking response"""

    success = serializers.BooleanField()
    tracking_data = serializers.DictField()
    errors = serializers.DictField()
    processed_count = serializers.IntegerField()
    error_count = serializers.IntegerField()
