from django.db.models.signals import post_save, post_delete
from django.dispatch import receiver
from .models import Product, Category, Brand, Review
from .utils import invalidate_product_cache, invalidate_category_cache, invalidate_brand_cache, safe_delete_pattern
from django.core.cache import cache

@receiver(post_save, sender=Product)
def product_saved(sender, instance, created, **kwargs):
    """
    Signal handler to invalidate cache when a product is saved
    """
    invalidate_product_cache(instance)

@receiver(post_delete, sender=Product)
def product_deleted(sender, instance, **kwargs):
    """
    Signal handler to invalidate cache when a product is deleted
    """
    invalidate_product_cache(instance)

@receiver(post_save, sender=Category)
def category_saved(sender, instance, created, **kwargs):
    """
    Signal handler to invalidate cache when a category is saved
    """
    invalidate_category_cache(instance)

    # Also invalidate featured products cache as they might be filtered by category
    safe_delete_pattern('featured_products_*')

@receiver(post_delete, sender=Category)
def category_deleted(sender, instance, **kwargs):
    """
    Signal handler to invalidate cache when a category is deleted
    """
    invalidate_category_cache(instance)

    # Also invalidate featured products cache as they might be filtered by category
    safe_delete_pattern('featured_products_*')

@receiver(post_save, sender=Brand)
def brand_saved(sender, instance, created, **kwargs):
    """
    Signal handler to invalidate cache when a brand is saved
    """
    invalidate_brand_cache(instance)

@receiver(post_delete, sender=Brand)
def brand_deleted(sender, instance, **kwargs):
    """
    Signal handler to invalidate cache when a brand is deleted
    """
    invalidate_brand_cache(instance)

@receiver(post_save, sender=Review)
def review_saved(sender, instance, created, **kwargs):
    """
    Signal handler to invalidate product cache when a review is saved
    """
    # Invalidate product's average rating cache
    cache.delete(f'product_avg_rating_{instance.product.id}')

    # Also invalidate featured products as they might be sorted by rating
    safe_delete_pattern('featured_products_*')

@receiver(post_delete, sender=Review)
def review_deleted(sender, instance, **kwargs):
    """
    Signal handler to invalidate product cache when a review is deleted
    """
    # Invalidate product's average rating cache
    cache.delete(f'product_avg_rating_{instance.product.id}')

    # Also invalidate featured products as they might be sorted by rating
    safe_delete_pattern('featured_products_*')
