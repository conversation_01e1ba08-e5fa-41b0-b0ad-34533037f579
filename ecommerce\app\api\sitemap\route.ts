import { NextRequest, NextResponse } from 'next/server';
import fs from 'fs';
import path from 'path';

/**
 * Custom route handler for serving sitemap.xml with proper headers
 * This ensures that the Content-Type header is set correctly for XML files
 * Google requires application/xml or text/xml Content-Type headers
 */
export async function GET(request: NextRequest) {
  try {
    // Path to the static sitemap.xml file
    const sitemapPath = path.join(process.cwd(), 'public', 'sitemap.xml');

    // Check if the file exists
    if (fs.existsSync(sitemapPath)) {
      // Read the sitemap file
      const sitemapContent = fs.readFileSync(sitemapPath, 'utf-8');

      // Return the sitemap with proper XML headers
      // Using application/xml as recommended by Google
      return new NextResponse(sitemapContent, {
        status: 200,
        headers: {
          'Content-Type': 'application/xml',
          'X-Robots-Tag': 'noindex, follow',
          'Cache-Control': 'public, max-age=3600, stale-while-revalidate=86400',
        },
      });
    } else {
      // If the static sitemap doesn't exist, try to use the dynamic one
      // This will be handled by the app/sitemap.ts file
      console.warn('Static sitemap.xml not found, falling back to dynamic generation');
      return NextResponse.next();
    }
  } catch (error) {
    console.error('Error serving sitemap:', error);
    return NextResponse.next();
  }
}
