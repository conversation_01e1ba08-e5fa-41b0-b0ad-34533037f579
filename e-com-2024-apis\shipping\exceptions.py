"""
Custom exceptions for shipping module
"""


class ShippingException(Exception):
    """Base exception for shipping-related errors"""
    pass


class RapidshypAPIException(ShippingException):
    """Exception raised when Rapidshyp API calls fail"""
    
    def __init__(self, message, status_code=None, response_data=None):
        super().__init__(message)
        self.status_code = status_code
        self.response_data = response_data


class RapidshypAuthenticationException(RapidshypAPIException):
    """Exception raised when Rapidshyp API authentication fails"""
    pass


class RapidshypServiceabilityException(RapidshypAPIException):
    """Exception raised when pincode is not serviceable"""
    pass


class RapidshypRateLimitException(RapidshypAPIException):
    """Exception raised when API rate limit is exceeded"""
    pass


class RapidshypTimeoutException(RapidshypAPIException):
    """Exception raised when API request times out"""
    pass


class ShippingConfigurationException(ShippingException):
    """Exception raised when shipping configuration is invalid"""
    pass


class FallbackShippingException(ShippingException):
    """Exception raised when fallback shipping methods fail"""
    pass


class OrderProcessingException(ShippingException):
    """Exception raised during order processing"""
    pass


class TrackingException(ShippingException):
    """Exception raised during tracking operations"""
    pass
