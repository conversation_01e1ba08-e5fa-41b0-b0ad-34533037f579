# Rapidshyp Shipping Integration Environment Variables
# Copy this file to .env and update with your actual values

# ============================================================================
# RAPIDSHYP API CONFIGURATION
# ============================================================================

# Enable/disable Rapidshyp integration
RAPIDSHYP_ENABLED=true

# Rapidshyp API credentials
RAPIDSHYP_API_KEY=your_rapidshyp_api_key_here

# Rapidshyp API settings
RAPIDSHYP_BASE_URL=https://api.rapidshyp.com/rapidshyp/apis/v1
RAPIDSHYP_STORE_NAME=DEFAULT
RAPIDSHYP_SANDBOX_MODE=true

# Default pickup location
RAPIDSHYP_DEFAULT_PICKUP_PINCODE=110001
RAPIDSHYP_CONTACT_NAME=Triumph Enterprises
RAPIDSHYP_PICKUP_NAME=Main Warehouse
RAPIDSHYP_PICKUP_EMAIL=<EMAIL>
RAPIDSHYP_PICKUP_PHONE=9848486452
RAPIDSHYP_PICKUP_ADDRESS1=D.No. 5-5-190/65A, Ehata Nooruddin Shah Qadri
RAPIDSHYP_PICKUP_ADDRESS2=Patel Nagar, Darussalam

# Webhook configuration
RAPIDSHYP_WEBHOOK_SECRET=your_webhook_secret_here

# ============================================================================
# DEVELOPMENT ENVIRONMENT EXAMPLE
# ============================================================================

# For development/testing
# RAPIDSHYP_ENABLED=true
# RAPIDSHYP_API_KEY=test_api_key_from_rapidshyp
# RAPIDSHYP_SANDBOX_MODE=true

# ============================================================================
# STAGING ENVIRONMENT EXAMPLE
# ============================================================================

# For staging environment
# RAPIDSHYP_ENABLED=true
# RAPIDSHYP_API_KEY=staging_api_key_from_rapidshyp
# RAPIDSHYP_SANDBOX_MODE=true

# ============================================================================
# PRODUCTION ENVIRONMENT EXAMPLE
# ============================================================================

# For production environment
# RAPIDSHYP_ENABLED=true
# RAPIDSHYP_API_KEY=production_api_key_from_rapidshyp
# RAPIDSHYP_SANDBOX_MODE=false

# ============================================================================
# FEATURE FLAGS
# ============================================================================

# These are configured in settings.py but can be overridden here if needed
# RAPIDSHYP_ENABLE_RATE_CACHING=true
# RAPIDSHYP_ENABLE_TRACKING_SYNC=true
# RAPIDSHYP_ENABLE_AUTO_PICKUP_SCHEDULING=true
# RAPIDSHYP_ENABLE_LABEL_GENERATION=true
# RAPIDSHYP_ENABLE_WEBHOOK_PROCESSING=true

# ============================================================================
# NOTES
# ============================================================================

# 1. To disable Rapidshyp completely, set RAPIDSHYP_ENABLED=false
# 2. The system will automatically fallback to existing shipping methods
# 3. All new fields in Order model are nullable, so no data migration issues
# 4. API endpoints will work with or without Rapidshyp enabled
# 5. For testing, use sandbox mode (RAPIDSHYP_SANDBOX_MODE=true)
# 6. Get your API key from Rapidshyp dashboard after account setup
# 7. Webhook secret is used to verify webhook authenticity
# 8. Update pickup location details according to your business address
