// import { useState } from "react";
// import {
//   View,
//   Text,
//   TextInput,
//   TouchableOpacity,
//   StyleSheet,
//   ScrollView,
//   ActivityIndicator,
// } from "react-native";
// import { SafeAreaView } from "react-native-safe-area-context";
// import { MaterialIcons } from "@expo/vector-icons";
// import { useAuth } from "@/context/AuthContext";
// import ProfileScreen from "./ProfileScreen";

// export default function AuthScreen({ route, navigation }) {
//   const returnTo = route.params?.returnTo;
//   const { isAuthenticated, setIsAuthenticated, loading } = useAuth();
//   const [email, setEmail] = useState("");
//   const [password, setPassword] = useState("");
//   const [activeTab, setActiveTab] = useState("login"); // 'login' or 'signup'
//   const { login, signup } = useAuth();

//   const handleLogin = async () => {
//     try {
//       await login(email, password);
//       // handleSuccessfulAuth();
      
//         navigation.navigate("MainTabs");
      
//     } catch (error) {
//       console.error("Login failed:", error);
//       // You might want to show an error toast here
//     }
//   };

//   const handleSignup = async () => {
//     try {
//       await signup(email, password);
//       // handleSuccessfulAuth();
//     } catch (error) {
//       console.error("Signup failed:", error);
//       // You might want to show an error toast here
//     }
//   };

//   // const handleSuccessfulAuth = () => {
//   //   // Use the returnScreen from route params if provided
//   //   if (route.params?.returnScreen) {
//   //     navigation.navigate(route.params.returnScreen);
//   //   } else {
//   //     // Use 'MainTabs' as default destination after successful authentication
//   //     // Make sure 'MainTabs' exists in your navigation configuration
//   //     navigation.navigate('MainTabs');
//   //   }

//   //   // If there's an onAuthenticated callback, call it
//   //   if (route.params?.onAuthenticated) {
//   //     route.params.onAuthenticated();
//   //   }
//   // };

//   return (
//     <SafeAreaView style={styles.container}>
//       {loading ? (
//         <View
//           style={{
//             padding: 10,
//             justifyContent: "center",
//             alignItems: "center",
//           }}
//         >
//           <ActivityIndicator size="large" color="#0000ff" />
//           {/* <Text style={{ marginTop: 5 }}>Loading more...</Text> */}
//         </View>
//       ) : (
//         <ScrollView contentContainerStyle={styles.scrollContent}>
//           <View style={styles.header}>
//             <Text style={styles.title}>
//               {activeTab === "login" ? "Welcome Back" : "Create Account"}
//             </Text>
//             <Text style={styles.subtitle}>
//               {activeTab === "login"
//                 ? "Login to your account"
//                 : "Sign up to get started"}
//             </Text>
//           </View>

//           {/* Tab switcher */}
//           <View style={styles.tabContainer}>
//             <TouchableOpacity
//               style={[styles.tab, activeTab === "login" && styles.activeTab]}
//               onPress={() => setActiveTab("login")}
//             >
//               <Text
//                 style={[
//                   styles.tabText,
//                   activeTab === "login" && styles.activeTabText,
//                 ]}
//               >
//                 Login
//               </Text>
//             </TouchableOpacity>
//             <TouchableOpacity
//               style={[styles.tab, activeTab === "signup" && styles.activeTab]}
//               onPress={() => setActiveTab("signup")}
//             >
//               <Text
//                 style={[
//                   styles.tabText,
//                   activeTab === "signup" && styles.activeTabText,
//                 ]}
//               >
//                 Sign Up
//               </Text>
//             </TouchableOpacity>
//           </View>

//           <View style={styles.form}>
//             <View style={styles.inputContainer}>
//               <MaterialIcons
//                 name="email"
//                 size={24}
//                 color="#666"
//                 style={styles.inputIcon}
//               />
//               <TextInput
//                 style={styles.input}
//                 placeholder="Email"
//                 value={email}
//                 onChangeText={setEmail}
//                 autoCapitalize="none"
//                 keyboardType="email-address"
//               />
//             </View>
//             <View style={styles.inputContainer}>
//               <MaterialIcons
//                 name="lock"
//                 size={24}
//                 color="#666"
//                 style={styles.inputIcon}
//               />
//               <TextInput
//                 style={styles.input}
//                 placeholder="Password"
//                 value={password}
//                 onChangeText={setPassword}
//                 secureTextEntry
//               />
//             </View>

//             {activeTab === "login" ? (
//               <>
//                 <TouchableOpacity
//                   style={styles.submitButton}
//                   onPress={handleLogin}
//                 >
//                   <Text style={styles.submitButtonText}>Login</Text>
//                 </TouchableOpacity>
//                 <TouchableOpacity
//                   style={styles.switchButton}
//                   onPress={() => setActiveTab("signup")}
//                 >
//                   <Text style={styles.switchButtonText}>
//                     Don't have an account? Sign Up
//                   </Text>
//                 </TouchableOpacity>
//               </>
//             ) : (
//               <>
//                 <TouchableOpacity
//                   style={styles.submitButton}
//                   onPress={handleSignup}
//                 >
//                   <Text style={styles.submitButtonText}>Sign Up</Text>
//                 </TouchableOpacity>
//                 <TouchableOpacity
//                   style={styles.switchButton}
//                   onPress={() => setActiveTab("login")}
//                 >
//                   <Text style={styles.switchButtonText}>
//                     Already have an account? Login
//                   </Text>
//                 </TouchableOpacity>
//               </>
//             )}
//           </View>
//         </ScrollView>
//       )}
//     </SafeAreaView>
//   );
// }

// const styles = StyleSheet.create({
//   container: {
//     flex: 1,
//     backgroundColor: "#ffffff",
//   },
//   scrollContent: {
//     flexGrow: 1,
//     padding: 16,
//   },
//   header: {
//     marginTop: 40,
//     marginBottom: 20,
//     alignItems: "center",
//   },
//   title: {
//     fontSize: 28,
//     fontWeight: "bold",
//     marginBottom: 8,
//   },
//   subtitle: {
//     fontSize: 16,
//     color: "#666",
//   },
//   tabContainer: {
//     flexDirection: "row",
//     marginHorizontal: 16,
//     marginBottom: 20,
//     borderRadius: 12,
//     backgroundColor: "#f3f4f6",
//     padding: 4,
//   },
//   tab: {
//     flex: 1,
//     paddingVertical: 12,
//     alignItems: "center",
//     borderRadius: 8,
//   },
//   activeTab: {
//     backgroundColor: "#ffffff",
//     shadowColor: "#000",
//     shadowOffset: { width: 0, height: 1 },
//     shadowOpacity: 0.1,
//     shadowRadius: 2,
//     elevation: 2,
//   },
//   tabText: {
//     fontSize: 16,
//     fontWeight: "500",
//     color: "#666",
//   },
//   activeTabText: {
//     color: "#2563EB",
//     fontWeight: "600",
//   },
//   form: {
//     padding: 16,
//   },
//   inputContainer: {
//     flexDirection: "row",
//     alignItems: "center",
//     backgroundColor: "#f3f4f6",
//     borderRadius: 12,
//     marginBottom: 16,
//     padding: 4,
//   },
//   inputIcon: {
//     padding: 10,
//   },
//   input: {
//     flex: 1,
//     paddingVertical: 12,
//     paddingHorizontal: 8,
//     fontSize: 16,
//   },
//   submitButton: {
//     backgroundColor: "#2563EB",
//     padding: 16,
//     borderRadius: 12,
//     alignItems: "center",
//     marginTop: 24,
//   },
//   submitButtonText: {
//     color: "white",
//     fontSize: 16,
//     fontWeight: "600",
//   },
//   socialButtons: {
//     flexDirection: "row",
//     justifyContent: "center",
//     marginTop: 32,
//   },
//   socialButton: {
//     width: 48,
//     height: 48,
//     borderRadius: 24,
//     backgroundColor: "#f3f4f6",
//     justifyContent: "center",
//     alignItems: "center",
//     marginHorizontal: 12,
//   },
//   switchButton: {
//     marginTop: 32,
//     alignItems: "center",
//   },
//   switchButtonText: {
//     color: "#2563EB",
//     fontSize: 14,
//     fontWeight: "500",
//   },
// });


import { useState } from "react";
import {
  View,
  Text,
  TextInput,
  TouchableOpacity,
  StyleSheet,
  ScrollView,
  ActivityIndicator,
  Modal,
  Platform
} from "react-native";
import { SafeAreaView } from "react-native-safe-area-context";
import { MaterialIcons } from "@expo/vector-icons";
import { useAuth } from "@/context/AuthContext";

export default function AuthScreen({ route, navigation }) {
  const returnTo = route.params?.returnTo;
  const { isAuthenticated, setIsAuthenticated, loading } = useAuth();
  const [email, setEmail] = useState("");
  const [password, setPassword] = useState("");
  const [name, setName] = useState("");
  const [phoneNumber, setPhoneNumber] = useState("");
  
  // Date of birth fields
  const [day, setDay] = useState("");
  const [month, setMonth] = useState("");
  const [year, setYear] = useState("");
  
  const [activeTab, setActiveTab] = useState("login"); // 'login' or 'signup'
  const { login, signup } = useAuth();

  const handleLogin = async () => {
    try {
      await login(email, password);
      navigation.navigate("MainTabs");
    } catch (error) {
      console.error("Login failed:", error);
      // You might want to show an error toast here
    }
  };

  const handleSignup = async () => {
    try {
      // Format date as YYYY-MM-DD for API
      const formattedDate = `${year}-${month.padStart(2, '0')}-${day.padStart(2, '0')}`;
      
      await signup(email, password, name, phoneNumber, formattedDate);
      navigation.navigate("MainTabs");
    } catch (error) {
      console.error("Signup failed:", error);
      // You might want to show an error toast here
    }
  };

  return (
    <SafeAreaView style={styles.container}>
      {loading ? (
        <View style={styles.loadingContainer}>
          <ActivityIndicator size="large" color="#0000ff" />
        </View>
      ) : (
        <ScrollView contentContainerStyle={styles.scrollContent}>
          <View style={styles.header}>
            <Text style={styles.title}>
              {activeTab === "login" ? "Welcome Back" : "Create Account"}
            </Text>
            <Text style={styles.subtitle}>
              {activeTab === "login"
                ? "Login to your account"
                : "Sign up to get started"}
            </Text>
          </View>

          {/* Tab switcher */}
          <View style={styles.tabContainer}>
            <TouchableOpacity
              style={[styles.tab, activeTab === "login" && styles.activeTab]}
              onPress={() => setActiveTab("login")}
            >
              <Text
                style={[
                  styles.tabText,
                  activeTab === "login" && styles.activeTabText,
                ]}
              >
                Login
              </Text>
            </TouchableOpacity>
            <TouchableOpacity
              style={[styles.tab, activeTab === "signup" && styles.activeTab]}
              onPress={() => setActiveTab("signup")}
            >
              <Text
                style={[
                  styles.tabText,
                  activeTab === "signup" && styles.activeTabText,
                ]}
              >
                Sign Up
              </Text>
            </TouchableOpacity>
          </View>

          <View style={styles.form}>
            <View style={styles.inputContainer}>
              <MaterialIcons
                name="email"
                size={24}
                color="#666"
                style={styles.inputIcon}
              />
              <TextInput
                style={styles.input}
                placeholder="Email"
                value={email}
                onChangeText={setEmail}
                autoCapitalize="none"
                keyboardType="email-address"
              />
            </View>
            
            <View style={styles.inputContainer}>
              <MaterialIcons
                name="lock"
                size={24}
                color="#666"
                style={styles.inputIcon}
              />
              <TextInput
                style={styles.input}
                placeholder="Password"
                value={password}
                onChangeText={setPassword}
                secureTextEntry
              />
            </View>

            {activeTab === "signup" && (
              <>
                <View style={styles.inputContainer}>
                  <MaterialIcons
                    name="person"
                    size={24}
                    color="#666"
                    style={styles.inputIcon}
                  />
                  <TextInput
                    style={styles.input}
                    placeholder="Full Name"
                    value={name}
                    onChangeText={setName}
                  />
                </View>
                
                <View style={styles.inputContainer}>
                  <MaterialIcons
                    name="phone"
                    size={24}
                    color="#666"
                    style={styles.inputIcon}
                  />
                  <TextInput
                    style={styles.input}
                    placeholder="Phone Number"
                    value={phoneNumber}
                    onChangeText={setPhoneNumber}
                    keyboardType="phone-pad"
                  />
                </View>
                
                {/* Date of birth input fields */}
                <Text style={styles.dateLabel}>Date of Birth (YYYY-MM-DD)</Text>
                <View style={styles.dateContainer}>
                  <View style={[styles.dateInputContainer, { flex: 2 }]}>
                    <TextInput
                      style={styles.dateInput}
                      placeholder="YYYY"
                      value={year}
                      onChangeText={setYear}
                      keyboardType="number-pad"
                      maxLength={4}
                    />
                  </View>
                  <Text style={styles.dateSeparator}>-</Text>
                  <View style={[styles.dateInputContainer, { flex: 1 }]}>
                    <TextInput
                      style={styles.dateInput}
                      placeholder="MM"
                      value={month}
                      onChangeText={setMonth}
                      keyboardType="number-pad"
                      maxLength={2}
                    />
                  </View>
                  <Text style={styles.dateSeparator}>-</Text>
                  <View style={[styles.dateInputContainer, { flex: 1 }]}>
                    <TextInput
                      style={styles.dateInput}
                      placeholder="DD"
                      value={day}
                      onChangeText={setDay}
                      keyboardType="number-pad"
                      maxLength={2}
                    />
                  </View>
                </View>
              </>
            )}

            {activeTab === "login" ? (
              <>
                <TouchableOpacity
                  style={styles.submitButton}
                  onPress={handleLogin}
                >
                  <Text style={styles.submitButtonText}>Login</Text>
                </TouchableOpacity>
                <TouchableOpacity
                  style={styles.switchButton}
                  onPress={() => setActiveTab("signup")}
                >
                  <Text style={styles.switchButtonText}>
                    Don't have an account? Sign Up
                  </Text>
                </TouchableOpacity>
              </>
            ) : (
              <>
                <TouchableOpacity
                  style={styles.submitButton}
                  onPress={handleSignup}
                >
                  <Text style={styles.submitButtonText}>Sign Up</Text>
                </TouchableOpacity>
                <TouchableOpacity
                  style={styles.switchButton}
                  onPress={() => setActiveTab("login")}
                >
                  <Text style={styles.switchButtonText}>
                    Already have an account? Login
                  </Text>
                </TouchableOpacity>
              </>
            )}
          </View>
        </ScrollView>
      )}
    </SafeAreaView>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: "#ffffff",
  },
  loadingContainer: {
    flex: 1,
    justifyContent: "center",
    alignItems: "center",
  },
  scrollContent: {
    flexGrow: 1,
    padding: 16,
  },
  header: {
    marginTop: 40,
    marginBottom: 20,
    alignItems: "center",
  },
  title: {
    fontSize: 28,
    fontWeight: "bold",
    marginBottom: 8,
  },
  subtitle: {
    fontSize: 16,
    color: "#666",
  },
  tabContainer: {
    flexDirection: "row",
    marginHorizontal: 16,
    marginBottom: 20,
    borderRadius: 12,
    backgroundColor: "#f3f4f6",
    padding: 4,
  },
  tab: {
    flex: 1,
    paddingVertical: 12,
    alignItems: "center",
    borderRadius: 8,
  },
  activeTab: {
    backgroundColor: "#ffffff",
    shadowColor: "#000",
    shadowOffset: { width: 0, height: 1 },
    shadowOpacity: 0.1,
    shadowRadius: 2,
    elevation: 2,
  },
  tabText: {
    fontSize: 16,
    fontWeight: "500",
    color: "#666",
  },
  activeTabText: {
    color: "#2563EB",
    fontWeight: "600",
  },
  form: {
    padding: 16,
  },
  inputContainer: {
    flexDirection: "row",
    alignItems: "center",
    backgroundColor: "#f3f4f6",
    borderRadius: 12,
    marginBottom: 16,
    padding: 4,
  },
  inputIcon: {
    padding: 10,
  },
  input: {
    flex: 1,
    paddingVertical: 12,
    paddingHorizontal: 8,
    fontSize: 16,
  },
  dateLabel: {
    fontSize: 14,
    color: "#666",
    marginBottom: 8,
    marginLeft: 4,
  },
  dateContainer: {
    flexDirection: "row",
    alignItems: "center",
    marginBottom: 16,
  },
  dateInputContainer: {
    backgroundColor: "#f3f4f6",
    borderRadius: 12,
    padding: 4,
  },
  dateInput: {
    paddingVertical: 12,
    paddingHorizontal: 8,
    fontSize: 16,
    textAlign: "center",
  },
  dateSeparator: {
    fontSize: 20,
    fontWeight: "bold",
    marginHorizontal: 8,
    color: "#666",
  },
  submitButton: {
    backgroundColor: "#2563EB",
    padding: 16,
    borderRadius: 12,
    alignItems: "center",
    marginTop: 24,
  },
  submitButtonText: {
    color: "white",
    fontSize: 16,
    fontWeight: "600",
  },
  switchButton: {
    marginTop: 32,
    alignItems: "center",
  },
  switchButtonText: {
    color: "#2563EB",
    fontSize: 14,
    fontWeight: "500",
  },
});