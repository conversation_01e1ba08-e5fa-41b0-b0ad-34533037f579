from rest_framework import serializers
from django.contrib.auth import get_user_model
from django.contrib.auth.password_validation import validate_password
from .models import Address, PaymentMethod, Wishlist, ContactMessage
from django.contrib.auth import authenticate
from products.serializers import ProductDetailSerializer

User = get_user_model()


class UserSerializer(serializers.ModelSerializer):
    display_image_url = serializers.SerializerMethodField()
    order_count = serializers.IntegerField(read_only=True)
    total_orders = serializers.IntegerField(read_only=True)
    last_order_date = serializers.DateTimeField(read_only=True)

    class Meta:
        model = User
        fields = (
            "id",
            "email",
            "name",
            "phone_number",
            "date_of_birth",
            "is_verified",
            "display_image_url",
            "order_count",
            "total_orders",
            "last_order_date",
        )
        read_only_fields = (
            "is_verified",
            "order_count",
            "total_orders",
            "last_order_date",
        )

    def get_display_image_url(self, obj):
        return obj.display_image_url


class SocialLoginSerializer(serializers.Serializer):
    email = serializers.EmailField(required=True)
    name = serializers.CharField(required=True)
    image_url = serializers.URLField(required=False, allow_blank=True, allow_null=True)
    provider = serializers.CharField(required=False, default="google")

    def validate(self, attrs):
        # Validate email format
        email = attrs.get('email')
        if not email:
            raise serializers.ValidationError({"email": "Email is required for social login"})

        # Ensure name is provided
        name = attrs.get('name')
        if not name:
            raise serializers.ValidationError({"name": "Name is required for social login"})

        return attrs


class UserRegistrationSerializer(serializers.ModelSerializer):
    password = serializers.CharField(
        write_only=True, required=True, validators=[validate_password]
    )

    class Meta:
        model = User
        fields = (
            "email",
            "password",
            "name",
            "phone_number",
            "date_of_birth",
        )

    def validate(self, attrs):
        return attrs

    def create(self, validated_data):
        user = User.objects.create_user(**validated_data)
        return user


class LoginSerializer(serializers.Serializer):
    email = serializers.EmailField(required=True)
    password = serializers.CharField(write_only=True, required=True)

    def validate(self, data):
        email = data.get("email")
        password = data.get("password")

        # Get the request object from context to pass to authenticate()
        # This ensures the user_login_failed signal receives the request object
        request = self.context.get('request')

        # Authenticate user with request object for proper signal handling
        user = authenticate(request=request, username=email, password=password)
        if user is None:
            raise serializers.ValidationError("Invalid email or password.")

        # Add the user to the validated data
        data["user"] = user
        return data


class AddressSerializer(serializers.ModelSerializer):
    class Meta:
        model = Address
        fields = "__all__"
        read_only_fields = ("user",)

    def create(self, validated_data):
        validated_data["user"] = self.context["request"].user
        return super().create(validated_data)


class PaymentMethodSerializer(serializers.ModelSerializer):
    class Meta:
        model = PaymentMethod
        fields = ("id", "card_type", "last_four", "is_default", "created_at")
        read_only_fields = ("user", "stripe_payment_method_id")


class WishlistSerializer(serializers.ModelSerializer):
    product = ProductDetailSerializer(read_only=True)

    class Meta:
        model = Wishlist
        fields = ["id", "product", "added_at"]
        read_only_fields = ("user",)


class ChangePasswordSerializer(serializers.Serializer):
    old_password = serializers.CharField(required=True)
    new_password = serializers.CharField(required=True, validators=[validate_password])

    def validate_old_password(self, value):
        user = self.context["request"].user
        if not user.check_password(value):
            raise serializers.ValidationError("Old password is incorrect")
        return value


class CustomerProfileSerializer(serializers.ModelSerializer):
    current_password = serializers.CharField(write_only=True, required=False)
    new_password = serializers.CharField(write_only=True, required=False)
    confirm_password = serializers.CharField(write_only=True, required=False)
    display_image_url = serializers.SerializerMethodField()
    image = serializers.ImageField(write_only=True, required=False)

    class Meta:
        model = User
        fields = [
            "id",
            "name",
            "email",
            "phone_number",
            "date_of_birth",
            "image",
            "display_image_url",
            "current_password",
            "new_password",
            "confirm_password",
        ]
        extra_kwargs = {
            "name": {"required": False},
            "email": {"required": False},
            "phone_number": {"required": False},
            "date_of_birth": {"required": False},
            "image": {"required": False},
        }

    def get_display_image_url(self, obj):
        if obj.image:
            return obj.image.url
        return None

    def validate(self, data):
        # Validate password change
        if "new_password" in data or "confirm_password" in data:
            if not data.get("current_password"):
                raise serializers.ValidationError(
                    {
                        "current_password": "Current password is required to change password."
                    }
                )
            if data["new_password"] != data["confirm_password"]:
                raise serializers.ValidationError(
                    {"confirm_password": "New passwords do not match."}
                )
            validate_password(data["new_password"])
        return data

    def update(self, instance, validated_data):
        # Update profile photo
        instance.image = validated_data.get("image", instance.image)

        # Update profile fields
        instance.name = validated_data.get("name", instance.name)
        instance.email = validated_data.get("email", instance.email)
        instance.phone_number = validated_data.get(
            "phone_number", instance.phone_number
        )
        instance.date_of_birth = validated_data.get(
            "date_of_birth", instance.date_of_birth
        )

        # Handle password change
        current_password = validated_data.get("current_password")
        new_password = validated_data.get("new_password")
        if current_password and new_password:
            if not instance.check_password(current_password):
                raise serializers.ValidationError(
                    {"current_password": "Current password is incorrect."}
                )
            instance.set_password(new_password)

        instance.save()
        return instance


class ContactMessageSerializer(serializers.ModelSerializer):
    class Meta:
        model = ContactMessage
        fields = ['id', 'name', 'email', 'subject', 'message', 'created_at']
        read_only_fields = ['created_at']


class ForgotPasswordSerializer(serializers.Serializer):
    """Serializer for forgot password request"""
    email = serializers.EmailField(required=True)

    def validate_email(self, value):
        """Validate that email exists in the system"""
        from django.contrib.auth import get_user_model
        User = get_user_model()

        try:
            user = User.objects.get(email=value, is_active=True)
        except User.DoesNotExist:
            # Don't reveal if email exists or not for security
            # Always return success to prevent email enumeration
            pass

        return value


class ResetPasswordSerializer(serializers.Serializer):
    """Serializer for password reset with token"""
    token = serializers.CharField(required=True)
    new_password = serializers.CharField(
        required=True,
        write_only=True,
        validators=[validate_password]
    )
    confirm_password = serializers.CharField(required=True, write_only=True)

    def validate(self, data):
        """Validate password reset data"""
        from .models import PasswordResetToken, PasswordHistory
        from django.contrib.auth import get_user_model
        import hashlib

        User = get_user_model()
        token = data.get('token')
        new_password = data.get('new_password')
        confirm_password = data.get('confirm_password')

        # Check if passwords match
        if new_password != confirm_password:
            raise serializers.ValidationError({
                'confirm_password': 'Passwords do not match.'
            })

        # Validate token
        try:
            token_hash = hashlib.sha256(token.encode()).hexdigest()
            reset_token = PasswordResetToken.objects.get(
                token_hash=token_hash,
                is_used=False
            )

            if not reset_token.is_valid():
                raise serializers.ValidationError({
                    'token': 'Reset token has expired or is invalid.'
                })

            # Check password history
            if PasswordHistory.check_password_reuse(reset_token.user, new_password):
                raise serializers.ValidationError({
                    'new_password': 'You cannot reuse any of your last 5 passwords. Please choose a different password.'
                })

            data['reset_token'] = reset_token
            data['user'] = reset_token.user

        except PasswordResetToken.DoesNotExist:
            raise serializers.ValidationError({
                'token': 'Invalid reset token.'
            })

        return data


class PasswordStrengthSerializer(serializers.Serializer):
    """Enhanced password strength validation"""
    password = serializers.CharField(required=True)

    def validate_password(self, value):
        """Enhanced password validation with security requirements"""
        import re

        errors = []

        # Minimum length
        if len(value) < 8:
            errors.append("Password must be at least 8 characters long.")

        # Maximum length
        if len(value) > 128:
            errors.append("Password must not exceed 128 characters.")

        # Must contain uppercase letter
        if not re.search(r'[A-Z]', value):
            errors.append("Password must contain at least one uppercase letter.")

        # Must contain lowercase letter
        if not re.search(r'[a-z]', value):
            errors.append("Password must contain at least one lowercase letter.")

        # Must contain digit
        if not re.search(r'\d', value):
            errors.append("Password must contain at least one number.")

        # Must contain special character
        if not re.search(r'[!@#$%^&*(),.?":{}|<>]', value):
            errors.append("Password must contain at least one special character (!@#$%^&*(),.?\":{}|<>).")

        # Check for common weak patterns
        weak_patterns = [
            r'123456',
            r'password',
            r'qwerty',
            r'abc123',
            r'admin',
            r'letmein'
        ]

        for pattern in weak_patterns:
            if re.search(pattern, value.lower()):
                errors.append("Password contains common weak patterns. Please choose a stronger password.")
                break

        # Check for sequential characters
        if re.search(r'(012|123|234|345|456|567|678|789|890)', value):
            errors.append("Password should not contain sequential numbers.")

        if re.search(r'(abc|bcd|cde|def|efg|fgh|ghi|hij|ijk|jkl|klm|lmn|mno|nop|opq|pqr|qrs|rst|stu|tuv|uvw|vwx|wxy|xyz)', value.lower()):
            errors.append("Password should not contain sequential letters.")

        if errors:
            raise serializers.ValidationError(errors)

        return value