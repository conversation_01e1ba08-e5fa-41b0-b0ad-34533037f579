"use client";

import { <PERSON>, <PERSON><PERSON>ontent, <PERSON><PERSON><PERSON>er, <PERSON><PERSON><PERSON><PERSON> } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu";

import { StatisticsCards } from "@/components/dashboard/StatisticsCards";
import { AnalyticsCharts } from "@/components/dashboard/AnalyticsCharts";
import { RecentActivity } from "@/components/dashboard/RecentActivity";
import { useDashboard } from "@/hooks/useDashboard";
import { useSession } from "next-auth/react";
import { useRouter } from "next/navigation";
import { useToast } from "@/components/ui/use-toast";
import { DASHBOARD_ANALYTICS_EXPORT } from "@/constant/urls";
import { MAIN_URL } from "@/constant/urls";
import {
  RefreshCw,
  Calendar,
  Download,
  Settings,
  Bell,
  TrendingUp,
  BarChart3,
  Users,
  ShoppingCart,
  Loader2,
  Ch<PERSON><PERSON>Down,
  FileSpreadsheet
} from "lucide-react";
import { useState, useEffect } from "react";
import useAuthSession from "@/hooks/useAuthSession";

const Dashboard = () => {
  const { status } = useSession();
  const router = useRouter();
  const { toast } = useToast();
  const [refreshing, setRefreshing] = useState(false);
  const [dateRange, setDateRange] = useState('30d');
  const isAuthenticated = status === 'authenticated';
const {session} = useAuthSession()
  const {
    statistics,
    analytics,
    graphData,
    recentOrders,
    loading,
    error,
    refreshDashboard,
    updateDateRange,
    hasError
  } = useDashboard(dateRange);

  // Date range options
  const dateRangeOptions = [
    { value: '7d', label: 'Last 7 days' },
    { value: '30d', label: 'Last 30 days' },
    { value: '90d', label: 'Last 90 days' },
    { value: '1y', label: 'Last year' }
  ];

  // Only redirect to login if explicitly unauthenticated and not loading
  useEffect(() => {
    if (status === 'loading') return; // Wait for session to load

    // Only redirect if we're sure the user is not authenticated
    // Don't redirect on token refresh errors - let the user see the dashboard
    if (status === 'unauthenticated') {
      // Add a small delay to prevent immediate redirects during token refresh
      const timer = setTimeout(() => {
        router.push('/login');
      }, 1000);

      return () => clearTimeout(timer);
    }
  }, [status, router]);

  const handleRefresh = async () => {
    setRefreshing(true);
    await refreshDashboard();
    setTimeout(() => setRefreshing(false), 1000);
  };

  // Date range handler
  const handleDateRangeChange = (newRange: string) => {
    setDateRange(newRange);
    updateDateRange(newRange);
    toast({
      title: "Date Range Updated",
      description: `Showing data for ${dateRangeOptions.find(opt => opt.value === newRange)?.label}`,
    });
  };

  // Export handlers
  const handleExportCSV = async () => {
    try {
      toast({
        title: "Export Started",
        description: "Generating CSV from server...",
      });

      // Get date range parameters
      const dateParams = getDateRangeParams(dateRange);
      const queryParams = new URLSearchParams({
        ...dateParams,
        format: 'csv'
      }).toString();

      // Try backend export first, fallback to client-side if it fails
      try {
        const response = await fetch(`${MAIN_URL}${DASHBOARD_ANALYTICS_EXPORT}?${queryParams}`, {
          headers: {
            'Authorization': `Bearer ${session?.user?.access || ''}`,
          },
        });

        if (response.ok) {
          const blob = await response.blob();
          const url = window.URL.createObjectURL(blob);
          const link = document.createElement('a');
          link.href = url;
          link.download = `dashboard-analytics-${dateRange}.csv`;
          document.body.appendChild(link);
          link.click();
          document.body.removeChild(link);
          window.URL.revokeObjectURL(url);

          toast({
            title: "Export Successful",
            description: "Dashboard data exported as CSV",
          });
          return;
        }
      } catch (backendError) {
        console.warn('Backend export failed, using client-side export:', backendError);
      }

      // Fallback to client-side export
      const csvContent = generateCSVContent();
      downloadFile(csvContent, `dashboard-analytics-${dateRange}.csv`, 'text/csv');
      toast({
        title: "Export Successful",
        description: "Dashboard data exported as CSV",
      });
    } catch (error) {
      console.error('CSV export error:', error);
      toast({
        title: "Export Failed",
        description: "Failed to export data as CSV",
        variant: "destructive",
      });
    }
  };

  // Helper function to get date range parameters (moved here for CSV export)
  const getDateRangeParams = (range: string = '30d') => {
    const now = new Date();
    let daysBack = 30;

    switch (range) {
      case '7d':
        daysBack = 7;
        break;
      case '30d':
        daysBack = 30;
        break;
      case '90d':
        daysBack = 90;
        break;
      case '1y':
        daysBack = 365;
        break;
      default:
        daysBack = 30;
    }

    const dateFrom = new Date(now.getTime() - (daysBack * 24 * 60 * 60 * 1000));
    const dateTo = now;

    return {
      date_from: dateFrom.toISOString().split('T')[0],
      date_to: dateTo.toISOString().split('T')[0]
    };
  };

  // const handleExportPDF = async () => {
  //   try {
  //     toast({
  //       title: "Export Started",
  //       description: "Generating professional PDF report...",
  //     });

  //     // Import jsPDF and autoTable
  //     const { jsPDF } = await import('jspdf');

  //     // Try to import and use autoTable
  //     let autoTable: any = null;
  //     try {
  //       const autoTableModule = await import('jspdf-autotable');
  //       autoTable = autoTableModule.default || autoTableModule;
  //     } catch (autoTableError) {
  //       console.warn('AutoTable not available, using enhanced basic PDF export');
  //     }

  //     const doc = new jsPDF();
  //     const pageWidth = doc.internal.pageSize.width;
  //     const pageHeight = doc.internal.pageSize.height;
  //     let currentY = 30;

  //     // Add header with background
  //     doc.setFillColor(79, 70, 229); // Indigo background
  //     doc.rect(0, 0, pageWidth, 50, 'F');

  //     // Add title
  //     doc.setTextColor(255, 255, 255); // White text
  //     doc.setFontSize(24);
  //     doc.setFont('helvetica', 'bold');
  //     doc.text('Dashboard Analytics Report', 20, 25);

  //     // Add subtitle
  //     doc.setFontSize(12);
  //     doc.setFont('helvetica', 'normal');
  //     const selectedRange = dateRangeOptions.find(opt => opt.value === dateRange)?.label || 'Last 30 days';
  //     doc.text(`${selectedRange} • Generated on ${new Date().toLocaleDateString()}`, 20, 35);

  //     // Reset text color for body
  //     doc.setTextColor(0, 0, 0);
  //     currentY = 70;

  //     // Add statistics section if available
  //     if (statistics) {
  //       // Section header with background
  //       doc.setFillColor(248, 250, 252); // Light gray background
  //       doc.rect(15, currentY - 5, pageWidth - 30, 25, 'F');

  //       doc.setFontSize(18);
  //       doc.setFont('helvetica', 'bold');
  //       doc.setTextColor(79, 70, 229); // Indigo text
  //       doc.text('📊 Key Performance Metrics', 20, currentY + 8);

  //       doc.setTextColor(0, 0, 0); // Reset to black
  //       currentY += 35;

  //       if (autoTable) {
  //         // Professional table with autoTable
  //         const statsData = [
  //           ['Metric', 'Current Period', 'Previous Period', 'Change (%)'],
  //           ['💰 Total Revenue', `₹${statistics.last_30_days.total_revenue.toLocaleString()}`, `₹${statistics.previous_30_days.total_revenue.toLocaleString()}`, `${statistics.percentage_delta.revenue_delta || 0}%`],
  //           ['👥 New Customers', statistics.last_30_days.new_customers.toString(), statistics.previous_30_days.new_customers.toString(), `${statistics.percentage_delta.new_customers_delta || 0}%`],
  //           ['📦 Total Orders', statistics.last_30_days.total_orders.toString(), statistics.previous_30_days.total_orders.toString(), `${statistics.percentage_delta.total_orders_delta || 0}%`],
  //           ['🛍️ Active Products', statistics.active_products.toString(), '-', '-']
  //         ];

  //         autoTable(doc, {
  //           head: [statsData[0]],
  //           body: statsData.slice(1),
  //           startY: currentY,
  //           theme: 'striped',
  //           headStyles: {
  //             fillColor: [79, 70, 229],
  //             textColor: [255, 255, 255],
  //             fontSize: 12,
  //             fontStyle: 'bold'
  //           },
  //           bodyStyles: {
  //             fontSize: 11,
  //             cellPadding: 8
  //           },
  //           alternateRowStyles: {
  //             fillColor: [248, 250, 252]
  //           },
  //           margin: { left: 20, right: 20 }
  //         });

  //         currentY = (doc as any).lastAutoTable?.finalY + 25 || currentY + 100;
  //       } else {
  //         // Enhanced fallback with better formatting
  //         const metrics = [
  //           { icon: '💰', label: 'Total Revenue', current: `₹${statistics.last_30_days.total_revenue.toLocaleString()}`, previous: `₹${statistics.previous_30_days.total_revenue.toLocaleString()}`, change: `${statistics.percentage_delta.revenue_delta || 0}%` },
  //           { icon: '👥', label: 'New Customers', current: statistics.last_30_days.new_customers.toString(), previous: statistics.previous_30_days.new_customers.toString(), change: `${statistics.percentage_delta.new_customers_delta || 0}%` },
  //           { icon: '📦', label: 'Total Orders', current: statistics.last_30_days.total_orders.toString(), previous: statistics.previous_30_days.total_orders.toString(), change: `${statistics.percentage_delta.total_orders_delta || 0}%` },
  //           { icon: '🛍️', label: 'Active Products', current: statistics.active_products.toString(), previous: '-', change: '-' }
  //         ];

  //         metrics.forEach((metric, index) => {
  //           // Metric box
  //           doc.setFillColor(255, 255, 255);
  //           doc.rect(20, currentY, pageWidth - 40, 25, 'F');
  //           doc.setDrawColor(229, 231, 235);
  //           doc.rect(20, currentY, pageWidth - 40, 25, 'S');

  //           // Metric content
  //           doc.setFontSize(12);
  //           doc.setFont('helvetica', 'bold');
  //           doc.text(`${metric.icon} ${metric.label}`, 25, currentY + 8);

  //           doc.setFont('helvetica', 'normal');
  //           doc.setFontSize(10);
  //           doc.text(`Current: ${metric.current}`, 25, currentY + 15);
  //           doc.text(`Previous: ${metric.previous}`, 25, currentY + 20);
  //           doc.text(`Change: ${metric.change}`, pageWidth - 60, currentY + 15);

  //           currentY += 30;
  //         });

  //         currentY += 10;
  //       }
  //     }

  //     // Add product performance section if available
  //     if (analytics?.product_performance && analytics.product_performance.length > 0) {
  //       // Check if we need a new page
  //       if (currentY > pageHeight - 100) {
  //         doc.addPage();
  //         currentY = 30;
  //       }

  //       // Section header with background
  //       doc.setFillColor(248, 250, 252);
  //       doc.rect(15, currentY - 5, pageWidth - 30, 25, 'F');

  //       doc.setFontSize(18);
  //       doc.setFont('helvetica', 'bold');
  //       doc.setTextColor(79, 70, 229);
  //       doc.text('🏆 Top Product Performance', 20, currentY + 8);

  //       doc.setTextColor(0, 0, 0);
  //       currentY += 35;

  //       if (autoTable) {
  //         // Professional product table
  //         const productData = analytics.product_performance.slice(0, 10).map((item, index) => [
  //           `${index + 1}`,
  //           item.name,
  //           item.sales.toString(),
  //           `₹${item.revenue.toLocaleString()}`,
  //           item.growth
  //         ]);

  //         autoTable(doc, {
  //           head: [['#', 'Product Name', 'Sales', 'Revenue', 'Growth']],
  //           body: productData,
  //           startY: currentY,
  //           theme: 'striped',
  //           headStyles: {
  //             fillColor: [16, 185, 129], // Green theme for products
  //             textColor: [255, 255, 255],
  //             fontSize: 12,
  //             fontStyle: 'bold'
  //           },
  //           bodyStyles: {
  //             fontSize: 10,
  //             cellPadding: 6
  //           },
  //           alternateRowStyles: {
  //             fillColor: [240, 253, 244]
  //           },
  //           columnStyles: {
  //             0: { cellWidth: 15, halign: 'center' },
  //             1: { cellWidth: 80 },
  //             2: { cellWidth: 25, halign: 'center' },
  //             3: { cellWidth: 35, halign: 'right' },
  //             4: { cellWidth: 25, halign: 'center' }
  //           },
  //           margin: { left: 20, right: 20 }
  //         });

  //         currentY = (doc as any).lastAutoTable?.finalY + 25 || currentY + 100;
  //       } else {
  //         // Enhanced fallback for products
  //         analytics.product_performance.slice(0, 10).forEach((item, index) => {
  //           if (currentY > pageHeight - 40) {
  //             doc.addPage();
  //             currentY = 30;
  //           }

  //           // Product card
  //           doc.setFillColor(255, 255, 255);
  //           doc.rect(20, currentY, pageWidth - 40, 20, 'F');
  //           doc.setDrawColor(229, 231, 235);
  //           doc.rect(20, currentY, pageWidth - 40, 20, 'S');

  //           // Product info
  //           doc.setFontSize(11);
  //           doc.setFont('helvetica', 'bold');
  //           doc.text(`${index + 1}. ${item.name}`, 25, currentY + 8);

  //           doc.setFont('helvetica', 'normal');
  //           doc.setFontSize(9);
  //           doc.text(`Sales: ${item.sales}`, 25, currentY + 15);
  //           doc.text(`Revenue: ₹${item.revenue.toLocaleString()}`, pageWidth/2, currentY + 15);
  //           doc.text(`Growth: ${item.growth}`, pageWidth - 60, currentY + 15);

  //           currentY += 25;
  //         });
  //         currentY += 15;
  //       }
  //     }

  //     // Add sales chart data if available
  //     if (analytics?.sales_chart && analytics.sales_chart.length > 0) {
  //       // Check if we need a new page
  //       if (currentY > pageHeight - 80) {
  //         doc.addPage();
  //         currentY = 30;
  //       }

  //       // Section header
  //       doc.setFillColor(248, 250, 252);
  //       doc.rect(15, currentY - 5, pageWidth - 30, 25, 'F');

  //       doc.setFontSize(18);
  //       doc.setFont('helvetica', 'bold');
  //       doc.setTextColor(79, 70, 229);
  //       doc.text('📈 Sales Trend Analysis', 20, currentY + 8);

  //       doc.setTextColor(0, 0, 0);
  //       currentY += 35;

  //       if (autoTable) {
  //         // Sales data table
  //         const salesData = analytics.sales_chart.map(item => [
  //           item.name,
  //           item.sales.toLocaleString()
  //         ]);

  //         autoTable(doc, {
  //           head: [['Period', 'Sales Volume']],
  //           body: salesData,
  //           startY: currentY,
  //           theme: 'striped',
  //           headStyles: {
  //             fillColor: [59, 130, 246], // Blue theme for sales
  //             textColor: [255, 255, 255],
  //             fontSize: 12,
  //             fontStyle: 'bold'
  //           },
  //           bodyStyles: {
  //             fontSize: 11,
  //             cellPadding: 8
  //           },
  //           alternateRowStyles: {
  //             fillColor: [239, 246, 255]
  //           },
  //           columnStyles: {
  //             0: { cellWidth: 100 },
  //             1: { cellWidth: 60, halign: 'right' }
  //           },
  //           margin: { left: 20, right: 20 }
  //         });

  //         currentY = (doc as any).lastAutoTable?.finalY + 25 || currentY + 80;
  //       } else {
  //         // Enhanced sales chart fallback
  //         analytics.sales_chart.forEach((item, index) => {
  //           if (currentY > pageHeight - 30) {
  //             doc.addPage();
  //             currentY = 30;
  //           }

  //           // Sales item
  //           doc.setFillColor(255, 255, 255);
  //           doc.rect(20, currentY, pageWidth - 40, 15, 'F');
  //           doc.setDrawColor(229, 231, 235);
  //           doc.rect(20, currentY, pageWidth - 40, 15, 'S');

  //           doc.setFontSize(11);
  //           doc.setFont('helvetica', 'normal');
  //           doc.text(item.name, 25, currentY + 8);
  //           doc.text(item.sales.toLocaleString(), pageWidth - 60, currentY + 8);

  //           currentY += 18;
  //         });
  //       }
  //     }

  //     // Add footer
  //     if (currentY > pageHeight - 50) {
  //       doc.addPage();
  //       currentY = 30;
  //     }

  //     doc.setFillColor(248, 250, 252);
  //     doc.rect(0, pageHeight - 30, pageWidth, 30, 'F');

  //     doc.setFontSize(10);
  //     doc.setTextColor(107, 114, 128);
  //     doc.text('Generated by E-Commerce Admin Dashboard', 20, pageHeight - 15);
  //     doc.text(`Report Date: ${new Date().toLocaleString()}`, pageWidth - 80, pageHeight - 15);

  //     doc.save(`dashboard-analytics-${dateRange}.pdf`);

  //     toast({
  //       title: "Export Successful",
  //       description: "Professional PDF report generated successfully!",
  //     });
  //   } catch (error) {
  //     console.error('PDF export error:', error);
  //     toast({
  //       title: "Export Failed",
  //       description: "Failed to export data as PDF",
  //       variant: "destructive",
  //     });
  //   }
  // };

  const handleExportExcel = async () => {
    try {
      toast({
        title: "Export Started",
        description: "Generating Excel report...",
      });

      const XLSX = await import('xlsx');

      // Create workbook
      const wb = XLSX.utils.book_new();

      // Statistics sheet
      if (statistics) {
        const statsData = [
          ['Metric', 'Current Period', 'Previous Period', 'Change (%)'],
          ['Total Revenue', statistics.last_30_days.total_revenue, statistics.previous_30_days.total_revenue, statistics.percentage_delta.revenue_delta || 0],
          ['New Customers', statistics.last_30_days.new_customers, statistics.previous_30_days.new_customers, statistics.percentage_delta.new_customers_delta || 0],
          ['Total Orders', statistics.last_30_days.total_orders, statistics.previous_30_days.total_orders, statistics.percentage_delta.total_orders_delta || 0],
          ['Active Products', statistics.active_products, '-', '-']
        ];

        const statsWs = XLSX.utils.aoa_to_sheet(statsData);
        XLSX.utils.book_append_sheet(wb, statsWs, 'Statistics');
      }

      // Product performance sheet
      if (analytics?.product_performance && analytics.product_performance.length > 0) {
        const productData = [
          ['Product ID', 'Product Name', 'Sales', 'Revenue', 'Growth'],
          ...analytics.product_performance.map(item => [
            item.id,
            item.name,
            item.sales,
            item.revenue,
            item.growth
          ])
        ];

        const productWs = XLSX.utils.aoa_to_sheet(productData);
        XLSX.utils.book_append_sheet(wb, productWs, 'Product Performance');
      }

      // Sales chart data sheet
      if (analytics?.sales_chart && analytics.sales_chart.length > 0) {
        const salesData = [
          ['Period', 'Sales'],
          ...analytics.sales_chart.map(item => [item.name, item.sales])
        ];

        const salesWs = XLSX.utils.aoa_to_sheet(salesData);
        XLSX.utils.book_append_sheet(wb, salesWs, 'Sales Chart');
      }

      // Save file
      XLSX.writeFile(wb, `dashboard-analytics-${dateRange}.xlsx`);

      toast({
        title: "Export Successful",
        description: "Dashboard data exported as Excel",
      });
    } catch (error) {
      console.error('Excel export error:', error);
      toast({
        title: "Export Failed",
        description: "Failed to export data as Excel",
        variant: "destructive",
      });
    }
  };

  // Helper function to generate CSV content
  const generateCSVContent = () => {
    if (!analytics?.product_performance && !statistics) {
      throw new Error('No analytics data available');
    }

    let csvContent = '';

    // Add header information
    const selectedRange = dateRangeOptions.find(opt => opt.value === dateRange)?.label || 'Last 30 days';
    csvContent += `Dashboard Analytics Report\n`;
    csvContent += `Date Range: ${selectedRange}\n`;
    csvContent += `Generated on: ${new Date().toLocaleDateString()}\n\n`;

    // Add statistics if available
    if (statistics) {
      csvContent += `Key Statistics\n`;
      csvContent += `Metric,Current Period,Previous Period,Change (%)\n`;
      csvContent += `Total Revenue,${statistics.last_30_days.total_revenue},${statistics.previous_30_days.total_revenue},${statistics.percentage_delta.revenue_delta || 0}\n`;
      csvContent += `New Customers,${statistics.last_30_days.new_customers},${statistics.previous_30_days.new_customers},${statistics.percentage_delta.new_customers_delta || 0}\n`;
      csvContent += `Total Orders,${statistics.last_30_days.total_orders},${statistics.previous_30_days.total_orders},${statistics.percentage_delta.total_orders_delta || 0}\n`;
      csvContent += `Active Products,${statistics.active_products},-,-\n\n`;
    }

    // Add product performance if available
    if (analytics?.product_performance && analytics.product_performance.length > 0) {
      csvContent += `Product Performance\n`;
      csvContent += `Product ID,Product Name,Sales,Revenue,Growth\n`;
      analytics.product_performance.forEach(item => {
        csvContent += `${item.id},"${item.name}",${item.sales},${item.revenue},${item.growth}\n`;
      });
      csvContent += '\n';
    }

    // Add sales chart data if available
    if (analytics?.sales_chart && analytics.sales_chart.length > 0) {
      csvContent += `Sales Chart Data\n`;
      csvContent += `Period,Sales\n`;
      analytics.sales_chart.forEach(item => {
        csvContent += `"${item.name}",${item.sales}\n`;
      });
    }

    return csvContent;
  };

  // Helper function to download file
  const downloadFile = (content: string, filename: string, mimeType: string) => {
    const blob = new Blob([content], { type: mimeType });
    const url = window.URL.createObjectURL(blob);
    const link = document.createElement('a');
    link.href = url;
    link.download = filename;
    document.body.appendChild(link);
    link.click();
    document.body.removeChild(link);
    window.URL.revokeObjectURL(url);
  };

  // Quick action handlers
  const handleViewOrders = () => {
    router.push('/orders');
  };

  const handleManageCustomers = () => {
    router.push('/customers');
  };

  const handleAnalyticsReport = () => {
    router.push('/analytics');
  };

  const handleSalesPerformance = () => {
    router.push('/analytics'); // Redirect to analytics page for sales performance
  };

  // Show loading screen while checking authentication
  if (status === 'loading') {
    return (
      <div className="min-h-screen bg-gradient-to-br from-gray-50 to-white flex items-center justify-center">
        <div className="text-center">
          <Loader2 className="h-8 w-8 animate-spin mx-auto mb-4 text-blue-600" />
          <p className="text-gray-600">Loading...</p>
        </div>
      </div>
    );
  }

  // Don't render dashboard if not authenticated (will redirect)
  if (!isAuthenticated) {
    return null;
  }

  return (
    <div className="min-h-screen bg-gradient-to-br from-gray-50 to-white">
      <div className="space-y-4 sm:space-y-6 lg:space-y-8 animate-fadeIn max-w-full overflow-hidden">
        {/* Header Section */}
        <div className="flex flex-col lg:flex-row lg:items-center lg:justify-between gap-4">
          <div className="min-w-0 flex-1">
            <h1 className="text-xl sm:text-2xl lg:text-3xl xl:text-4xl font-bold bg-gradient-to-r from-gray-900 to-gray-600 bg-clip-text text-transparent truncate">
              Dashboard
            </h1>
            <p className="text-gray-600 mt-1 sm:mt-2 text-xs sm:text-sm lg:text-base">
              Welcome back! Here&apos;s what&apos;s happening with your store today.
            </p>
          </div>

          <div className="flex flex-col sm:flex-row items-stretch sm:items-center gap-2 sm:gap-3 flex-shrink-0">
            {/* Date Range Selector */}
            <DropdownMenu>
              <DropdownMenuTrigger asChild>
                <Button variant="outline" size="sm" className="gap-2 text-xs sm:text-sm whitespace-nowrap">
                  <Calendar className="h-3 w-3 sm:h-4 sm:w-4" />
                  <span className="hidden sm:inline">
                    {dateRangeOptions.find(opt => opt.value === dateRange)?.label}
                  </span>
                  <span className="sm:hidden">
                    {dateRange}
                  </span>
                  <ChevronDown className="h-3 w-3 sm:h-4 sm:w-4" />
                </Button>
              </DropdownMenuTrigger>
              <DropdownMenuContent align="end">
                {dateRangeOptions.map((option) => (
                  <DropdownMenuItem
                    key={option.value}
                    onClick={() => handleDateRangeChange(option.value)}
                    className={dateRange === option.value ? "bg-gray-100" : ""}
                  >
                    {option.label}
                  </DropdownMenuItem>
                ))}
              </DropdownMenuContent>
            </DropdownMenu>

            {/* Export Dropdown */}
            <DropdownMenu>
              <DropdownMenuTrigger asChild>
                <Button variant="outline" size="sm" className="gap-2 text-xs sm:text-sm whitespace-nowrap">
                  <Download className="h-3 w-3 sm:h-4 sm:w-4" />
                  <span className="hidden sm:inline">Export</span>
                  <span className="sm:hidden">Export</span>
                  <ChevronDown className="h-3 w-3 sm:h-4 sm:w-4" />
                </Button>
              </DropdownMenuTrigger>
              <DropdownMenuContent align="end">
                <DropdownMenuItem onClick={handleExportCSV}>
                  <FileSpreadsheet className="h-4 w-4 mr-2" />
                  Export as CSV
                </DropdownMenuItem>
                {/* <DropdownMenuItem onClick={handleExportPDF}>
                  <FileText className="h-4 w-4 mr-2" />
                  Export as PDF
                </DropdownMenuItem> */}
                <DropdownMenuItem onClick={handleExportExcel}>
                  <FileSpreadsheet className="h-4 w-4 mr-2" />
                  Export as Excel
                </DropdownMenuItem>
              </DropdownMenuContent>
            </DropdownMenu>

            {/* Refresh Button */}
            <Button
              variant="outline"
              size="sm"
              onClick={handleRefresh}
              disabled={refreshing}
              className="gap-2 text-xs sm:text-sm whitespace-nowrap"
            >
              <RefreshCw className={`h-3 w-3 sm:h-4 sm:w-4 ${refreshing ? 'animate-spin' : ''}`} />
              <span className="hidden sm:inline">Refresh</span>
              <span className="sm:hidden">Refresh</span>
            </Button>
          </div>
        </div>

        {/* Error Alert */}
        {hasError && (
          <Card className="border-amber-200 bg-amber-50">
            <CardContent className="pt-6">
              <div className="flex items-center gap-2 text-amber-800">
                <Bell className="h-4 w-4" />
                <p className="text-sm">
                  Some data couldn&apos;t be loaded. Please check your connection and try refreshing.
                </p>
              </div>
            </CardContent>
          </Card>
        )}

        {/* Statistics Cards */}
        <StatisticsCards
          data={statistics}
          loading={loading.statistics}
          error={error.statistics}
        />

        {/* Analytics Charts */}
        <AnalyticsCharts
          analyticsData={analytics}
          graphData={graphData}
          loading={loading.analytics || loading.graphData}
          error={error.analytics || error.graphData}
        />

        {/* Recent Activity */}
        <div className="grid gap-4 sm:gap-6 lg:grid-cols-3">
          <div className="lg:col-span-2 min-w-0">
            <RecentActivity
              recentOrders={recentOrders}
              loading={loading.recentOrders}
              error={error.recentOrders}
            />
          </div>

          {/* Quick Actions */}
          <Card className="border-0 shadow-lg bg-gradient-to-br from-white to-indigo-50/30">
            <CardHeader className="pb-3 sm:pb-6">
              <CardTitle className="flex items-center gap-2 text-gray-900 text-lg sm:text-xl">
                <Settings className="h-4 w-4 sm:h-5 sm:w-5 text-indigo-500" />
                Quick Actions
              </CardTitle>
            </CardHeader>
            <CardContent className="space-y-2 sm:space-y-3 px-4 sm:px-6">
              <Button
                className="w-full justify-start gap-2 text-xs sm:text-sm h-8 sm:h-10"
                variant="outline"
                onClick={handleViewOrders}
              >
                <ShoppingCart className="h-3 w-3 sm:h-4 sm:w-4" />
                <span className="truncate">View All Orders</span>
              </Button>
              <Button
                className="w-full justify-start gap-2 text-xs sm:text-sm h-8 sm:h-10"
                variant="outline"
                onClick={handleManageCustomers}
              >
                <Users className="h-3 w-3 sm:h-4 sm:w-4" />
                <span className="truncate">Manage Customers</span>
              </Button>
              <Button
                className="w-full justify-start gap-2 text-xs sm:text-sm h-8 sm:h-10"
                variant="outline"
                onClick={handleAnalyticsReport}
              >
                <BarChart3 className="h-3 w-3 sm:h-4 sm:w-4" />
                <span className="truncate">Analytics Report</span>
              </Button>
              <Button
                className="w-full justify-start gap-2 text-xs sm:text-sm h-8 sm:h-10"
                variant="outline"
                onClick={handleSalesPerformance}
              >
                <TrendingUp className="h-3 w-3 sm:h-4 sm:w-4" />
                <span className="truncate">Sales Performance</span>
              </Button>
            </CardContent>
          </Card>
        </div>
      </div>
    </div>
  );
};

export default Dashboard;