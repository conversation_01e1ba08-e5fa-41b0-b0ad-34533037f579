"use client";
import { useEffect, useMemo, useState } from "react";
import { CartItemList } from "../../components/cart/CartItemList";
import { OrderSummary } from "../../components/cart/OrderSummary";
// import { PromoCodeInput } from "../../components/cart/PromoCodeInput";
import { Button } from "../../components/ui/button";
import { ShoppingBag } from "lucide-react";
import Link from "next/link";
import MainHOF from "../../layout/MainHOF";
import useApi from "../../hooks/useApi";
import { MAIN_URL, UPDATE_CART, USER_CART } from "../../constant/urls";
import { useSession } from "next-auth/react";
import { CartLoading } from "@/components/ui/loading/CartLoading";
import useStorage from "@/hooks/useStorage";
import ClientOnly from "@/components/ClientOnly";

const Cart = () => {
  const { read, error }: any = useApi(MAIN_URL);
  const {
    data: cart,
    update,
    loading: cartLoading,
    error: cartError,
  } = useApi(MAIN_URL);
  const [promotion, setPromotion] = useState<any>({});
  const { status }: any = useSession();
  const [data, setData] = useState<any>({});
  const [loading, setLoading] = useState(false);
  const storage = useStorage('local');

  // Client-side only effect to get promotion from localStorage using our custom hook
  useEffect(() => {
    const getPromotionFromStorage = () => {
      try {
        const promo = storage.getItem("promotion") ?? "{}";
        const parsedPromotion = JSON.parse(promo);
        if (parsedPromotion && Object.keys(parsedPromotion).length > 0) {
          setPromotion(parsedPromotion);
        }
      } catch (error) {
        console.error("Error reading promotion from localStorage:", error);
      }
    };

    getPromotionFromStorage();
  }, [storage]);

  const getUserCart = async () => {
    try {
      setLoading(true);
      const response = await read(USER_CART);
      if (Boolean(response.total_items > 0)) {
        setData(response);
      }
    } catch (error) {
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    if (status === "authenticated") {
      getUserCart();
    }
  }, [status]);

  // Calculate GST breakdown using useMemo for better performance
  const gstBreakdown = useMemo(() => {
    if (!Array.isArray(data?.items) || data?.items.length === 0) {
      return null;
    }

    // If we have backend GST breakdown, use it
    if (data?.gst_breakdown) {
      return data.gst_breakdown;
    }

    // Calculate GST breakdown from current cart items
    let totalBaseAmount = 0;
    let totalGstAmount = 0;
    let totalMrp = 0;
    const itemDetails: any[] = [];

    data.items.forEach((item: any) => {
      const unitPrice = parseFloat(item.product.price);
      const quantity = item.quantity;
      const itemMrp = unitPrice * quantity;

      // Calculate base price (assuming 18% GST for simplification)
      const gstRate = 18; // Default GST rate
      const itemBaseAmount = itemMrp / (1 + gstRate / 100);
      const itemGstAmount = itemMrp - itemBaseAmount;

      totalBaseAmount += itemBaseAmount;
      totalGstAmount += itemGstAmount;
      totalMrp += itemMrp;

      itemDetails.push({
        product: item.product,
        quantity: quantity,
        gst_rate: gstRate,
        gst_amount: itemGstAmount,
        cgst_amount: itemGstAmount / 2,
        sgst_amount: itemGstAmount / 2,
        igst_amount: 0,
        hsn_code: item.product.hsn_code || "default"
      });
    });

    return {
      subtotal: totalBaseAmount,
      total_gst_amount: totalGstAmount,
      total_cgst_amount: totalGstAmount / 2,
      total_sgst_amount: totalGstAmount / 2,
      total_igst_amount: 0,
      total_mrp: totalMrp,
      item_details: itemDetails,
      is_inter_state: false
    };
  }, [data?.items, data?.gst_breakdown]);

  // Calculate subtotal from GST breakdown
  const subtotalValue = useMemo(() => {
    if (!gstBreakdown) return 0;
    return parseFloat(gstBreakdown.subtotal.toString());
  }, [gstBreakdown]);

  const gstAmount = gstBreakdown?.total_gst_amount || (subtotalValue * 0.18);
  const cgstAmount = gstBreakdown?.total_cgst_amount || (gstAmount / 2);
  const sgstAmount = gstBreakdown?.total_sgst_amount || (gstAmount / 2);
  const igstAmount = gstBreakdown?.total_igst_amount || 0;

  // Format subtotal for display but keep the numeric value for calculations
  const subtotal = subtotalValue.toFixed(2);

  // Calculate shipping cost based on numeric subtotal value
  const shippingCost = subtotalValue > 500 ? 0 : 29.99;

  // Calculate total properly with numeric values including GST
  // Use total_mrp from GST breakdown if available (this is the correct GST-inclusive total)
  let totalValue = gstBreakdown?.total_mrp
    ? parseFloat(gstBreakdown.total_mrp.toString()) + shippingCost
    : subtotalValue + gstAmount + shippingCost;

  if (Boolean(promotion?.discount)) {
    totalValue = totalValue - Number(promotion?.discount);
  }

  // Ensure total is never negative
  totalValue = Math.max(0, totalValue);

  const handleAddOrRemoveToCart = async (itemId: number, action: string) => {
    try {
      const res = await update(UPDATE_CART, {
        item_id: itemId,
        action,
      });

      if (Boolean(res)) {
        // Update local state immediately for better UX
        // useMemo will automatically recalculate GST breakdown when items change
        setData((prev: any) => {
          if (!prev?.items) return prev;

          const updatedItems = action === "delete"
            ? prev.items.filter((item: any) => item.id !== itemId)
            : prev.items.map((item: any) => {
                if (item.id === itemId) {
                  const newQuantity = action === "add"
                    ? item.quantity + 1
                    : Math.max(1, item.quantity - 1);
                  return { ...item, quantity: newQuantity };
                }
                return item;
              });

          return {
            ...prev,
            items: updatedItems,
            total_items: updatedItems.reduce((sum: number, item: any) => sum + item.quantity, 0),
            // Remove gst_breakdown so useMemo will recalculate it
            gst_breakdown: undefined
          };
        });
      }
    } catch (error) {
      console.error("Failed to update cart:", error);
      // On error, refresh from backend to ensure consistency
      getUserCart();
    }
  };

  if (loading) {
    return (
      <MainHOF>
        <div className="container mx-auto px-4 py-8">
          <CartLoading />
        </div>
      </MainHOF>
    );
  }

  return (
    <MainHOF>
      <div className="container mx-auto px-4 py-8 max-w-7xl">
        <div className="flex items-center justify-between mb-8">
          <h1 className="text-3xl font-bold">Your Shopping Cart</h1>
          <div className="text-sm text-gray-500">
            {data?.items?.length > 0 && `${data.items.length} item${data.items.length > 1 ? 's' : ''} in your cart`}
          </div>
        </div>

        {Boolean(data?.items?.length) ? (
          <div className="grid grid-cols-1 lg:grid-cols-3 gap-8">
            <div className="lg:col-span-2 space-y-8">
              <div className="bg-white rounded-lg shadow-sm p-6 transition-all duration-300">
                <CartItemList
                  handleAddOrRemoveToCart={handleAddOrRemoveToCart}
                  items={data?.items ?? []}
                />
              </div>

              {/* <div className="bg-white rounded-lg shadow-sm p-6 transition-all duration-300">
                <PromoCodeInput
                  setPromotion={setPromotion}
                  promotion={promotion}
                />
              </div> */}

              <div className="flex justify-between items-center">
                <Link href="/shop">
                  <Button variant="outline" className="flex items-center gap-2 px-6 py-5 transition-all duration-300 hover:bg-gray-100">
                    <ShoppingBag className="h-4 w-4" />
                    Continue Shopping
                  </Button>
                </Link>

                <div className="text-sm text-gray-500">
                  All prices are inclusive of GST
                </div>
              </div>
            </div>

            <div>
              <OrderSummary
                subtotal={subtotalValue}
                shippingCost={shippingCost ?? 0}
                discount={promotion?.discount ?? 0}
                total={totalValue}
                gstAmount={gstAmount}
                cgstAmount={cgstAmount}
                sgstAmount={sgstAmount}
                igstAmount={igstAmount}
                showGstBreakdown={false}
                gstBreakdown={gstBreakdown}
              />
            </div>
          </div>
        ) : (
          <div className="text-center py-16 bg-white rounded-lg shadow-sm p-12 max-w-2xl mx-auto">
            <div className="flex justify-center mb-6">
              <ShoppingBag className="h-16 w-16 text-gray-300" />
            </div>
            <h2 className="text-2xl font-semibold mb-4">Your cart is empty</h2>
            <p className="text-gray-500 mb-8">Looks like you haven't added anything to your cart yet.</p>
            <Link href="/shop">
              <Button className="px-8 py-6 text-base">
                <ShoppingBag className="mr-2 h-5 w-5" />
                Start Shopping
              </Button>
            </Link>
          </div>
        )}
      </div>
    </MainHOF>
  );
};

export default Cart;
