'use client';

import { Metadata } from 'next';
import { useEffect } from 'react';
import Head from 'next/head';
import Script from 'next/script';

interface ProductMetadataProps {
  product: {
    id: number;
    name: string;
    slug: string;
    description: string;
    price: number;
    category?: {
      name: string;
      slug: string;
    };
    images?: Array<{
      id: number;
      image: string;
    }>;
    average_rating?: number;
  };
}

export const ProductMetadata = ({ product }: ProductMetadataProps) => {
  const baseUrl = process.env.NEXT_PUBLIC_SITE_URL || 
    (typeof window !== 'undefined' ? window.location.origin : '');
  
  const productUrl = `${baseUrl}/product/${product.slug}`;
  const mainImage = product.images && product.images.length > 0 
    ? (product.images[0].image.startsWith('http') 
        ? product.images[0].image 
        : `${baseUrl}${product.images[0].image}`)
    : `${baseUrl}/assets/products/product-placeholder.svg`;

  // Product structured data
  const productSchema = {
    '@context': 'https://schema.org',
    '@type': 'Product',
    name: product.name,
    description: product.description,
    image: mainImage,
    sku: `SKU-${product.id}`,
    mpn: `MPN-${product.id}`,
    brand: {
      '@type': 'Brand',
      name: 'Triumph Enterprises',
    },
    offers: {
      '@type': 'Offer',
      url: productUrl,
      priceCurrency: 'INR',
      price: product.price,
      priceValidUntil: new Date(new Date().setFullYear(new Date().getFullYear() + 1)).toISOString().split('T')[0],
      availability: 'https://schema.org/InStock',
      seller: {
        '@type': 'Organization',
        name: 'Triumph Enterprises',
      },
    },
  };

  // Add review data if available
  if (product.average_rating) {
    Object.assign(productSchema, {
      aggregateRating: {
        '@type': 'AggregateRating',
        ratingValue: product.average_rating,
        bestRating: '5',
        worstRating: '1',
        ratingCount: '1',
      },
    });
  }

  // Breadcrumb structured data
  const breadcrumbSchema = {
    '@context': 'https://schema.org',
    '@type': 'BreadcrumbList',
    itemListElement: [
      {
        '@type': 'ListItem',
        position: 1,
        name: 'Home',
        item: baseUrl,
      },
      {
        '@type': 'ListItem',
        position: 2,
        name: 'Shop',
        item: `${baseUrl}/shop`,
      },
      ...(product.category
        ? [
            {
              '@type': 'ListItem',
              position: 3,
              name: product.category.name,
              item: `${baseUrl}/shop/category/${product.category.slug}`,
            },
          ]
        : []),
      {
        '@type': 'ListItem',
        position: product.category ? 4 : 3,
        name: product.name,
        item: productUrl,
      },
    ],
  };

  return (
    <>
      <Head>
        <title>{`${product.name} | Triumph Enterprises`}</title>
        <meta name="description" content={product?.description?.substring(0, 160)} />
        <meta name="keywords" content={`${product?.name}, ${product?.category?.name || 'hardware'}, triumph enterprises, security products`} />
        
        {/* Open Graph tags */}
        <meta property="og:title" content={`${product.name} | Triumph Enterprises`} />
        <meta property="og:description" content={product?.description?.substring(0, 160)} />
        <meta property="og:image" content={mainImage} />
        <meta property="og:url" content={productUrl} />
        <meta property="og:type" content="product" />
        <meta property="product:price:amount" content={product.price.toString()} />
        <meta property="product:price:currency" content="INR" />
        
        {/* Twitter Card tags */}
        <meta name="twitter:card" content="summary_large_image" />
        <meta name="twitter:title" content={`${product.name} | Triumph Enterprises`} />
        <meta name="twitter:description" content={product?.description?.substring(0, 160)} />
        <meta name="twitter:image" content={mainImage} />
        
        {/* Canonical URL */}
        <link rel="canonical" href={productUrl} />
      </Head>

      {/* Structured Data */}
      <Script
        id="product-schema"
        type="application/ld+json"
        dangerouslySetInnerHTML={{ __html: JSON.stringify(productSchema) }}
      />
      <Script
        id="breadcrumb-schema"
        type="application/ld+json"
        dangerouslySetInnerHTML={{ __html: JSON.stringify(breadcrumbSchema) }}
      />
    </>
  );
};
