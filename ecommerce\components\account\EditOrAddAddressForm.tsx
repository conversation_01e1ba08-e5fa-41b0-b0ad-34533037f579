import React from "react";
import {
  Sheet,
  <PERSON><PERSON><PERSON>onte<PERSON>,
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
  Sheet<PERSON>rigger,
} from "@/components/ui/sheet";

const EditOrAddAddressForm = ({
  open,
  onOpenChange,
  form,
  button,
  title,
}: any) => {
  return (
    <Sheet open={open} onOpenChange={onOpenChange}>
      <SheetTrigger asChild>{button}</SheetTrigger>
      <SheetContent>
        <SheetHeader>
          <SheetTitle>{title}</SheetTitle>
        </SheetHeader>
        <div className="h-full overflow-y-scroll py-6">{form}</div>
      </SheetContent>
    </Sheet>
  );
};

export default EditOrAddAddressForm;
