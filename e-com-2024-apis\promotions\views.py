# from rest_framework import viewsets, permissions, status
# from rest_framework.decorators import action
# from rest_framework.response import Response
# from .models import Promotion, PromotionUsage
# from .serializers import PromotionSerializer, PromotionUsageSerializer
# from orders.models import Cart

# class PromotionViewSet(viewsets.ModelViewSet):
#     queryset = Promotion.objects.all()
#     serializer_class = PromotionSerializer
#     permission_classes = [permissions.IsAuthenticated]

#     def get_permissions(self):
#         if self.action in ['create', 'update', 'partial_update', 'destroy']:
#             return [permissions.IsAdminUser()]
#         return [permissions.IsAuthenticated()]

#     def get_queryset(self):
#         if self.request.user.is_staff:
#             return Promotion.objects.all()
#         return Promotion.objects.filter(is_active=True)

#     @action(detail=True, methods=['post'])
#     def apply_to_cart(self, request, pk=None):
#         promotion = self.get_object()
        
#         if not promotion.is_valid:
#             return Response(
#                 {"detail": "This promotion code is not valid"},
#                 status=status.HTTP_400_BAD_REQUEST
#             )

#         try:
#             cart = Cart.objects.get(user=request.user)
#         except Cart.DoesNotExist:
#             return Response(
#                 {"detail": "Cart not found"},
#                 status=status.HTTP_404_NOT_FOUND
#             )

#         if cart.subtotal < promotion.min_purchase_amount:
#             return Response(
#                 {
#                     "detail": f"Minimum purchase amount of {promotion.min_purchase_amount} required"
#                 },
#                 status=status.HTTP_400_BAD_REQUEST
#             )

#         discount = promotion.calculate_discount(cart.subtotal)
        
#         return Response({
#             "discount": discount,
#             "final_total": cart.subtotal - discount
#         })

# class PromotionUsageViewSet(viewsets.ReadOnlyModelViewSet):
#     serializer_class = PromotionUsageSerializer
#     permission_classes = [permissions.IsAuthenticated]

#     def get_queryset(self):
#         if self.request.user.is_staff:
#             return PromotionUsage.objects.all()
#         return PromotionUsage.objects.filter(user=self.request.user)

from rest_framework import generics, permissions, status, views
from rest_framework.response import Response
from django.db import transaction
from django.shortcuts import get_object_or_404
from .models import Promotion, PromotionUsage
from .serializers import (
    PromotionSerializer, 
    PromotionUsageSerializer, 
    ApplyPromotionSerializer,
    PromotionCodeOnlySerializer
)
from orders.models import Cart
from decimal import Decimal
from django.utils import timezone
from django.db.models import Q, F

class PromotionListCreateView(generics.ListCreateAPIView):
    serializer_class = PromotionSerializer
    permission_classes = [permissions.IsAuthenticated]
    
    def get_queryset(self):
        queryset = Promotion.objects.all()
        if not self.request.user.is_staff:
            queryset = queryset.filter(is_active=True)
        return queryset

    def get_permissions(self):
        if self.request.method == 'POST':
            return [permissions.IsAdminUser()]
        return [permissions.IsAuthenticated()]

class PromotionRetrieveUpdateDestroyView(generics.RetrieveUpdateDestroyAPIView):
    queryset = Promotion.objects.all()
    serializer_class = PromotionSerializer
    permission_classes = [permissions.IsAuthenticated]
    lookup_field = 'code'
    
    def get_permissions(self):
        if self.request.method in ['PUT', 'PATCH', 'DELETE']:
            return [permissions.IsAdminUser()]
        return [permissions.IsAuthenticated()]


class ApplyPromotionView(generics.GenericAPIView):
    serializer_class = ApplyPromotionSerializer
    permission_classes = [permissions.IsAuthenticated]

    @transaction.atomic
    def post(self, request):
        serializer = self.get_serializer(data=request.data)
        serializer.is_valid(raise_exception=True)
        
        promotion = get_object_or_404(
            Promotion, 
            code=serializer.validated_data['code']
        )
        
        # Check if promotion is valid
        if not promotion.is_valid:
            return Response(
                {"detail": "This promotion code is not valid"},
                status=status.HTTP_400_BAD_REQUEST
            )

        try:
            cart = Cart.objects.select_for_update().get(user=request.user)
        except Cart.DoesNotExist:
            return Response(
                {"detail": "Cart not found"},
                status=status.HTTP_404_NOT_FOUND
            )

        # Validate minimum purchase amount
        if cart.subtotal < promotion.min_purchase_amount:
            return Response(
                {
                    "detail": (
                        f"Minimum purchase amount of {promotion.min_purchase_amount} "
                        f"required. Current cart total: {cart.subtotal}"
                    )
                },
                status=status.HTTP_400_BAD_REQUEST
            )

        # Check if user has already used this promotion
        if PromotionUsage.objects.filter(
            promotion=promotion,
            user=request.user,
            order__isnull=False
        ).exists():
            return Response(
                {"detail": "You have already used this promotion"},
                status=status.HTTP_400_BAD_REQUEST
            )

        discount = promotion.calculate_discount(cart.subtotal)
        final_total = max(Decimal('0.00'), cart.subtotal - discount)

        return Response({
            "discount": discount,
            "final_total": final_total,
            "message": "Promotion applied successfully"
        })

class PromotionUsageListView(generics.ListAPIView):
    serializer_class = PromotionUsageSerializer
    permission_classes = [permissions.IsAuthenticated]

    def get_queryset(self):
        queryset = PromotionUsage.objects.select_related(
            'promotion', 'user', 'order'
        )
        if not self.request.user.is_staff:
            queryset = queryset.filter(user=self.request.user)
        return queryset

class PromotionView(views.APIView):
    permission_classes = [permissions.AllowAny]

    def get(self, request):
        now = timezone.now()
        base_query = Promotion.objects.filter(
            is_active=True,
            start_date__lte=now,
            end_date__gte=now
        ).order_by('-created_at')

        if not request.user.is_authenticated:
            # For unauthenticated users, return latest valid promotion
            promotion = base_query.first()
        else:
            # Get promotions not used by the user and within usage limit
            used_promotions = PromotionUsage.objects.filter(
                user=request.user
            ).values_list('promotion_id', flat=True)

            promotion = base_query.filter(
                Q(usage_limit__isnull=True) |
                Q(times_used__lt=F('usage_limit'))
            ).exclude(
                id__in=used_promotions
            ).first()

        if not promotion:
            return Response({'message': 'No valid promotions available'}, status=status.HTTP_404_NOT_FOUND)

        serializer = PromotionCodeOnlySerializer(promotion)
        return Response(serializer.data)