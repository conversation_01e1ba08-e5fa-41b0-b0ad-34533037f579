import { NextAuthOptions } from "next-auth";
import Credentials<PERSON>rovider from "next-auth/providers/credentials";
import axios from "axios";
import { MAIN_URL, USER_LOGIN } from "@/constant/urls";

export const authOptions: NextAuthOptions = {
  providers: [
    CredentialsProvider({
      name: "credentials",
      credentials: {
        email: { label: "Email", type: "email" },
        password: { label: "Password", type: "password" }
      },
      async authorize(credentials) {
        if (!credentials?.email || !credentials?.password) {
          throw new Error("Email and password are required");
        }

        try {
          const response = await axios.post(`${MAIN_URL}${USER_LOGIN}`, {
            email: credentials.email,
            password: credentials.password,
          });

          const user = response.data;

          if (user && user.access) {
            return {
              id: user.user?.id || user.id,
              email: user.user?.email || credentials.email,
              name: user.user?.first_name && user.user?.last_name 
                ? `${user.user.first_name} ${user.user.last_name}`
                : user.user?.email || credentials.email,
              access: user.access,
              refresh: user.refresh,
              ...user,
            };
          }

          throw new Error("Invalid credentials");
        } catch (error) {
          console.error("Authentication error:", error);
          
          if (axios.isAxiosError(error)) {
            if (error.response?.status === 401) {
              throw new Error("Invalid email or password");
            }
            if (error.response?.data?.detail) {
              throw new Error(error.response.data.detail);
            }
            if (error.response?.data?.message) {
              throw new Error(error.response.data.message);
            }
          }
          
          throw new Error("Authentication failed. Please try again.");
        }
      }
    })
  ],
  
  callbacks: {
    async jwt({ token, user, account }) {
      // Initial sign in
      if (account && user) {
        return {
          ...token,
          access: user.access,
          refresh: user.refresh,
          ...user
        };
      }

      // Return previous token if the access token has not expired yet
      return token;
    },

    async session({ session, token }) {
      return {
        ...session,
        user: {
          ...session.user,
          access: token.access,
          refresh: token.refresh,
          user: token.user,
        },
      };
    },
  },

  pages: {
    signIn: "/login",
    error: "/login",
  },

  session: {
    strategy: "jwt",
    maxAge: 24 * 60 * 60, // 24 hours
  },

  jwt: {
    maxAge: 24 * 60 * 60, // 24 hours
  },

  secret: process.env.NEXTAUTH_SECRET || "your-secret-key-here",

  debug: process.env.NODE_ENV === "development",
};
