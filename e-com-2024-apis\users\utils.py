import logging
from django.core.mail import EmailMultiAlternatives
from django.template.loader import render_to_string
from django.conf import settings
from django.utils import timezone
from datetime import datetime

logger = logging.getLogger(__name__)

def send_contact_emails(contact_data):
    """
    Send emails to both the sender and receiver after a contact form submission.

    Args:
        contact_data (dict): Dictionary containing contact form data
            - name: Sender's name
            - email: Sender's email
            - subject: Email subject
            - message: Email message

    Returns:
        bool: True if emails were sent successfully, False otherwise
    """
    try:
        # Get current date for the notification email
        current_date = datetime.now().strftime("%B %d, %Y, %I:%M %p")

        # Send confirmation email to the sender
        send_confirmation_email(contact_data)

        # Send notification email to the site admin
        contact_data['date'] = current_date
        send_notification_email(contact_data)

        return True
    except Exception as e:
        logger.error(f"Error sending contact emails: {str(e)}")
        return False

def send_confirmation_email(contact_data):
    """
    Send a confirmation email to the person who submitted the contact form.

    Args:
        contact_data (dict): Dictionary containing contact form data
    """
    subject = "Thank you for contacting Triumph Enterprises"
    from_email = settings.DEFAULT_FROM_EMAIL
    to_email = contact_data['email']

    # Render HTML content
    html_content = render_to_string('emails/contact_confirmation.html', {
        'name': contact_data['name'],
        'subject': contact_data['subject'],
        'message': contact_data['message']
    })

    # Create email message
    email = EmailMultiAlternatives(subject, "", from_email, [to_email])
    email.attach_alternative(html_content, "text/html")

    # Send email
    email.send()

def send_notification_email(contact_data):
    """
    Send a notification email to the site admin about the new contact form submission.

    Args:
        contact_data (dict): Dictionary containing contact form data
    """
    subject = f"New Contact Form Submission: {contact_data['subject']}"
    from_email = settings.DEFAULT_FROM_EMAIL

    # Send to both the default admin email and the Triumph Enterprises email
    to_emails = [settings.DEFAULT_FROM_EMAIL, "<EMAIL>"]

    # Render HTML content
    html_content = render_to_string('emails/contact_notification.html', {
        'name': contact_data['name'],
        'email': contact_data['email'],
        'subject': contact_data['subject'],
        'message': contact_data['message'],
        'date': contact_data['date']
    })

    # Create email message
    email = EmailMultiAlternatives(subject, "", from_email, to_emails)
    email.attach_alternative(html_content, "text/html")

    # Send email
    email.send()


def send_deletion_request_emails(deletion_request):
    """
    Send emails for data deletion request - both user confirmation and admin notification.

    Args:
        deletion_request: DataDeletionRequest object

    Returns:
        bool: True if emails were sent successfully, False otherwise
    """
    try:
        # Send confirmation email to the user
        send_deletion_confirmation_email(deletion_request)

        # Send notification email to the admin
        send_deletion_admin_notification(deletion_request)

        return True
    except Exception as e:
        logger.error(f"Error sending deletion request emails: {str(e)}")
        return False


def send_deletion_confirmation_email(deletion_request):
    """
    Send a confirmation email to the user who submitted the deletion request.

    Args:
        deletion_request: DataDeletionRequest object
    """
    subject = "Data Deletion Request Received - Triumph Enterprises"
    from_email = settings.DEFAULT_FROM_EMAIL
    to_email = deletion_request.user.email

    # Render HTML content
    html_content = render_to_string('emails/privacy/deletion_confirmation.html', {
        'user': deletion_request.user,
        'deletion_request': deletion_request,
        'request_date': deletion_request.request_date.strftime("%B %d, %Y, %I:%M %p"),
        'site_name': 'Triumph Enterprises'
    })

    # Create email message
    email = EmailMultiAlternatives(subject, "", from_email, [to_email])
    email.attach_alternative(html_content, "text/html")

    # Send email
    email.send()
    logger.info(f"Deletion confirmation email sent to user {deletion_request.user.email}")


def send_deletion_admin_notification(deletion_request):
    """
    Send a notification email to the admin about the new deletion request.

    Args:
        deletion_request: DataDeletionRequest object
    """
    subject = f"New Data Deletion Request - User: {deletion_request.user.email}"
    from_email = settings.DEFAULT_FROM_EMAIL

    # Send to both the default admin email and the Triumph Enterprises email
    to_emails = [settings.DEFAULT_FROM_EMAIL, "<EMAIL>"]

    # Render HTML content
    html_content = render_to_string('emails/privacy/deletion_admin_notification.html', {
        'user': deletion_request.user,
        'deletion_request': deletion_request,
        'request_date': deletion_request.request_date.strftime("%B %d, %Y, %I:%M %p"),
        'site_name': 'Triumph Enterprises'
    })

    # Create email message
    email = EmailMultiAlternatives(subject, "", from_email, to_emails)
    email.attach_alternative(html_content, "text/html")

    # Send email
    email.send()
    logger.info(f"Deletion admin notification email sent for user {deletion_request.user.email}")


def send_deletion_status_update_email(deletion_request):
    """
    Send a status update email to the user when their deletion request is processed.

    Args:
        deletion_request: DataDeletionRequest object
    """
    subject = f"Data Deletion Request Update - Triumph Enterprises"
    from_email = settings.DEFAULT_FROM_EMAIL
    to_email = deletion_request.user.email

    # Render HTML content
    html_content = render_to_string('emails/privacy/deletion_status_update.html', {
        'user': deletion_request.user,
        'deletion_request': deletion_request,
        'processed_date': deletion_request.processed_at.strftime("%B %d, %Y, %I:%M %p") if deletion_request.processed_at else None,
        'site_name': 'Triumph Enterprises'
    })

    # Create email message
    email = EmailMultiAlternatives(subject, "", from_email, [to_email])
    email.attach_alternative(html_content, "text/html")

    # Send email
    email.send()
    logger.info(f"Deletion status update email sent to user {deletion_request.user.email}")


def send_password_reset_email(user, reset_token, request):
    """
    Send password reset email to user.

    Args:
        user: User object
        reset_token: PasswordResetToken object
        request: HTTP request object
    """
    subject = "Password Reset Request - Triumph Enterprises"
    from_email = settings.DEFAULT_FROM_EMAIL
    to_email = user.email

    # Create reset URL using FRONTEND_URL from settings
    frontend_url = settings.FRONTEND_URL.rstrip('/')  # Remove trailing slash if present
    reset_url = f"{frontend_url}/auth/reset-password?token={reset_token.token}"

    # Render HTML content
    html_content = render_to_string('emails/password_reset.html', {
        'user': user,
        'reset_url': reset_url,
        'token': reset_token.token,
        'expires_at': reset_token.expires_at.strftime("%B %d, %Y, %I:%M %p"),
        'site_name': 'Triumph Enterprises',
        'ip_address': reset_token.ip_address
    })

    # Create email message
    email = EmailMultiAlternatives(subject, "", from_email, [to_email])
    email.attach_alternative(html_content, "text/html")

    # Send email
    email.send()

    logger.info(f"Password reset email sent to user {user.email}")


def send_password_changed_notification(user, request):
    """
    Send notification email when password is successfully changed.

    Args:
        user: User object
        request: HTTP request object
    """
    from backend.security_monitoring import get_client_ip, get_user_agent

    subject = "Password Changed Successfully - Triumph Enterprises"
    from_email = settings.DEFAULT_FROM_EMAIL
    to_email = user.email

    # Get request details
    ip_address = get_client_ip(request)
    user_agent = get_user_agent(request)

    # Render HTML content
    html_content = render_to_string('emails/password_changed.html', {
        'user': user,
        'change_time': timezone.now().strftime("%B %d, %Y, %I:%M %p"),
        'ip_address': ip_address,
        'user_agent': user_agent,
        'site_name': 'Triumph Enterprises'
    })

    # Create email message
    email = EmailMultiAlternatives(subject, "", from_email, [to_email])
    email.attach_alternative(html_content, "text/html")

    # Send email
    email.send()

    logger.info(f"Password changed notification sent to user {user.email}")


def check_password_reset_rate_limit(user, ip_address):
    """
    Check if user or IP has exceeded password reset rate limits.

    Args:
        user: User object
        ip_address: IP address string

    Returns:
        tuple: (is_limited, message)
    """
    from .models import PasswordResetToken
    from django.utils import timezone
    from datetime import timedelta

    # Check rate limits (last hour)
    one_hour_ago = timezone.now() - timedelta(hours=1)

    # Check user-based rate limit (max 3 per hour)
    user_requests = PasswordResetToken.objects.filter(
        user=user,
        created_at__gte=one_hour_ago
    ).count()

    if user_requests >= 3:
        return True, "Too many password reset requests. Please try again in an hour."

    # Check IP-based rate limit (max 5 per hour from same IP)
    ip_requests = PasswordResetToken.objects.filter(
        ip_address=ip_address,
        created_at__gte=one_hour_ago
    ).count()

    if ip_requests >= 5:
        return True, "Too many password reset requests from this IP. Please try again in an hour."

    return False, ""


def validate_password_strength(password):
    """
    Validate password strength with enhanced security requirements.

    Args:
        password: Password string to validate

    Returns:
        tuple: (is_valid, errors_list)
    """
    import re

    errors = []

    # Minimum length
    if len(password) < 8:
        errors.append("Password must be at least 8 characters long.")

    # Maximum length
    if len(password) > 128:
        errors.append("Password must not exceed 128 characters.")

    # Must contain uppercase letter
    if not re.search(r'[A-Z]', password):
        errors.append("Password must contain at least one uppercase letter.")

    # Must contain lowercase letter
    if not re.search(r'[a-z]', password):
        errors.append("Password must contain at least one lowercase letter.")

    # Must contain digit
    if not re.search(r'\d', password):
        errors.append("Password must contain at least one number.")

    # Must contain special character
    if not re.search(r'[!@#$%^&*(),.?":{}|<>]', password):
        errors.append("Password must contain at least one special character.")

    # Check for common weak patterns
    weak_patterns = [
        r'123456', r'password', r'qwerty', r'abc123',
        r'admin', r'letmein', r'welcome', r'monkey'
    ]

    for pattern in weak_patterns:
        if re.search(pattern, password.lower()):
            errors.append("Password contains common weak patterns.")
            break

    return len(errors) == 0, errors

def format_user_display_name(user):
    """
    Format user display name for UI purposes.

    Args:
        user: User object

    Returns:
        str: Formatted display name
    """
    if user.name and user.name.strip():
        return user.name.strip()
    return user.email

def anonymize_user_data(user):
    """
    Anonymize user data for privacy compliance.

    Args:
        user: User object

    Returns:
        dict: Anonymized user data
    """
    import hashlib
    import uuid

    # Create a unique anonymized identifier
    anonymized_id = str(uuid.uuid4())[:8]

    # Hash the email for consistency while maintaining anonymity
    email_hash = hashlib.md5(user.email.encode()).hexdigest()[:8]

    return {
        'id': user.id,
        'email': f'anonymized_{email_hash}@example.com',
        'name': f'Anonymized User {anonymized_id}',
        'phone_number': 'REDACTED',
        'date_joined': user.date_joined,
        'is_active': False,  # Mark as inactive for anonymized data
        'anonymized_at': timezone.now(),
        'original_email_hash': email_hash
    }
