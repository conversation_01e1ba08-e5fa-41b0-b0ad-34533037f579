import os
import requests
import time
import re

# Configuration
INPUT_FILE = 'haier_image_urls/all_image_urls.txt'
OUTPUT_DIR = 'haier_product_images_downloaded'

def create_folder(folder_path):
    """Create folder if it doesn't exist"""
    if not os.path.exists(folder_path):
        os.makedirs(folder_path)
        print(f"Created folder: {folder_path}")

def sanitize_filename(filename):
    """Remove invalid characters from filename"""
    return re.sub(r'[\\/*?:"<>|]', "", filename)

def download_image(url, save_path, headers=None):
    """Download image from URL and save to specified path"""
    try:
        if headers is None:
            headers = {
                'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36',
                'Referer': 'https://www.haier.com/'
            }

        response = requests.get(url, headers=headers, stream=True, timeout=15)

        if response.status_code == 200:
            with open(save_path, 'wb') as f:
                for chunk in response.iter_content(1024):
                    f.write(chunk)
            print(f"Downloaded: {save_path}")
            return True
        else:
            print(f"Failed to download {url}, status code: {response.status_code}")
            return False
    except Exception as e:
        print(f"Error downloading {url}: {e}")
        return False

def main():
    # Create base output directory
    create_folder(OUTPUT_DIR)

    # Read the file line by line and parse manually
    products = {}

    with open(INPUT_FILE, 'r') as f:
        lines = f.readlines()

    # Skip header line
    for line in lines[1:]:
        # Find the last comma that separates the URL from the rest
        last_comma_index = line.rfind(',http')

        if last_comma_index == -1:
            print(f"Skipping line with no URL: {line.strip()}")
            continue

        # Split the line into product info and URL
        product_info = line[:last_comma_index]
        url = line[last_comma_index+1:].strip()

        # Find the first comma that separates the model from the name
        first_comma_index = product_info.find(',')

        if first_comma_index == -1:
            print(f"Skipping line with no model: {line.strip()}")
            continue

        model = product_info[:first_comma_index].strip()
        name = product_info[first_comma_index+1:].strip()

        # Skip tracking pixels and small icons
        if any(x in url.lower() for x in ['facebook.com/tr', 'doubleclick.net', '_36.png']):
            continue

        # Create a key for the product
        product_key = f"{model}_{name}"

        if product_key not in products:
            products[product_key] = []

        products[product_key].append(url)

    print(f"Found {len(products)} products with image URLs")

    # Download images for each product
    for i, (product_key, urls) in enumerate(products.items()):
        # Create a folder for this product
        product_dir = os.path.join(OUTPUT_DIR, sanitize_filename(product_key))
        create_folder(product_dir)

        # Download each image
        successful_downloads = 0
        for j, url in enumerate(urls):
            # Extract file extension from URL
            file_extension = os.path.splitext(url.split('?')[0])[1]
            if not file_extension:
                file_extension = '.jpg'  # Default extension

            # Create filename
            filename = f"image_{j+1}{file_extension}"
            save_path = os.path.join(product_dir, filename)

            # Download the image
            if download_image(url, save_path):
                successful_downloads += 1

            # Add a small delay to avoid overwhelming the server
            time.sleep(0.5)

        print(f"Completed {i+1}/{len(products)}: {product_key} - Downloaded {successful_downloads}/{len(urls)} images")

    print("Image download process completed!")

if __name__ == "__main__":
    main()
