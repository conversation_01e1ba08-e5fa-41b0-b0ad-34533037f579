import csv
import os
import logging
from datetime import datetime
from django.core.management.base import BaseCommand
from products.models import Product

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler('product_cleanup.log'),
        logging.StreamHandler()
    ]
)
logger = logging.getLogger('product_cleanup')


class Command(BaseCommand):
    help = 'Clean up products by removing those not present in the CSV file'

    def add_arguments(self, parser):
        parser.add_argument(
            '--csv-file',
            type=str,
            default="D:\\Triumph\\e-com-2024-apis\\update_16052025_products.csv",
            help='Path to the CSV file containing valid products'
        )
        parser.add_argument(
            '--dry-run',
            action='store_true',
            help='Perform a dry run without actually deleting products'
        )
        parser.add_argument(
            '--export-report',
            action='store_true',
            help='Export a CSV report of deleted products'
        )
        parser.add_argument(
            '--report-path',
            type=str,
            default="deleted_products_report.csv",
            help='Path to save the deleted products report (used with --export-report)'
        )

    def handle(self, **options):
        start_time = datetime.now()
        logger.info(f"Starting product cleanup operation at {start_time}")

        csv_file_path = options['csv_file']
        dry_run = options['dry_run']
        export_report = options['export_report']
        report_path = options['report_path']

        logger.info(f"CSV file path: {csv_file_path}")
        logger.info(f"Dry run mode: {'Enabled' if dry_run else 'Disabled'}")
        logger.info(f"Export report: {'Enabled' if export_report else 'Disabled'}")
        if export_report:
            logger.info(f"Report path: {report_path}")

        if not os.path.exists(csv_file_path):
            error_msg = f'CSV file not found: {csv_file_path}'
            logger.error(error_msg)
            self.stdout.write(self.style.ERROR(error_msg))
            return

        # Read product IDs from CSV
        logger.info("Reading product IDs from CSV file...")
        valid_product_ids = self._read_product_ids_from_csv(csv_file_path)

        if not valid_product_ids:
            warning_msg = 'No valid product IDs found in the CSV file.'
            logger.warning(warning_msg)
            self.stdout.write(self.style.WARNING(warning_msg))
            return

        # Get all products from the database
        logger.info("Retrieving all products from database...")
        all_products = Product.objects.all()
        total_products = all_products.count()

        # Find products to delete (those not in the CSV)
        logger.info("Identifying products to delete...")
        products_to_delete = all_products.exclude(id__in=valid_product_ids)
        products_to_delete_count = products_to_delete.count()

        # Products to keep
        products_to_keep_count = total_products - products_to_delete_count

        # Display summary before deletion
        summary = [
            f'Total products in database: {total_products}',
            f'Products in CSV file: {len(valid_product_ids)}',
            f'Products to delete: {products_to_delete_count}',
            f'Products to keep: {products_to_keep_count}'
        ]

        for line in summary:
            logger.info(line)
            self.stdout.write(self.style.SUCCESS(line))

        # List products to be deleted
        if products_to_delete_count > 0:
            logger.info("Products to be deleted:")
            self.stdout.write(self.style.WARNING('Products to be deleted:'))

            for product in products_to_delete:
                product_info = f'  - ID: {product.id}, Name: {product.name}'
                logger.info(product_info)
                self.stdout.write(product_info)

        # Export report if requested
        if export_report and products_to_delete_count > 0:
            self._export_products_report(products_to_delete, report_path)

        # Perform deletion if not a dry run
        if not dry_run and products_to_delete_count > 0:
            confirmation = input('Do you want to proceed with deletion? (yes/no): ')
            if confirmation.lower() == 'yes':
                logger.info("Deleting products...")

                # Export report before deletion if requested
                if export_report and not os.path.exists(report_path):
                    self._export_products_report(products_to_delete, report_path)

                # Delete products
                products_to_delete.delete()

                success_msg = [
                    f'Successfully deleted {products_to_delete_count} products.',
                    f'Remaining products: {products_to_keep_count}'
                ]

                for msg in success_msg:
                    logger.info(msg)
                    self.stdout.write(self.style.SUCCESS(msg))
            else:
                cancel_msg = 'Deletion cancelled by user.'
                logger.info(cancel_msg)
                self.stdout.write(self.style.WARNING(cancel_msg))
        elif dry_run:
            dry_run_msg = 'Dry run - no products were deleted.'
            logger.info(dry_run_msg)
            self.stdout.write(self.style.WARNING(dry_run_msg))
        else:
            no_delete_msg = 'No products to delete.'
            logger.info(no_delete_msg)
            self.stdout.write(self.style.SUCCESS(no_delete_msg))

        end_time = datetime.now()
        duration = end_time - start_time
        completion_msg = f"Product cleanup operation completed at {end_time}. Duration: {duration}"
        logger.info(completion_msg)
        self.stdout.write(self.style.SUCCESS(completion_msg))

    def _read_product_ids_from_csv(self, csv_file_path):
        """Read product IDs from the CSV file."""
        valid_product_ids = []
        skipped_rows = 0

        try:
            logger.info(f"Opening CSV file: {csv_file_path}")
            with open(csv_file_path, 'r', encoding='utf-8') as csv_file:
                csv_reader = csv.DictReader(csv_file)

                # Log CSV columns
                columns = csv_reader.fieldnames
                logger.info(f"CSV columns: {', '.join(columns) if columns else 'None'}")

                # Check if 'id' column exists
                if 'id' not in csv_reader.fieldnames:
                    error_msg = "CSV file does not have an 'id' column."
                    logger.error(error_msg)
                    self.stdout.write(self.style.ERROR(error_msg))
                    return []

                # Process each row
                row_count = 0
                for row in csv_reader:
                    row_count += 1
                    try:
                        product_id = int(row['id'])
                        valid_product_ids.append(product_id)
                    except (ValueError, KeyError) as e:
                        skipped_rows += 1
                        warning_msg = f"Skipping row {row_count} with invalid ID: {row.get('id', 'N/A')}. Error: {str(e)}"
                        logger.warning(warning_msg)
                        self.stdout.write(self.style.WARNING(warning_msg))
                        continue

            # Log summary of CSV processing
            logger.info(f"CSV processing complete. Total rows processed: {row_count}")
            logger.info(f"Valid product IDs found: {len(valid_product_ids)}")

            if skipped_rows > 0:
                logger.warning(f"Skipped {skipped_rows} rows due to invalid data")

            return valid_product_ids
        except Exception as e:
            error_msg = f"Error reading CSV file: {str(e)}"
            logger.error(error_msg)
            self.stdout.write(self.style.ERROR(error_msg))
            return []

    def _export_products_report(self, products_queryset, report_path):
        """Export a CSV report of products to be deleted."""
        try:
            logger.info(f"Exporting products report to {report_path}")

            with open(report_path, 'w', newline='', encoding='utf-8') as csvfile:
                fieldnames = ['id', 'name', 'slug', 'description', 'price', 'stock',
                             'is_active', 'created_at', 'updated_at', 'category', 'brand']
                writer = csv.DictWriter(csvfile, fieldnames=fieldnames)

                writer.writeheader()

                for product in products_queryset:
                    writer.writerow({
                        'id': product.id,
                        'name': product.name,
                        'slug': product.slug,
                        'description': product.description[:100] + '...' if product.description and len(product.description) > 100 else product.description,
                        'price': product.price,
                        'stock': product.stock,
                        'is_active': product.is_active,
                        'created_at': product.created_at,
                        'updated_at': product.updated_at,
                        'category': product.category.name if product.category else 'None',
                        'brand': product.brand.name if product.brand else 'None'
                    })

            success_msg = f"Successfully exported {products_queryset.count()} products to {report_path}"
            logger.info(success_msg)
            self.stdout.write(self.style.SUCCESS(success_msg))

        except Exception as e:
            error_msg = f"Error exporting products report: {str(e)}"
            logger.error(error_msg)
            self.stdout.write(self.style.ERROR(error_msg))
