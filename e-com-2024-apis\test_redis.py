#!/usr/bin/env python
"""
Redis Connection Test Script

This script tests the connection to Redis using the configuration from Django settings.
It can be run directly to verify Redis connectivity without starting the Django server.

Usage:
    python test_redis.py

"""

import os
import sys
import redis
from dotenv import load_dotenv

# Load environment variables from .env file
load_dotenv()

def test_redis_connection():
    """Test the Redis connection using the URL from environment variables."""
    redis_url = os.getenv("REDIS_URL", "redis://127.0.0.1:6379/1")
    print(f"Testing Redis connection to: {redis_url}")
    
    try:
        # Create Redis client with a short timeout
        redis_client = redis.from_url(redis_url, socket_timeout=5.0)
        
        # Test connection with PING
        response = redis_client.ping()
        if response:
            print("✅ Redis connection successful!")
            
            # Test basic operations
            print("Testing basic Redis operations...")
            
            # Set a test key
            redis_client.set("test_key", "test_value")
            
            # Get the test key
            value = redis_client.get("test_key")
            if value and value.decode() == "test_value":
                print("✅ Redis SET/GET operations successful!")
            else:
                print("❌ Redis SET/GET operations failed!")
                
            # Delete the test key
            redis_client.delete("test_key")
            
            # Check if key was deleted
            if not redis_client.exists("test_key"):
                print("✅ Redis DELETE operation successful!")
            else:
                print("❌ Redis DELETE operation failed!")
                
            return True
    except redis.exceptions.ConnectionError as e:
        print(f"❌ Redis connection error: {str(e)}")
    except redis.exceptions.TimeoutError as e:
        print(f"❌ Redis connection timeout: {str(e)}")
    except Exception as e:
        print(f"❌ Unexpected error: {str(e)}")
    
    return False

if __name__ == "__main__":
    success = test_redis_connection()
    sys.exit(0 if success else 1)
