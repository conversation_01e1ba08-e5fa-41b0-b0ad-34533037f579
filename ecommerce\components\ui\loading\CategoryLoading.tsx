import React from "react";

export const CategoryLoading = () => {
  return (
    <div id="webcrumbs">
      <div className="w-full bg-white rounded-lg shadow-md">
        <div className="h-8 bg-neutral-200 rounded-t-lg animate-pulse"></div>
        <ul className="space-y-4 p-4">
          <li className="h-4 bg-neutral-200 rounded-md animate-pulse"></li>
          <li className="h-4 bg-neutral-200 rounded-md animate-pulse"></li>
          <li className="h-4 bg-neutral-200 rounded-md animate-pulse"></li>
          <li className="h-4 bg-neutral-200 rounded-md animate-pulse"></li>
          <li className="h-4 bg-neutral-200 rounded-md animate-pulse"></li>
          <li className="h-4 bg-neutral-200 rounded-md animate-pulse"></li>
          <li className="h-4 bg-neutral-200 rounded-md animate-pulse"></li>
          <li className="h-4 bg-neutral-200 rounded-md animate-pulse"></li>
        </ul>
      </div>
    </div>
  );
};
