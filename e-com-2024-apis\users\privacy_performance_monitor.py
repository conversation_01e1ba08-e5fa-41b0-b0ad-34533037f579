"""
Performance monitoring system for privacy features
Tracks response times, resource usage, and user experience metrics
"""

import time
import logging
from datetime import datetime, timedelta
from django.utils import timezone
from django.core.cache import cache
from django.db import connection
from django.contrib.auth import get_user_model
from functools import wraps
import psutil
import json

User = get_user_model()
logger = logging.getLogger(__name__)

class PrivacyPerformanceMonitor:
    """Monitor performance of privacy-related operations"""
    
    def __init__(self):
        self.metrics_cache_timeout = 300  # 5 minutes
        self.performance_thresholds = {
            'csv_export': 5.0,  # seconds
            'consent_update': 1.0,  # seconds
            'data_deletion': 2.0,  # seconds
            'consent_fetch': 0.5,  # seconds
        }
    
    def performance_monitor(self, operation_type):
        """Decorator to monitor performance of privacy operations"""
        def decorator(func):
            @wraps(func)
            def wrapper(*args, **kwargs):
                start_time = time.time()
                start_memory = self._get_memory_usage()
                start_queries = len(connection.queries)
                
                try:
                    result = func(*args, **kwargs)
                    success = True
                    error = None
                except Exception as e:
                    result = None
                    success = False
                    error = str(e)
                    raise
                finally:
                    end_time = time.time()
                    end_memory = self._get_memory_usage()
                    end_queries = len(connection.queries)
                    
                    # Calculate metrics
                    execution_time = end_time - start_time
                    memory_used = end_memory - start_memory
                    queries_executed = end_queries - start_queries
                    
                    # Log performance metrics
                    self._log_performance_metrics(
                        operation_type=operation_type,
                        execution_time=execution_time,
                        memory_used=memory_used,
                        queries_executed=queries_executed,
                        success=success,
                        error=error
                    )
                    
                    # Check for performance issues
                    self._check_performance_thresholds(operation_type, execution_time)
                
                return result
            return wrapper
        return decorator
    
    def _get_memory_usage(self):
        """Get current memory usage in MB"""
        try:
            process = psutil.Process()
            return process.memory_info().rss / 1024 / 1024  # Convert to MB
        except:
            return 0
    
    def _log_performance_metrics(self, operation_type, execution_time, memory_used, 
                                queries_executed, success, error):
        """Log performance metrics to cache and database"""
        
        metrics = {
            'timestamp': timezone.now().isoformat(),
            'operation_type': operation_type,
            'execution_time': round(execution_time, 3),
            'memory_used': round(memory_used, 2),
            'queries_executed': queries_executed,
            'success': success,
            'error': error
        }
        
        # Log to Django logger
        if success:
            logger.info(f"Privacy operation {operation_type} completed in {execution_time:.3f}s")
        else:
            logger.error(f"Privacy operation {operation_type} failed after {execution_time:.3f}s: {error}")
        
        # Store in cache for dashboard
        cache_key = f"privacy_metrics_{operation_type}_{int(time.time())}"
        cache.set(cache_key, metrics, timeout=self.metrics_cache_timeout)
        
        # Update aggregated metrics
        self._update_aggregated_metrics(operation_type, metrics)
    
    def _update_aggregated_metrics(self, operation_type, metrics):
        """Update aggregated performance metrics"""
        cache_key = f"privacy_aggregated_{operation_type}"
        aggregated = cache.get(cache_key, {
            'total_operations': 0,
            'total_time': 0,
            'total_memory': 0,
            'total_queries': 0,
            'success_count': 0,
            'error_count': 0,
            'avg_time': 0,
            'avg_memory': 0,
            'avg_queries': 0,
            'last_updated': timezone.now().isoformat()
        })
        
        # Update counters
        aggregated['total_operations'] += 1
        aggregated['total_time'] += metrics['execution_time']
        aggregated['total_memory'] += metrics['memory_used']
        aggregated['total_queries'] += metrics['queries_executed']
        
        if metrics['success']:
            aggregated['success_count'] += 1
        else:
            aggregated['error_count'] += 1
        
        # Calculate averages
        total_ops = aggregated['total_operations']
        aggregated['avg_time'] = round(aggregated['total_time'] / total_ops, 3)
        aggregated['avg_memory'] = round(aggregated['total_memory'] / total_ops, 2)
        aggregated['avg_queries'] = round(aggregated['total_queries'] / total_ops, 1)
        aggregated['success_rate'] = round((aggregated['success_count'] / total_ops) * 100, 1)
        aggregated['last_updated'] = timezone.now().isoformat()
        
        # Store updated aggregated metrics
        cache.set(cache_key, aggregated, timeout=3600)  # 1 hour
    
    def _check_performance_thresholds(self, operation_type, execution_time):
        """Check if operation exceeded performance thresholds"""
        threshold = self.performance_thresholds.get(operation_type)
        if threshold and execution_time > threshold:
            logger.warning(
                f"Privacy operation {operation_type} exceeded threshold: "
                f"{execution_time:.3f}s > {threshold}s"
            )
            
            # Increment slow operation counter
            cache_key = f"privacy_slow_operations_{operation_type}"
            slow_count = cache.get(cache_key, 0) + 1
            cache.set(cache_key, slow_count, timeout=3600)
    
    def get_performance_dashboard_data(self):
        """Get performance data for dashboard display"""
        dashboard_data = {
            'timestamp': timezone.now().isoformat(),
            'operations': {},
            'system_health': self._get_system_health(),
            'alerts': self._get_performance_alerts()
        }
        
        # Get metrics for each operation type
        for operation_type in self.performance_thresholds.keys():
            cache_key = f"privacy_aggregated_{operation_type}"
            metrics = cache.get(cache_key)
            
            if metrics:
                dashboard_data['operations'][operation_type] = metrics
            else:
                dashboard_data['operations'][operation_type] = {
                    'total_operations': 0,
                    'avg_time': 0,
                    'avg_memory': 0,
                    'success_rate': 100,
                    'status': 'No data'
                }
        
        return dashboard_data
    
    def _get_system_health(self):
        """Get overall system health metrics"""
        try:
            cpu_percent = psutil.cpu_percent(interval=1)
            memory = psutil.virtual_memory()
            disk = psutil.disk_usage('/')
            
            return {
                'cpu_usage': round(cpu_percent, 1),
                'memory_usage': round(memory.percent, 1),
                'disk_usage': round(disk.percent, 1),
                'status': 'healthy' if cpu_percent < 80 and memory.percent < 80 else 'warning'
            }
        except:
            return {
                'cpu_usage': 0,
                'memory_usage': 0,
                'disk_usage': 0,
                'status': 'unknown'
            }
    
    def _get_performance_alerts(self):
        """Get current performance alerts"""
        alerts = []
        
        # Check for slow operations
        for operation_type in self.performance_thresholds.keys():
            cache_key = f"privacy_slow_operations_{operation_type}"
            slow_count = cache.get(cache_key, 0)
            
            if slow_count > 5:  # More than 5 slow operations in the last hour
                alerts.append({
                    'type': 'performance',
                    'severity': 'warning',
                    'operation': operation_type,
                    'message': f"{operation_type} has {slow_count} slow operations in the last hour",
                    'threshold': self.performance_thresholds[operation_type]
                })
        
        # Check aggregated metrics for concerning trends
        for operation_type in self.performance_thresholds.keys():
            cache_key = f"privacy_aggregated_{operation_type}"
            metrics = cache.get(cache_key)
            
            if metrics:
                # Check success rate
                if metrics.get('success_rate', 100) < 95:
                    alerts.append({
                        'type': 'reliability',
                        'severity': 'high',
                        'operation': operation_type,
                        'message': f"{operation_type} success rate is {metrics['success_rate']}%",
                        'success_rate': metrics['success_rate']
                    })
                
                # Check average response time
                threshold = self.performance_thresholds[operation_type]
                if metrics.get('avg_time', 0) > threshold * 0.8:  # 80% of threshold
                    alerts.append({
                        'type': 'performance',
                        'severity': 'medium',
                        'operation': operation_type,
                        'message': f"{operation_type} average time is {metrics['avg_time']}s",
                        'avg_time': metrics['avg_time'],
                        'threshold': threshold
                    })
        
        return alerts
    
    def generate_performance_report(self, days=7):
        """Generate detailed performance report"""
        report = {
            'report_period': f"Last {days} days",
            'generated_at': timezone.now().isoformat(),
            'summary': {},
            'detailed_metrics': {},
            'recommendations': []
        }
        
        # Get summary for each operation
        total_operations = 0
        total_avg_time = 0
        
        for operation_type in self.performance_thresholds.keys():
            cache_key = f"privacy_aggregated_{operation_type}"
            metrics = cache.get(cache_key, {})
            
            if metrics:
                report['detailed_metrics'][operation_type] = metrics
                total_operations += metrics.get('total_operations', 0)
                total_avg_time += metrics.get('avg_time', 0)
        
        # Generate summary
        report['summary'] = {
            'total_operations': total_operations,
            'overall_avg_time': round(total_avg_time / len(self.performance_thresholds), 3),
            'system_health': self._get_system_health(),
            'active_alerts': len(self._get_performance_alerts())
        }
        
        # Generate recommendations
        report['recommendations'] = self._generate_recommendations(report['detailed_metrics'])
        
        return report
    
    def _generate_recommendations(self, metrics):
        """Generate performance improvement recommendations"""
        recommendations = []
        
        for operation_type, data in metrics.items():
            threshold = self.performance_thresholds[operation_type]
            avg_time = data.get('avg_time', 0)
            avg_queries = data.get('avg_queries', 0)
            success_rate = data.get('success_rate', 100)
            
            # Performance recommendations
            if avg_time > threshold * 0.8:
                recommendations.append({
                    'type': 'performance',
                    'operation': operation_type,
                    'priority': 'high' if avg_time > threshold else 'medium',
                    'recommendation': f"Optimize {operation_type} - current avg time {avg_time}s exceeds 80% of threshold ({threshold}s)"
                })
            
            # Database query recommendations
            if avg_queries > 10:
                recommendations.append({
                    'type': 'database',
                    'operation': operation_type,
                    'priority': 'medium',
                    'recommendation': f"Reduce database queries for {operation_type} - currently averaging {avg_queries} queries per operation"
                })
            
            # Reliability recommendations
            if success_rate < 98:
                recommendations.append({
                    'type': 'reliability',
                    'operation': operation_type,
                    'priority': 'high',
                    'recommendation': f"Improve error handling for {operation_type} - success rate is {success_rate}%"
                })
        
        return recommendations

# Global monitor instance
privacy_monitor = PrivacyPerformanceMonitor()

# Decorator for easy use
def monitor_privacy_performance(operation_type):
    """Decorator to monitor privacy operation performance"""
    return privacy_monitor.performance_monitor(operation_type)
