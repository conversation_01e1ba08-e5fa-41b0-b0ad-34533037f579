#!/usr/bin/env python3
"""
Verify Database Sync Setup

This script verifies that database synchronization signals are properly registered
and the system is ready for automatic sync.
"""

import os
import django

# Setup Django environment
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'backend.settings')
django.setup()

from django.db.models.signals import post_save, post_delete
from django.conf import settings
from products.models import Product, Category, Brand
from backend.db_sync import DatabaseSynchronizer


def check_signal_registration():
    """Check if sync signals are properly registered"""
    print("🔍 CHECKING SIGNAL REGISTRATION")
    print("=" * 40)
    
    models_to_check = [
        (Product, "Product"),
        (Category, "Category"), 
        (Brand, "Brand")
    ]
    
    for model_class, model_name in models_to_check:
        # Check post_save signals
        save_receivers = post_save._live_receivers(sender=model_class)
        save_sync_receivers = [r for r in save_receivers if 'sync' in str(r)]
        
        # Check post_delete signals  
        delete_receivers = post_delete._live_receivers(sender=model_class)
        delete_sync_receivers = [r for r in delete_receivers if 'sync' in str(r)]
        
        print(f"{model_name}:")
        print(f"  Save signals: {len(save_sync_receivers)} sync handlers")
        print(f"  Delete signals: {len(delete_sync_receivers)} sync handlers")
        
        if len(save_sync_receivers) > 0 and len(delete_sync_receivers) > 0:
            print(f"  ✅ {model_name} sync signals properly registered")
        else:
            print(f"  ❌ {model_name} sync signals NOT registered")


def check_database_config():
    """Check database configuration"""
    print("\n🗄️ CHECKING DATABASE CONFIGURATION")
    print("=" * 40)
    
    print(f"Total databases configured: {len(settings.DATABASES)}")
    
    for db_name, db_config in settings.DATABASES.items():
        print(f"\n{db_name}:")
        print(f"  Host: {db_config.get('HOST', 'Not specified')}")
        print(f"  Engine: {db_config.get('ENGINE', 'Not specified')}")
        
        if db_name == 'default':
            print(f"  Role: PRIMARY (read/write)")
        else:
            print(f"  Role: REPLICA (read-only)")
    
    replica_dbs = DatabaseSynchronizer.get_replica_databases()
    print(f"\nReplica databases detected: {len(replica_dbs)}")
    if replica_dbs:
        print(f"Replica names: {', '.join(replica_dbs)}")


def check_sync_status():
    """Check current sync status"""
    print("\n📊 CHECKING SYNC STATUS")
    print("=" * 40)
    
    replica_dbs = DatabaseSynchronizer.get_replica_databases()
    
    if not replica_dbs:
        print("❌ No replica databases configured")
        print("Add DB_READ_HOST and related environment variables to enable sync")
        return
    
    from backend.db_sync import sync_status
    
    print(f"Sync enabled: {'✅ YES' if sync_status() else '❌ NO'}")
    
    # Check record counts
    models_to_check = [Product, Category, Brand]
    
    for model_class in models_to_check:
        model_name = model_class.__name__
        primary_count = model_class.objects.count()
        
        print(f"\n{model_name}:")
        print(f"  PRIMARY: {primary_count} records")
        
        for db_name in replica_dbs:
            try:
                replica_count = model_class.objects.using(db_name).count()
                if replica_count == primary_count:
                    status = "✅ SYNCED"
                else:
                    status = f"⚠️ OUT OF SYNC (diff: {abs(primary_count - replica_count)})"
                
                print(f"  {db_name.upper()}: {replica_count} records {status}")
            except Exception as e:
                print(f"  {db_name.upper()}: ❌ ERROR - {e}")


def show_sync_commands():
    """Show useful sync commands"""
    print("\n💡 USEFUL SYNC COMMANDS")
    print("=" * 40)
    
    commands = [
        "# Check replica health",
        "python manage.py check_replica_health --detailed",
        "",
        "# Sync all data to replicas",
        "python manage.py sync_databases",
        "",
        "# Sync specific model",
        "python manage.py sync_databases --model product",
        "",
        "# Check what would be synced (dry run)",
        "python manage.py sync_databases --dry-run",
        "",
        "# Test sync functionality",
        "python test_database_sync.py",
    ]
    
    for command in commands:
        print(command)


def main():
    """Run all verification checks"""
    print("🔧 DATABASE SYNC SETUP VERIFICATION")
    print("=" * 50)
    
    # Check signal registration
    check_signal_registration()
    
    # Check database configuration
    check_database_config()
    
    # Check sync status
    check_sync_status()
    
    # Show useful commands
    show_sync_commands()
    
    print("\n✅ VERIFICATION COMPLETED!")
    
    # Summary
    replica_dbs = DatabaseSynchronizer.get_replica_databases()
    if replica_dbs:
        print(f"\n🎯 SUMMARY: Sync is configured and ready!")
        print(f"   • {len(replica_dbs)} replica database(s) detected")
        print(f"   • Automatic sync enabled for product data")
        print(f"   • Add/update/delete operations will sync automatically")
    else:
        print(f"\n🎯 SUMMARY: Ready for replica setup!")
        print(f"   • Sync code is installed and ready")
        print(f"   • Add DB_READ_HOST environment variables to enable")
        print(f"   • Sync will activate automatically when replicas are configured")


if __name__ == "__main__":
    main()
