"""
Comprehensive tests for user models and their methods
Tests Customer, Address, PaymentMethod, Wishlist, UserConsent, and DataDeletionRequest models
"""

import pytest
from decimal import Decimal
from unittest.mock import patch
from django.test import TestCase
from django.contrib.auth import get_user_model
from django.core.exceptions import ValidationError
from django.utils import timezone
from datetime import timedelta

from users.models import (
    Address, PaymentMethod, Wishlist, ContactMessage, 
    UserConsent, DataDeletionRequest, PasswordResetToken
)
from products.models import Product, Category, Brand

User = get_user_model()

class TestCustomerModel(TestCase):
    """Test Customer (User) model methods and properties"""
    
    def setUp(self):
        """Set up test data"""
        self.user = User.objects.create_user(
            email='<EMAIL>',
            password='testpass123',
            name='Test User',
            phone_number='+1234567890'
        )
    
    def test_user_creation(self):
        """Test user creation"""
        self.assertEqual(self.user.email, '<EMAIL>')
        self.assertEqual(self.user.name, 'Test User')
        self.assertTrue(self.user.check_password('testpass123'))
    
    def test_user_str_method(self):
        """Test user string representation"""
        self.assertEqual(str(self.user), '<EMAIL>')
    
    def test_user_username_field(self):
        """Test that email is used as username field"""
        self.assertEqual(User.USERNAME_FIELD, 'email')
    
    def test_user_required_fields(self):
        """Test required fields"""
        self.assertEqual(User.REQUIRED_FIELDS, [])
    
    def test_user_phone_number_encryption(self):
        """Test phone number encryption"""
        # Phone number should be encrypted in database
        user_from_db = User.objects.get(email='<EMAIL>')
        self.assertEqual(user_from_db.phone_number, '+1234567890')
    
    def test_user_is_verified_default(self):
        """Test default verification status"""
        self.assertFalse(self.user.is_verified)
    
    def test_user_profile_image_url(self):
        """Test profile image URL field"""
        self.user.profile_image_url = 'https://example.com/image.jpg'
        self.user.save()
        self.assertEqual(self.user.profile_image_url, 'https://example.com/image.jpg')
    
    def test_user_date_of_birth(self):
        """Test date of birth field"""
        from datetime import date
        self.user.date_of_birth = date(1990, 1, 1)
        self.user.save()
        self.assertEqual(self.user.date_of_birth, date(1990, 1, 1))

class TestAddressModel(TestCase):
    """Test Address model methods and properties"""
    
    def setUp(self):
        """Set up test data"""
        self.user = User.objects.create_user(
            email='<EMAIL>',
            password='testpass123',
            name='Test User'
        )
        self.address = Address.objects.create(
            user=self.user,
            street_address='123 Main St',
            apartment='Apt 4B',
            city='Test City',
            state='Test State',
            postal_code='12345',
            country='Test Country',
            is_default=True
        )
    
    def test_address_creation(self):
        """Test address creation"""
        self.assertEqual(self.address.user, self.user)
        self.assertEqual(self.address.street_address, '123 Main St')
        self.assertEqual(self.address.city, 'Test City')
        self.assertTrue(self.address.is_default)
    
    def test_address_str_method(self):
        """Test address string representation"""
        # Address model uses default Django __str__ method
        expected = f"Address object ({self.address.id})"
        self.assertEqual(str(self.address), expected)
    

    
    def test_address_validation(self):
        """Test address validation"""
        # Test required fields
        with self.assertRaises(ValidationError):
            address = Address(user=self.user)
            address.full_clean()
    
    def test_multiple_addresses_one_default(self):
        """Test that only one address can be default per user"""
        # Create second address as default
        address2 = Address.objects.create(
            user=self.user,
            street_address='456 Oak St',
            city='Test City 2',
            state='Test State',
            postal_code='67890',
            country='Test Country',
            is_default=True
        )
        
        # First address should no longer be default
        self.address.refresh_from_db()
        self.assertFalse(self.address.is_default)
        self.assertTrue(address2.is_default)

class TestPaymentMethodModel(TestCase):
    """Test PaymentMethod model"""
    
    def setUp(self):
        """Set up test data"""
        self.user = User.objects.create_user(
            email='<EMAIL>',
            password='testpass123',
            name='Test User'
        )
        self.payment_method = PaymentMethod.objects.create(
            user=self.user,
            card_type='VISA',
            last_four='1234',
            stripe_payment_method_id='pm_test_123',
            is_default=True
        )
    
    def test_payment_method_creation(self):
        """Test payment method creation"""
        self.assertEqual(self.payment_method.user, self.user)
        self.assertEqual(self.payment_method.card_type, 'VISA')
        self.assertEqual(self.payment_method.last_four, '1234')
        self.assertTrue(self.payment_method.is_default)
    
    def test_payment_method_str_method(self):
        """Test payment method string representation"""
        # PaymentMethod model uses default Django __str__ method
        expected = f"PaymentMethod object ({self.payment_method.id})"
        self.assertEqual(str(self.payment_method), expected)
    
    def test_payment_method_choices(self):
        """Test payment method creation with different card types"""
        valid_types = ['VISA', 'MASTERCARD', 'AMEX', 'DISCOVER']
        for card_type in valid_types:
            pm = PaymentMethod(
                user=self.user,
                card_type=card_type,
                last_four='1234',
                stripe_payment_method_id='pm_test_456'
            )
            pm.full_clean()  # Should not raise ValidationError

class TestWishlistModel(TestCase):
    """Test Wishlist model"""
    
    def setUp(self):
        """Set up test data"""
        self.user = User.objects.create_user(
            email='<EMAIL>',
            password='testpass123',
            name='Test User'
        )
        
        # Create test product
        self.category = Category.objects.create(name='Test Category')
        self.brand = Brand.objects.create(name='Test Brand')
        self.product = Product.objects.create(
            name='Test Product',
            price=Decimal('99.99'),
            category=self.category,
            brand=self.brand,
            stock=10
        )
        
        self.wishlist_item = Wishlist.objects.create(
            user=self.user,
            product=self.product
        )
    
    def test_wishlist_creation(self):
        """Test wishlist item creation"""
        self.assertEqual(self.wishlist_item.user, self.user)
        self.assertEqual(self.wishlist_item.product, self.product)
        self.assertIsNotNone(self.wishlist_item.added_at)
    
    def test_wishlist_str_method(self):
        """Test wishlist string representation"""
        # Wishlist model uses default Django __str__ method
        expected = f"Wishlist object ({self.wishlist_item.id})"
        self.assertEqual(str(self.wishlist_item), expected)
    
    def test_wishlist_unique_constraint(self):
        """Test that user can't add same product twice to wishlist"""
        with self.assertRaises(Exception):  # IntegrityError
            Wishlist.objects.create(
                user=self.user,
                product=self.product
            )

class TestContactMessageModel(TestCase):
    """Test ContactMessage model"""
    
    def test_contact_message_creation(self):
        """Test contact message creation"""
        message = ContactMessage.objects.create(
            name='John Doe',
            email='<EMAIL>',
            subject='Test Subject',
            message='Test message content'
        )
        
        self.assertEqual(message.name, 'John Doe')
        self.assertEqual(message.email, '<EMAIL>')
        self.assertEqual(message.subject, 'Test Subject')
        self.assertFalse(message.is_read)
        self.assertIsNotNone(message.created_at)
    
    def test_contact_message_str_method(self):
        """Test contact message string representation"""
        message = ContactMessage.objects.create(
            name='John Doe',
            email='<EMAIL>',
            subject='Test Subject',
            message='Test message content'
        )
        
        expected = "John Doe - Test Subject"
        self.assertEqual(str(message), expected)

class TestUserConsentModel(TestCase):
    """Test UserConsent model"""
    
    def setUp(self):
        """Set up test data"""
        self.user = User.objects.create_user(
            email='<EMAIL>',
            password='testpass123',
            name='Test User'
        )
    
    def test_user_consent_creation(self):
        """Test user consent creation"""
        consent = UserConsent.objects.create(
            user=self.user,
            consent_type='MARKETING',
            granted=True,
            ip_address='***********'
        )
        
        self.assertEqual(consent.user, self.user)
        self.assertEqual(consent.consent_type, 'MARKETING')
        self.assertTrue(consent.granted)
        # granted_at is only set when grant_consent() method is called
        self.assertIsNone(consent.granted_at)
    
    def test_user_consent_str_method(self):
        """Test user consent string representation"""
        consent = UserConsent.objects.create(
            user=self.user,
            consent_type='MARKETING',
            granted=True,
            ip_address='***********'
        )
        
        expected = f"{self.user.email} - MARKETING - Granted"
        self.assertEqual(str(consent), expected)
    
    def test_grant_consent_method(self):
        """Test grant consent method"""
        consent = UserConsent.objects.create(
            user=self.user,
            consent_type='MARKETING',
            granted=False,
            ip_address='***********'
        )
        
        consent.grant_consent('***********', 'Mozilla/5.0')
        
        self.assertTrue(consent.granted)
        self.assertIsNotNone(consent.granted_at)
        self.assertIsNone(consent.withdrawn_at)
        self.assertEqual(consent.ip_address, '***********')
    
    def test_withdraw_consent_method(self):
        """Test withdraw consent method"""
        consent = UserConsent.objects.create(
            user=self.user,
            consent_type='MARKETING',
            granted=True,
            ip_address='***********'
        )
        
        consent.withdraw_consent('***********', 'Mozilla/5.0')
        
        self.assertFalse(consent.granted)
        self.assertIsNotNone(consent.withdrawn_at)
        self.assertEqual(consent.ip_address, '***********')

class TestDataDeletionRequestModel(TestCase):
    """Test DataDeletionRequest model"""
    
    def setUp(self):
        """Set up test data"""
        self.user = User.objects.create_user(
            email='<EMAIL>',
            password='testpass123',
            name='Test User'
        )
    
    def test_data_deletion_request_creation(self):
        """Test data deletion request creation"""
        request = DataDeletionRequest.objects.create(
            user=self.user,
            reason='No longer need account',
            ip_address='***********'
        )
        
        self.assertEqual(request.user, self.user)
        self.assertEqual(request.reason, 'No longer need account')
        self.assertEqual(request.status, 'PENDING')
        self.assertIsNotNone(request.request_date)
    
    def test_data_deletion_request_str_method(self):
        """Test data deletion request string representation"""
        request = DataDeletionRequest.objects.create(
            user=self.user,
            reason='No longer need account',
            ip_address='***********'
        )
        
        expected = f"Deletion request - {self.user.email} - PENDING"
        self.assertEqual(str(request), expected)

class TestPasswordResetTokenModel(TestCase):
    """Test PasswordResetToken model"""
    
    def setUp(self):
        """Set up test data"""
        self.user = User.objects.create_user(
            email='<EMAIL>',
            password='testpass123',
            name='Test User'
        )
    
    def test_password_reset_token_creation(self):
        """Test password reset token creation"""
        token = PasswordResetToken.objects.create(user=self.user, ip_address='***********')
        
        self.assertEqual(token.user, self.user)
        self.assertIsNotNone(token.token)
        self.assertIsNotNone(token.token_hash)
        self.assertIsNotNone(token.expires_at)
        self.assertFalse(token.is_used)
    
    def test_password_reset_token_is_valid(self):
        """Test token validity check"""
        token = PasswordResetToken.objects.create(user=self.user, ip_address='***********')

        # Fresh token should be valid
        self.assertTrue(token.is_valid())

        # Used token should be invalid
        token.mark_as_used()
        self.assertFalse(token.is_valid())

        # Expired token should be invalid
        token = PasswordResetToken.objects.create(user=self.user, ip_address='***********')
        token.expires_at = timezone.now() - timedelta(minutes=1)
        token.save()
        self.assertFalse(token.is_valid())
    
    def test_password_reset_token_cleanup(self):
        """Test expired token cleanup"""
        # Create expired token
        expired_token = PasswordResetToken.objects.create(user=self.user, ip_address='***********')
        expired_token.expires_at = timezone.now() - timedelta(minutes=1)
        expired_token.save()

        # Create valid token
        valid_token = PasswordResetToken.objects.create(user=self.user, ip_address='***********')
        
        # Cleanup expired tokens
        count = PasswordResetToken.cleanup_expired_tokens()
        
        self.assertEqual(count, 1)
        self.assertFalse(PasswordResetToken.objects.filter(id=expired_token.id).exists())
        self.assertTrue(PasswordResetToken.objects.filter(id=valid_token.id).exists())
