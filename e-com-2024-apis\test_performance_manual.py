#!/usr/bin/env python
"""
Manual performance test script for ProductListSerializer optimizations
Run this script to see the performance improvements in action
"""

import os
import sys
import django
from django.conf import settings

# Setup Django
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'backend.settings')
django.setup()

from django.db import connection, reset_queries
from django.test.utils import override_settings
from products.models import Product
from products.serializers import ProductListSerializer
from products.views import get_optimized_product_queryset


def count_queries(func):
    """Decorator to count database queries"""
    def wrapper(*args, **kwargs):
        reset_queries()
        result = func(*args, **kwargs)
        query_count = len(connection.queries)
        return result, query_count
    return wrapper


@count_queries
def test_unoptimized_approach():
    """Test the old unoptimized approach"""
    print("Testing UNOPTIMIZED approach...")
    
    # Get products without optimization
    products = Product.objects.filter(is_active=True)[:10]
    
    # Serialize without prefetched data (this will cause N+1 queries)
    serializer = ProductListSerializer(products, many=True)
    data = serializer.data
    
    return len(data)


@count_queries  
def test_optimized_approach():
    """Test the new optimized approach"""
    print("Testing OPTIMIZED approach...")
    
    # Get products with optimization
    products = get_optimized_product_queryset(
        Product.objects.filter(is_active=True)
    )[:10]
    
    # Serialize with prefetched data
    serializer = ProductListSerializer(products, many=True)
    data = serializer.data
    
    return len(data)


def main():
    """Run performance comparison"""
    print("=" * 60)
    print("ProductListSerializer Performance Test")
    print("=" * 60)
    
    # Check if we have products to test with
    product_count = Product.objects.count()
    if product_count == 0:
        print("No products found in database. Please create some test data first.")
        return
    
    print(f"Found {product_count} products in database")
    print()
    
    # Test unoptimized approach
    try:
        unoptimized_count, unoptimized_queries = test_unoptimized_approach()
        print(f"✓ Serialized {unoptimized_count} products")
        print(f"✓ Database queries: {unoptimized_queries}")
    except Exception as e:
        print(f"✗ Error in unoptimized test: {e}")
        unoptimized_queries = "Error"
    
    print()
    
    # Test optimized approach  
    try:
        optimized_count, optimized_queries = test_optimized_approach()
        print(f"✓ Serialized {optimized_count} products")
        print(f"✓ Database queries: {optimized_queries}")
    except Exception as e:
        print(f"✗ Error in optimized test: {e}")
        optimized_queries = "Error"
    
    print()
    print("=" * 60)
    print("PERFORMANCE COMPARISON")
    print("=" * 60)
    
    if isinstance(unoptimized_queries, int) and isinstance(optimized_queries, int):
        improvement = unoptimized_queries - optimized_queries
        percentage = (improvement / unoptimized_queries) * 100 if unoptimized_queries > 0 else 0
        
        print(f"Unoptimized queries: {unoptimized_queries}")
        print(f"Optimized queries:   {optimized_queries}")
        print(f"Query reduction:     {improvement} queries ({percentage:.1f}% improvement)")
        
        if optimized_queries < unoptimized_queries:
            print("🎉 OPTIMIZATION SUCCESSFUL!")
        else:
            print("⚠️  No improvement detected")
    else:
        print("Could not compare due to errors")
    
    print()
    print("=" * 60)
    
    # Show sample query details if in debug mode
    if settings.DEBUG and isinstance(optimized_queries, int):
        print("Sample queries from optimized approach:")
        for i, query in enumerate(connection.queries[-optimized_queries:], 1):
            print(f"{i}. {query['sql'][:100]}...")
        print()


if __name__ == "__main__":
    main()
