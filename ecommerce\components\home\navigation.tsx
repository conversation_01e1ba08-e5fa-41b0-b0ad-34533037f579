// "use client";
// import * as React from "react";
// import Link from "next/link";

// const categories = [
//   {
//     title: "SALE",
//     href: "/sale",
//   },
//   {
//     title: "NEW IN",
//     href: "/new-in",
//   },
//   {
//     title: "WATCHES",
//     href: "/watches",
//     items: [
//       { title: "All Watches", href: "/watches" },
//       { title: "Women's Watches", href: "/watches/women" },
//       { title: "Men's Watches", href: "/watches/men" },
//     ],
//   },
//   {
//     title: "SUNGLASSES",
//     href: "/sunglasses",
//   },
//   {
//     title: "WATCH CHARMS",
//     href: "/watch-charms",
//   },
//   {
//     title: "COUPLE WATCHES",
//     href: "/couple-watches",
//   },
//   {
//     title: "LOVE TRIANGLES & STACKS",
//     href: "/love-triangles-stacks",
//   },
//   {
//     title: "WATCH BRACELET STACKS",
//     href: "/watch-bracelet-stacks",
//     items: [
//       { title: "All Bracelet Stacks", href: "/watch-bracelet-stacks" },
//       {
//         title: "Women's Bracelet Stacks",
//         href: "/watch-bracelet-stacks/women",
//       },
//       { title: "Men's Bracelet Stacks", href: "/watch-bracelet-stacks/men" },
//     ],
//   },
//   {
//     title: "JEWELLERY",
//     href: "/jewellery",
//   },
// ];

// export default function Navigation({ categories }: Category[]) {
//   const [openDropdown, setOpenDropdown] = React.useState<string | null>(null);
//   const timeoutRef = React.useRef<any>(null);

//   const handleMouseEnter = (categoryTitle: string) => {
//     if (timeoutRef.current) clearTimeout(timeoutRef.current);
//     setOpenDropdown(categoryTitle);
//   };

//   const handleMouseLeave = () => {
//     timeoutRef.current = setTimeout(() => {
//       setOpenDropdown(null);
//     }, 150); // Small delay before closing
//   };

//   return (
//     <nav className="w-full">
//       <ul className="flex flex-wrap justify-center gap-1 md:gap-2">
//         {categories.map((category: Category) => (
//           <li
//             key={category?.name}
//             className="relative"
//             onMouseEnter={() => handleMouseEnter(category?.name)}
//             onMouseLeave={handleMouseLeave}
//           >
//             {Array.isArray(category?.subcategories) &&
//             Boolean(category?.subcategories.length > 0) ? (
//               <>
//                 <button className="inline-flex h-9 items-center justify-center rounded-md bg-background px-4 py-2 text-xs md:text-sm font-medium transition-colors hover:bg-accent hover:text-accent-foreground focus:bg-accent focus:text-accent-foreground focus:outline-none">
//                   {category?.name}
//                 </button>
//                 {openDropdown === category?.name && (
//                   <div
//                     className="absolute left-1/2 top-full z-50 -translate-x-1/2 transform"
//                     onMouseEnter={() => handleMouseEnter(category?.name)}
//                     onMouseLeave={handleMouseLeave}
//                   >
//                     {/* Added invisible bridge to prevent gap between button and dropdown */}
//                     <div className="h-2 -mt-1" />
//                     <ul className="min-w-[12rem] rounded-md border bg-white p-2 shadow-lg">
//                       {Array.isArray(category?.subcategories) &&
//                         Boolean(category?.subcategories.length > 0) &&
//                         category?.subcategories.map((subcategory) => (
//                           <li key={subcategory.name}>
//                             <Link
//                               href={`/products/categories/${subcategory.slug}`}
//                               className="block rounded-md px-4 py-2 text-sm hover:bg-accent hover:text-accent-foreground"
//                             >
//                               {subcategory.name}
//                             </Link>
//                           </li>
//                         ))}
//                     </ul>
//                   </div>
//                 )}
//               </>
//             ) : (
//               <Link
//                 href={`/products/categories/${category.slug}`}
//                 className="inline-flex h-9 items-center justify-center rounded-md bg-background px-4 py-2 text-xs md:text-sm font-medium transition-colors hover:bg-accent hover:text-accent-foreground focus:bg-accent focus:text-accent-foreground focus:outline-none"
//               >
//                 {category.name}
//               </Link>
//             )}
//           </li>
//         ))}
//       </ul>
//     </nav>
//   );
// }


"use client";
import * as React from "react";
import Link from "next/link";
import { usePathname } from "next/navigation";
import { Skeleton } from "@/components/ui/skeleton";

export default function Navigation({ categories }: any) {
  const [openDropdown, setOpenDropdown] = React.useState<string | null>(null);
  const timeoutRef = React.useRef<any>(null);
  const isComponentMounted = React.useRef(false);
  const pathName = usePathname();
  const [isLoading, setIsLoading] = React.useState(true);

  React.useEffect(() => {
    isComponentMounted.current = true;

    return () => {
      isComponentMounted.current = false;
      // Clear any pending timeouts on unmount
      if (timeoutRef.current) {
        clearTimeout(timeoutRef.current);
        timeoutRef.current = null;
      }
    };
  }, []);

  React.useEffect(() => {
    // Set loading to false when categories are available
    if (categories && Array.isArray(categories)) {
      setIsLoading(false);
    }
  }, [categories]);

  const handleMouseEnter = (categoryTitle: string) => {
    if (timeoutRef.current) {
      clearTimeout(timeoutRef.current);
      timeoutRef.current = null;
    }

    if (isComponentMounted.current) {
      setOpenDropdown(categoryTitle);
    }
  };

  const handleMouseLeave = () => {
    if (isComponentMounted.current) {
      timeoutRef.current = setTimeout(() => {
        if (isComponentMounted.current) {
          setOpenDropdown(null);
        }
      }, 300); // Increased delay for better UX
    }
  };

  // Since the API now filters categories with products, we don't need complex frontend logic

  // Show skeleton loader when categories are loading
  if (isLoading) {
    return (
      <nav className="w-full min-h-[40px]"> {/* Set minimum height to prevent layout shift */}
        <ul className="flex flex-wrap justify-center gap-1 md:gap-2">
          {Array.from({ length: 6 }).map((_, index) => (
            <li key={index} className="relative">
              <div className="inline-flex h-10 items-center justify-center rounded-md bg-white/80 backdrop-blur-sm px-4 py-2 shadow-sm border border-gray-100">
                <Skeleton className="w-16 h-4" />
              </div>
            </li>
          ))}
        </ul>
      </nav>
    );
  }

  // Don't render navigation if no categories
  if (!categories || !Array.isArray(categories) || categories.length === 0) {
    return null;
  }

  return (
    <nav className="w-full">
      <ul className="flex flex-wrap justify-center gap-1 md:gap-2">
        {categories.map((category: any) => (
          <li
            key={category?.name}
            className="relative group"
            onMouseEnter={() => handleMouseEnter(category?.name)}
            onMouseLeave={handleMouseLeave}
          >
            {Array.isArray(category?.subcategories) &&
            Boolean(category?.subcategories.length > 0) ? (
              <>
                <Link
                  href={`/products/categories/${category.slug}?callbackUrl=%2F${pathName}`}
                  className={`inline-flex h-10 items-center justify-center rounded-md bg-white/80 backdrop-blur-sm px-4 py-2 text-xs md:text-sm font-medium transition-all duration-300 hover:bg-gray-100 hover:text-gray-900 hover:shadow-md hover:-translate-y-[1px] focus:bg-gray-100 focus:text-gray-900 focus:outline-none shadow-sm border border-gray-100 ${
                    openDropdown === category?.name ? 'bg-gray-100 text-gray-900 shadow-md -translate-y-[1px]' : ''
                  }`}
                >
                  {category.name}
                  {/* Small dropdown indicator */}
                  <svg
                    className={`ml-1 h-3 w-3 transition-transform duration-200 ${
                      openDropdown === category?.name ? 'rotate-180' : ''
                    }`}
                    fill="none"
                    stroke="currentColor"
                    viewBox="0 0 24 24"
                  >
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M19 9l-7 7-7-7" />
                  </svg>
                </Link>
                {/* Dropdown with smooth transitions */}
                <div
                  className={`absolute left-1/2 top-full z-50 -translate-x-1/2 transform transition-all duration-300 ease-in-out ${
                    openDropdown === category?.name
                      ? 'opacity-100 visible translate-y-0'
                      : 'opacity-0 invisible -translate-y-2 pointer-events-none'
                  }`}
                  onMouseEnter={() => handleMouseEnter(category?.name)}
                  onMouseLeave={handleMouseLeave}
                >
                  {/* Larger invisible bridge to prevent gaps */}
                  <div className="h-4 -mt-2 w-full" />
                  <ul className="min-w-[12rem] rounded-lg border border-gray-200 bg-white p-2 shadow-xl backdrop-blur-sm">
                    {/* Subcategories are already filtered by the API to only include those with products */}
                    {category?.subcategories?.map((subcategory: any) => (
                      <li key={subcategory.name}>
                        <Link
                          href={`/products/categories/${subcategory.slug}?callbackUrl=%2F${pathName}`}
                          className="block rounded-md px-4 py-2 text-sm text-gray-700 hover:bg-gray-100 hover:text-gray-900 transition-colors duration-200"
                        >
                          {subcategory.name}
                        </Link>
                      </li>
                    ))}
                  </ul>
                </div>
              </>
            ) : (
              <Link
                href={`/products/categories/${category.slug}?callbackUrl=%2F${pathName}`}
                className="inline-flex h-9 items-center justify-center rounded-md bg-background px-4 py-2 text-xs md:text-sm font-medium transition-colors hover:bg-accent hover:text-accent-foreground focus:bg-accent focus:text-accent-foreground focus:outline-none"
              >
                {category.name}
              </Link>
            )}
          </li>
        ))}
      </ul>
    </nav>
  );
}