import os
import re
import csv
import json
import logging
import threading
import time
from concurrent.futures import Thr<PERSON><PERSON>oolExecutor
from pathlib import Path
from typing import List, Dict, Optional

import fitz  # PyMuPDF
import pandas as pd
import requests
from tqdm import tqdm

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler("pdf_extraction.log"),
        logging.StreamHandler()
    ]
)
logger = logging.getLogger(__name__)

# Constants
TARGET_CATEGORIES = ["Padlocks", "Furniture Locks", "Rim Locks", "Mortise Locks"]
OUTPUT_DIR = "output"
IMAGES_DIR = os.path.join(OUTPUT_DIR, "images")
CSV_DIR = os.path.join(OUTPUT_DIR, "csv")
JSON_DIR = os.path.join(OUTPUT_DIR, "json")

# Create necessary directories
os.makedirs(OUTPUT_DIR, exist_ok=True)
os.makedirs(IMAGES_DIR, exist_ok=True)
os.makedirs(CSV_DIR, exist_ok=True)
os.makedirs(JSON_DIR, exist_ok=True)

# Lock for thread-safe operations
thread_lock = threading.Lock()


class PDFExtractor:
    """Class to extract data from PDF files"""

    def __init__(self, pdf_path: str):
        self.pdf_path = pdf_path
        self.filename = os.path.basename(pdf_path)
        self.doc = fitz.open(pdf_path)
        logger.info(f"Opened PDF: {self.filename}")

    def extract_text(self) -> str:
        """Extract all text from the PDF"""
        text = ""
        for page_num in range(len(self.doc)):
            page = self.doc[page_num]
            text += page.get_text()
        return text

    def extract_tables(self) -> List[Dict]:
        """Extract tables from PDF and return as list of dictionaries"""
        tables = []
        # Implementation depends on PDF structure
        # This is a placeholder - actual implementation will vary based on PDF format
        return tables

    def close(self):
        """Close the PDF document"""
        self.doc.close()


class GodrejPDFExtractor(PDFExtractor):
    """Specific extractor for Godrej PDF format"""

    def extract_products(self) -> List[Dict]:
        """Extract products from Godrej PDF"""
        products = []
        try:
            # Extract text from each page
            current_category = None
            current_subcategory = None

            # Find the page ranges for each target category
            contents_page = None
            category_page_ranges = {}

            # First, find the contents page
            for page_num in range(len(self.doc)):
                page = self.doc[page_num]
                text = page.get_text()
                if "CONTENTS" in text:
                    contents_page = page_num
                    logger.info(f"Found contents page at page {page_num+1}")
                    break

            # Parse the contents page to get page ranges for each category
            if contents_page is not None:
                contents_text = self.doc[contents_page].get_text()
                lines = contents_text.split('\n')

                print(f"Contents page text:\n{contents_text}")

                # In this PDF, the category is on one line and the page range is on the next line
                for i, line in enumerate(lines):
                    for category in TARGET_CATEGORIES:
                        if category in line and i < len(lines) - 1:
                            print(f"Found category line: {line}")
                            # Get the next line which should contain the page range
                            next_line = lines[i + 1]
                            print(f"Next line: {next_line}")

                            # Extract page range from the next line
                            match = re.search(r'(\d+)\s*-\s*(\d+)', next_line)
                            if match:
                                start_page = int(match.group(1))
                                end_page = int(match.group(2))
                                category_page_ranges[category] = (start_page, end_page)
                                logger.info(f"Category {category} found on pages {start_page}-{end_page}")
                            else:
                                print(f"No page range match in next line: {next_line}")

            # Process each target category
            for category, (start_page, end_page) in category_page_ranges.items():
                logger.info(f"Processing category: {category} on pages {start_page}-{end_page}")

                # Adjust for 0-based indexing
                start_page = start_page - 1
                end_page = end_page - 1

                # Process each page in the range
                for page_num in range(start_page, end_page + 1):
                    if page_num >= len(self.doc):
                        continue

                    page = self.doc[page_num]

                    # Try to extract tables from the page
                    tables = self._extract_tables_from_page(page)

                    print(f"Page {page_num+1}: Found {len(tables)} tables")

                    if tables:
                        for table_idx, table in enumerate(tables):
                            print(f"Table {table_idx+1} has {len(table)} rows")
                            # Process each row in the table
                            current_subcategory = None

                            for row_idx, row in enumerate(table):
                                print(f"Row {row_idx+1}: {row}")

                                # Skip header rows
                                if row and isinstance(row[0], str) and ("Sr." in row[0] or "Description" in row[0] or "Product Code" in row[0]):
                                    print(f"Skipping header row: {row}")
                                    continue

                                # Check if row contains subcategory
                                if row and len(row) > 0 and isinstance(row[0], str) and row[0].strip().isupper() and len(row[0]) > 3:
                                    current_subcategory = row[0].strip()
                                    logger.info(f"Found subcategory: {current_subcategory}")
                                    continue

                                # Try to extract product information
                                product = self._parse_table_row(row, category, current_subcategory)
                                if product:
                                    products.append(product)
                                    logger.info(f"Extracted product: {product['name']}")
                                else:
                                    print(f"Failed to extract product from row: {row}")

                    # If no tables were found, try text-based extraction
                    else:
                        text = page.get_text()
                        lines = text.split('\n')

                        # Look for product information in lines
                        for i, line in enumerate(lines):
                            # Skip header lines
                            if "Sr." in line or "Description" in line or "Product Code" in line:
                                continue

                            # Check if line contains subcategory
                            if line.strip().isupper() and len(line.strip()) > 3:
                                current_subcategory = line.strip()
                                logger.info(f"Found subcategory: {current_subcategory}")
                                continue

                            # Try to extract product information
                            product = self._parse_product_line(line, category, current_subcategory)
                            if product:
                                products.append(product)
                                logger.info(f"Extracted product: {product['name']}")

            logger.info(f"Extracted {len(products)} products from Godrej PDF")
            return products

        except Exception as e:
            logger.error(f"Error extracting products from Godrej PDF: {str(e)}")
            return []

    def _extract_tables_from_page(self, page) -> List[List[List]]:
        """Extract tables from a page"""
        try:
            # Use PyMuPDF to extract tables
            tables = []

            # Extract tables using PyMuPDF
            tab = page.find_tables(
                vertical_strategy="lines",
                horizontal_strategy="lines",
                snap_tolerance=3,
                snap_x_tolerance=3,
                snap_y_tolerance=3
            )

            if tab.tables:
                for table in tab.tables:
                    rows = []
                    for row_cells in table.extract():
                        rows.append(row_cells)
                    tables.append(rows)

            return tables

        except Exception as e:
            logger.error(f"Error extracting tables: {str(e)}")
            return []

    def _parse_table_row(self, row: List, category: str, subcategory: Optional[str] = None) -> Optional[Dict]:
        """Parse a table row into a product dictionary"""
        try:
            # Skip empty rows
            if not row or len(row) < 3:
                return None

            # Print the row for debugging
            print(f"Parsing row: {row}")

            # For Godrej PDF, the table structure is:
            # [Sr.No., None, Description, Product Code, UNSPSC Code, Std. Pkg., UNIT MRP (Rs.), Product]

            # Check if this is a product row (has a numeric first column and a product code)
            if len(row) >= 7 and row[0] and row[3]:
                try:
                    # Try to convert first column to int to check if it's a product row
                    sr_no = str(row[0]).strip()
                    int(sr_no)  # Just to check if it's a number

                    description = str(row[2]).strip() if row[2] else ""
                    product_code = str(row[3]).strip() if row[3] else ""

                    # Price is in column 6 (index 6)
                    price_str = str(row[6]).strip() if row[6] else ""

                    # Extract numeric part from price string
                    price_match = re.search(r'(\d+(?:\.\d+)?)', price_str)
                    if price_match:
                        price = float(price_match.group(1))
                    else:
                        price = None

                    # Create product if we have the minimum required information
                    if description and product_code and price:
                        product = {
                            "name": f"{description} {category}",
                            "model_number": product_code,
                            "price": price,
                            "description": description,
                            "brand": "Godrej",
                            "category": category,
                            "subcategory": subcategory if subcategory else category,
                            "images": []
                        }
                        print(f"Successfully extracted product: {product}")
                        return product
                except (ValueError, TypeError):
                    pass

            return None

        except Exception as e:
            logger.error(f"Error parsing table row: {row}, Error: {str(e)}")
            return None

    def _parse_product_line(self, line: str, category: str, subcategory: Optional[str]) -> Optional[Dict]:
        """Parse a product line into a dictionary"""
        try:
            # Remove extra whitespace
            line = ' '.join(line.split())

            # Pattern for product line: Sr.No, Description, Product Code, Price
            pattern = r'(\d+)\s+(.*?)\s+(\d{4})\s+.*?(\d+)$'
            match = re.search(pattern, line)

            if match:
                sr_no = match.group(1)
                description = match.group(2).strip()
                product_code = match.group(3)
                price = float(match.group(4))

                product = {
                    "name": f"{description} {category}",
                    "model_number": product_code,
                    "price": price,
                    "description": description,
                    "brand": "Godrej",
                    "category": category,
                    "subcategory": subcategory if subcategory else category,
                    "images": []
                }
                return product

            return None

        except Exception as e:
            logger.error(f"Error parsing product line: {line}, Error: {str(e)}")
            return None

    def _is_target_category(self, product: Dict) -> bool:
        """Check if product belongs to target categories"""
        if "category" in product:
            return any(category.lower() in product["category"].lower() for category in TARGET_CATEGORIES)
        return False


class StandardPDFExtractor(PDFExtractor):
    """Specific extractor for Standard MRP PDF format"""

    def extract_products(self) -> List[Dict]:
        """Extract products from Standard MRP PDF"""
        products = []
        try:
            # Find the index or table of contents
            toc_page = None
            category_page_ranges = {}

            # First, look for a table of contents or index
            for page_num in range(min(10, len(self.doc))):  # Check first 10 pages
                page = self.doc[page_num]
                text = page.get_text()

                if "INDEX" in text or "CONTENTS" in text or "Table of Contents" in text:
                    toc_page = page_num
                    logger.info(f"Found table of contents on page {page_num+1}")
                    break

            # If we found a TOC, try to extract page ranges for target categories
            if toc_page is not None:
                toc_text = self.doc[toc_page].get_text()
                lines = toc_text.split('\n')

                for category in TARGET_CATEGORIES:
                    for i, line in enumerate(lines):
                        if category in line:
                            # Try to extract page numbers
                            page_match = re.search(r'(\d+)(?:\s*-\s*(\d+))?', line)
                            if page_match:
                                start_page = int(page_match.group(1))
                                end_page = int(page_match.group(2)) if page_match.group(2) else start_page + 5  # Assume 5 pages if no end page
                                category_page_ranges[category] = (start_page, end_page)
                                logger.info(f"Category {category} found on pages {start_page}-{end_page}")

            # If we couldn't find page ranges from TOC, search through all pages
            if not category_page_ranges:
                logger.info("No page ranges found in TOC, searching all pages")

                # Scan through all pages to find target categories
                for page_num in range(len(self.doc)):
                    page = self.doc[page_num]
                    text = page.get_text()

                    for category in TARGET_CATEGORIES:
                        if category in text:
                            # Process this page for the category
                            logger.info(f"Found category {category} on page {page_num+1}")

                            # Extract tables from the page
                            tables = self._extract_tables_from_page(page)
                            current_subcategory = None

                            if tables:
                                for table in tables:
                                    # Process each row in the table
                                    for row in table:
                                        # Skip header rows
                                        if row and isinstance(row[0], str) and ("Sr." in row[0] or "S.No." in row[0] or "Item Code" in row[0]):
                                            continue

                                        # Check if row contains subcategory
                                        if row and len(row) > 0 and isinstance(row[0], str) and row[0].strip().isupper() and len(row[0]) > 3:
                                            current_subcategory = row[0].strip()
                                            logger.info(f"Found subcategory: {current_subcategory}")
                                            continue

                                        # Try to extract product information
                                        product = self._parse_table_row(row, category, current_subcategory)
                                        if product:
                                            products.append(product)
                                            logger.info(f"Extracted product: {product['name']}")

                            # If no tables were found, try text-based extraction
                            else:
                                lines = text.split('\n')

                                # Look for product information in lines
                                for i, line in enumerate(lines):
                                    # Skip header lines
                                    if "Sr." in line or "S.No." in line or "Item Code" in line:
                                        continue

                                    # Check if line contains subcategory
                                    if line.strip().isupper() and len(line.strip()) > 3:
                                        current_subcategory = line.strip()
                                        logger.info(f"Found subcategory: {current_subcategory}")
                                        continue

                                    # Try to extract product information
                                    product = self._parse_product_line(line, category, current_subcategory)
                                    if product:
                                        products.append(product)
                                        logger.info(f"Extracted product: {product['name']}")

            # If we have page ranges from TOC, process those pages
            else:
                for category, (start_page, end_page) in category_page_ranges.items():
                    logger.info(f"Processing category: {category} on pages {start_page}-{end_page}")

                    # Adjust for 0-based indexing
                    start_page = start_page - 1
                    end_page = end_page - 1

                    # Process each page in the range
                    for page_num in range(start_page, end_page + 1):
                        if page_num >= len(self.doc):
                            continue

                        page = self.doc[page_num]

                        # Extract tables from the page
                        tables = self._extract_tables_from_page(page)
                        current_subcategory = None

                        if tables:
                            for table in tables:
                                # Process each row in the table
                                for row in table:
                                    # Skip header rows
                                    if row and isinstance(row[0], str) and ("Sr." in row[0] or "S.No." in row[0] or "Item Code" in row[0]):
                                        continue

                                    # Check if row contains subcategory
                                    if row and len(row) > 0 and isinstance(row[0], str) and row[0].strip().isupper() and len(row[0]) > 3:
                                        current_subcategory = row[0].strip()
                                        logger.info(f"Found subcategory: {current_subcategory}")
                                        continue

                                    # Try to extract product information
                                    product = self._parse_table_row(row, category, current_subcategory)
                                    if product:
                                        products.append(product)
                                        logger.info(f"Extracted product: {product['name']}")

                        # If no tables were found, try text-based extraction
                        else:
                            text = page.get_text()
                            lines = text.split('\n')

                            # Look for product information in lines
                            for i, line in enumerate(lines):
                                # Skip header lines
                                if "Sr." in line or "S.No." in line or "Item Code" in line:
                                    continue

                                # Check if line contains subcategory
                                if line.strip().isupper() and len(line.strip()) > 3:
                                    current_subcategory = line.strip()
                                    logger.info(f"Found subcategory: {current_subcategory}")
                                    continue

                                # Try to extract product information
                                product = self._parse_product_line(line, category, current_subcategory)
                                if product:
                                    products.append(product)
                                    logger.info(f"Extracted product: {product['name']}")

            logger.info(f"Extracted {len(products)} products from Standard MRP PDF")
            return products

        except Exception as e:
            logger.error(f"Error extracting products from Standard MRP PDF: {str(e)}")
            return []

    def _extract_tables_from_page(self, page) -> List[List[List]]:
        """Extract tables from a page"""
        try:
            # Use PyMuPDF to extract tables
            tables = []

            # Extract tables using PyMuPDF
            tab = page.find_tables(
                vertical_strategy="lines",
                horizontal_strategy="lines",
                snap_tolerance=3,
                snap_x_tolerance=3,
                snap_y_tolerance=3
            )

            if tab.tables:
                for table in tab.tables:
                    rows = []
                    for row_cells in table.extract():
                        rows.append(row_cells)
                    tables.append(rows)

            return tables

        except Exception as e:
            logger.error(f"Error extracting tables: {str(e)}")
            return []

    def _parse_table_row(self, row: List, category: str, subcategory: Optional[str] = None) -> Optional[Dict]:
        """Parse a table row into a product dictionary"""
        try:
            # Skip empty rows
            if not row or len(row) < 3:
                return None

            # Extract product information from row
            # Assuming columns: Sr. No., Item Code, Description, MRP
            item_code = None
            description = None
            price = None

            # Try to identify columns based on content
            for i, cell in enumerate(row):
                cell_str = str(cell).strip()

                # Skip empty cells
                if not cell_str:
                    continue

                # Check if cell contains item code (usually numeric)
                if not item_code and cell_str.isdigit():
                    item_code = cell_str

                # Check if cell contains price (usually has Rs. or just a number)
                elif not price and ("Rs." in cell_str or "₹" in cell_str or (cell_str.replace(".", "").isdigit())):
                    # Extract numeric part
                    price_match = re.search(r'(\d+(?:\.\d+)?)', cell_str)
                    if price_match:
                        price = float(price_match.group(1))

                # Assume longest text is description
                elif not description or (cell_str and len(cell_str) > len(description)):
                    description = cell_str

            # Create product if we have the minimum required information
            if item_code and description and price:
                product = {
                    "name": f"{description} {category}",
                    "model_number": item_code,
                    "price": price,
                    "description": description,
                    "brand": "Standard",
                    "category": category,
                    "subcategory": subcategory if subcategory else category,
                    "images": []
                }
                return product

            return None

        except Exception as e:
            logger.error(f"Error parsing table row: {row}, Error: {str(e)}")
            return None

    def _parse_product_line(self, line: str, category: str, subcategory: Optional[str] = None) -> Optional[Dict]:
        """Parse a product line into a dictionary"""
        try:
            # Remove extra whitespace
            line = ' '.join(line.split())

            # Different patterns for product lines
            # Pattern 1: Sr.No, Description, Product Code, Price
            pattern1 = r'(\d+)\s+(.*?)\s+(\d{3,})\s+.*?(\d+(?:\.\d+)?)$'

            # Pattern 2: Product Code, Description, Price
            pattern2 = r'^(\d{3,})\s+(.*?)\s+(\d+(?:\.\d+)?)$'

            # Try pattern 1 first
            match = re.search(pattern1, line)

            if match:
                # Extract using pattern 1
                description = match.group(2).strip()
                product_code = match.group(3)
                price = float(match.group(4))
            else:
                # Try pattern 2
                match = re.search(pattern2, line)
                if match:
                    product_code = match.group(1)
                    description = match.group(2).strip()
                    price = float(match.group(3))
                else:
                    return None

            # Create product dictionary
            product = {
                "name": f"{description} {category}",
                "model_number": product_code,
                "price": price,
                "description": description,
                "brand": "Standard",
                "category": category,
                "subcategory": subcategory if subcategory else category,
                "images": []
            }
            return product

        except Exception as e:
            logger.error(f"Error parsing product line: {line}, Error: {str(e)}")
            return None

    def _is_target_category(self, product: Dict) -> bool:
        """Check if product belongs to target categories"""
        if "category" in product:
            return any(category.lower() in product["category"].lower() for category in TARGET_CATEGORIES)
        return False


def save_to_csv(products: List[Dict], filename: str) -> str:
    """Save products to CSV file"""
    csv_path = os.path.join(CSV_DIR, filename)
    try:
        if products:
            # Get all unique keys from all products
            fieldnames = set()
            for product in products:
                fieldnames.update(product.keys())

            # Check if file exists and try to handle permission issues
            try:
                # Try to open the file with 'w' mode
                with open(csv_path, 'w', newline='', encoding='utf-8') as csvfile:
                    writer = csv.DictWriter(csvfile, fieldnames=sorted(fieldnames))
                    writer.writeheader()
                    writer.writerows(products)
            except PermissionError:
                # If permission error, try with a different filename
                base, ext = os.path.splitext(filename)
                new_filename = f"{base}_new{ext}"
                csv_path = os.path.join(CSV_DIR, new_filename)

                with open(csv_path, 'w', newline='', encoding='utf-8') as csvfile:
                    writer = csv.DictWriter(csvfile, fieldnames=sorted(fieldnames))
                    writer.writeheader()
                    writer.writerows(products)

            logger.info(f"Saved {len(products)} products to {csv_path}")
        else:
            logger.warning(f"No products to save to CSV")
            # Create an empty file if no products
            try:
                with open(csv_path, 'w', newline='', encoding='utf-8') as csvfile:
                    pass
            except PermissionError:
                # If permission error, try with a different filename
                base, ext = os.path.splitext(filename)
                new_filename = f"{base}_new{ext}"
                csv_path = os.path.join(CSV_DIR, new_filename)

                with open(csv_path, 'w', newline='', encoding='utf-8') as csvfile:
                    pass

        return csv_path

    except Exception as e:
        logger.error(f"Error saving to CSV: {str(e)}")
        return ""


class SimpleImageScraper:
    """A simplified image scraper that uses requests instead of Selenium"""

    def __init__(self):
        self.headers = {
            'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36'
        }

    def search_images(self, product_name: str, product_code: str, category: str) -> List[str]:
        """Search for product images using a simple approach"""
        image_urls = []

        # Create a placeholder image URL based on product details
        # This is a simplified approach - in a real implementation, you would use an actual image search API
        search_term = f"{category} {product_name} {product_code}".strip().replace(" ", "-").lower()

        # Generate some placeholder image URLs
        image_urls = [
            f"https://example.com/images/{search_term}-1.jpg",
            f"https://example.com/images/{search_term}-2.jpg",
            f"https://example.com/images/{search_term}-3.jpg"
        ]

        logger.info(f"Generated {len(image_urls)} placeholder image URLs for {product_name}")
        return image_urls

    def download_image(self, url: str, product_id: str, index: int) -> Optional[str]:
        """Create a placeholder for image download"""
        try:
            # Create directory for product if it doesn't exist
            product_dir = os.path.join(IMAGES_DIR, product_id)
            os.makedirs(product_dir, exist_ok=True)

            # Create a placeholder image path
            image_path = os.path.join(product_dir, f"{index}.jpg")

            # In a real implementation, you would download the image here
            # For now, just create an empty file as a placeholder
            with open(image_path, 'w') as f:
                f.write(f"Placeholder for image from {url}")

            return image_path

        except Exception as e:
            logger.error(f"Error creating placeholder image for {url}: {str(e)}")
            return None

    def scrape_images(self, product: Dict) -> List[str]:
        """Generate placeholder image paths for a product"""
        image_paths = []

        # Generate search terms
        product_name = product.get("name", "")
        product_code = str(product.get("model_number", ""))
        category = product.get("category", "")

        # Get placeholder image URLs
        image_urls = self.search_images(product_name, product_code, category)

        # Create placeholder image files
        product_id = str(product.get("model_number", "")).replace(" ", "_")
        if not product_id:
            product_id = str(product.get("name", "")).replace(" ", "_")

        for i, url in enumerate(image_urls):
            image_path = self.download_image(url, product_id, i+1)
            if image_path:
                image_paths.append(image_path)

        return image_paths


def csv_to_json(csv_path: str) -> List[Dict]:
    """Convert CSV data to JSON format"""
    try:
        df = pd.read_csv(csv_path)
        products = []

        for _, row in df.iterrows():
            product = {
                "name": f"{row.get('name', '')} {row.get('category', '')}".strip(),
                "price": row.get('price', 0),
                "discount": row.get('discount', 0),
                "description": row.get('description', ''),
                "rating": row.get('rating', 0),
                "brand": row.get('brand', ''),
                "category": row.get('category', ''),
                "images": []
            }

            # Add any additional fields from CSV
            for col in df.columns:
                if col not in product and not pd.isna(row[col]):
                    product[col] = row[col]

            products.append(product)

        logger.info(f"Converted {len(products)} products from CSV to JSON format")
        return products

    except Exception as e:
        logger.error(f"Error converting CSV to JSON: {str(e)}")
        return []


def scrape_product_images(product: Dict, scraper: SimpleImageScraper) -> Dict:
    """Scrape images for a product and update the product dict"""
    try:
        logger.info(f"Scraping images for product: {product.get('name', '')}")
        image_paths = scraper.scrape_images(product)

        # Update product with image paths
        product["images"] = image_paths

        return product

    except Exception as e:
        logger.error(f"Error scraping images for product {product.get('name', '')}: {str(e)}")
        return product


def process_products_with_threading(products: List[Dict], max_workers: int = 4) -> List[Dict]:
    """Process products with multithreading for image scraping"""
    try:
        # Create a shared scraper instance
        scraper = SimpleImageScraper()

        # Create a progress bar
        progress_bar = tqdm(total=len(products), desc="Scraping product images")

        # Function to update progress bar
        def update_progress(_):
            progress_bar.update(1)

        # Process products with ThreadPoolExecutor
        with ThreadPoolExecutor(max_workers=max_workers) as executor:
            # Submit tasks
            futures = []
            for product in products:
                future = executor.submit(scrape_product_images, product, scraper)
                future.add_done_callback(update_progress)
                futures.append(future)

            # Get results
            updated_products = []
            for future in futures:
                try:
                    updated_product = future.result()
                    updated_products.append(updated_product)
                except Exception as e:
                    logger.error(f"Error processing product: {str(e)}")

        # Close progress bar
        progress_bar.close()

        return updated_products

    except Exception as e:
        logger.error(f"Error in multithreaded processing: {str(e)}")
        return products


def save_json(products: List[Dict], filename: str) -> str:
    """Save products to JSON file"""
    json_path = os.path.join(JSON_DIR, filename)
    try:
        with open(json_path, 'w', encoding='utf-8') as f:
            json.dump({"products": products}, f, indent=2, ensure_ascii=False)

        logger.info(f"Saved {len(products)} products to {json_path}")
        return json_path

    except Exception as e:
        logger.error(f"Error saving JSON: {str(e)}")
        return ""


def validate_json(products: List[Dict]) -> bool:
    """Validate JSON structure"""
    try:
        required_fields = ["name", "price", "category", "brand"]

        for product in products:
            # Check required fields
            for field in required_fields:
                if field not in product:
                    logger.warning(f"Product missing required field: {field}")
                    return False

            # Check data types
            if not isinstance(product.get("price"), (int, float)):
                logger.warning(f"Product price is not a number: {product.get('price')}")
                return False

            if not isinstance(product.get("images"), list):
                logger.warning(f"Product images is not a list: {product.get('images')}")
                return False

        return True

    except Exception as e:
        logger.error(f"Error validating JSON: {str(e)}")
        return False


def main():
    """Main function to extract data from PDFs and convert to JSON"""
    try:
        print("Starting PDF extraction process...")

        # Step 1: Extract data from PDFs
        pdf_files = [
            "Godrej Product Price List Apr 22.pdf",
            "Standard MRP_Feb 2022.pdf"
        ]

        # Check if PDF files exist
        for pdf_file in pdf_files:
            if not os.path.exists(pdf_file):
                print(f"Error: PDF file not found: {pdf_file}")
                logger.error(f"PDF file not found: {pdf_file}")
                return

        print(f"Found PDF files: {pdf_files}")

        all_products = []

        for pdf_file in pdf_files:
            print(f"Processing PDF: {pdf_file}")
            logger.info(f"Processing PDF: {pdf_file}")

            # Determine which extractor to use based on filename
            if "Godrej" in pdf_file:
                print("Using Godrej PDF Extractor")
                extractor = GodrejPDFExtractor(pdf_file)
            else:
                print("Using Standard PDF Extractor")
                extractor = StandardPDFExtractor(pdf_file)

            # Extract products
            print("Extracting products...")
            products = extractor.extract_products()
            extractor.close()

            print(f"Extracted {len(products)} products")

            # Filter products to target categories
            filtered_products = [p for p in products if p.get("category", "") in TARGET_CATEGORIES]
            print(f"Filtered to {len(filtered_products)} products in target categories")
            logger.info(f"Filtered to {len(filtered_products)} products in target categories")

            # Save to CSV
            csv_filename = os.path.splitext(pdf_file)[0] + ".csv"
            print(f"Saving to CSV: {csv_filename}")
            csv_path = save_to_csv(filtered_products, csv_filename)

            if csv_path:
                print(f"Successfully saved to CSV: {csv_path}")
                # Convert to JSON format
                print("Converting CSV to JSON format...")
                json_products = csv_to_json(csv_path)
                all_products.extend(json_products)

        print(f"Total products extracted: {len(all_products)}")
        logger.info(f"Total products extracted: {len(all_products)}")

        # Step 2: Process products with multithreading for image scraping
        if all_products:
            print("Starting image scraping with multithreading...")
            updated_products = process_products_with_threading(all_products)

            # Step 3: Validate and save final JSON
            print("Validating JSON structure...")
            if validate_json(updated_products):
                json_path = save_json(updated_products, "product_data.json")
                print(f"Successfully saved product data to {json_path}")
                logger.info(f"Successfully saved product data to {json_path}")
            else:
                print("JSON validation failed")
                logger.error("JSON validation failed")
        else:
            print("No products extracted, skipping image scraping and JSON creation")
            logger.error("No products extracted, skipping image scraping and JSON creation")

        print("PDF extraction process completed")

    except Exception as e:
        print(f"Error in main function: {str(e)}")
        logger.error(f"Error in main function: {str(e)}")
        import traceback
        traceback.print_exc()
        logger.error(traceback.format_exc())


if __name__ == "__main__":
    main()
