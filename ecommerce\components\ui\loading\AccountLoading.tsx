import React from 'react'

const AccountLoading = () => {
  return (
    <div className="animate-pulse">
      <div className="min-h-screen bg-gradient-to-b from-gray-50 to-white">
        <div className="container mx-auto px-4 py-12 max-w-6xl">
          {/* Header */}
          <div className="flex flex-col md:flex-row justify-between items-start md:items-center mb-10 gap-4">
            <div className="w-48 h-8 bg-gray-200 rounded-lg"></div>
            <div className="w-28 h-10 bg-gray-200 rounded-lg"></div>
          </div>

          {/* Profile Card */}
          <div className="border-0 shadow-md rounded-2xl bg-white overflow-hidden">
            <div className="p-8 flex items-center md:flex-row flex-col gap-8">
              <div className="relative">
                <div className="h-28 w-28 rounded-full bg-gray-200"></div>
              </div>

              <div className="flex-1 flex items-center md:items-start flex-col space-y-3">
                <div className="w-40 h-6 bg-gray-200 rounded-lg"></div>
                <div className="space-y-2 mt-1 w-full">
                  <div className="w-56 h-4 bg-gray-200 rounded-lg"></div>
                  <div className="w-48 h-4 bg-gray-200 rounded-lg"></div>
                  <div className="w-40 h-4 bg-gray-200 rounded-lg"></div>
                </div>
              </div>

              <div className="w-32 h-10 bg-gray-200 rounded-lg"></div>
            </div>
          </div>

          {/* Tabs */}
          <div className="mt-12">
            <div className="w-full">
              <div className="grid w-full grid-cols-3 lg:w-auto mb-6 rounded-xl bg-gray-100/80 p-1.5">
                <div className="rounded-lg h-10 bg-white"></div>
                <div className="rounded-lg h-10 bg-transparent"></div>
                <div className="rounded-lg h-10 bg-transparent"></div>
              </div>

              <div className="bg-white rounded-xl shadow-sm border border-gray-100 p-6">
                {/* Tab Content */}
                <div className="space-y-6">
                  <div className="w-40 h-6 bg-gray-200 rounded-lg"></div>

                  <div className="space-y-4">
                    {[1, 2, 3].map((i) => (
                      <div key={i} className="border border-gray-200 rounded-xl overflow-hidden shadow-sm bg-white">
                        <div className="px-6 py-4">
                          <div className="flex flex-col sm:flex-row sm:items-center justify-between w-full gap-3">
                            <div className="flex items-center gap-3">
                              <div className="bg-gray-200 p-2 rounded-full w-9 h-9"></div>
                              <div className="w-32 h-5 bg-gray-200 rounded-lg"></div>
                            </div>
                            <div className="flex flex-wrap items-center gap-3 sm:gap-4">
                              <div className="w-20 h-5 bg-gray-200 rounded-full"></div>
                              <div className="w-24 h-5 bg-gray-200 rounded-lg"></div>
                            </div>
                          </div>
                        </div>
                      </div>
                    ))}
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  )
}

export default AccountLoading