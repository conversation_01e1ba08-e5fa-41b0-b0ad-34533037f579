import { Card, CardContent } from "../../components/ui/card";
import { Button } from "../../components/ui/button";
import { Star, Eye } from "lucide-react";
import { useToast } from "../../components/ui/use-toast";
// import {
//   Pagination,
//   PaginationContent,
//   PaginationItem,
//   PaginationLink,
// } from "../../components/ui/pagination";
import { useEffect, useState } from "react";
import { MAIN_URL, PRODUCTS, CATEGORIZE_PRODUCTS } from "../../constant/urls";
import useApi from "../../hooks/useApi";
import Pagination from "../ui/newPagination";
import { useRouter, useSearchParams } from "next/navigation";
import Product from "../product/Product";
import { ProductType } from "../../types/product";
import ProductCardLoading from "../ui/loading/ProductCardLoading";

interface ProductGridProps {
  filters: {
    priceRange: number[];
    categories: string[];
    brands: string[];
    rating: number;
  };
  sortBy: string;
  currentPage: number;
  onPageChange: (page: number) => void;
}

export const ProductGrid = ({
  filters,
  sortBy,
  currentPage,
  onPageChange,
}: ProductGridProps) => {
  const { data, loading, read }: any = useApi(MAIN_URL);
  const searchParams = useSearchParams();
  const router = useRouter();
  const [categorySlug, setCategorySlug] = useState<string | null>(null);

  // Check for category parameter in URL
  useEffect(() => {
    const category = searchParams.get('category');
    if (category) {
      setCategorySlug(category);
    }
  }, [searchParams]);

  useEffect(() => {
    let url;

    // If we have a category slug in the URL, use the category-specific endpoint
    if (categorySlug) {
      url = `${CATEGORIZE_PRODUCTS(categorySlug)}?page=${currentPage}`;
    } else {
      // Otherwise use the regular products endpoint with filters
      url = `${PRODUCTS}?page=${currentPage}&ordering=${sortBy}`;

      // Add filter parameters
      if (Boolean(filters.categories.length)) {
        const categoryParam = `&category=[${filters.categories.join(",")}]`;
        url += categoryParam;
      }

      if (Boolean(filters.brands.length)) {
        const brandParam = `&brand=[${filters.brands.join(",")}]`;
        url += brandParam;
      }

      if (Boolean(filters.priceRange.length)) {
        const priceParam = `&price=${filters.priceRange[0]}-${filters.priceRange[1]}`;
        url += priceParam;
      }
    }

    // Make the API call
    read(url);
  }, [currentPage, sortBy, filters, categorySlug, read]);

  if (loading) {
    return (
      <div>
        <div className="grid grid-cols-2 xs:grid-cols-2 sm:grid-cols-2 md:grid-cols-3 lg:grid-cols-4 xl:grid-cols-4 2xl:grid-cols-5 gap-2 xs:gap-3 sm:gap-4 md:gap-5 lg:gap-6">
          {Array.from({ length: 10 }).map((_, i) => (
            <ProductCardLoading key={i} />
          ))}
        </div>
        <div className="mt-8">
          <Pagination
            itemsPerPage={10}
            currentPage={currentPage}
            setCurrentPage={onPageChange}
            count={data?.count ?? 0}
          />
        </div>
      </div>
    );
  }

  // Extract products from different API response formats
  const getProductsFromData = () => {
    if (!data) return [];

    // Handle regular products endpoint response
    if (Array.isArray(data.results)) {
      return data.results;
    }

    // Handle category-specific endpoint response
    if (data.results && data.results.products && Array.isArray(data.results.products)) {
      return data.results.products;
    }

    // Handle other possible response formats
    if (Array.isArray(data)) {
      return data;
    }

    // If no products found, return empty array
    return [];
  };

  const products = getProductsFromData();
  const totalCount = data?.count || (products?.length || 0);

  // Show a message if no products are found
  if (!loading && (!products || products.length === 0)) {
    return (
      <div className="text-center py-16">
        <div className="mb-4">
          <svg xmlns="http://www.w3.org/2000/svg" className="h-12 w-12 mx-auto text-gray-400" fill="none" viewBox="0 0 24 24" stroke="currentColor">
            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={1.5} d="M3 3h2l.4 2M7 13h10l4-8H5.4M7 13L5.4 5M7 13l-2.293 2.293c-.63.63-.184 1.707.707 1.707H17m0 0a2 2 0 100 4 2 2 0 000-4zm-8 2a2 2 0 11-4 0 2 2 0 014 0z" />
          </svg>
        </div>
        <h3 className="text-lg font-medium text-gray-900 mb-1">No products found</h3>
        <p className="text-gray-500 mb-6">Try adjusting your filters or search criteria</p>
      </div>
    );
  }

  return (
    <div>
      <div className="grid grid-cols-2 xs:grid-cols-2 sm:grid-cols-2 md:grid-cols-3 lg:grid-cols-4 xl:grid-cols-4 2xl:grid-cols-5 gap-2 xs:gap-3 sm:gap-4 md:gap-5 lg:gap-6">
        {products.map((product: ProductType) => (
          <Product {...product} key={product.id} />
        ))}
      </div>
      <div className="mt-8">
        <Pagination
          itemsPerPage={10}
          currentPage={currentPage}
          setCurrentPage={onPageChange}
          count={totalCount}
        />
      </div>
    </div>
  );
};
