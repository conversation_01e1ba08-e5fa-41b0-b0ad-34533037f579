import {
  View,
  Text,
  StyleSheet,
  FlatList,
  Image,
  ActivityIndicator,
  TouchableOpacity,
} from "react-native";
import { SafeAreaView } from "react-native-safe-area-context";
import ProductCard from "../components/components/ProductCard";
import { useWishlist } from "@/hooks/useUserDetailHook";
import { useAuth } from "@/context/AuthContext";
import { addToCart } from "@/hooks/API";
import { useCallback, useEffect } from "react";
import { MaterialIcons } from "@expo/vector-icons";
import { useFocusEffect } from "@react-navigation/native";

export default function WishlistScreen({ navigation }) {
  const { wishlist: WISHLIST_ITEMS, loading, refetchWishlist  } = useWishlist();

  const { accessToken, isAuthenticated } = useAuth();
  const handleAddToCart = async (
    productId: any,
    quantity: number,
    accessToken: string
  ) => {
    try {
      const response = await addToCart(productId, quantity, accessToken);
      return response;
    } catch (error) {
      console.log("Error adding to cart", error);
    }
  };

  useFocusEffect(
    useCallback(() => {
      refetchWishlist();
    }, [])
  );

 

  if (!isAuthenticated) {
    return (
      <SafeAreaView style={styles.container}>
        <View style={styles.header}>
          <Text style={styles.title}>My Wishlist</Text>
        </View>
        <View style={styles.authRequired}>
          <MaterialIcons name="lock" size={48} color="#666" />
          <Text style={styles.authTitle}>Authentication Required</Text>
          <Text style={styles.authText}>Please login to view your orders</Text>
          <TouchableOpacity
            style={styles.loginButton}
            onPress={() => navigation.navigate('Auth', {
              returnTo: 'Orders'
            })}
          >
            <Text style={styles.loginButtonText}>Login</Text>
          </TouchableOpacity>
        </View>
      </SafeAreaView>
    );
  }
  

  const renderItem = ({ item }) => {
    const product = item.product;
    return (
      <ProductCard
        id={product.id}
        name={product.name}
        price={parseFloat(product.price)}
        image={
          product.image ||
          (product.images.length > 0 ? product.images[0].image : null)
        }
        rating={product.average_rating || 0}
        onAddToCart={() => handleAddToCart(product.id, 1, accessToken)}
        onPress={() => navigation.navigate("ProductDetails", { product })}
      />
    );
  };

  return (
    <SafeAreaView style={styles.container}>
      <View style={styles.header}>
        <Text style={styles.title}>Wishlist</Text>
      </View>
      {loading ? (
        <View style={styles.loadingContainer}>
          <ActivityIndicator size="large" color="#2563EB" />
          <Text>Loading Wishlist...</Text>
        </View>
      ) : (
        <FlatList
          data={WISHLIST_ITEMS}
          renderItem={renderItem}
          numColumns={2}
          keyExtractor={(item) => item.id.toString()}
          contentContainerStyle={styles.productList}
          columnWrapperStyle={styles.productRow}
          ListEmptyComponent={
            <View style={styles.emptyState}>
              <Text style={styles.emptyStateText}>Your wishlist is empty</Text>
            </View>
          }
        />
      )}
    </SafeAreaView>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: "#ffffff",
  },
  authRequired: {
    flex: 1,
    alignItems: 'center',
    justifyContent: 'center',
    padding: 16,
  },
  authTitle: {
    fontSize: 20,
    fontWeight: 'bold',
    marginTop: 16,
    marginBottom: 8,
  },
  authText: {
    fontSize: 16,
    color: '#666',
    textAlign: 'center',
    marginBottom: 24,
  },
  loginButton: {
    backgroundColor: '#2563EB',
    paddingHorizontal: 32,
    paddingVertical: 12,
    borderRadius: 8,
  },
  loginButtonText: {
    color: 'white',
    fontSize: 16,
    fontWeight: '600',
  },
  header: {
    padding: 16,
    borderBottomWidth: 1,
    borderBottomColor: "#f3f4f6",
  },
  title: {
    fontSize: 24,
    fontWeight: "bold",
  },
  productList: {
    padding: 8,
  },
  productRow: {
    justifyContent: "space-evenly",
  },
  emptyState: {
    flex: 1,
    alignItems: "center",
    justifyContent: "center",
    paddingVertical: 32,
  },
  emptyStateText: {
    fontSize: 16,
    color: "#666",
  },
  loadingContainer: {
    flex: 1,
    alignItems: "center",
    justifyContent: "center",
  },
});
