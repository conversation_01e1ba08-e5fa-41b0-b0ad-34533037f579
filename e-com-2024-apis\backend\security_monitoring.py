"""
Security monitoring and logging utilities for the e-commerce application.
This module provides security event logging, failed login tracking, and audit trails.
"""

import logging
from datetime import datetime, timedelta
from django.contrib.auth.signals import user_login_failed, user_logged_in, user_logged_out
from django.dispatch import receiver
from django.core.cache import cache
from django.conf import settings
from django.contrib.auth import get_user_model
from django.utils import timezone
from django.db import models
from .models import SecurityEvent, FailedLoginAttempt, BlockedIP

# Import security notification functions
try:
    from .security_notifications import (
        send_ip_blocked_alert,
        send_multiple_failed_logins_alert,
        security_notifier
    )
except ImportError:
    # Create dummy functions if security notifications are not available
    def send_ip_blocked_alert(*args, **kwargs):
        pass

    def send_multiple_failed_logins_alert(*args, **kwargs):
        pass

    security_notifier = None

# Set up security logger
security_logger = logging.getLogger('security')

User = get_user_model()

def get_client_ip(request):
    """Extract client IP address from request"""
    if request is None:
        # Use a simple logger without extra fields to avoid formatting errors
        import logging
        logger = logging.getLogger('django')
        logger.warning("get_client_ip called with None request")
        return '0.0.0.0'  # Default fallback IP

    try:
        # Check X-Forwarded-For header first (for load balancers/proxies)
        x_forwarded_for = request.META.get('HTTP_X_FORWARDED_FOR')
        if x_forwarded_for:
            ip = x_forwarded_for.split(',')[0].strip()
            return ip

        # Check X-Real-IP header (for nginx proxy)
        x_real_ip = request.META.get('HTTP_X_REAL_IP')
        if x_real_ip:
            return x_real_ip.strip()

        # Fall back to REMOTE_ADDR
        ip = request.META.get('REMOTE_ADDR', '0.0.0.0')
        return ip
    except (AttributeError, TypeError) as e:
        import logging
        logger = logging.getLogger('django')
        logger.warning(f"Error extracting client IP: {e}")
        return '0.0.0.0'  # Default fallback IP

def get_user_agent(request):
    """Extract user agent from request"""
    if request is None:
        # Use a simple logger without extra fields to avoid formatting errors
        import logging
        logger = logging.getLogger('django')
        logger.warning("get_user_agent called with None request")
        return 'Unknown'

    try:
        return request.META.get('HTTP_USER_AGENT', 'Unknown')
    except (AttributeError, TypeError) as e:
        import logging
        logger = logging.getLogger('django')
        logger.warning(f"Error extracting user agent: {e}")
        return 'Unknown'

def parse_browser_info(user_agent):
    """Parse user agent string to extract readable browser information"""
    if not user_agent or user_agent == 'Unknown':
        return {
            'browser': 'Unknown',
            'version': 'Unknown',
            'os': 'Unknown',
            'raw_user_agent': user_agent
        }

    browser = 'Unknown'
    version = 'Unknown'
    os = 'Unknown'

    # Extract OS information
    if 'Windows NT' in user_agent:
        os = 'Windows'
    elif 'Macintosh' in user_agent:
        os = 'macOS'
    elif 'Linux' in user_agent:
        os = 'Linux'
    elif 'Android' in user_agent:
        os = 'Android'
    elif 'iPhone' in user_agent or 'iPad' in user_agent:
        os = 'iOS'

    # Extract browser information (order matters - check most specific first)
    if 'Edg/' in user_agent:
        browser = 'Edge'
        # Extract Edge version
        import re
        match = re.search(r'Edg/(\d+\.\d+)', user_agent)
        if match:
            version = match.group(1)
    elif 'OPR/' in user_agent or 'Opera/' in user_agent:
        browser = 'Opera'
        import re
        match = re.search(r'(?:OPR|Opera)/(\d+\.\d+)', user_agent)
        if match:
            version = match.group(1)
    elif 'Firefox/' in user_agent:
        browser = 'Firefox'
        import re
        match = re.search(r'Firefox/(\d+\.\d+)', user_agent)
        if match:
            version = match.group(1)
    elif 'Chrome/' in user_agent:
        browser = 'Chrome'
        import re
        match = re.search(r'Chrome/(\d+\.\d+)', user_agent)
        if match:
            version = match.group(1)
    elif 'Safari/' in user_agent:
        browser = 'Safari'
        import re
        match = re.search(r'Version/(\d+\.\d+)', user_agent)
        if match:
            version = match.group(1)

    return {
        'browser': browser,
        'version': version,
        'os': os,
        'raw_user_agent': user_agent
    }

def is_ip_blocked(ip_address, username=None):
    """
    Check if IP address is temporarily blocked due to failed attempts.

    Args:
        ip_address: IP address to check
        username: Optional username to check for user-specific bypass

    Returns:
        bool: True if IP is blocked and user cannot bypass, False otherwise
    """
    # Check database for persistent blocks first
    try:
        blocked_ip = BlockedIP.objects.get(ip_address=ip_address)
        is_blocked = blocked_ip.is_active()

        # If IP is blocked, check if this specific user should be allowed to bypass
        # BUT only if it's not a permanent block (permanent blocks cannot be bypassed)
        if is_blocked and username and not blocked_ip.is_permanent:
            if should_allow_user_bypass_ip_block(ip_address, username):
                return False

        # Cache the result for 5 minutes
        cache_key = f"blocked_ip_{ip_address}"
        cache.set(cache_key, is_blocked, 300)
        return is_blocked
    except BlockedIP.DoesNotExist:
        # Check cache as fallback
        cache_key = f"blocked_ip_{ip_address}"
        cached_result = cache.get(cache_key, False)

        # If cached as blocked, check for user bypass
        # BUT only if it's not a permanent block
        if cached_result and username:
            if not is_permanent_block(ip_address) and should_allow_user_bypass_ip_block(ip_address, username):
                return False

        return cached_result

def is_permanent_block(ip_address):
    """
    Check if an IP address has a permanent block.

    Args:
        ip_address: IP address to check

    Returns:
        bool: True if IP has a permanent block, False otherwise
    """
    try:
        blocked_ip = BlockedIP.objects.get(ip_address=ip_address)
        return blocked_ip.is_permanent
    except BlockedIP.DoesNotExist:
        return False

def should_allow_user_bypass_ip_block(ip_address, username):
    """
    Check if a specific user should be allowed to bypass IP blocking.

    This allows legitimate users to login even if their IP is blocked due to
    other users' failed attempts from the same IP.

    IMPORTANT: Permanent blocks (is_permanent=True) can NEVER be bypassed.

    Args:
        ip_address: The blocked IP address
        username: Username attempting to login

    Returns:
        bool: True if user should bypass the block, False otherwise
    """
    # CRITICAL: Never allow bypass of permanent blocks
    if is_permanent_block(ip_address):
        security_logger.warning(
            f"Bypass attempt denied for permanent block: user '{username}' from {ip_address}",
            extra={'ip': ip_address, 'user': username}
        )
        return False

    time_window = timezone.now() - timedelta(minutes=15)

    # Check this user's recent failed attempts from this IP
    user_attempts = FailedLoginAttempt.objects.filter(
        ip_address=ip_address,
        username=username,
        timestamp__gte=time_window
    ).aggregate(total=models.Sum('attempt_count'))['total'] or 0

    # Allow bypass if this specific user has fewer than 3 failed attempts
    # This means the IP block was likely caused by other users
    if user_attempts < 3:
        security_logger.info(
            f"Allowing user '{username}' to bypass IP block for {ip_address} "
            f"(user has only {user_attempts} failed attempts)",
            extra={'ip': ip_address, 'user': username}
        )
        return True

    return False

def get_ip_block_remaining_time(ip_address):
    """Get remaining time in minutes for IP block"""
    cache_key = f"blocked_ip_{ip_address}"

    # Check if IP is blocked
    if not cache.get(cache_key, False):
        return 0

    # Get the expiration time if stored
    expiry_key = f"blocked_ip_expiry_{ip_address}"
    expiry_time = cache.get(expiry_key)

    if expiry_time:
        from django.utils import timezone
        remaining_seconds = (expiry_time - timezone.now()).total_seconds()
        return max(0, int(remaining_seconds / 60))  # Convert to minutes

    # Fallback: assume default duration if expiry not stored
    return 30  # Default assumption

def get_ip_block_error_message(ip_address):
    """Get user-friendly error message for blocked IP"""
    remaining_minutes = get_ip_block_remaining_time(ip_address)

    if remaining_minutes <= 0:
        return "Too many failed attempts. Please try again later."

    if remaining_minutes == 1:
        return "Too many failed login attempts. Your IP has been temporarily blocked. Please try again in 1 minute."
    elif remaining_minutes < 60:
        return f"Too many failed login attempts. Your IP has been temporarily blocked. Please try again in {remaining_minutes} minutes."
    else:
        hours = remaining_minutes // 60
        remaining_mins = remaining_minutes % 60
        if hours == 1 and remaining_mins == 0:
            return "Too many failed login attempts. Your IP has been temporarily blocked. Please try again in 1 hour."
        elif remaining_mins == 0:
            return f"Too many failed login attempts. Your IP has been temporarily blocked. Please try again in {hours} hours."
        else:
            return f"Too many failed login attempts. Your IP has been temporarily blocked. Please try again in {hours} hour(s) and {remaining_mins} minute(s)."

def block_ip(ip_address, duration_minutes=30, reason="Multiple failed login attempts", duration_hours=None):
    """Temporarily block an IP address"""
    from django.utils import timezone

    # Convert duration to minutes if hours provided
    if duration_hours is not None:
        duration_minutes = duration_hours * 60

    # Create or update BlockedIP record
    blocked_until = timezone.now() + timedelta(minutes=duration_minutes)

    blocked_ip, created = BlockedIP.objects.get_or_create(
        ip_address=ip_address,
        defaults={
            'reason': reason,
            'blocked_until': blocked_until,
        }
    )

    if not created:
        # Update existing block
        blocked_ip.reason = reason
        blocked_ip.blocked_until = blocked_until
        blocked_ip.blocked_at = timezone.now()
        blocked_ip.save()

    # Also update cache for immediate effect
    cache_key = f"blocked_ip_{ip_address}"
    expiry_key = f"blocked_ip_expiry_{ip_address}"

    cache.set(cache_key, True, duration_minutes * 60)
    cache.set(expiry_key, blocked_until, duration_minutes * 60)

    security_logger.warning(
        f"IP address {ip_address} blocked for {duration_minutes} minutes due to: {reason}",
        extra={'ip': ip_address, 'user': 'system'}
    )

    # Send security email notification
    try:
        send_ip_blocked_alert(ip_address, duration_minutes, reason)
    except Exception as e:
        # Graceful fallback if security notifications are not available
        security_logger.info(f"Security notifications not available for IP block alert: {str(e)}")

def check_failed_attempts(ip_address, username=None):
    """Check failed login attempts and block if necessary"""
    time_window = timezone.now() - timedelta(minutes=15)

    # Check IP-based attempts - sum attempt_count for this IP
    ip_attempts_qs = FailedLoginAttempt.objects.filter(
        ip_address=ip_address,
        timestamp__gte=time_window
    )
    ip_attempts = sum(attempt.attempt_count for attempt in ip_attempts_qs)

    # Check username-based attempts if provided - sum attempt_count for this username
    username_attempts = 0
    if username:
        username_attempts_qs = FailedLoginAttempt.objects.filter(
            username=username,
            timestamp__gte=time_window
        )
        username_attempts = sum(attempt.attempt_count for attempt in username_attempts_qs)

    # Send email alert for multiple failed attempts (before blocking)
    if ip_attempts >= 3 or username_attempts >= 2:
        try:
            send_multiple_failed_logins_alert(
                ip_address,
                username or 'Unknown',
                max(ip_attempts, username_attempts)
            )
        except Exception as e:
            # Graceful fallback if security notifications are not available
            security_logger.info(f"Security notifications not available for failed login alert: {str(e)}")

    # IMPROVED BLOCKING LOGIC:
    # 1. Block IP only if there are excessive attempts from multiple different users
    # 2. Or if a single user has too many attempts

    # Count unique users from this IP in the time window
    unique_users_from_ip = FailedLoginAttempt.objects.filter(
        ip_address=ip_address,
        timestamp__gte=time_window
    ).values('username').distinct().count()

    # Block IP if:
    # - More than 10 total attempts from IP AND attempts from 3+ different users (potential attack)
    # - OR single user has 8+ attempts (persistent brute force)
    should_block_ip = (
        (ip_attempts >= 10 and unique_users_from_ip >= 3) or
        (username_attempts >= 8)
    )

    if should_block_ip:
        # Use shorter block duration for less severe cases
        duration = 15 if username_attempts >= 8 else 30  # 15 min for user-specific, 30 min for IP-wide
        block_ip(ip_address, duration_minutes=duration)
        return True

    return False

# Alias for test compatibility
check_failed_login_attempts = check_failed_attempts

def record_failed_login(request, username):
    """Record a failed login attempt"""
    try:
        ip_address = get_client_ip(request)
        user_agent = get_user_agent(request)

        # Parse browser information for better logging
        browser_info = parse_browser_info(user_agent)

        # Log the failed attempt - increment if exists, create if new
        time_window = timezone.now() - timedelta(minutes=15)

        # Try to find existing attempt within time window
        existing_attempt = FailedLoginAttempt.objects.filter(
            ip_address=ip_address,
            username=username,
            timestamp__gte=time_window
        ).first()

        if existing_attempt:
            # Increment existing attempt count
            existing_attempt.attempt_count += 1
            existing_attempt.timestamp = timezone.now()  # Update timestamp
            existing_attempt.user_agent = user_agent  # Update user agent
            existing_attempt.save()
        else:
            # Create new attempt record
            FailedLoginAttempt.objects.create(
                ip_address=ip_address,
                username=username,
                user_agent=user_agent,
                attempt_count=1
            )

        # Create security event with enhanced browser details
        SecurityEvent.objects.create(
            event_type='LOGIN_FAILED',
            ip_address=ip_address,
            user_agent=user_agent,
            details={
                'username': username,
                'timestamp': timezone.now().isoformat(),
                'browser': browser_info['browser'],
                'browser_version': browser_info['version'],
                'operating_system': browser_info['os'],
            }
        )

        # Check if we should block this IP
        check_failed_attempts(ip_address, username)

        security_logger.warning(
            f"Failed login attempt for username '{username}' from {browser_info['browser']} {browser_info['version']} on {browser_info['os']}",
            extra={'ip': ip_address, 'user': username or 'unknown'}
        )
    except Exception as e:
        # Critical: Don't let security monitoring break authentication
        security_logger.error(
            f"Error in record_failed_login: {e}",
            extra={'error_type': 'security_monitoring_error'}
        )

def parse_user_agent(user_agent):
    """Parse user agent string to extract browser information - alias for parse_browser_info"""
    return parse_browser_info(user_agent)

def cleanup_old_attempts(days=7):
    """Remove failed login attempts older than specified days"""
    cutoff_date = timezone.now() - timedelta(days=days)

    deleted_attempts = FailedLoginAttempt.objects.filter(timestamp__lt=cutoff_date).delete()

    security_logger.info(
        f"Cleaned up {deleted_attempts[0]} old failed login attempts",
        extra={'ip': 'system', 'user': 'system'}
    )

    return deleted_attempts[0]

@receiver(user_login_failed)
def log_failed_login(sender, credentials, request, **kwargs):
    """Log failed login attempts and implement blocking"""
    try:
        ip_address = get_client_ip(request)
        user_agent = get_user_agent(request)
        username = credentials.get('username', '') if credentials else ''

        # Parse browser information for better logging
        browser_info = parse_browser_info(user_agent)

        # Log the failed attempt - increment if exists, create if new
        time_window = timezone.now() - timedelta(minutes=15)

        # Try to find existing attempt within time window
        existing_attempt = FailedLoginAttempt.objects.filter(
            ip_address=ip_address,
            username=username,
            timestamp__gte=time_window
        ).first()

        if existing_attempt:
            # Increment existing attempt count
            existing_attempt.attempt_count += 1
            existing_attempt.timestamp = timezone.now()  # Update timestamp
            existing_attempt.user_agent = user_agent  # Update user agent
            existing_attempt.save()
        else:
            # Create new attempt record
            FailedLoginAttempt.objects.create(
                ip_address=ip_address,
                username=username,
                user_agent=user_agent,
                attempt_count=1
            )

        # Create security event with enhanced browser details
        SecurityEvent.objects.create(
            event_type='LOGIN_FAILED',
            ip_address=ip_address,
            user_agent=user_agent,
            details={
                'username': username,
                'timestamp': timezone.now().isoformat(),
                'browser': browser_info['browser'],
                'browser_version': browser_info['version'],
                'operating_system': browser_info['os'],
            }
        )

        # Check if we should block this IP
        check_failed_attempts(ip_address, username)

        security_logger.warning(
            f"Failed login attempt for username '{username}' from {browser_info['browser']} {browser_info['version']} on {browser_info['os']}",
            extra={'ip': ip_address, 'user': username or 'unknown'}
        )
    except Exception as e:
        # Critical: Don't let security monitoring break authentication
        security_logger.error(
            f"Error in log_failed_login signal handler: {e}",
            extra={'error_type': 'security_monitoring_error'}
        )

@receiver(user_logged_in)
def log_successful_login(sender, request, user, **kwargs):
    """Log successful login attempts"""
    try:
        ip_address = get_client_ip(request)
        user_agent = get_user_agent(request)

        # Parse browser information for better logging
        browser_info = parse_browser_info(user_agent)

        # Create security event with enhanced browser details
        SecurityEvent.objects.create(
            user=user,
            event_type='LOGIN_SUCCESS',
            ip_address=ip_address,
            user_agent=user_agent,
            details={
                'username': user.email if user else 'unknown',
                'timestamp': timezone.now().isoformat(),
                'browser': browser_info['browser'],
                'browser_version': browser_info['version'],
                'operating_system': browser_info['os'],
            }
        )

        security_logger.info(
            f"Successful login for user '{user.email if user else 'unknown'}' from {browser_info['browser']} {browser_info['version']} on {browser_info['os']}",
            extra={'ip': ip_address, 'user': user.email if user else 'unknown'}
        )
    except Exception as e:
        # Don't let security monitoring break authentication
        security_logger.error(
            f"Error in log_successful_login signal handler: {e}",
            extra={'error_type': 'security_monitoring_error'}
        )

@receiver(user_logged_out)
def log_logout(sender, request, user, **kwargs):
    """Log logout events"""
    try:
        if user and request:
            ip_address = get_client_ip(request)
            user_agent = get_user_agent(request)

            # Create security event
            SecurityEvent.objects.create(
                user=user,
                event_type='LOGOUT',
                ip_address=ip_address,
                user_agent=user_agent,
                details={
                    'username': user.email if user else 'unknown',
                    'timestamp': timezone.now().isoformat(),
                }
            )

            security_logger.info(
                f"User logout: '{user.email if user else 'unknown'}'",
                extra={'ip': ip_address, 'user': user.email if user else 'unknown'}
            )
    except Exception as e:
        # Don't let security monitoring break logout functionality
        security_logger.error(
            f"Error in log_logout signal handler: {e}",
            extra={'error_type': 'security_monitoring_error'}
        )

def log_security_event(event_type, request, user=None, details=None):
    """Generic function to log security events"""
    try:
        ip_address = get_client_ip(request)
        user_agent = get_user_agent(request)

        SecurityEvent.objects.create(
            user=user,
            event_type=event_type,
            ip_address=ip_address,
            user_agent=user_agent,
            details=details or {}
        )

        security_logger.warning(
            f"Security event: {event_type}",
            extra={'ip': ip_address, 'user': user.email if user else 'anonymous'}
        )
    except Exception as e:
        # Don't let security monitoring break the main functionality
        security_logger.error(
            f"Error in log_security_event: {e}",
            extra={'error_type': 'security_monitoring_error', 'event_type': event_type}
        )

    # Send email notification for high/medium priority security events
    try:
        if security_notifier:
            # Map event types to notification types
            notification_event_map = {
                'SUSPICIOUS_ACTIVITY': 'SUSPICIOUS_ACTIVITY',
                'ACCOUNT_LOCKED': 'ACCOUNT_LOCKED',
                'ADMIN_ACTION': 'ADMIN_ACTION',
                'PASSWORD_CHANGE': 'UNUSUAL_LOGIN_PATTERN',  # Map to medium priority
            }

            notification_event = notification_event_map.get(event_type)
            if notification_event:
                event_data = details or {}
                event_data.update({
                    'user_agent': user_agent,
                    'event_timestamp': timezone.now().isoformat(),
                })

                security_notifier.send_security_alert(
                    notification_event,
                    event_data,
                    ip_address=ip_address,
                    user_email=user.email if user else None
                )
    except Exception as e:
        # Graceful fallback if security notifications are not available
        security_logger.info(f"Security notifications not available for event {event_type}: {str(e)}")

def log_data_access(request, user, resource, action='READ'):
    """Log data access for audit trails"""
    try:
        details = {
            'resource': resource,
            'action': action,
            'timestamp': timezone.now().isoformat(),
        }

        log_security_event('DATA_ACCESS', request, user, details)
    except Exception as e:
        security_logger.error(
            f"Error in log_data_access: {e}",
            extra={'error_type': 'security_monitoring_error'}
        )

def log_admin_action(request, user, action, target=None):
    """Log administrative actions"""
    try:
        details = {
            'action': action,
            'target': target,
            'timestamp': timezone.now().isoformat(),
        }

        log_security_event('ADMIN_ACTION', request, user, details)
    except Exception as e:
        security_logger.error(
            f"Error in log_admin_action: {e}",
            extra={'error_type': 'security_monitoring_error'}
        )

# Cleanup function to remove old security events
def cleanup_old_security_events(days=90):
    """Remove security events older than specified days"""
    cutoff_date = timezone.now() - timedelta(days=days)
    
    deleted_events = SecurityEvent.objects.filter(timestamp__lt=cutoff_date).delete()
    deleted_attempts = FailedLoginAttempt.objects.filter(timestamp__lt=cutoff_date).delete()
    
    security_logger.info(
        f"Cleaned up {deleted_events[0]} security events and {deleted_attempts[0]} failed login attempts",
        extra={'ip': 'system', 'user': 'system'}
    )
    
    return deleted_events[0] + deleted_attempts[0]
