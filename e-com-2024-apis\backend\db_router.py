"""
Database router for read/write splitting with replica support.

This router automatically directs read operations to read replicas
and write operations to the primary database, with fallback mechanisms
to ensure reliability.
"""

import random
from django.conf import settings


class DatabaseRouter:
    """
    A router to control all database operations on models for different
    databases with read/write splitting support.
    """

    def __init__(self):
        """Initialize the router with available databases"""
        self.read_databases = []
        self.write_database = 'default'
        
        # Check which databases are available
        if hasattr(settings, 'DATABASES'):
            for db_name in settings.DATABASES.keys():
                if db_name != 'default' and 'read' in db_name.lower():
                    self.read_databases.append(db_name)
        
        # If no read replicas are configured, use default for reads too
        if not self.read_databases:
            self.read_databases = ['default']

    def db_for_read(self, model, **hints):
        """Suggest the database to read from."""
        # For testing, always use default database
        if hasattr(settings, 'TESTING') and settings.TESTING:
            return 'default'
            
        # Check if we're in a transaction - if so, use the write database
        # to ensure read-after-write consistency
        if self._in_transaction():
            return self.write_database
            
        # Use read replica for read operations
        if self.read_databases and len(self.read_databases) > 1:
            # Randomly distribute reads across available replicas
            return random.choice(self.read_databases)
        elif self.read_databases:
            return self.read_databases[0]
        
        return self.write_database

    def db_for_write(self, model, **hints):
        """Suggest the database to write to."""
        # All writes go to the primary database
        return self.write_database

    def allow_relation(self, obj1, obj2, **hints):
        """Allow relations if models are in the same app."""
        db_set = {'default'}
        if self.read_databases:
            db_set.update(self.read_databases)
        
        if obj1._state.db in db_set and obj2._state.db in db_set:
            return True
        return None

    def allow_migrate(self, db, app_label, model_name=None, **hints):
        """Ensure that migrations only run on the primary database."""
        # Only allow migrations on the default (primary) database
        return db == 'default'

    def _in_transaction(self):
        """Check if we're currently in a database transaction."""
        try:
            from django.db import transaction
            return transaction.get_connection().in_atomic_block
        except Exception:
            # If we can't determine transaction state, be conservative
            return False


class ReplicaAwareManager:
    """
    Utility class to provide replica-aware database operations.
    
    This can be used as a mixin or standalone utility to ensure
    certain operations use specific databases.
    """
    
    @staticmethod
    def using_read_replica(queryset):
        """Force a queryset to use a read replica."""
        router = DatabaseRouter()
        if router.read_databases and router.read_databases[0] != 'default':
            return queryset.using(router.read_databases[0])
        return queryset
    
    @staticmethod
    def using_primary(queryset):
        """Force a queryset to use the primary database."""
        return queryset.using('default')
    
    @staticmethod
    def get_read_database():
        """Get the name of a read database."""
        router = DatabaseRouter()
        if router.read_databases:
            return random.choice(router.read_databases)
        return 'default'
    
    @staticmethod
    def get_write_database():
        """Get the name of the write database."""
        return 'default'


def with_read_replica(func):
    """
    Decorator to force a function to use read replicas for database operations.
    
    Usage:
        @with_read_replica
        def get_products():
            return Product.objects.all()
    """
    def wrapper(*args, **kwargs):
        # Store original database routing
        original_db = getattr(settings, '_FORCE_DB_READ', None)
        
        try:
            # Force read operations to use replica
            settings._FORCE_DB_READ = ReplicaAwareManager.get_read_database()
            return func(*args, **kwargs)
        finally:
            # Restore original setting
            if original_db is not None:
                settings._FORCE_DB_READ = original_db
            else:
                delattr(settings, '_FORCE_DB_READ')
    
    return wrapper


def with_primary_db(func):
    """
    Decorator to force a function to use the primary database.
    
    Usage:
        @with_primary_db
        def create_order():
            return Order.objects.create(...)
    """
    def wrapper(*args, **kwargs):
        # Store original database routing
        original_db = getattr(settings, '_FORCE_DB_WRITE', None)
        
        try:
            # Force operations to use primary database
            settings._FORCE_DB_WRITE = 'default'
            return func(*args, **kwargs)
        finally:
            # Restore original setting
            if original_db is not None:
                settings._FORCE_DB_WRITE = original_db
            else:
                delattr(settings, '_FORCE_DB_WRITE')
    
    return wrapper
