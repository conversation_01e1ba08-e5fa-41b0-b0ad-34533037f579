"""
Comprehensive tests for order views and services
Tests cart management, order creation, GST calculations, and invoice generation
"""

import pytest
import json
from decimal import Decimal
from unittest.mock import patch, MagicMock
from django.test import TestCase
from django.urls import reverse
from django.contrib.auth import get_user_model
from rest_framework.test import APITestCase, APIClient
from rest_framework import status
from rest_framework_simplejwt.tokens import RefreshToken

from orders.models import (
    Cart, CartItem, Order, OrderItem, ShippingMethod, Payment, Invoice
)
from orders.gst_service import gst_service
from orders.invoice_service import invoice_service
from products.models import Product, Category, Brand, GST
from users.models import Address

User = get_user_model()

class TestCartViews(APITestCase):
    """Test cart management API endpoints"""
    
    def setUp(self):
        """Set up test data"""
        self.user = User.objects.create_user(
            email='<EMAIL>',
            password='testpass123',
            name='Test User'
        )
        
        # Authenticate user
        refresh = RefreshToken.for_user(self.user)
        self.client.credentials(HTTP_AUTHORIZATION=f'Bearer {refresh.access_token}')
        
        # Create test products
        self.category = Category.objects.create(name='Electronics')
        self.brand = Brand.objects.create(name='Apple')
        self.gst = GST.objects.create(
            name='Standard GST',
            rate=Decimal('18.00')
        )
        
        self.product1 = Product.objects.create(
            name='iPhone 13',
            category=self.category,
            brand=self.brand,
            price=Decimal('999.00'),
            gst=self.gst,
            stock=50
        )
        
        self.product2 = Product.objects.create(
            name='iPad',
            category=self.category,
            brand=self.brand,
            price=Decimal('599.00'),
            gst=self.gst,
            stock=30
        )
    
    def test_add_to_cart(self):
        """Test adding product to cart"""
        url = reverse('cart-add-item')
        cart_data = {
            'product_id': self.product1.id,
            'quantity': 2
        }
        
        response = self.client.post(url, cart_data)
        
        self.assertEqual(response.status_code, status.HTTP_201_CREATED)
        self.assertTrue(CartItem.objects.filter(
            cart__user=self.user,
            product=self.product1,
            quantity=2
        ).exists())
    
    def test_get_cart(self):
        """Test getting cart contents"""
        # Add items to cart
        cart = Cart.objects.create(user=self.user)
        CartItem.objects.create(cart=cart, product=self.product1, quantity=2)
        CartItem.objects.create(cart=cart, product=self.product2, quantity=1)
        
        url = reverse('cart-detail')
        response = self.client.get(url)
        
        self.assertEqual(response.status_code, status.HTTP_200_OK)
        self.assertEqual(len(response.data['items']), 2)
        self.assertEqual(response.data['total_items'], 3)
        
        # Check total price calculation
        expected_total = (self.product1.price * 2) + (self.product2.price * 1)
        self.assertEqual(Decimal(response.data['subtotal']), expected_total)
    
    def test_update_cart_item_quantity(self):
        """Test updating cart item quantity"""
        cart = Cart.objects.create(user=self.user)
        cart_item = CartItem.objects.create(cart=cart, product=self.product1, quantity=1)
        
        url = reverse('cart-update-item')
        update_data = {
            'item_id': cart_item.id,
            'action': 'add'  # Add action to increase quantity
        }
        
        response = self.client.put(url, update_data)
        
        self.assertEqual(response.status_code, status.HTTP_200_OK)
        cart_item.refresh_from_db()
        self.assertEqual(cart_item.quantity, 2)  # Was 1, now 2 after add action
    
    def test_remove_from_cart(self):
        """Test removing product from cart"""
        cart = Cart.objects.create(user=self.user)
        cart_item = CartItem.objects.create(cart=cart, product=self.product1, quantity=2)
        
        url = reverse('cart-update-item')  # Use update with quantity 0 to remove
        remove_data = {'item_id': cart_item.id, 'action': 'delete'}  # Delete action removes item
        
        response = self.client.put(url, remove_data)  # Use PUT for update
        
        self.assertEqual(response.status_code, status.HTTP_200_OK)
        self.assertFalse(CartItem.objects.filter(id=cart_item.id).exists())
    
    def test_clear_cart(self):
        """Test clearing entire cart"""
        cart = Cart.objects.create(user=self.user)
        CartItem.objects.create(cart=cart, product=self.product1, quantity=2)
        CartItem.objects.create(cart=cart, product=self.product2, quantity=1)
        
        url = reverse('cart-clear')
        response = self.client.post(url)
        
        self.assertEqual(response.status_code, status.HTTP_200_OK)
        self.assertEqual(CartItem.objects.filter(cart=cart).count(), 0)
    
    def test_cart_unauthenticated(self):
        """Test cart access without authentication"""
        self.client.credentials()  # Remove authentication
        
        url = reverse('cart-detail')
        response = self.client.get(url)
        
        self.assertEqual(response.status_code, status.HTTP_401_UNAUTHORIZED)

class TestOrderViews(APITestCase):
    """Test order management API endpoints"""
    
    def setUp(self):
        """Set up test data"""
        self.user = User.objects.create_user(
            email='<EMAIL>',
            password='testpass123',
            name='Test User'
        )
        
        # Authenticate user
        refresh = RefreshToken.for_user(self.user)
        self.client.credentials(HTTP_AUTHORIZATION=f'Bearer {refresh.access_token}')
        
        # Create test data
        self.category = Category.objects.create(name='Electronics')
        self.brand = Brand.objects.create(name='Apple')
        self.gst = GST.objects.create(
            name='Standard GST',
            rate=Decimal('18.00')
        )
        
        self.product = Product.objects.create(
            name='iPhone 13',
            category=self.category,
            brand=self.brand,
            price=Decimal('999.00'),
            gst=self.gst,
            stock=50
        )
        
        self.shipping_address = Address.objects.create(
            user=self.user,
            street_address='123 Main St',
            city='Test City',
            state='Test State',
            postal_code='12345',
            country='Test Country'
        )
        
        self.billing_address = Address.objects.create(
            user=self.user,
            street_address='456 Oak St',
            city='Test City',
            state='Test State',
            postal_code='12345',
            country='Test Country'
        )
        
        self.shipping_method = ShippingMethod.objects.create(
            name='Standard Shipping',
            description='5-7 business days',
            price=Decimal('9.99'),
            estimated_days=7
        )
    
    def test_create_order(self):
        """Test creating a new order"""
        url = reverse('order-list')
        order_data = {
            'shipping_address_id': self.shipping_address.id,
            'billing_address_id': self.billing_address.id,
            'shipping_method_id': self.shipping_method.id,
            'items': [
                {
                    'product_id': self.product.id,
                    'quantity': 2
                }
            ],
            'payment_method': 'PHONEPE'
        }
        
        response = self.client.post(url, order_data, format='json')
        
        self.assertEqual(response.status_code, status.HTTP_201_CREATED)
        self.assertTrue(Order.objects.filter(user=self.user).exists())
        
        # Check order items
        order = Order.objects.get(user=self.user)
        self.assertEqual(order.items.count(), 1)
        self.assertEqual(order.items.first().quantity, 2)
    
    def test_list_user_orders(self):
        """Test listing user's orders"""
        # Create test order
        order = Order.objects.create(
            user=self.user,
            shipping_address=self.shipping_address,
            billing_address=self.billing_address,
            shipping_method=self.shipping_method,
            subtotal=Decimal('100.00'),
            shipping_cost=self.shipping_method.price,
            total=Decimal('109.99')
        )
        
        url = reverse('order-list')
        response = self.client.get(url)
        
        self.assertEqual(response.status_code, status.HTTP_200_OK)
        self.assertEqual(len(response.data['results']), 1)
        self.assertEqual(response.data['results'][0]['id'], str(order.id))
    
    def test_get_order_detail(self):
        """Test getting order details"""
        order = Order.objects.create(
            user=self.user,
            shipping_address=self.shipping_address,
            billing_address=self.billing_address,
            shipping_method=self.shipping_method,
            subtotal=Decimal('100.00'),
            shipping_cost=self.shipping_method.price,
            total=Decimal('109.99')
        )
        
        OrderItem.objects.create(
            order=order,
            product=self.product,
            quantity=1,
            unit_price=self.product.price,
            total_price=self.product.price,
            product_name=self.product.name
        )
        
        url = reverse('order-detail', kwargs={'pk': order.id})
        response = self.client.get(url)
        
        self.assertEqual(response.status_code, status.HTTP_200_OK)
        self.assertEqual(response.data['id'], str(order.id))
        self.assertEqual(len(response.data['items']), 1)
    
    def test_cancel_order(self):
        """Test cancelling an order"""
        order = Order.objects.create(
            user=self.user,
            shipping_address=self.shipping_address,
            billing_address=self.billing_address,
            shipping_method=self.shipping_method,
            subtotal=Decimal('100.00'),
            shipping_cost=self.shipping_method.price,
            total=Decimal('109.99'),
            status='PENDING'
        )
        
        url = reverse('order-cancel', kwargs={'pk': order.id})
        response = self.client.post(url)
        
        self.assertEqual(response.status_code, status.HTTP_200_OK)
        order.refresh_from_db()
        self.assertEqual(order.status, 'CANCELLED')
    
    def test_order_minimum_amount_validation(self):
        """Test order minimum amount validation"""
        # Create a low-priced product
        cheap_product = Product.objects.create(
            name='Cheap Item',
            category=self.category,
            brand=self.brand,
            price=Decimal('5.00'),
            stock=10
        )
        
        url = reverse('order-list')
        order_data = {
            'shipping_address_id': self.shipping_address.id,
            'billing_address_id': self.billing_address.id,
            'shipping_method_id': self.shipping_method.id,
            'items': [
                {
                    'product_id': cheap_product.id,
                    'quantity': 1
                }
            ],
            'payment_method': 'PHONEPE'
        }
        
        response = self.client.post(url, order_data, format='json')
        
        # Should fail due to minimum order amount
        self.assertEqual(response.status_code, status.HTTP_400_BAD_REQUEST)

class TestGSTService(TestCase):
    """Test GST calculation service"""
    
    def setUp(self):
        """Set up test data"""
        self.user = User.objects.create_user(
            email='<EMAIL>',
            password='testpass123',
            name='Test User'
        )

        self.gst_18 = GST.objects.create(
            name='Standard GST',
            rate=Decimal('18.00'),
            cgst_rate=Decimal('9.00'),
            sgst_rate=Decimal('9.00'),
            igst_rate=Decimal('18.00')
        )

        self.gst_12 = GST.objects.create(
            name='Reduced GST',
            rate=Decimal('12.00'),
            cgst_rate=Decimal('6.00'),
            sgst_rate=Decimal('6.00'),
            igst_rate=Decimal('12.00')
        )

        self.category = Category.objects.create(name='Electronics')
        self.brand = Brand.objects.create(name='Apple')

        self.product = Product.objects.create(
            name='iPhone 13',
            category=self.category,
            brand=self.brand,
            price=Decimal('999.00'),
            gst=self.gst_18,
            stock=50
        )
    
    def test_calculate_product_gst(self):
        """Test GST calculation for a product"""
        result = gst_service.calculate_product_gst(self.product, quantity=1)

        self.assertIn('base_amount', result)
        self.assertIn('gst_amount', result)
        self.assertIn('cgst_amount', result)
        self.assertIn('sgst_amount', result)
    
    def test_calculate_product_gst_from_mrp(self):
        """Test calculating GST from MRP (inclusive price)"""
        result = gst_service.calculate_product_gst_from_mrp(self.product, quantity=1)

        self.assertIn('base_amount', result)
        self.assertIn('gst_amount', result)
        self.assertIn('cgst_amount', result)
        self.assertIn('sgst_amount', result)
    
    def test_is_inter_state_transaction(self):
        """Test inter-state transaction detection"""
        # Create addresses in different states
        billing_address = Address.objects.create(
            user=self.user,
            street_address='123 Main St',
            city='Mumbai',
            state='Maharashtra',
            postal_code='400001',
            country='India'
        )

        shipping_address = Address.objects.create(
            user=self.user,
            street_address='456 Park Ave',
            city='Delhi',
            state='Delhi',
            postal_code='110001',
            country='India'
        )

        # Test inter-state transaction
        is_inter_state = gst_service.is_inter_state_transaction(billing_address, shipping_address)
        self.assertTrue(is_inter_state)

class TestInvoiceService(TestCase):
    """Test invoice generation service"""
    
    def setUp(self):
        """Set up test data"""
        self.user = User.objects.create_user(
            email='<EMAIL>',
            password='testpass123',
            name='Test User'
        )
        
        self.category = Category.objects.create(name='Electronics')
        self.brand = Brand.objects.create(name='Apple')
        self.gst = GST.objects.create(
            name='Standard GST',
            rate=Decimal('18.00')
        )
        
        self.product = Product.objects.create(
            name='iPhone 13',
            category=self.category,
            brand=self.brand,
            price=Decimal('999.00'),
            gst=self.gst,
            stock=50
        )
        
        self.shipping_address = Address.objects.create(
            user=self.user,
            street_address='123 Main St',
            city='Test City',
            state='Test State',
            postal_code='12345',
            country='Test Country'
        )
        
        self.shipping_method = ShippingMethod.objects.create(
            name='Standard Shipping',
            description='5-7 business days',
            price=Decimal('9.99'),
            estimated_days=7
        )
        
        self.order = Order.objects.create(
            user=self.user,
            shipping_address=self.shipping_address,
            billing_address=self.shipping_address,
            shipping_method=self.shipping_method,
            subtotal=Decimal('999.00'),
            shipping_cost=self.shipping_method.price,
            total=Decimal('1008.99'),
            status='PAID'  # Set status to PAID for invoice generation
        )
        
        OrderItem.objects.create(
            order=self.order,
            product=self.product,
            quantity=1,
            unit_price=self.product.price,
            total_price=self.product.price,
            product_name=self.product.name
        )
    
    def test_generate_invoice(self):
        """Test invoice generation"""
        invoice = invoice_service.generate_invoice(self.order)
        
        self.assertIsNotNone(invoice)
        self.assertEqual(invoice.order, self.order)
        self.assertIsNotNone(invoice.invoice_number)
        self.assertEqual(invoice.order, self.order)
    
    def test_invoice_number_generation(self):
        """Test unique invoice number generation"""
        invoice1 = invoice_service.generate_invoice(self.order)
        
        # Create another order
        order2 = Order.objects.create(
            user=self.user,
            shipping_address=self.shipping_address,
            billing_address=self.shipping_address,
            shipping_method=self.shipping_method,
            subtotal=Decimal('490.01'),
            shipping_cost=self.shipping_method.price,
            total=Decimal('500.00'),
            status='PAID'  # Set status to PAID for invoice generation
        )
        
        invoice2 = invoice_service.generate_invoice(order2)
        
        self.assertNotEqual(invoice1.invoice_number, invoice2.invoice_number)
    
    def test_generate_invoice_pdf(self):
        """Test PDF invoice generation"""
        invoice = invoice_service.generate_invoice(self.order)

        # Test that invoice was created successfully
        self.assertIsNotNone(invoice)
        self.assertEqual(invoice.order, self.order)
        self.assertIsNotNone(invoice.invoice_number)

class TestShippingMethodViews(APITestCase):
    """Test shipping method API endpoints"""
    
    def setUp(self):
        """Set up test data"""
        self.user = User.objects.create_user(
            email='<EMAIL>',
            password='testpass123',
            name='Test User'
        )

        self.shipping1 = ShippingMethod.objects.create(
            name='Standard Shipping',
            description='5-7 business days',
            price=Decimal('9.99'),
            estimated_days=7,
            is_active=True
        )
        
        self.shipping2 = ShippingMethod.objects.create(
            name='Express Shipping',
            description='1-2 business days',
            price=Decimal('19.99'),
            estimated_days=2,
            is_active=True
        )
        
        self.inactive_shipping = ShippingMethod.objects.create(
            name='Inactive Shipping',
            description='Inactive shipping method',
            price=Decimal('5.99'),
            estimated_days=5,
            is_active=False
        )
    
    def test_list_shipping_methods(self):
        """Test listing active shipping methods"""
        # Authenticate user
        refresh = RefreshToken.for_user(self.user)
        self.client.credentials(HTTP_AUTHORIZATION=f'Bearer {refresh.access_token}')

        url = reverse('shipping-method-list')
        response = self.client.get(url)
        
        self.assertEqual(response.status_code, status.HTTP_200_OK)
        self.assertEqual(len(response.data), 2)  # Only active methods
        
        method_names = [method['name'] for method in response.data]
        self.assertIn('Standard Shipping', method_names)
        self.assertIn('Express Shipping', method_names)
        self.assertNotIn('Inactive Shipping', method_names)
