import {
  Table,
  TableBody,
  Table<PERSON>ell,
  TableHead,
  TableHeader,
  TableRow,
} from "@/components/ui/table";

const data = [
  {
    id: 1,
    name: "Wireless Headphones",
    sales: 245,
    revenue: 24500,
    growth: "+12.5%",
  },
  {
    id: 2,
    name: "Smart Watch",
    sales: 190,
    revenue: 38000,
    growth: "****%",
  },
  {
    id: 3,
    name: "Running Shoes",
    sales: 175,
    revenue: 13125,
    growth: "****%",
  },
];

export const ProductPerformance = () => {
  return (
    <Table>
      <TableHeader>
        <TableRow>
          <TableHead>Product Name</TableHead>
          <TableHead className="text-right">Sales</TableHead>
          <TableHead className="text-right">Revenue</TableHead>
          <TableHead className="text-right">Growth</TableHead>
        </TableRow>
      </TableHeader>
      <TableBody>
        {data.map((item) => (
          <TableRow key={item.id}>
            <TableCell className="font-medium">{item.name}</TableCell>
            <TableCell className="text-right">{item.sales}</TableCell>
            <TableCell className="text-right">
              ${item.revenue.toLocaleString()}
            </TableCell>
            <TableCell className="text-right text-green-600">
              {item.growth}
            </TableCell>
          </TableRow>
        ))}
      </TableBody>
    </Table>
  );
};