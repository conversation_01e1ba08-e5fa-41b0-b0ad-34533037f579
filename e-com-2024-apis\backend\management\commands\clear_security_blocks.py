from django.core.management.base import BaseCommand
from django.core.cache import cache
from django.conf import settings
import re


class Command(BaseCommand):
    help = 'Clear all IP blocks and security-related cache entries'

    def add_arguments(self, parser):
        parser.add_argument(
            '--ip',
            type=str,
            help='Clear blocks for a specific IP address',
        )
        parser.add_argument(
            '--all',
            action='store_true',
            help='Clear all security blocks',
        )

    def handle(self, *args, **options):
        if options['ip']:
            self.clear_ip_blocks(options['ip'])
        elif options['all']:
            self.clear_all_blocks()
        else:
            self.stdout.write(
                self.style.WARNING('Please specify --ip <ip_address> or --all')
            )

    def clear_ip_blocks(self, ip_address):
        """Clear blocks for a specific IP address"""
        cache_keys = [
            f"blocked_ip_{ip_address}",
            f"failed_attempts_ip_{ip_address}",
            f"security_email_throttle_IP_BLOCKED_{ip_address}",
            f"security_email_throttle_MULTIPLE_FAILED_LOGINS_{ip_address}",
        ]
        
        for key in cache_keys:
            cache.delete(key)
            self.stdout.write(f"Cleared cache key: {key}")
        
        self.stdout.write(
            self.style.SUCCESS(f'Successfully cleared all blocks for IP: {ip_address}')
        )

    def clear_all_blocks(self):
        """Clear all security-related cache entries"""
        try:
            # Get all cache keys (this might not work with all cache backends)
            if hasattr(cache, '_cache') and hasattr(cache._cache, 'keys'):
                all_keys = cache._cache.keys()
                security_keys = [
                    key for key in all_keys 
                    if any(pattern in str(key) for pattern in [
                        'blocked_ip_',
                        'failed_attempts_',
                        'security_email_throttle_'
                    ])
                ]
                
                for key in security_keys:
                    cache.delete(key)
                    self.stdout.write(f"Cleared cache key: {key}")
                
                self.stdout.write(
                    self.style.SUCCESS(f'Successfully cleared {len(security_keys)} security cache entries')
                )
            else:
                # Fallback: Clear common patterns
                common_ips = ['127.0.0.1', 'localhost', '::1']
                for ip in common_ips:
                    self.clear_ip_blocks(ip)
                
                self.stdout.write(
                    self.style.WARNING('Cache backend does not support key listing. Cleared common IP patterns.')
                )
                
        except Exception as e:
            self.stdout.write(
                self.style.ERROR(f'Error clearing cache: {str(e)}')
            )

    def get_blocked_ips(self):
        """Get list of currently blocked IPs (if possible)"""
        try:
            if hasattr(cache, '_cache') and hasattr(cache._cache, 'keys'):
                all_keys = cache._cache.keys()
                blocked_keys = [
                    key for key in all_keys 
                    if 'blocked_ip_' in str(key)
                ]
                
                blocked_ips = []
                for key in blocked_keys:
                    if cache.get(key):
                        ip = str(key).replace('blocked_ip_', '')
                        blocked_ips.append(ip)
                
                return blocked_ips
            return []
        except Exception:
            return []
