"""
Comprehensive test suite for privacy features
Tests CSV export, consent management, and security features
"""

import json
import csv
import io
from django.test import TestCase, Client
from django.contrib.auth import get_user_model
from django.urls import reverse
from django.utils import timezone
from rest_framework.test import APITestCase
from rest_framework import status
from unittest.mock import patch, MagicMock

from users.models import UserConsent, DataDeletionRequest, Address, Wishlist
from users.privacy_views import DataExportView
from orders.models import Order, OrderItem
from products.models import Product, Category, Brand

User = get_user_model()

class PrivacyCSVExportTestCase(APITestCase):
    """Test CSV export functionality"""
    
    def setUp(self):
        self.user = User.objects.create_user(
            email='<EMAIL>',
            name='Test User',
            password='testpass123'
        )
        self.client.force_authenticate(user=self.user)
        
        # Create test data
        self.address = Address.objects.create(
            user=self.user,
            street_address='123 Test St',
            city='Test City',
            state='Test State',
            postal_code='12345',
            country='Test Country'
        )
        
        # Create test product and order
        self.category = Category.objects.create(name='Test Category')
        self.brand = Brand.objects.create(name='Test Brand')
        self.product = Product.objects.create(
            name='Test Product',
            price=29.99,
            category=self.category,
            brand=self.brand
        )
        
    def test_csv_export_structure(self):
        """Test that CSV export has correct structure"""
        url = reverse('data-export')  # Adjust URL name as needed
        response = self.client.get(url)
        
        self.assertEqual(response.status_code, status.HTTP_200_OK)
        self.assertEqual(response['Content-Type'], 'text/csv; charset=utf-8')
        self.assertIn('attachment', response['Content-Disposition'])
        
        # Parse CSV content
        csv_content = response.content.decode('utf-8')
        
        # Check required sections
        self.assertIn('=== USER DATA EXPORT ===', csv_content)
        self.assertIn('=== PERSONAL INFORMATION ===', csv_content)
        self.assertIn('=== ADDRESSES ===', csv_content)
        self.assertIn('=== ORDERS ===', csv_content)
        self.assertIn('=== ORDER ITEMS ===', csv_content)
        self.assertIn('=== WISHLIST ===', csv_content)
        self.assertIn('=== CONSENT HISTORY ===', csv_content)
        
        # Check user data is included
        self.assertIn('<EMAIL>', csv_content)
        self.assertIn('Test User', csv_content)
    
    def test_csv_export_with_orders_and_items(self):
        """Test CSV export includes order items correctly"""
        # Create order with items
        order = Order.objects.create(
            user=self.user,
            shipping_address=self.address,
            billing_address=self.address,
            status='DELIVERED',
            total=59.98
        )
        
        OrderItem.objects.create(
            order=order,
            product=self.product,
            product_name='Test Product',
            quantity=2,
            unit_price=29.99,
            total_price=59.98
        )
        
        url = reverse('data-export')
        response = self.client.get(url)
        csv_content = response.content.decode('utf-8')
        
        # Check order items section
        self.assertIn('=== ORDER ITEMS ===', csv_content)
        self.assertIn('Test Product', csv_content)
        self.assertIn('29.99', csv_content)
        self.assertIn('59.98', csv_content)
    
    def test_csv_export_performance_with_large_dataset(self):
        """Test CSV export performance with large dataset"""
        # Create multiple orders and items
        for i in range(10):
            order = Order.objects.create(
                user=self.user,
                shipping_address=self.address,
                billing_address=self.address,
                status='DELIVERED',
                total=100.00
            )
            
            # Create multiple items per order
            for j in range(5):
                OrderItem.objects.create(
                    order=order,
                    product=self.product,
                    product_name=f'Product {i}-{j}',
                    quantity=1,
                    unit_price=20.00,
                    total_price=20.00
                )
        
        url = reverse('data-export')
        
        # Measure response time
        import time
        start_time = time.time()
        response = self.client.get(url)
        end_time = time.time()
        
        self.assertEqual(response.status_code, status.HTTP_200_OK)
        self.assertLess(end_time - start_time, 5.0)  # Should complete within 5 seconds
    
    def test_csv_export_unauthorized(self):
        """Test CSV export requires authentication"""
        self.client.force_authenticate(user=None)
        url = reverse('data-export')
        response = self.client.get(url)
        
        self.assertEqual(response.status_code, status.HTTP_401_UNAUTHORIZED)

class ConsentManagementTestCase(APITestCase):
    """Test consent management functionality"""
    
    def setUp(self):
        self.user = User.objects.create_user(
            email='<EMAIL>',
            name='Test User',
            password='testpass123'
        )
        self.client.force_authenticate(user=self.user)
    
    def test_consent_creation_and_update(self):
        """Test creating and updating consent"""
        url = reverse('consent-management')  # Adjust URL name as needed
        
        # Create consent
        consent_data = {
            'consents': {
                'MARKETING': True,
                'ANALYTICS': False,
                'COOKIES': True
            }
        }
        
        response = self.client.post(url, consent_data, format='json')
        self.assertEqual(response.status_code, status.HTTP_200_OK)
        
        # Check consent was created
        marketing_consent = UserConsent.objects.get(
            user=self.user,
            consent_type='MARKETING'
        )
        self.assertTrue(marketing_consent.granted)
        
        analytics_consent = UserConsent.objects.get(
            user=self.user,
            consent_type='ANALYTICS'
        )
        self.assertFalse(analytics_consent.granted)
    
    def test_consent_withdrawal(self):
        """Test consent withdrawal"""
        # Create initial consent
        UserConsent.objects.create(
            user=self.user,
            consent_type='MARKETING',
            granted=True,
            granted_at=timezone.now()
        )
        
        url = reverse('consent-management')
        
        # Withdraw consent
        consent_data = {
            'consents': {
                'MARKETING': False
            }
        }
        
        response = self.client.post(url, consent_data, format='json')
        self.assertEqual(response.status_code, status.HTTP_200_OK)
        
        # Check consent was withdrawn
        consent = UserConsent.objects.get(
            user=self.user,
            consent_type='MARKETING'
        )
        self.assertFalse(consent.granted)
        self.assertIsNotNone(consent.withdrawn_at)

class DataDeletionTestCase(APITestCase):
    """Test data deletion functionality"""
    
    def setUp(self):
        self.user = User.objects.create_user(
            email='<EMAIL>',
            name='Test User',
            password='testpass123'
        )
        self.client.force_authenticate(user=self.user)
    
    def test_deletion_request_creation(self):
        """Test creating deletion request"""
        url = reverse('data-deletion')  # Adjust URL name as needed
        
        deletion_data = {
            'reason': 'No longer need the account'
        }
        
        response = self.client.post(url, deletion_data, format='json')
        self.assertEqual(response.status_code, status.HTTP_200_OK)
        
        # Check deletion request was created
        deletion_request = DataDeletionRequest.objects.get(user=self.user)
        self.assertEqual(deletion_request.reason, 'No longer need the account')
        self.assertEqual(deletion_request.status, 'PENDING')
    
    def test_duplicate_deletion_request_prevention(self):
        """Test that duplicate deletion requests are prevented"""
        # Create initial deletion request
        DataDeletionRequest.objects.create(
            user=self.user,
            reason='First request',
            status='PENDING'
        )
        
        url = reverse('data-deletion')
        
        deletion_data = {
            'reason': 'Second request'
        }
        
        response = self.client.post(url, deletion_data, format='json')
        self.assertEqual(response.status_code, status.HTTP_400_BAD_REQUEST)

class PrivacySecurityTestCase(TestCase):
    """Test privacy security features"""
    
    def setUp(self):
        self.user = User.objects.create_user(
            email='<EMAIL>',
            name='Test User',
            password='testpass123'
        )
    
    @patch('users.privacy_security_audit.SecurityEvent')
    def test_security_audit_detection(self, mock_security_event):
        """Test security audit detects suspicious patterns"""
        from users.privacy_security_audit import PrivacySecurityAuditor
        
        # Mock excessive export events
        mock_security_event.objects.filter.return_value.values.return_value.annotate.return_value = [
            {'user': self.user.id, 'export_count': 15}
        ]
        
        auditor = PrivacySecurityAuditor()
        findings = auditor._audit_data_exports()
        
        self.assertEqual(len(findings), 1)
        self.assertEqual(findings[0]['type'], 'EXCESSIVE_DATA_EXPORTS')
        self.assertEqual(findings[0]['severity'], 'HIGH')
    
    def test_consent_pattern_detection(self):
        """Test detection of unusual consent patterns"""
        from users.privacy_security_audit import PrivacySecurityAuditor
        
        # Create multiple consent changes
        for i in range(12):
            UserConsent.objects.create(
                user=self.user,
                consent_type='MARKETING',
                granted=i % 2 == 0,
                granted_at=timezone.now()
            )
        
        auditor = PrivacySecurityAuditor()
        findings = auditor._audit_consent_patterns()
        
        # Should detect excessive consent changes
        excessive_changes = [f for f in findings if f['type'] == 'EXCESSIVE_CONSENT_CHANGES']
        self.assertEqual(len(excessive_changes), 1)

class PrivacyIntegrationTestCase(APITestCase):
    """Integration tests for privacy features"""
    
    def setUp(self):
        self.user = User.objects.create_user(
            email='<EMAIL>',
            name='Test User',
            password='testpass123'
        )
        self.client.force_authenticate(user=self.user)
    
    def test_full_privacy_workflow(self):
        """Test complete privacy workflow"""
        # 1. Set consent preferences
        consent_url = reverse('consent-management')
        consent_data = {
            'consents': {
                'MARKETING': True,
                'ANALYTICS': True,
                'COOKIES': True
            }
        }
        
        response = self.client.post(consent_url, consent_data, format='json')
        self.assertEqual(response.status_code, status.HTTP_200_OK)
        
        # 2. Export data
        export_url = reverse('data-export')
        response = self.client.get(export_url)
        self.assertEqual(response.status_code, status.HTTP_200_OK)
        
        csv_content = response.content.decode('utf-8')
        self.assertIn('MARKETING', csv_content)
        self.assertIn('True', csv_content)
        
        # 3. Request data deletion
        deletion_url = reverse('data-deletion')
        deletion_data = {
            'reason': 'Privacy concerns'
        }
        
        response = self.client.post(deletion_url, deletion_data, format='json')
        self.assertEqual(response.status_code, status.HTTP_200_OK)
        
        # Verify deletion request was created
        deletion_request = DataDeletionRequest.objects.get(user=self.user)
        self.assertEqual(deletion_request.reason, 'Privacy concerns')
