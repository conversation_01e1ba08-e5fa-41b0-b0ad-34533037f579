#!/usr/bin/env python3
"""
Advanced Image Quality Cleaner for Haier Products
This script provides comprehensive image quality filtering with configurable options
"""

import json
import os
import shutil
import glob
import argparse
from datetime import datetime
from PIL import Image, ImageFilter
import numpy as np
from pathlib import Path

class AdvancedImageCleaner:
    def __init__(self, config_file='image_quality_config.json'):
        self.config = self.load_config(config_file)
        self.stats = {
            'total_images_checked': 0,
            'low_quality_removed': 0,
            'corrupted_removed': 0,
            'blurry_removed': 0,
            'small_size_removed': 0,
            'large_size_removed': 0,
            'bad_aspect_removed': 0,
            'products_updated': 0,
            'directories_cleaned': 0,
            'total_space_freed': 0
        }
        
    def load_config(self, config_file):
        """Load configuration from JSON file"""
        try:
            with open(config_file, 'r') as f:
                config = json.load(f)
            print(f"Loaded configuration from {config_file}")
            return config
        except FileNotFoundError:
            print(f"Config file {config_file} not found. Using default settings.")
            return self.get_default_config()
        except Exception as e:
            print(f"Error loading config: {e}. Using default settings.")
            return self.get_default_config()
    
    def get_default_config(self):
        """Return default configuration"""
        return {
            "quality_thresholds": {
                "min_width": 800,
                "min_height": 600,
                "min_file_size": 50000,
                "max_file_size": 10000000,
                "min_aspect_ratio": 0.5,
                "max_aspect_ratio": 2.0
            },
            "directories_to_clean": [
                "haier_product_images_downloaded",
                "haier_product_images_hq"
            ],
            "file_paths": {
                "json_file": "output/json/haier_kitchen_products.json",
                "backup_dir": "backups"
            },
            "image_extensions": ["*.jpg", "*.jpeg", "*.png", "*.gif", "*.bmp", "*.webp"],
            "blur_detection": {
                "enabled": True,
                "threshold": 1000
            },
            "cleanup_options": {
                "remove_empty_directories": True,
                "create_backup": True,
                "verbose_logging": True
            }
        }
    
    def analyze_image_quality(self, image_path):
        """Comprehensive image quality analysis"""
        try:
            file_size = os.path.getsize(image_path)
            self.stats['total_space_freed'] += file_size
            
            quality_issues = []
            
            # File size check
            min_size = self.config['quality_thresholds']['min_file_size']
            max_size = self.config['quality_thresholds']['max_file_size']
            
            if file_size < min_size:
                quality_issues.append(f"File too small: {file_size/1000:.1f}KB < {min_size/1000:.1f}KB")
                self.stats['small_size_removed'] += 1
            elif file_size > max_size:
                quality_issues.append(f"File too large: {file_size/1000000:.1f}MB > {max_size/1000000:.1f}MB")
                self.stats['large_size_removed'] += 1
            
            # Image analysis
            with Image.open(image_path) as img:
                width, height = img.size
                
                # Dimension check
                min_width = self.config['quality_thresholds']['min_width']
                min_height = self.config['quality_thresholds']['min_height']
                
                if width < min_width or height < min_height:
                    quality_issues.append(f"Dimensions too small: {width}x{height} < {min_width}x{min_height}")
                
                # Aspect ratio check
                aspect_ratio = width / height
                min_aspect = self.config['quality_thresholds']['min_aspect_ratio']
                max_aspect = self.config['quality_thresholds']['max_aspect_ratio']
                
                if aspect_ratio < min_aspect or aspect_ratio > max_aspect:
                    quality_issues.append(f"Bad aspect ratio: {aspect_ratio:.2f} (range: {min_aspect}-{max_aspect})")
                    self.stats['bad_aspect_removed'] += 1
                
                # Blur detection
                if self.config['blur_detection']['enabled']:
                    if self.is_image_blurry(img):
                        quality_issues.append("Image is blurry or low quality")
                        self.stats['blurry_removed'] += 1
                
                # Additional quality checks
                if self.has_quality_issues(img):
                    quality_issues.append("Additional quality issues detected")
            
            return len(quality_issues) > 0, quality_issues
            
        except Exception as e:
            self.stats['corrupted_removed'] += 1
            return True, [f"Corrupted or unreadable: {str(e)}"]
    
    def is_image_blurry(self, img):
        """Advanced blur detection using Laplacian variance"""
        try:
            # Convert to grayscale
            gray = img.convert('L')
            
            # Resize for faster processing
            gray = gray.resize((200, 200))
            
            # Convert to numpy array
            img_array = np.array(gray)
            
            # Calculate Laplacian variance
            laplacian_var = cv2.Laplacian(img_array, cv2.CV_64F).var()
            
            threshold = self.config['blur_detection']['threshold']
            return laplacian_var < threshold
            
        except Exception:
            # Fallback to simple variance method
            try:
                gray = img.convert('L').resize((100, 100))
                pixels = list(gray.getdata())
                mean = sum(pixels) / len(pixels)
                variance = sum((p - mean) ** 2 for p in pixels) / len(pixels)
                return variance < 1000
            except Exception:
                return False
    
    def has_quality_issues(self, img):
        """Check for additional quality issues"""
        try:
            # Check if image is mostly one color (likely placeholder/error image)
            colors = img.getcolors(maxcolors=256*256*256)
            if colors and len(colors) < 10:  # Very few colors
                return True
            
            # Check if image is too dark or too bright
            if img.mode in ['RGB', 'RGBA']:
                # Convert to grayscale for brightness analysis
                gray = img.convert('L')
                pixels = list(gray.getdata())
                avg_brightness = sum(pixels) / len(pixels)
                
                # Too dark or too bright
                if avg_brightness < 20 or avg_brightness > 235:
                    return True
            
            return False
            
        except Exception:
            return False
    
    def clean_directory(self, directory_path, dry_run=False):
        """Clean low-quality images from a directory"""
        if not os.path.exists(directory_path):
            print(f"Directory not found: {directory_path}")
            return []
        
        print(f"\n{'[DRY RUN] ' if dry_run else ''}Cleaning directory: {directory_path}")
        
        removed_images = []
        image_files = []
        
        # Find all image files
        for ext in self.config['image_extensions']:
            pattern = os.path.join(directory_path, '**', ext)
            image_files.extend(glob.glob(pattern, recursive=True))
        
        print(f"Found {len(image_files)} images to analyze")
        
        for image_path in image_files:
            self.stats['total_images_checked'] += 1
            
            has_issues, issues = self.analyze_image_quality(image_path)
            
            if has_issues:
                file_size = os.path.getsize(image_path)
                
                if self.config['cleanup_options']['verbose_logging']:
                    print(f"{'[WOULD REMOVE] ' if dry_run else 'Removing: '}{os.path.basename(image_path)}")
                    for issue in issues:
                        print(f"  - {issue}")
                
                if not dry_run:
                    try:
                        os.remove(image_path)
                        removed_images.append(image_path)
                        self.stats['total_space_freed'] += file_size
                        self.stats['low_quality_removed'] += 1
                    except Exception as e:
                        print(f"Error removing {image_path}: {e}")
                else:
                    removed_images.append(image_path)
                    self.stats['low_quality_removed'] += 1
        
        # Clean empty directories
        if not dry_run and self.config['cleanup_options']['remove_empty_directories']:
            self.remove_empty_directories(directory_path)
        
        return removed_images
    
    def remove_empty_directories(self, root_dir):
        """Remove empty directories recursively"""
        for dirpath, dirnames, filenames in os.walk(root_dir, topdown=False):
            if not filenames and not dirnames and dirpath != root_dir:
                try:
                    os.rmdir(dirpath)
                    print(f"Removed empty directory: {dirpath}")
                    self.stats['directories_cleaned'] += 1
                except Exception as e:
                    if self.config['cleanup_options']['verbose_logging']:
                        print(f"Error removing directory {dirpath}: {e}")
    
    def update_json_file(self, removed_images, dry_run=False):
        """Update JSON file to remove references to deleted images"""
        json_file = self.config['file_paths']['json_file']
        
        if not os.path.exists(json_file):
            print(f"JSON file not found: {json_file}")
            return
        
        print(f"\n{'[DRY RUN] ' if dry_run else ''}Updating JSON file: {json_file}")
        
        if not dry_run and self.config['cleanup_options']['create_backup']:
            # Create backup
            backup_dir = self.config['file_paths'].get('backup_dir', 'backups')
            os.makedirs(backup_dir, exist_ok=True)
            
            timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
            backup_path = os.path.join(backup_dir, f"haier_products_backup_{timestamp}.json")
            shutil.copy2(json_file, backup_path)
            print(f"Created backup: {backup_path}")
        
        # Load JSON
        try:
            with open(json_file, 'r', encoding='utf-8') as f:
                products = json.load(f)
        except Exception as e:
            print(f"Error loading JSON: {e}")
            return
        
        # Convert removed images to relative paths
        removed_relative_paths = set()
        for img_path in removed_images:
            relative_path = os.path.relpath(img_path).replace('\\', '/')
            removed_relative_paths.add(relative_path)
        
        # Update products
        updated_products = []
        for product in products:
            updated_product = product.copy()
            product_updated = False
            
            # Update regular images
            if 'images' in updated_product:
                original_count = len(updated_product['images'])
                updated_product['images'] = [
                    img for img in updated_product['images'] 
                    if img not in removed_relative_paths
                ]
                
                if len(updated_product['images']) != original_count:
                    product_updated = True
                    if self.config['cleanup_options']['verbose_logging']:
                        print(f"Updated images for {updated_product.get('model', 'Unknown')}: "
                              f"{original_count} -> {len(updated_product['images'])}")
            
            # Update HQ images
            if 'hq_images' in updated_product:
                original_count = len(updated_product['hq_images'])
                updated_product['hq_images'] = [
                    img for img in updated_product['hq_images'] 
                    if img not in removed_relative_paths
                ]
                
                if len(updated_product['hq_images']) != original_count:
                    product_updated = True
                    if self.config['cleanup_options']['verbose_logging']:
                        print(f"Updated HQ images for {updated_product.get('model', 'Unknown')}: "
                              f"{original_count} -> {len(updated_product['hq_images'])}")
            
            if product_updated:
                self.stats['products_updated'] += 1
            
            updated_products.append(updated_product)
        
        # Save updated JSON
        if not dry_run:
            try:
                with open(json_file, 'w', encoding='utf-8') as f:
                    json.dump(updated_products, f, indent=2)
                print(f"Successfully updated JSON file")
            except Exception as e:
                print(f"Error saving JSON: {e}")
    
    def print_statistics(self):
        """Print detailed cleaning statistics"""
        print("\n" + "="*60)
        print("DETAILED CLEANING STATISTICS")
        print("="*60)
        print(f"Total images analyzed: {self.stats['total_images_checked']}")
        print(f"Images removed by category:")
        print(f"  - Too small file size: {self.stats['small_size_removed']}")
        print(f"  - Too large file size: {self.stats['large_size_removed']}")
        print(f"  - Bad aspect ratio: {self.stats['bad_aspect_removed']}")
        print(f"  - Blurry/low quality: {self.stats['blurry_removed']}")
        print(f"  - Corrupted files: {self.stats['corrupted_removed']}")
        print(f"Total images removed: {self.stats['low_quality_removed']}")
        print(f"Products updated in JSON: {self.stats['products_updated']}")
        print(f"Empty directories cleaned: {self.stats['directories_cleaned']}")
        print(f"Total space freed: {self.stats['total_space_freed']/1000000:.2f} MB")
    
    def run_cleanup(self, dry_run=False, specific_dir=None):
        """Run the complete cleanup process"""
        print("Advanced Haier Image Quality Cleaner")
        print("="*40)
        
        # Print configuration
        thresholds = self.config['quality_thresholds']
        print(f"Quality thresholds:")
        print(f"  Minimum dimensions: {thresholds['min_width']}x{thresholds['min_height']}")
        print(f"  File size range: {thresholds['min_file_size']/1000:.0f}KB - {thresholds['max_file_size']/1000000:.1f}MB")
        print(f"  Aspect ratio range: {thresholds['min_aspect_ratio']} - {thresholds['max_aspect_ratio']}")
        print(f"  Blur detection: {'Enabled' if self.config['blur_detection']['enabled'] else 'Disabled'}")
        
        if dry_run:
            print("\n*** DRY RUN MODE - No files will be deleted ***")
        
        all_removed_images = []
        
        # Determine directories to clean
        directories = [specific_dir] if specific_dir else self.config['directories_to_clean']
        
        # Clean each directory
        for directory in directories:
            if os.path.exists(directory):
                removed_images = self.clean_directory(directory, dry_run)
                all_removed_images.extend(removed_images)
            else:
                print(f"Directory not found: {directory}")
        
        # Update JSON file
        if all_removed_images:
            self.update_json_file(all_removed_images, dry_run)
        
        # Print statistics
        self.print_statistics()


def main():
    """Main function with command line arguments"""
    parser = argparse.ArgumentParser(description='Advanced Image Quality Cleaner for Haier Products')
    parser.add_argument('--dry-run', action='store_true', help='Run in dry-run mode (no files deleted)')
    parser.add_argument('--config', default='image_quality_config.json', help='Configuration file path')
    parser.add_argument('--directory', help='Specific directory to clean (optional)')
    parser.add_argument('--interactive', action='store_true', help='Run in interactive mode')
    
    args = parser.parse_args()
    
    if args.interactive:
        # Interactive mode
        print("Advanced Haier Image Quality Cleaner")
        print("="*40)
        
        dry_run = input("Run in dry-run mode first? (y/n): ").lower().strip() == 'y'
        config_file = input(f"Configuration file [{args.config}]: ").strip() or args.config
        
        cleaner = AdvancedImageCleaner(config_file)
        cleaner.run_cleanup(dry_run=dry_run, specific_dir=args.directory)
        
        if dry_run:
            proceed = input("\nProceed with actual cleanup? (y/n): ").lower().strip()
            if proceed == 'y':
                cleaner = AdvancedImageCleaner(config_file)  # Reset stats
                cleaner.run_cleanup(dry_run=False, specific_dir=args.directory)
    else:
        # Command line mode
        cleaner = AdvancedImageCleaner(args.config)
        cleaner.run_cleanup(dry_run=args.dry_run, specific_dir=args.directory)


if __name__ == "__main__":
    main()
