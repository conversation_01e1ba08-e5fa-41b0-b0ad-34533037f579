"use client";

import { useState } from "react";
import MainHOF from "../../layout/MainHOF";
import { <PERSON>, CardContent, CardHeader, CardTitle } from "../../components/ui/card";
import { Button } from "../../components/ui/button";
import { Input } from "../../components/ui/input";
import { Label } from "../../components/ui/label";
import GSTBreakdown from "../../components/ui/GSTBreakdown";
import { OrderSummary } from "../../components/cart/OrderSummary";

const TestGSTPage = () => {
  const [testData, setTestData] = useState({
    subtotal: 50000,
    gstAmount: 9000,
    cgstAmount: 4500,
    sgstAmount: 4500,
    igstAmount: 0,
    billingState: "Telangana",
    shippingState: "Telangana",
  });

  const [isInterState, setIsInterState] = useState(false);

  const handleStateChange = (field: string, value: string) => {
    const newData = { ...testData, [field]: value };
    setTestData(newData);
    
    // Update inter-state status
    const inter = newData.billingState.toLowerCase().trim() !== newData.shippingState.toLowerCase().trim();
    setIsInterState(inter);
    
    // Update GST amounts based on transaction type
    if (inter) {
      // Inter-state: Only IGST
      newData.igstAmount = newData.gstAmount;
      newData.cgstAmount = 0;
      newData.sgstAmount = 0;
    } else {
      // Intra-state: CGST + SGST
      newData.igstAmount = 0;
      newData.cgstAmount = newData.gstAmount / 2;
      newData.sgstAmount = newData.gstAmount / 2;
    }
    setTestData(newData);
  };

  const mockGstBreakdown = {
    total_gst_amount: testData.gstAmount,
    total_cgst_amount: testData.cgstAmount,
    total_sgst_amount: testData.sgstAmount,
    total_igst_amount: testData.igstAmount,
    is_inter_state: isInterState,
    item_details: [
      {
        product: { name: "Test Laptop" },
        quantity: 1,
        gst_rate: 18,
        gst_amount: 9000,
        cgst_amount: isInterState ? 0 : 4500,
        sgst_amount: isInterState ? 0 : 4500,
        igst_amount: isInterState ? 9000 : 0,
        hsn_code: "8471"
      }
    ]
  };

  const presetScenarios = [
    {
      name: "Intra-state (Telangana → Telangana)",
      billing: "Telangana",
      shipping: "Telangana",
      description: "Same state transaction - CGST + SGST applicable"
    },
    {
      name: "Inter-state (Telangana → Maharashtra)",
      billing: "Telangana",
      shipping: "Maharashtra",
      description: "Different state transaction - IGST applicable"
    },
    {
      name: "Inter-state (Karnataka → Delhi)",
      billing: "Karnataka",
      shipping: "Delhi",
      description: "Different state transaction - IGST applicable"
    },
    {
      name: "Intra-state (Maharashtra → Maharashtra)",
      billing: "Maharashtra",
      shipping: "Maharashtra",
      description: "Same state transaction - CGST + SGST applicable"
    }
  ];

  return (
    <MainHOF>
      <div className="container mx-auto px-4 py-8">
        <div className="max-w-6xl mx-auto">
          <h1 className="text-3xl font-bold mb-8 text-center">GST Rules Testing Page</h1>
          
          <div className="grid grid-cols-1 lg:grid-cols-2 gap-8">
            {/* Controls */}
            <div className="space-y-6">
              <Card>
                <CardHeader>
                  <CardTitle>Test Configuration</CardTitle>
                </CardHeader>
                <CardContent className="space-y-4">
                  <div>
                    <Label htmlFor="subtotal">Subtotal (Base Amount)</Label>
                    <Input
                      id="subtotal"
                      type="number"
                      value={testData.subtotal}
                      onChange={(e) => setTestData({...testData, subtotal: Number(e.target.value)})}
                    />
                  </div>
                  
                  <div>
                    <Label htmlFor="gstAmount">Total GST Amount</Label>
                    <Input
                      id="gstAmount"
                      type="number"
                      value={testData.gstAmount}
                      onChange={(e) => setTestData({...testData, gstAmount: Number(e.target.value)})}
                    />
                  </div>
                  
                  <div>
                    <Label htmlFor="billingState">Billing State</Label>
                    <Input
                      id="billingState"
                      value={testData.billingState}
                      onChange={(e) => handleStateChange('billingState', e.target.value)}
                    />
                  </div>
                  
                  <div>
                    <Label htmlFor="shippingState">Shipping State</Label>
                    <Input
                      id="shippingState"
                      value={testData.shippingState}
                      onChange={(e) => handleStateChange('shippingState', e.target.value)}
                    />
                  </div>
                  
                  <div className="pt-4 border-t">
                    <div className="text-sm font-medium mb-2">Transaction Type:</div>
                    <div className={`text-lg font-bold ${isInterState ? 'text-blue-600' : 'text-green-600'}`}>
                      {isInterState ? '🔄 Inter-state Transaction' : '🏠 Intra-state Transaction'}
                    </div>
                    <div className="text-sm text-gray-600 mt-1">
                      {isInterState 
                        ? `${testData.billingState} → ${testData.shippingState}` 
                        : `Within ${testData.billingState}`}
                    </div>
                  </div>
                </CardContent>
              </Card>

              <Card>
                <CardHeader>
                  <CardTitle>Preset Scenarios</CardTitle>
                </CardHeader>
                <CardContent className="space-y-2">
                  {presetScenarios.map((scenario, index) => (
                    <Button
                      key={index}
                      variant="outline"
                      className="w-full text-left justify-start h-auto p-3"
                      onClick={() => {
                        handleStateChange('billingState', scenario.billing);
                        handleStateChange('shippingState', scenario.shipping);
                      }}
                    >
                      <div>
                        <div className="font-medium">{scenario.name}</div>
                        <div className="text-sm text-gray-600">{scenario.description}</div>
                      </div>
                    </Button>
                  ))}
                </CardContent>
              </Card>
            </div>

            {/* Results */}
            <div className="space-y-6">
              <Card>
                <CardHeader>
                  <CardTitle>GST Breakdown Component</CardTitle>
                </CardHeader>
                <CardContent>
                  <GSTBreakdown
                    gstAmount={testData.gstAmount}
                    cgstAmount={testData.cgstAmount}
                    sgstAmount={testData.sgstAmount}
                    igstAmount={testData.igstAmount}
                    showDetails={true}
                    gstBreakdown={mockGstBreakdown}
                    billingState={testData.billingState}
                    shippingState={testData.shippingState}
                  />
                </CardContent>
              </Card>

              <Card>
                <CardHeader>
                  <CardTitle>Order Summary Component</CardTitle>
                </CardHeader>
                <CardContent>
                  <OrderSummary
                    subtotal={testData.subtotal}
                    shippingCost={100}
                    discount={0}
                    total={testData.subtotal + testData.gstAmount + 100}
                    gstAmount={testData.gstAmount}
                    cgstAmount={testData.cgstAmount}
                    sgstAmount={testData.sgstAmount}
                    igstAmount={testData.igstAmount}
                    showGstBreakdown={true}
                    gstBreakdown={mockGstBreakdown}
                    billingAddress={{ state: testData.billingState }}
                    shippingAddress={{ state: testData.shippingState }}
                  />
                </CardContent>
              </Card>

              <Card>
                <CardHeader>
                  <CardTitle>GST Calculation Summary</CardTitle>
                </CardHeader>
                <CardContent className="space-y-3">
                  <div className="grid grid-cols-2 gap-4 text-sm">
                    <div>
                      <div className="font-medium">Subtotal (Base):</div>
                      <div>₹{testData.subtotal.toFixed(2)}</div>
                    </div>
                    <div>
                      <div className="font-medium">Total GST:</div>
                      <div>₹{testData.gstAmount.toFixed(2)}</div>
                    </div>
                    <div>
                      <div className="font-medium">CGST:</div>
                      <div className={testData.cgstAmount > 0 ? 'text-green-600' : 'text-gray-400'}>
                        ₹{testData.cgstAmount.toFixed(2)}
                      </div>
                    </div>
                    <div>
                      <div className="font-medium">SGST:</div>
                      <div className={testData.sgstAmount > 0 ? 'text-green-600' : 'text-gray-400'}>
                        ₹{testData.sgstAmount.toFixed(2)}
                      </div>
                    </div>
                    <div>
                      <div className="font-medium">IGST:</div>
                      <div className={testData.igstAmount > 0 ? 'text-blue-600' : 'text-gray-400'}>
                        ₹{testData.igstAmount.toFixed(2)}
                      </div>
                    </div>
                    <div>
                      <div className="font-medium">Total Amount:</div>
                      <div className="font-bold">₹{(testData.subtotal + testData.gstAmount + 100).toFixed(2)}</div>
                    </div>
                  </div>
                  
                  <div className="pt-3 border-t text-xs text-gray-600">
                    <div className="font-medium mb-1">Indian GST Rules Applied:</div>
                    {isInterState ? (
                      <div>✅ Inter-state: Only IGST charged at full rate</div>
                    ) : (
                      <div>✅ Intra-state: CGST + SGST charged (split equally)</div>
                    )}
                  </div>
                </CardContent>
              </Card>
            </div>
          </div>
        </div>
      </div>
    </MainHOF>
  );
};

export default TestGSTPage;
