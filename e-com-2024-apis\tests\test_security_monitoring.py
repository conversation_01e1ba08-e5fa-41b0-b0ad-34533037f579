"""
Comprehensive tests for security monitoring system
Tests failed login tracking, IP blocking, security events, and browser detection
"""

import pytest
from unittest.mock import patch, MagicMock
from django.test import TestCase, RequestFactory
from django.contrib.auth import get_user_model
from django.utils import timezone
from datetime import timedelta

User = get_user_model()

class TestSecurityModels(TestCase):
    """Test security monitoring models"""
    
    def setUp(self):
        """Set up test data"""
        self.factory = RequestFactory()
    
    def test_failed_login_attempt_model(self):
        """Test FailedLoginAttempt model"""
        from backend.security_monitoring import FailedLoginAttempt
        
        attempt = FailedLoginAttempt.objects.create(
            ip_address="***********",
            username="testuser",
            user_agent="Mozilla/5.0",
            attempt_count=1
        )
        
        self.assertEqual(attempt.ip_address, "***********")
        self.assertEqual(attempt.username, "testuser")
        self.assertEqual(attempt.attempt_count, 1)
        self.assertIsNotNone(attempt.timestamp)
    
    def test_security_event_model(self):
        """Test SecurityEvent model"""
        from backend.security_monitoring import SecurityEvent
        
        event = SecurityEvent.objects.create(
            event_type="LOGIN_FAILED",
            ip_address="***********",
            user_agent="Mozilla/5.0",
            details={"username": "testuser"}
        )
        
        self.assertEqual(event.event_type, "LOGIN_FAILED")
        self.assertEqual(event.ip_address, "***********")
        self.assertEqual(event.details["username"], "testuser")
        self.assertIsNotNone(event.timestamp)
    
    def test_blocked_ip_model(self):
        """Test BlockedIP model"""
        from backend.security_monitoring import BlockedIP
        
        blocked = BlockedIP.objects.create(
            ip_address="***********",
            reason="Too many failed login attempts",
            blocked_until=timezone.now() + timedelta(hours=1)
        )
        
        self.assertEqual(blocked.ip_address, "***********")
        self.assertTrue(blocked.is_active())
        
        # Test expired block
        blocked.blocked_until = timezone.now() - timedelta(hours=1)
        blocked.save()
        self.assertFalse(blocked.is_active())

class TestSecurityFunctions(TestCase):
    """Test security monitoring functions"""
    
    def setUp(self):
        """Set up test data"""
        self.factory = RequestFactory()
    
    def test_get_client_ip(self):
        """Test client IP extraction"""
        from backend.security_monitoring import get_client_ip
        
        # Test with X-Forwarded-For header
        request = self.factory.get('/')
        request.META['HTTP_X_FORWARDED_FOR'] = '***********, ********'
        ip = get_client_ip(request)
        self.assertEqual(ip, '***********')
        
        # Test with X-Real-IP header
        request = self.factory.get('/')
        request.META['HTTP_X_REAL_IP'] = '***********'
        ip = get_client_ip(request)
        self.assertEqual(ip, '***********')
        
        # Test with REMOTE_ADDR
        request = self.factory.get('/')
        request.META['REMOTE_ADDR'] = '***********'
        ip = get_client_ip(request)
        self.assertEqual(ip, '***********')
    
    def test_get_user_agent(self):
        """Test user agent extraction"""
        from backend.security_monitoring import get_user_agent
        
        request = self.factory.get('/')
        request.META['HTTP_USER_AGENT'] = 'Mozilla/5.0 (Windows NT 10.0; Win64; x64)'
        user_agent = get_user_agent(request)
        self.assertEqual(user_agent, 'Mozilla/5.0 (Windows NT 10.0; Win64; x64)')
        
        # Test without user agent
        request = self.factory.get('/')
        user_agent = get_user_agent(request)
        self.assertEqual(user_agent, 'Unknown')
    
    def test_parse_user_agent(self):
        """Test user agent parsing"""
        from backend.security_monitoring import parse_user_agent
        
        # Test Chrome user agent
        chrome_ua = 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36'
        result = parse_user_agent(chrome_ua)
        self.assertEqual(result['browser'], 'Chrome')
        self.assertIn('91.0', result['version'])
        self.assertEqual(result['os'], 'Windows')
        
        # Test Firefox user agent
        firefox_ua = 'Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:89.0) Gecko/20100101 Firefox/89.0'
        result = parse_user_agent(firefox_ua)
        self.assertEqual(result['browser'], 'Firefox')
        
        # Test Safari user agent
        safari_ua = 'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/14.1.1 Safari/605.1.15'
        result = parse_user_agent(safari_ua)
        self.assertEqual(result['browser'], 'Safari')
        self.assertEqual(result['os'], 'macOS')
        
        # Test Edge user agent
        edge_ua = 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36 Edg/91.0.864.59'
        result = parse_user_agent(edge_ua)
        self.assertEqual(result['browser'], 'Edge')
        
        # Test unknown user agent
        unknown_ua = 'CustomBot/1.0'
        result = parse_user_agent(unknown_ua)
        self.assertEqual(result['browser'], 'Unknown')
    
    def test_is_ip_blocked(self):
        """Test IP blocking check"""
        from backend.security_monitoring import is_ip_blocked, BlockedIP
        
        # Test non-blocked IP
        self.assertFalse(is_ip_blocked('***********'))
        
        # Create a blocked IP
        BlockedIP.objects.create(
            ip_address='***********',
            reason='Test block',
            blocked_until=timezone.now() + timedelta(hours=1)
        )
        
        # Test blocked IP
        self.assertTrue(is_ip_blocked('***********'))
        
        # Test expired block
        BlockedIP.objects.filter(ip_address='***********').update(
            blocked_until=timezone.now() - timedelta(hours=1)
        )
        self.assertFalse(is_ip_blocked('***********'))
    
    def test_block_ip(self):
        """Test IP blocking functionality"""
        from backend.security_monitoring import block_ip, is_ip_blocked
        
        # Block an IP
        block_ip('***********', reason='Test block', duration_hours=2)
        
        # Verify it's blocked
        self.assertTrue(is_ip_blocked('***********'))
    
    def test_record_failed_login(self):
        """Test failed login recording"""
        from backend.security_monitoring import record_failed_login, FailedLoginAttempt
        
        request = self.factory.post('/login/')
        request.META['REMOTE_ADDR'] = '***********'
        request.META['HTTP_USER_AGENT'] = 'Mozilla/5.0'
        
        # Record first failed login
        record_failed_login(request, 'testuser')
        
        # Check that attempt was recorded
        attempt = FailedLoginAttempt.objects.get(ip_address='***********', username='testuser')
        self.assertEqual(attempt.attempt_count, 1)
        
        # Record another failed login
        record_failed_login(request, 'testuser')
        
        # Check that attempt count increased
        attempt.refresh_from_db()
        self.assertEqual(attempt.attempt_count, 2)
    
    @patch('backend.security_monitoring.send_multiple_failed_logins_alert')
    def test_check_failed_login_attempts_with_alert(self, mock_alert):
        """Test failed login attempt checking with email alerts"""
        from backend.security_monitoring import check_failed_login_attempts, FailedLoginAttempt
        
        # Create multiple failed attempts
        FailedLoginAttempt.objects.create(
            ip_address='***********',
            username='testuser',
            attempt_count=4
        )
        
        # Check should trigger alert but not block yet
        result = check_failed_login_attempts('***********', 'testuser')
        self.assertFalse(result)  # Not blocked yet
        mock_alert.assert_called_once()
    
    def test_check_failed_login_attempts_blocking(self):
        """Test failed login attempt blocking with new logic"""
        from backend.security_monitoring import check_failed_login_attempts, FailedLoginAttempt

        # Test 1: Single user with 8+ attempts should be blocked
        FailedLoginAttempt.objects.create(
            ip_address='***********',
            username='testuser',
            attempt_count=8
        )

        # Check should block the IP for this user
        result = check_failed_login_attempts('***********', 'testuser')
        self.assertTrue(result)  # Should be blocked

        # Test 2: Multiple users with 10+ total attempts from 3+ users should trigger IP block
        FailedLoginAttempt.objects.create(
            ip_address='***********',
            username='user1',
            attempt_count=4
        )
        FailedLoginAttempt.objects.create(
            ip_address='***********',
            username='user2',
            attempt_count=4
        )
        FailedLoginAttempt.objects.create(
            ip_address='***********',
            username='user3',
            attempt_count=3
        )

        # Check should block the IP due to distributed attack
        result = check_failed_login_attempts('***********', 'user1')
        self.assertTrue(result)  # Should be blocked
    
    def test_log_security_event(self):
        """Test security event logging"""
        from backend.security_monitoring import log_security_event, SecurityEvent
        
        user = User.objects.create_user(
            email='<EMAIL>',
            password='testpass123',
            name='Test User'
        )
        
        request = self.factory.post('/login/')
        request.META['REMOTE_ADDR'] = '***********'
        request.META['HTTP_USER_AGENT'] = 'Mozilla/5.0'
        
        # Log a security event
        log_security_event('LOGIN_SUCCESS', request, user=user, details={'test': 'data'})
        
        # Check that event was logged
        event = SecurityEvent.objects.get(event_type='LOGIN_SUCCESS')
        self.assertEqual(event.user, user)
        self.assertEqual(event.ip_address, '***********')
        self.assertEqual(event.details['test'], 'data')
    
    def test_cleanup_old_attempts(self):
        """Test cleanup of old failed login attempts"""
        from backend.security_monitoring import cleanup_old_attempts, FailedLoginAttempt
        
        # Create old attempt
        old_attempt = FailedLoginAttempt.objects.create(
            ip_address='***********',
            username='olduser',
            attempt_count=1
        )
        old_attempt.timestamp = timezone.now() - timedelta(days=8)
        old_attempt.save()
        
        # Create recent attempt
        FailedLoginAttempt.objects.create(
            ip_address='***********',
            username='newuser',
            attempt_count=1
        )
        
        # Cleanup old attempts
        cleanup_old_attempts()
        
        # Check that old attempt was deleted
        self.assertFalse(FailedLoginAttempt.objects.filter(username='olduser').exists())
        self.assertTrue(FailedLoginAttempt.objects.filter(username='newuser').exists())
