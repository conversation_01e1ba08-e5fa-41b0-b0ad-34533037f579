import { useEffect, useCallback, useState } from 'react';
import { debounce } from '@/utils/debounce';

export function useInfiniteScroll(threshold = 0.8) {
  const [page, setPage] = useState(1);
  const [loading, setLoading] = useState(false);
  const [hasMore, setHasMore] = useState(true);

  const handleScroll = useCallback(
    debounce(() => {
      if (!hasMore || loading) return;

      const scrollHeight = document.documentElement.scrollHeight;
      const scrollTop = document.documentElement.scrollTop;
      const clientHeight = document.documentElement.clientHeight;
      
      if ((scrollTop + clientHeight) / scrollHeight >= threshold) {
        setPage((prev) => prev + 1);
      }
    }, 150),
    [hasMore, loading]
  );

  useEffect(() => {
    window.addEventListener('scroll', handleScroll);
    return () => window.removeEventListener('scroll', handleScroll);
  }, [handleScroll]);

  return {
    page,
    loading,
    setLoading,
    hasMore,
    setHasMore,
  };
}