#!/usr/bin/env python
"""
Performance testing script for database replica optimization.

This script demonstrates the performance benefits of using database replicas
for read operations while maintaining all existing functionality.

Usage:
    python test_replica_performance.py
"""

import os
import sys
import time
import statistics
from concurrent.futures import ThreadPoolExecutor, as_completed

# Setup Django
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'backend.settings')
import django
django.setup()

from django.db import connection
from django.test.utils import override_settings
from django.contrib.auth import get_user_model
from products.models import Product
from orders.models import Order
from backend.db_utils import ReplicaAwareQuerySet
from products.views import get_optimized_product_queryset

User = get_user_model()


class PerformanceTester:
    """Test performance of database operations with and without replicas"""
    
    def __init__(self):
        self.results = {}
    
    def time_operation(self, operation_name, operation_func, iterations=10):
        """Time a database operation multiple times and return statistics"""
        print(f"\n🔄 Testing {operation_name} ({iterations} iterations)...")
        
        times = []
        query_counts = []
        
        for i in range(iterations):
            # Clear query log
            connection.queries_log.clear()
            
            # Time the operation
            start_time = time.time()
            result = operation_func()
            end_time = time.time()
            
            # Record metrics
            execution_time = (end_time - start_time) * 1000  # Convert to milliseconds
            query_count = len(connection.queries)
            
            times.append(execution_time)
            query_counts.append(query_count)
            
            print(f"  Iteration {i+1}: {execution_time:.2f}ms, {query_count} queries")
        
        # Calculate statistics
        avg_time = statistics.mean(times)
        min_time = min(times)
        max_time = max(times)
        avg_queries = statistics.mean(query_counts)
        
        result_data = {
            'avg_time_ms': avg_time,
            'min_time_ms': min_time,
            'max_time_ms': max_time,
            'avg_queries': avg_queries,
            'total_iterations': iterations
        }
        
        self.results[operation_name] = result_data
        
        print(f"  📊 Average: {avg_time:.2f}ms, Queries: {avg_queries:.1f}")
        return result_data
    
    def test_product_list_operations(self):
        """Test product listing operations"""
        print("\n" + "="*60)
        print("🛍️  PRODUCT LIST OPERATIONS")
        print("="*60)
        
        # Test 1: Standard product query
        def standard_product_query():
            return list(Product.objects.filter(is_active=True)[:20])
        
        self.time_operation("Standard Product Query", standard_product_query)
        
        # Test 2: Optimized query without replica
        def optimized_query_no_replica():
            queryset = get_optimized_product_queryset(use_replica=False)
            return list(queryset.filter(is_active=True)[:20])
        
        self.time_operation("Optimized Query (No Replica)", optimized_query_no_replica)
        
        # Test 3: Optimized query with replica support
        def optimized_query_with_replica():
            queryset = get_optimized_product_queryset(use_replica=True)
            return list(queryset.filter(is_active=True)[:20])
        
        self.time_operation("Optimized Query (With Replica)", optimized_query_with_replica)
        
        # Test 4: Replica-aware utility
        def replica_aware_query():
            queryset = ReplicaAwareQuerySet.get_optimized_product_queryset(use_replica=True)
            return list(queryset.filter(is_active=True)[:20])
        
        self.time_operation("Replica-Aware Query", replica_aware_query)
    
    def test_user_operations(self):
        """Test user-related operations"""
        print("\n" + "="*60)
        print("👥 USER OPERATIONS")
        print("="*60)
        
        # Test 1: Standard user query
        def standard_user_query():
            return list(User.objects.all()[:10])
        
        self.time_operation("Standard User Query", standard_user_query)
        
        # Test 2: Optimized user query
        def optimized_user_query():
            queryset = ReplicaAwareQuerySet.get_optimized_user_queryset(use_replica=True)
            return list(queryset[:10])
        
        self.time_operation("Optimized User Query", optimized_user_query)
    
    def test_concurrent_operations(self):
        """Test concurrent read operations"""
        print("\n" + "="*60)
        print("🔄 CONCURRENT OPERATIONS")
        print("="*60)
        
        def concurrent_product_reads(num_threads=5, operations_per_thread=3):
            """Test concurrent product reads"""
            def read_products():
                queryset = ReplicaAwareQuerySet.get_optimized_product_queryset(use_replica=True)
                return list(queryset.filter(is_active=True)[:10])
            
            start_time = time.time()
            
            with ThreadPoolExecutor(max_workers=num_threads) as executor:
                futures = []
                for _ in range(num_threads):
                    for _ in range(operations_per_thread):
                        future = executor.submit(read_products)
                        futures.append(future)
                
                # Wait for all operations to complete
                results = []
                for future in as_completed(futures):
                    try:
                        result = future.result()
                        results.append(len(result))
                    except Exception as e:
                        print(f"Error in concurrent operation: {e}")
            
            end_time = time.time()
            total_time = (end_time - start_time) * 1000
            total_operations = num_threads * operations_per_thread
            
            print(f"  📊 {total_operations} concurrent operations completed in {total_time:.2f}ms")
            print(f"  📊 Average time per operation: {total_time/total_operations:.2f}ms")
            
            return {
                'total_time_ms': total_time,
                'total_operations': total_operations,
                'avg_time_per_operation_ms': total_time / total_operations
            }
        
        self.results['Concurrent Operations'] = concurrent_product_reads()
    
    def print_summary(self):
        """Print performance summary"""
        print("\n" + "="*60)
        print("📈 PERFORMANCE SUMMARY")
        print("="*60)
        
        for operation, data in self.results.items():
            if 'avg_time_ms' in data:
                print(f"\n{operation}:")
                print(f"  Average Time: {data['avg_time_ms']:.2f}ms")
                print(f"  Range: {data['min_time_ms']:.2f}ms - {data['max_time_ms']:.2f}ms")
                print(f"  Average Queries: {data['avg_queries']:.1f}")
            else:
                print(f"\n{operation}:")
                for key, value in data.items():
                    print(f"  {key.replace('_', ' ').title()}: {value}")
        
        # Calculate improvements
        if 'Standard Product Query' in self.results and 'Optimized Query (With Replica)' in self.results:
            standard_time = self.results['Standard Product Query']['avg_time_ms']
            optimized_time = self.results['Optimized Query (With Replica)']['avg_time_ms']
            improvement = ((standard_time - optimized_time) / standard_time) * 100
            
            print(f"\n🚀 OPTIMIZATION BENEFITS:")
            print(f"  Performance Improvement: {improvement:.1f}%")
            print(f"  Time Reduction: {standard_time - optimized_time:.2f}ms")
    
    def run_all_tests(self):
        """Run all performance tests"""
        print("🚀 Starting Database Replica Performance Tests")
        print("="*60)
        
        # Enable query logging
        with override_settings(DEBUG=True):
            self.test_product_list_operations()
            self.test_user_operations()
            self.test_concurrent_operations()
            self.print_summary()
        
        print("\n✅ Performance testing completed!")


def main():
    """Main function to run performance tests"""
    print("Database Replica Performance Testing")
    print("====================================")
    
    # Check if we have data to test with
    product_count = Product.objects.count()
    user_count = User.objects.count()
    
    print(f"📊 Test Data Available:")
    print(f"  Products: {product_count}")
    print(f"  Users: {user_count}")
    
    if product_count == 0:
        print("\n⚠️  Warning: No products found. Consider adding test data for more meaningful results.")
    
    if user_count == 0:
        print("\n⚠️  Warning: No users found. Consider adding test data for more meaningful results.")
    
    # Run performance tests
    tester = PerformanceTester()
    tester.run_all_tests()
    
    print("\n💡 Tips for Production:")
    print("  1. Set up read replicas using the DATABASE_REPLICA_OPTIMIZATION_GUIDE.md")
    print("  2. Monitor replica health with: python manage.py check_replica_health")
    print("  3. Use load balancers to distribute replica traffic")
    print("  4. Monitor query performance in production")


if __name__ == "__main__":
    main()
