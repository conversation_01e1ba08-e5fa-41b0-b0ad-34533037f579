// const isProd = true;
// process.env.NODE_ENV === "production";

const isProd = process.env.NEXT_PUBLIC_IS_PROD

const devUrl = "http://localhost:8000";
// const prodUrl = "https://api-e-com.TRIUMPH ENTERPRISES.in";
const prodUrl = process.env.NEXT_PUBLIC_API_URL;

console.log("process.env.IS_PROD", process.env.NEXT_PUBLIC_IS_PROD)
console.log("process.env.API_BACKEND_URL", process.env.NEXT_PUBLIC_API_URL)

export const MAIN_URL = isProd ? prodUrl : devUrl;

console.log("MAIN_URL", MAIN_URL)

const version = "/api/v1/";

export const PRODUCTS = `${version}products/`;

export const CATEGORIES = `${version}products/categories/`;

export const BRANDS = `${version}products/brands/`;

export const USER_SIGNUP = `${version}users/`;

export const USER_LOGIN = `${version}users/login/`;

export const USER_LOGOUT = `${version}users/logout/`;

export const USER_SOCIAL_LOGIN = `${version}users/social/login/`;

export const USER_CART = `${version}orders/cart/`;

export const ADD_TO_CART = `${version}orders/cart/add-item/`;

export const UPDATE_CART = `${version}orders/cart/update-item/`;

export const TOKEN_REFFRESH = `${version}users/token/refresh/`;

export const USER_DETAIL = `${version}users/detail/`;

export const USER_REFFRESH_BLACKLIST = `${version}users/token/blacklist/`;

export const USER_ADDRESS = `${version}users/addresses/`;

export const ORDERS = `${version}orders/`;

export const ADD_TO_WISHLIST = `${version}users/wishlist/`;

export const FUTURED_PRODUCTS = `${version}products/feature/products/`;

export const RANDOM_PRODUCTS = `${version}products/feature/products/?random=true`;

export const REMOVE_FROM_WISHLIST = `${version}users/remove/wishlist/`;

export const CATEGORIZE_PRODUCTS = (slug: any) => {
  return `${version}products/categories/${slug}/products/`;
};

export const SHIPPING_METHODS = `${version}orders/shipping-methods/`;

export const PROMOCODE_APPLY = `${version}promotions/apply/code/`;

export const PROFILE_UPDATE = `${version}users/profile/update/`;

export const GET_PROMO_CODE = `${version}promotions/get/single/promotion/`;

// Payment URLs
export const PAYMENTS_PHONEPE_INITIATE = `${version}payments/phonepe/initiate`;

// Contact Form URL
export const CONTACT_FORM = `${version}users/contact/`;

// Password Reset URLs
export const FORGOT_PASSWORD = `${version}users/forgot-password/`;
export const RESET_PASSWORD = `${version}users/reset-password/`;

// Rapidshyp Shipping Integration URLs
export const SHIPPING_CALCULATE_RATES = `${version}shipping/calculate-rates/`;
export const SHIPPING_VALIDATE_PINCODE = `${version}shipping/validate-pincode/`;
export const SHIPPING_TRACK_ORDER = (orderId: string) => `${version}shipping/track/${orderId}/`;
export const SHIPPING_BULK_TRACK = `${version}shipping/bulk-track/`;
export const SHIPPING_STATUS = `${version}shipping/status/`;
export const SHIPPING_HEALTH = `${version}shipping/health/`;


