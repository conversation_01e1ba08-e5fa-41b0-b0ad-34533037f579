# Generated manually for Rapidshyp shipping integration

from django.db import migrations, models
import django.db.models.deletion
import django.core.validators
import uuid


class Migration(migrations.Migration):

    initial = True

    dependencies = [
        ('orders', '0003_add_rapidshyp_fields'),
    ]

    operations = [
        migrations.CreateModel(
            name='RapidshypConfiguration',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('store_name', models.CharField(default='DEFAULT', max_length=100)),
                ('pickup_address_name', models.CharField(max_length=100)),
                ('default_pickup_pincode', models.Char<PERSON>ield(max_length=6)),
                ('contact_name', models.CharField(max_length=100)),
                ('contact_phone', models.Char<PERSON>ield(max_length=15)),
                ('contact_email', models.EmailField(max_length=254)),
                ('address_line_1', models.Char<PERSON><PERSON>(max_length=200)),
                ('address_line_2', models.Char<PERSON>ield(blank=True, max_length=200)),
                ('is_active', models.<PERSON><PERSON>an<PERSON>ield(default=True)),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('updated_at', models.DateTimeField(auto_now=True)),
            ],
            options={
                'verbose_name': 'Rapidshyp Configuration',
                'verbose_name_plural': 'Rapidshyp Configurations',
            },
        ),
        migrations.CreateModel(
            name='RapidshypShipment',
            fields=[
                ('id', models.UUIDField(default=uuid.uuid4, editable=False, primary_key=True, serialize=False)),
                ('rapidshyp_order_id', models.CharField(max_length=100, unique=True)),
                ('shipment_id', models.CharField(blank=True, max_length=100)),
                ('awb_number', models.CharField(blank=True, max_length=100)),
                ('courier_code', models.CharField(max_length=20)),
                ('courier_name', models.CharField(max_length=100)),
                ('parent_courier_name', models.CharField(blank=True, max_length=100)),
                ('current_status', models.CharField(choices=[('SCB', 'Shipment Booked'), ('PSH', 'Pickup Scheduled'), ('OFP', 'Out for Pickup'), ('PUE', 'Pick up Exception'), ('PCN', 'Pickup Cancelled'), ('PUC', 'Pickup Completed'), ('SPD', 'Shipped/Dispatched'), ('INT', 'In Transit'), ('RAD', 'Reached at Destination'), ('DED', 'Delivery Delayed'), ('OFD', 'Out for Delivery'), ('DEL', 'Delivered'), ('UND', 'Undelivered'), ('RTO_REQ', 'RTO Requested'), ('RTO', 'RTO Confirmed'), ('RTO_INT', 'RTO In Transit'), ('RTO_RAD', 'RTO - Reached at Destination'), ('RTO_OFD', 'RTO Out for Delivery'), ('RTO_DEL', 'RTO Delivered'), ('RTO_UND', 'RTO Undelivered'), ('CAN', 'Shipment Cancelled'), ('ONH', 'Shipment On Hold'), ('LST', 'Shipment Lost'), ('DMG', 'Shipment Damaged'), ('MSR', 'Shipment Misrouted'), ('DPO', 'Shipment Disposed-Off')], default='SCB', max_length=20)),
                ('status_description', models.TextField(blank=True)),
                ('pickup_scheduled', models.BooleanField(default=False)),
                ('pickup_date', models.DateTimeField(blank=True, null=True)),
                ('expected_delivery_date', models.DateField(blank=True, null=True)),
                ('actual_delivery_date', models.DateTimeField(blank=True, null=True)),
                ('freight_mode', models.CharField(blank=True, max_length=50)),
                ('total_freight', models.DecimalField(decimal_places=2, max_digits=10, validators=[django.core.validators.MinValueValidator(0)])),
                ('cutoff_time', models.CharField(blank=True, max_length=10)),
                ('max_weight', models.DecimalField(blank=True, decimal_places=2, max_digits=8, null=True)),
                ('min_weight', models.DecimalField(blank=True, decimal_places=2, max_digits=8, null=True)),
                ('label_url', models.URLField(blank=True)),
                ('invoice_url', models.URLField(blank=True)),
                ('manifest_url', models.URLField(blank=True)),
                ('tracking_url', models.URLField(blank=True)),
                ('rapidshyp_response_data', models.JSONField(blank=True, help_text='Raw response from Rapidshyp API', null=True)),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('updated_at', models.DateTimeField(auto_now=True)),
                ('order', models.OneToOneField(on_delete=django.db.models.deletion.CASCADE, related_name='rapidshyp_shipment', to='orders.order')),
            ],
            options={
                'verbose_name': 'Rapidshyp Shipment',
                'verbose_name_plural': 'Rapidshyp Shipments',
                'ordering': ['-created_at'],
            },
        ),
        migrations.CreateModel(
            name='ShippingRateCache',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('pickup_pincode', models.CharField(max_length=6)),
                ('delivery_pincode', models.CharField(max_length=6)),
                ('weight', models.DecimalField(decimal_places=2, max_digits=8)),
                ('cod', models.BooleanField(default=False)),
                ('total_order_value', models.DecimalField(decimal_places=2, default=0, max_digits=10)),
                ('rates_data', models.JSONField(help_text='Cached response from Rapidshyp serviceability API')),
                ('is_serviceable', models.BooleanField(default=False)),
                ('cached_at', models.DateTimeField(auto_now_add=True)),
                ('expires_at', models.DateTimeField()),
            ],
            options={
                'verbose_name': 'Shipping Rate Cache',
                'verbose_name_plural': 'Shipping Rate Cache',
            },
        ),
        migrations.CreateModel(
            name='RapidshypAPILog',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('method', models.CharField(choices=[('serviceability_check', 'Serviceability Check'), ('create_order', 'Create Order'), ('assign_awb', 'Assign AWB'), ('schedule_pickup', 'Schedule Pickup'), ('track_order', 'Track Order'), ('generate_label', 'Generate Label'), ('cancel_order', 'Cancel Order')], max_length=50)),
                ('endpoint', models.CharField(max_length=200)),
                ('request_data', models.JSONField()),
                ('response_data', models.JSONField(blank=True, null=True)),
                ('response_status_code', models.IntegerField(blank=True, null=True)),
                ('response_time_ms', models.IntegerField(blank=True, null=True)),
                ('is_success', models.BooleanField(default=False)),
                ('error_message', models.TextField(blank=True)),
                ('created_at', models.DateTimeField(auto_now_add=True)),
            ],
            options={
                'verbose_name': 'Rapidshyp API Log',
                'verbose_name_plural': 'Rapidshyp API Logs',
                'ordering': ['-created_at'],
            },
        ),
        migrations.CreateModel(
            name='TrackingEvent',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('status', models.CharField(max_length=20)),
                ('status_description', models.TextField()),
                ('location', models.CharField(blank=True, max_length=200)),
                ('remarks', models.TextField(blank=True)),
                ('event_timestamp', models.DateTimeField()),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('shipment', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='tracking_events', to='shipping.rapidshypshipment')),
            ],
            options={
                'verbose_name': 'Tracking Event',
                'verbose_name_plural': 'Tracking Events',
                'ordering': ['-event_timestamp'],
            },
        ),
        migrations.AddIndex(
            model_name='trackingevent',
            index=models.Index(fields=['shipment', 'event_timestamp'], name='shipping_tr_shipme_b8b8b8_idx'),
        ),
        migrations.AddIndex(
            model_name='trackingevent',
            index=models.Index(fields=['status'], name='shipping_tr_status_b8b8b8_idx'),
        ),
        migrations.AddIndex(
            model_name='shippingratecache',
            index=models.Index(fields=['pickup_pincode', 'delivery_pincode'], name='shipping_sh_pickup__b8b8b8_idx'),
        ),
        migrations.AddIndex(
            model_name='shippingratecache',
            index=models.Index(fields=['cached_at'], name='shipping_sh_cached__b8b8b8_idx'),
        ),
        migrations.AddIndex(
            model_name='shippingratecache',
            index=models.Index(fields=['expires_at'], name='shipping_sh_expires_b8b8b8_idx'),
        ),
        migrations.AlterUniqueTogether(
            name='shippingratecache',
            unique_together={('pickup_pincode', 'delivery_pincode', 'weight', 'cod')},
        ),
        migrations.AddIndex(
            model_name='rapidshypshipment',
            index=models.Index(fields=['order'], name='shipping_ra_order_i_b8b8b8_idx'),
        ),
        migrations.AddIndex(
            model_name='rapidshypshipment',
            index=models.Index(fields=['awb_number'], name='shipping_ra_awb_num_b8b8b8_idx'),
        ),
        migrations.AddIndex(
            model_name='rapidshypshipment',
            index=models.Index(fields=['current_status'], name='shipping_ra_current_b8b8b8_idx'),
        ),
        migrations.AddIndex(
            model_name='rapidshypshipment',
            index=models.Index(fields=['created_at'], name='shipping_ra_created_b8b8b8_idx'),
        ),
        migrations.AddIndex(
            model_name='rapidshypshipment',
            index=models.Index(fields=['rapidshyp_order_id'], name='shipping_ra_rapidsh_b8b8b8_idx'),
        ),
        migrations.AddIndex(
            model_name='rapidshypapilog',
            index=models.Index(fields=['method', 'created_at'], name='shipping_ra_method_b8b8b8_idx'),
        ),
        migrations.AddIndex(
            model_name='rapidshypapilog',
            index=models.Index(fields=['is_success'], name='shipping_ra_is_succ_b8b8b8_idx'),
        ),
        migrations.AddIndex(
            model_name='rapidshypapilog',
            index=models.Index(fields=['created_at'], name='shipping_ra_created_b8b8b8_idx'),
        ),
    ]
