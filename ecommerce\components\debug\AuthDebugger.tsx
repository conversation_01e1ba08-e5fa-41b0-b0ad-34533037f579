'use client'

import { useSession } from "next-auth/react";
import { useState } from "react";
import { <PERSON>, <PERSON><PERSON>ontent, CardHeader, CardTitle } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import { MAIN_URL } from "@/constant/urls";
import axios from "axios";

export const AuthDebugger = () => {
  const { data: session, status } = useSession();
  const [testResult, setTestResult] = useState<any>(null);
  const [loading, setLoading] = useState(false);

  const testApiCall = async () => {
    setLoading(true);
    setTestResult(null);

    try {
      const config = {
        headers: {
          "Content-Type": "application/json",
          ...(session?.user?.access && {
            "Authorization": `Bearer ${session.user.access}`
          })
        }
      };

      console.log("=== FRONTEND API TEST ===");
      console.log("Session status:", status);
      console.log("Session object:", session);
      console.log("Access token exists:", !!session?.user?.access);
      console.log("Access token preview:", session?.user?.access?.substring(0, 50) + "...");
      console.log("Request config:", config);
      console.log("API URL:", `${MAIN_URL}/api/v1/users/detail/`);

      const response = await axios.get(`${MAIN_URL}/api/v1/users/detail/`, config);
      console.log("✅ API call successful:", response.status);

      setTestResult({
        success: true,
        data: response.data,
        status: response.status,
        requestHeaders: config.headers
      });
    } catch (error: any) {
      console.error("❌ API call failed:", error);
      console.error("Error response:", error.response);

      setTestResult({
        success: false,
        error: error.response?.data || error.message,
        status: error.response?.status,
        headers: error.response?.headers,
        requestHeaders: config.headers
      });
    } finally {
      setLoading(false);
    }
  };

  return (
    <Card className="w-full max-w-4xl mx-auto">
      <CardHeader>
        <CardTitle>Authentication Debugger</CardTitle>
      </CardHeader>
      <CardContent className="space-y-4">
        <div>
          <h3 className="font-semibold mb-2">Session Status</h3>
          <Badge variant={status === "authenticated" ? "default" : "destructive"}>
            {status}
          </Badge>
        </div>

        <div>
          <h3 className="font-semibold mb-2">Session Data</h3>
          <pre className="bg-gray-100 p-3 rounded text-sm overflow-auto max-h-40">
            {JSON.stringify(session, null, 2)}
          </pre>
        </div>

        <div>
          <h3 className="font-semibold mb-2">Token Information</h3>
          <div className="space-y-2">
            <div>
              <strong>Access Token Exists:</strong> {session?.user?.access ? "Yes" : "No"}
            </div>
            {session?.user?.access && (
              <div>
                <strong>Access Token Preview:</strong> {session.user.access.substring(0, 50)}...
              </div>
            )}
            <div>
              <strong>Refresh Token Exists:</strong> {session?.user?.refresh ? "Yes" : "No"}
            </div>
            <div>
              <strong>User ID:</strong> {session?.user?.id || "N/A"}
            </div>
            <div>
              <strong>User Email:</strong> {session?.user?.email || "N/A"}
            </div>
          </div>
        </div>

        <div>
          <h3 className="font-semibold mb-2">API Test</h3>
          <Button onClick={testApiCall} disabled={loading}>
            {loading ? "Testing..." : "Test API Call"}
          </Button>
          
          {testResult && (
            <div className="mt-4">
              <h4 className="font-semibold mb-2">Test Result</h4>
              <Badge variant={testResult.success ? "default" : "destructive"}>
                {testResult.success ? "Success" : "Failed"}
              </Badge>
              <pre className="bg-gray-100 p-3 rounded text-sm overflow-auto max-h-60 mt-2">
                {JSON.stringify(testResult, null, 2)}
              </pre>
            </div>
          )}
        </div>

        <div>
          <h3 className="font-semibold mb-2">Environment Info</h3>
          <div className="space-y-1">
            <div><strong>API URL:</strong> {MAIN_URL}</div>
            <div><strong>Environment:</strong> {process.env.NODE_ENV}</div>
          </div>
        </div>
      </CardContent>
    </Card>
  );
};
