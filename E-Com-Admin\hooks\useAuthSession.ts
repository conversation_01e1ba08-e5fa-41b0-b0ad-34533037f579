'use client';

import { useSession, signOut } from "next-auth/react";
import { useRouter } from "next/navigation";
import { useEffect, useCallback } from "react";

// Extend the Session and User types to include access and refresh tokens
declare module "next-auth" {
  interface User {
    access?: string;
    refresh?: string;
  }
  interface Session {
    user: User;
  }
}

/**
 * Enhanced authentication session hook that handles token refresh errors
 * and provides utilities for session management
 */
export const useAuthSession = () => {
  const { data: session, status, update } = useSession();
  const router = useRouter();

  // Check if session has refresh error

  // eslint-disable-next-line @typescript-eslint/no-explicit-any
  const hasRefreshError = (session as any)?.error === "RefreshAccessTokenError";

  // Handle session refresh errors
  useEffect(() => {
    if (hasRefreshError) {
      console.warn('🚨 Session refresh error detected, user needs to re-authenticate');
      // Don't automatically sign out immediately, let the user see the error
      // The component can decide when to sign out
    }
  }, [hasRefreshError]);

  // Force session update (useful after token operations)
  const refreshSession = useCallback(async () => {
    try {
      console.log('🔄 Manually refreshing session...');
      await update();
      console.log('✅ Session refreshed successfully');
    } catch (error) {
      console.error('❌ Failed to refresh session:', error);
    }
  }, [update]);

  // Sign out with proper cleanup
  const signOutWithCleanup = useCallback(async (redirectToLogin = true) => {
    try {
      console.log('🚪 Signing out user...');
      await signOut({ redirect: false });
      
      if (redirectToLogin) {
        router.push('/login');
      }
    } catch (error) {
      console.error('❌ Error during sign out:', error);
      // Force redirect even if signOut fails
      if (redirectToLogin) {
        router.push('/login');
      }
    }
  }, [router]);

  // Check if user is authenticated and has valid tokens
  const isAuthenticated = status === "authenticated" && !hasRefreshError;

  // Get access token safely
  const getAccessToken = useCallback(() => {
    if (isAuthenticated && session?.user?.access) {
      return session.user.access;
    }
    return null;
  }, [isAuthenticated, session]);

  // Get refresh token safely
  const getRefreshToken = useCallback(() => {
    if (status === "authenticated" && session?.user?.refresh) {
      return session.user.refresh;
    }
    return null;
  }, [status, session]);

  return {
    session,
    status,
    isAuthenticated,
    hasRefreshError,
    refreshSession,
    signOutWithCleanup,
    getAccessToken,
    getRefreshToken,
  };
};

export default useAuthSession;
