import { View, Text, StyleSheet, FlatList, TouchableOpacity } from 'react-native';
import { SafeAreaView } from 'react-native-safe-area-context';
import { MaterialIcons } from '@expo/vector-icons';
import { useEffect, useState } from 'react';
import axios from 'axios';
import { BaseURL } from '@/constants/ApiEndpoint';

const CATEGORIES = [
  {
    id: '1',
    name: 'Electronics',
    icon: 'devices',
    color: '#2563EB',
  },
  {
    id: '2',
    name: 'Fashion',
    icon: 'checkroom',
    color: '#DC2626',
  },
  {
    id: '3',
    name: 'Home',
    icon: 'home',
    color: '#059669',
  },
  {
    id: '4',
    name: 'Beauty',
    icon: 'spa',
    color: '#D97706',
  },
  {
    id: '5',
    name: 'Sports',
    icon: 'sports-basketball',
    color: '#7C3AED',
  },
  {
    id: '6',
    name: 'Books',
    icon: 'menu-book',
    color: '#BE185D',
  },
];


export default function CategoriesScreen({ navigation }) {
  const [categories, setCategories] = useState([]);

  const fetchCategories = async () => {
    try {
      const response = await axios.get(BaseURL + '/api/v1/products/categories/');
      setCategories(response.data);
    } catch (error) {
      console.error('Error fetching categories:', error);
    }
  }

  useEffect(() => {
    fetchCategories();
  },[])
  const renderCategory = ({ item }) => (
    <TouchableOpacity 
      style={styles.categoryCard}
      onPress={() => navigation.navigate('CategoriesProduct', {category: { slug: item.slug }})}
    >
      <View style={[styles.iconContainer, { backgroundColor: item.color }]}>
        <MaterialIcons name={item.icon} size={32} color="white" />
      </View>
      <Text style={styles.categoryName}>{item.name}</Text>
      <MaterialIcons name="chevron-right" size={24} color="#666" />
    </TouchableOpacity>
  );

  return (
    <SafeAreaView style={styles.container}>
      <View style={styles.header}>
        <Text style={styles.title}>Categories</Text>
      </View>

      <FlatList
        data={categories}
        renderItem={renderCategory}
        keyExtractor={(item) => item.id}
        contentContainerStyle={styles.categoryList}
      />
    </SafeAreaView>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#ffffff',
  },
  header: {
    padding: 16,
    borderBottomWidth: 1,
    borderBottomColor: '#f3f4f6',
  },
  title: {
    fontSize: 24,
    fontWeight: 'bold',
  },
  categoryList: {
    padding: 16,
  },
  categoryCard: {
    flexDirection: 'row',
    alignItems: 'center',
    padding: 16,
    backgroundColor: 'white',
    borderRadius: 12,
    marginBottom: 12,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 8,
    elevation: 3,
  },
  iconContainer: {
    width: 48,
    height: 48,
    borderRadius: 24,
    justifyContent: 'center',
    alignItems: 'center',
    marginRight: 16,
  },
  categoryName: {
    flex: 1,
    fontSize: 16,
    fontWeight: '600',
  },
});