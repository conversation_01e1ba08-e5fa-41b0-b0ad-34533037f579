import { useState, useCallback } from 'react';
import useApi from './useApi';
import {
  MAIN_URL,
  PRODUCTS,
  PRODUCT_DETAIL_BY_ID,
  PRODUCT_CREATE,
  PRODUCT_UPDATE,
  PRODUCT_DELETE,
  PRODUCT_IMAGE_UPLOAD
} from '@/constant/urls';
import { 
  Product, 
  ProductsResponse, 
  ProductCreateRequest, 
  ProductUpdateRequest,
  ImageUploadResponse 
} from '@/types/product';

export const useProducts = () => {
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const { create, update, remove, read } = useApi<ProductsResponse>(MAIN_URL);

  const createProduct = useCallback(async (productData: ProductCreateRequest): Promise<Product | null> => {
    setLoading(true);
    setError(null);

    try {
      const response = await create(PRODUCT_CREATE, productData);
      if (typeof response === 'string') {
        setError(response);
        return null;
      }
      // The create method might return the created product directly
      return response as unknown as Product;
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : 'Failed to create product';
      setError(errorMessage);
      return null;
    } finally {
      setLoading(false);
    }
  }, [create]);

  const updateProduct = useCallback(async (id: number, productData: Partial<ProductCreateRequest>): Promise<Product | null> => {
    setLoading(true);
    setError(null);

    try {
      const response = await update(PRODUCT_UPDATE(id), productData);
      if (typeof response === 'string') {
        setError(response);
        return null;
      }
      return response as unknown as Product;
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : 'Failed to update product';
      setError(errorMessage);
      return null;
    } finally {
      setLoading(false);
    }
  }, [update]);

  const deleteProduct = useCallback(async (id: string): Promise<boolean> => {
    setLoading(true);
    setError(null);
    
    try {
      const response = await remove(PRODUCT_DELETE(Number(id)));
      if (typeof response === 'string') {
        setError(response);
        return false;
      }
      return true;
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : 'Failed to delete product';
      setError(errorMessage);
      return false;
    } finally {
      setLoading(false);
    }
  }, [remove]);

  const getProducts = useCallback(async (params?: {
    page?: number;
    page_size?: number;
    search?: string;
    category?: string;
    brand?: string;
    is_admin?: boolean;
  }): Promise<ProductsResponse | null> => {
    setLoading(true);
    setError(null);

    try {
      let url = PRODUCTS;
      const queryParams = new URLSearchParams();

      if (params?.page) queryParams.append('page', params.page.toString());
      if (params?.page_size) queryParams.append('page_size', params.page_size.toString());
      if (params?.search) queryParams.append('search', params.search);
      if (params?.category) queryParams.append('category', params.category);
      if (params?.brand) queryParams.append('brand', params.brand);
      if (params?.is_admin) queryParams.append('is_admin', 'True');

      if (queryParams.toString()) {
        url += `?${queryParams.toString()}`;
      }

      const response = await read(url);
      if (typeof response === 'string') {
        setError(response);
        return null;
      }
      return response as ProductsResponse;
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : 'Failed to fetch products';
      setError(errorMessage);
      return null;
    } finally {
      setLoading(false);
    }
  }, [read]);

  const getProductById = useCallback(async (id: string): Promise<Product | null> => {
    setLoading(true);
    setError(null);

    try {
      const response = await read(PRODUCT_DETAIL_BY_ID(Number(id)));
      if (typeof response === 'string') {
        setError(response);
        return null;
      }
      return response as unknown as Product;
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : 'Failed to fetch product';
      setError(errorMessage);
      return null;
    } finally {
      setLoading(false);
    }
  }, [read]);

  const uploadProductImage = useCallback(async (file: File): Promise<ImageUploadResponse | null> => {
    setLoading(true);
    setError(null);
    
    try {
      const formData = new FormData();
      formData.append('image', file);
      
      const response = await create(PRODUCT_IMAGE_UPLOAD, formData);
      if (typeof response === 'string') {
        setError(response);
        return null;
      }
      return response as unknown as ImageUploadResponse;
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : 'Failed to upload image';
      setError(errorMessage);
      return null;
    } finally {
      setLoading(false);
    }
  }, [create]);

  return {
    loading,
    error,
    createProduct,
    updateProduct,
    deleteProduct,
    getProducts,
    getProductById,
    uploadProductImage,
  };
};
