# from django.urls import path, include
# from rest_framework.routers import DefaultRouter
# from .views import PromotionViewSet, PromotionUsageViewSet

# router = DefaultRouter()
# router.register(r'', PromotionViewSet)
# router.register(r'usage', PromotionUsageViewSet, basename='promotion-usage')

# urlpatterns = [
#     path('', include(router.urls)),
# ]

from django.urls import path
from .views import (
    PromotionListCreateView,
    PromotionRetrieveUpdateDestroyView,
    ApplyPromotionView,
    PromotionUsageListView,
    PromotionView,
)

app_name = "promotions"

urlpatterns = [
    path("", PromotionListCreateView.as_view(), name="promotion-list"),
    path(
        "<str:code>/",
        PromotionRetrieveUpdateDestroyView.as_view(),
        name="promotion-detail",
    ),
    path("apply/code/", ApplyPromotionView.as_view(), name="apply-promotion"),
    path(
        "usages/detail/", PromotionUsageListView.as_view(), name="promotion-usage-list"
    ),
    path("get/single/promotion/", PromotionView.as_view(), name="get-single-promotion"),
]
