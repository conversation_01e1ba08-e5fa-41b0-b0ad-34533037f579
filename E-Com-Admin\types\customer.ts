export interface Customer {
  id: string;
  name: string;
  email: string;
  phone_number?: string;
  date_of_birth?: string;
  is_verified: boolean;
  profile_image_url?: string;
  image?: string;
  date_joined: string;
  last_login?: string;
  is_active: boolean;
  totalOrders?: number;
  totalSpent?: number;
  lastPurchase?: string;
  status: 'active' | 'inactive';
  notes?: string;
}

export interface CustomerOrder {
  id: string;
  date: string;
  total: number;
  status: string;
  tracking_number?: string;
  estimated_delivery_date?: string;
}

export interface CustomerDetail extends Customer {
  orders: CustomerOrder[];
  addresses?: Address[];
  payment_methods?: PaymentMethod[];
}

export interface Address {
  id: string;
  address_type: 'BILLING' | 'SHIPPING';
  street_address: string;
  apartment?: string;
  city: string;
  state: string;
  postal_code: string;
  country: string;
  is_default: boolean;
}

export interface PaymentMethod {
  id: string;
  card_type: string;
  last_four: string;
  is_default: boolean;
  created_at: string;
}

export interface CustomersListResponse {
  results: Customer[];
  count: number;
  next?: string;
  previous?: string;
}

export interface CustomerNotesUpdate {
  notes: string;
}
