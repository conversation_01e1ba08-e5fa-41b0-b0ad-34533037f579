# Sitemap Headers Fix

This document explains the changes made to fix the Content-Type header issue with the sitemap.xml file.

## Problem

Google Search Console was reporting that the sitemap.xml file could not be read. The issue was that the server was not sending the correct Content-Type header with the sitemap.xml response. For XML files like sitemaps, the Content-Type should be either `application/xml` or `text/xml`.

## Solution

We implemented multiple layers of fixes to ensure that the sitemap.xml file is served with the correct Content-Type header:

### 1. Next.js Middleware

Updated the middleware.ts file to add a specific handler for sitemap.xml requests. This middleware intercepts requests to /sitemap.xml and adds the appropriate Content-Type header.

```typescript
// In middleware.ts
if (request.nextUrl.pathname === '/sitemap.xml') {
  const response = NextResponse.next();
  response.headers.set('Content-Type', 'application/xml');
  return response;
}
```

### 2. Custom API Route Handler

Created a custom API route handler at `app/api/sitemap/route.ts` that serves the sitemap.xml file with the correct headers:

```typescript
// In app/api/sitemap/route.ts
return new NextResponse(sitemapContent, {
  status: 200,
  headers: {
    'Content-Type': 'application/xml',
    'Cache-Control': 'public, max-age=3600, stale-while-revalidate=86400',
  },
});
```

### 3. Next.js Config Headers

Updated the next.config.ts file to add headers for XML files:

```typescript
// In next.config.ts
async headers() {
  return [
    {
      source: '/:path*.xml',
      headers: [
        {
          key: 'Content-Type',
          value: 'application/xml',
        },
        // ...
      ],
    },
    // ...
  ];
}
```

## Testing

After implementing these changes, you should:

1. Rebuild and redeploy your application
2. Test the sitemap.xml URL directly in your browser
3. Use a tool like curl to check the headers:
   ```bash
   curl -I https://trio.net.in/sitemap.xml
   ```
4. Resubmit the sitemap to Google Search Console

## Why Multiple Layers?

We implemented multiple layers of fixes to ensure that the sitemap.xml file is served correctly regardless of how it's accessed:

1. The middleware handles all requests to /sitemap.xml
2. The custom API route provides a fallback if the middleware doesn't work
3. The Next.js config headers provide a global configuration for all XML files

This redundancy ensures that the sitemap.xml file will always be served with the correct Content-Type header, even if one of the methods fails.

## Additional Notes

- The same approach was applied to robots.txt to ensure it's served with the correct Content-Type header (text/plain)
- Cache-Control headers were added to improve performance and reduce server load
- The changes are compatible with both the dynamic sitemap generation (app/sitemap.ts) and the static sitemap generation (scripts/generate-sitemap.js)
