"""
Management command to migrate existing data to encrypted fields.
This command safely migrates unencrypted data to encrypted fields without data loss.
"""

from django.core.management.base import BaseCommand
from django.db import transaction
from backend.encryption_utils import migrate_to_encrypted_field, encryption_manager
from users.models import Customer, Address, ContactMessage
import logging

logger = logging.getLogger(__name__)


class Command(BaseCommand):
    help = 'Migrate existing data to encrypted fields'
    
    def add_arguments(self, parser):
        parser.add_argument(
            '--dry-run',
            action='store_true',
            help='Show what would be migrated without actually doing it',
        )
        parser.add_argument(
            '--batch-size',
            type=int,
            default=1000,
            help='Number of records to process in each batch (default: 1000)',
        )
        parser.add_argument(
            '--model',
            type=str,
            choices=['customer', 'address', 'contact', 'all'],
            default='all',
            help='Which model to migrate (default: all)',
        )
    
    def handle(self, *args, **options):
        dry_run = options['dry_run']
        batch_size = options['batch_size']
        model_choice = options['model']
        
        if dry_run:
            self.stdout.write(
                self.style.WARNING('DRY RUN MODE - No data will be modified')
            )
        
        self.stdout.write('Starting encryption migration...')
        
        # Test encryption functionality first
        if not self._test_encryption():
            self.stdout.write(
                self.style.ERROR('Encryption test failed. Aborting migration.')
            )
            return
        
        total_migrated = 0
        
        try:
            if model_choice in ['customer', 'all']:
                total_migrated += self._migrate_customer_data(dry_run, batch_size)
            
            if model_choice in ['address', 'all']:
                total_migrated += self._migrate_address_data(dry_run, batch_size)
            
            if model_choice in ['contact', 'all']:
                total_migrated += self._migrate_contact_data(dry_run, batch_size)
            
            if dry_run:
                self.stdout.write(
                    self.style.SUCCESS(
                        f'DRY RUN: Would migrate {total_migrated} records total'
                    )
                )
            else:
                self.stdout.write(
                    self.style.SUCCESS(
                        f'Encryption migration completed! Migrated {total_migrated} records total'
                    )
                )
        
        except Exception as e:
            self.stdout.write(
                self.style.ERROR(f'Migration failed: {str(e)}')
            )
            logger.error(f"Encryption migration failed: {e}", exc_info=True)
    
    def _test_encryption(self):
        """Test that encryption is working correctly"""
        try:
            test_data = "test_encryption_data_123"
            encrypted = encryption_manager.encrypt(test_data)
            decrypted = encryption_manager.decrypt(encrypted)
            
            if test_data == decrypted:
                self.stdout.write(
                    self.style.SUCCESS('✅ Encryption test passed')
                )
                return True
            else:
                self.stdout.write(
                    self.style.ERROR('❌ Encryption test failed: data mismatch')
                )
                return False
        except Exception as e:
            self.stdout.write(
                self.style.ERROR(f'❌ Encryption test failed: {str(e)}')
            )
            return False
    
    def _migrate_customer_data(self, dry_run, batch_size):
        """Migrate Customer phone_number field"""
        self.stdout.write('Migrating Customer phone numbers...')
        
        if dry_run:
            count = Customer.objects.exclude(phone_number='').count()
            self.stdout.write(f'Would migrate {count} customer phone numbers')
            return count
        
        return migrate_to_encrypted_field(Customer, 'phone_number', batch_size)
    
    def _migrate_address_data(self, dry_run, batch_size):
        """Migrate Address sensitive fields"""
        self.stdout.write('Migrating Address data...')
        
        fields_to_migrate = [
            'street_address', 'apartment', 'city', 'state', 
            'postal_code', 'order_user_phone', 'order_user_email'
        ]
        
        total_migrated = 0
        
        for field in fields_to_migrate:
            self.stdout.write(f'  Migrating Address.{field}...')
            
            if dry_run:
                # Count non-empty fields
                if field == 'order_user_email':
                    count = Address.objects.exclude(**{f'{field}': ''}).count()
                else:
                    count = Address.objects.exclude(**{f'{field}': ''}).count()
                self.stdout.write(f'    Would migrate {count} records')
                total_migrated += count
            else:
                migrated = migrate_to_encrypted_field(Address, field, batch_size)
                total_migrated += migrated
        
        return total_migrated
    
    def _migrate_contact_data(self, dry_run, batch_size):
        """Migrate ContactMessage sensitive fields"""
        self.stdout.write('Migrating ContactMessage data...')
        
        total_migrated = 0
        
        for field in ['name', 'email']:
            self.stdout.write(f'  Migrating ContactMessage.{field}...')
            
            if dry_run:
                count = ContactMessage.objects.exclude(**{f'{field}': ''}).count()
                self.stdout.write(f'    Would migrate {count} records')
                total_migrated += count
            else:
                migrated = migrate_to_encrypted_field(ContactMessage, field, batch_size)
                total_migrated += migrated
        
        return total_migrated
