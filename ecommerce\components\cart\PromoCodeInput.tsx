import { useState } from "react";
import { Button } from "../../components/ui/button";
import { Input } from "../../components/ui/input";
import { useToast } from "../../components/ui/use-toast";
import useApi from "@/hooks/useApi";
import { MAIN_URL, PROMOCODE_APPLY } from "@/constant/urls";
import useStorage from "@/hooks/useStorage";

interface PromoCodeInputProps {
  setPromotion: any;
  promotion: any;
}

export const PromoCodeInput = ({
  setPromotion,
  promotion,
}: PromoCodeInputProps) => {
  const { toast } = useToast();
  const [isLoading, setIsLoading] = useState(false);
  const [isApply, setIsApply] = useState(Boolean(promotion?.code));
  const [promoCode, setPromoCode] = useState<string>(promotion?.code ?? "");
  const { create } = useApi(MAIN_URL);
  const storage = useStorage('local');

  const handleApplyPromo = async () => {
    setIsLoading(true);
    // Simulate API call to validate promo code

    try {
      const response: any = await create(PROMOCODE_APPLY, {
        code: promoCode,
      });

      if (Boolean(response?.discount)) {
        setPromotion((promo: any) => {
          return { ...promo, ...response };
        });

        // Safely store promotion in localStorage using our custom hook
        storage.setItem(
          "promotion",
          JSON.stringify({ ...response, code: promoCode })
        );

        toast({
          variant: "success",
          title: "Promo code applied",
          description: response?.message,
        });

        setIsApply(true);
      } else {
        const error: any = JSON.parse(response);
        toast({
          title: "Invalid promo code",
          description: error?.detail,
          variant: "destructive",
        });
      }
    } catch (err: any) {
      console.log("error while applying promo", err);
    } finally {
      setIsLoading(false);
    }
  };

  const handleRemovePromo = () => {
    setPromotion({});
    setIsApply(false);
    // Safely remove promotion from localStorage using our custom hook
    storage.removeItem("promotion");
  };

  const handleOnChange = (e: any) => {
    setPromoCode(e.target.value);
  };

  return (
    <div>
      <h3 className="text-xl font-semibold mb-4 flex items-center gap-2">
        <svg xmlns="http://www.w3.org/2000/svg" width="20" height="20" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round" className="text-primary">
          <path d="M21 16V8a2 2 0 0 0-1-1.73l-7-4a2 2 0 0 0-2 0l-7 4A2 2 0 0 0 3 8v8a2 2 0 0 0 1 1.73l7 4a2 2 0 0 0 2 0l7-4A2 2 0 0 0 21 16z"></path>
          <polyline points="3.29 7 12 12 20.71 7"></polyline>
          <line x1="12" y1="22" x2="12" y2="12"></line>
        </svg>
        Apply Promo Code
      </h3>

      {isApply ? (
        <div className="bg-green-50 border border-green-200 rounded-lg p-4 mb-4 flex justify-between items-center">
          <div>
            <div className="font-medium text-green-700 flex items-center gap-2">
              <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round">
                <path d="M22 11.08V12a10 10 0 1 1-5.93-9.14"></path>
                <polyline points="22 4 12 14.01 9 11.01"></polyline>
              </svg>
              Code "{promotion?.code}" applied
            </div>
            <p className="text-sm text-green-600">You saved ₹{promotion?.discount}</p>
          </div>
          <Button
            variant="ghost"
            size="sm"
            onClick={handleRemovePromo}
            className="text-red-500 hover:text-red-700 hover:bg-red-50"
          >
            Remove
          </Button>
        </div>
      ) : (
        <form
          onSubmit={(e) => {
            e.preventDefault();
            handleApplyPromo();
          }}
          className="space-y-4"
        >
          <div className="flex gap-2">
            <Input
              placeholder="Enter promo code"
              value={promoCode}
              onChange={handleOnChange}
              className="h-12"
            />
            <Button
              onClick={handleApplyPromo}
              disabled={isLoading || !promoCode}
              className="min-w-[100px]"
            >
              {isLoading ? (
                <div className="flex items-center gap-2">
                  <div className="w-4 h-4 border-t-2 border-b-2 border-white rounded-full animate-spin"></div>
                  <span>...</span>
                </div>
              ) : (
                "Apply"
              )}
            </Button>
          </div>
          <div className="flex items-center gap-2 text-sm text-muted-foreground">
            <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round">
              <circle cx="12" cy="12" r="10"></circle>
              <line x1="12" y1="8" x2="12" y2="12"></line>
              <line x1="12" y1="16" x2="12.01" y2="16"></line>
            </svg>
            Try "Welcome" for 10% off your first order
          </div>
        </form>
      )}
    </div>
  );
};
