from rest_framework import serializers
from .models import (
    SubCategorie,
    Category,
    Product,
    ProductVariant,
    ProductImage,
    Review,
    Brand,
)
from django.utils.text import slugify
from backend.minio_settings import get_minio_media_url

# Cache for default GST rate to avoid repeated database queries
_default_gst_rate = None


class SubCategorieSerializer(serializers.ModelSerializer):
    class Meta:
        model = SubCategorie
        fields = ["id", "name", "slug"]
        read_only_fields = ["slug"]


class CategorySerializer(serializers.ModelSerializer):
    subcategories = SubCategorieSerializer(many=True, read_only=True)

    class Meta:
        model = Category
        fields = ["id", "name", "slug", "subcategories"]
        read_only_fields = ["slug"]

    def create(self, validated_data):
        validated_data["slug"] = slugify(validated_data["name"])
        return super().create(validated_data)


class ProductImageSerializer(serializers.ModelSerializer):
    image_url = serializers.SerializerMethodField()

    class Meta:
        model = ProductImage
        fields = ["id", "product", "image", "is_primary", "image_url"]

    def get_image_url(self, obj):
        if obj.image:
            # Use get_minio_media_url to handle the path correctly
            return get_minio_media_url(obj.image.name)
        return None


class ProductVariantSerializer(serializers.ModelSerializer):
    class Meta:
        model = ProductVariant
        fields = ["id", "product", "name", "sku", "price_adjustment", "stock"]


class ReviewSerializer(serializers.ModelSerializer):
    user_email = serializers.SerializerMethodField()

    class Meta:
        model = Review
        fields = [
            "id",
            "product",
            "user",
            "user_email",
            "rating",
            "title",
            "comment",
            "created_at",
            "is_approved",
        ]
        read_only_fields = ["user", "is_approved"]

    def get_user_email(self, obj):
        return obj.user.email


class ReviewCreateSerializer(serializers.ModelSerializer):
    title = serializers.CharField(max_length=100, required=False, allow_blank=True)
    product = serializers.PrimaryKeyRelatedField(queryset=Product.objects.all(), required=False)

    class Meta:
        model = Review
        fields = ["product", "rating", "title", "comment"]

    def create(self, validated_data):
        user = self.context["request"].user
        validated_data["user"] = user
        product = validated_data.get("product")

        # Check if user has already reviewed this product (if product is provided)
        if product and Review.objects.filter(product=product, user=user).exists():
            raise serializers.ValidationError(
                {"detail": "You have already reviewed this product"}
            )

        # Set default title if not provided
        if not validated_data.get("title"):
            if product:
                validated_data["title"] = f"Review for {product.name}"
            else:
                validated_data["title"] = "Product Review"

        return Review.objects.create(**validated_data)


class ProductSerializer(serializers.ModelSerializer):
    images = ProductImageSerializer(many=True, read_only=True)
    variants = ProductVariantSerializer(many=True, read_only=True)
    category_name = serializers.CharField(source="category.name", read_only=True)
    average_rating = serializers.DecimalField(
        max_digits=3, decimal_places=2, read_only=True
    )
    total_reviews = serializers.IntegerField(read_only=True)

    # GST inclusive pricing fields
    mrp = serializers.DecimalField(max_digits=10, decimal_places=2, read_only=True)
    base_price = serializers.DecimalField(max_digits=10, decimal_places=2, read_only=True)
    gst_amount = serializers.SerializerMethodField()
    gst_rate = serializers.SerializerMethodField()

    class Meta:
        model = Product
        fields = [
            "id",
            "name",
            "slug",
            "description",
            "category",
            "category_name",
            "price",
            "mrp",
            "base_price",
            "gst_amount",
            "gst_rate",
            "stock",
            "is_active",
            "images",
            "variants",
            "average_rating",
            "total_reviews",
            "created_at",
            "updated_at",
        ]
        read_only_fields = ["slug"]

    def get_gst_amount(self, obj):
        """Get GST amount from MRP"""
        return round(obj.calculate_gst_from_mrp(), 2)

    def get_gst_rate(self, obj):
        """Get GST rate percentage"""
        return float(obj.get_gst_rate().rate)

    def create(self, validated_data):
        validated_data["slug"] = slugify(validated_data["name"])
        return super().create(validated_data)


class ProductDetailSerializer(ProductSerializer):
    reviews = ReviewSerializer(many=True, read_only=True)

    class Meta(ProductSerializer.Meta):
        fields = ProductSerializer.Meta.fields + ["reviews"]


class CategoryDetailSerializer(serializers.ModelSerializer):
    products = ProductSerializer(many=True, read_only=True)

    class Meta:
        model = Category
        fields = [
            "id",
            "name",
            "slug",
            "description",
            "parent",
            "image",
            "is_active",
            "products",
        ]


# Duplicate imports and serializers removed - using the ones defined above


class SubCategoriesNameOnlySerializer(serializers.ModelSerializer):
    product_count = serializers.IntegerField(read_only=True)

    class Meta:
        model = SubCategorie
        fields = ["id", "name", "slug", "product_count"]
        read_only_fields = ["slug"]


class CategoryNameOnlySerializer(serializers.ModelSerializer):
    subcategories = SubCategoriesNameOnlySerializer(many=True, read_only=True)
    product_count = serializers.IntegerField(read_only=True)
    total_product_count = serializers.IntegerField(read_only=True)

    class Meta:
        model = Category
        fields = ["id", "name", "slug", "subcategories", "product_count", "total_product_count"]
        read_only_fields = ["slug"]


class OptimizedCategoryNameOnlySerializer(serializers.ModelSerializer):
    """
    Optimized serializer that only includes categories and subcategories with products.
    This reduces frontend complexity and improves performance.
    """
    subcategories = serializers.SerializerMethodField()
    product_count = serializers.IntegerField(read_only=True)
    total_product_count = serializers.IntegerField(read_only=True)
    image_url = serializers.SerializerMethodField()

    class Meta:
        model = Category
        fields = ["id", "name", "slug", "image", "image_url", "subcategories", "product_count", "total_product_count"]
        read_only_fields = ["slug"]

    def get_image_url(self, obj):
        """Get the full URL for the category image"""
        if obj.image:
            # Use get_minio_media_url to handle the path correctly
            return get_minio_media_url(obj.image.name)
        return None

    def get_subcategories(self, obj):
        """Only return subcategories that have products"""
        subcategories_with_products = []
        for subcategory in obj.subcategories.all():
            if hasattr(subcategory, 'product_count') and subcategory.product_count > 0:
                subcategories_with_products.append({
                    "id": subcategory.id,
                    "name": subcategory.name,
                    "slug": subcategory.slug,
                    "product_count": subcategory.product_count
                })
        return subcategories_with_products


class BrandNameOnlySerializer(serializers.ModelSerializer):
    class Meta:
        model = Brand
        fields = ["id", "name", "image"]


class BrandSerializer(serializers.ModelSerializer):
    image_url = serializers.SerializerMethodField()

    class Meta:
        model = Brand
        fields = ["id", "name", "description", "image", "image_url", "is_active"]

    def get_image_url(self, obj):
        if obj.image:
            # Use get_minio_media_url to handle the path correctly
            return get_minio_media_url(obj.image.name)
        return None


class SimpleCategorySerializer(serializers.ModelSerializer):
    """Simple category serializer for ProductListSerializer to avoid N+1 queries"""
    class Meta:
        model = Category
        fields = ["id", "name", "slug"]


class ProductListSerializer(serializers.ModelSerializer):
    category = SimpleCategorySerializer(read_only=True)
    brand = serializers.SerializerMethodField()
    category_id = serializers.PrimaryKeyRelatedField(
        queryset=Category.objects.all(), write_only=True, source="category"
    )
    images = ProductImageSerializer(many=True, read_only=True)
    image = serializers.SerializerMethodField()
    variants = ProductVariantSerializer(many=True, read_only=True)
    average_rating = serializers.SerializerMethodField()
    review_count = serializers.SerializerMethodField()

    # GST inclusive pricing fields
    mrp = serializers.DecimalField(max_digits=10, decimal_places=2, read_only=True)
    base_price = serializers.SerializerMethodField()  # Changed to SerializerMethodField to avoid property access
    gst_amount = serializers.SerializerMethodField()
    gst_rate = serializers.SerializerMethodField()

    def __init__(self, *args, **kwargs):
        # Determine if 'GET' request for fields customization
        fields = kwargs.pop("fields", None)
        super().__init__(*args, **kwargs)

        # Dynamically remove or include fields
        if fields is not None:
            allowed = set(fields)
            existing = set(self.fields)
            for field_name in existing - allowed:
                self.fields.pop(field_name)

    def get_brand(self, obj):
        if obj.brand:
            # Use prefetched brand data to avoid additional queries
            return {
                'id': obj.brand.id,
                'name': obj.brand.name,
                'description': obj.brand.description,
                'image': obj.brand.image.name if obj.brand.image else None,
                'image_url': get_minio_media_url(obj.brand.image.name) if obj.brand.image else None,
                'is_active': obj.brand.is_active
            }
        return None

    def get_image(self, obj):
        # Use prefetched images to avoid additional queries
        if hasattr(obj, '_prefetched_objects_cache') and 'images' in obj._prefetched_objects_cache:
            images = obj._prefetched_objects_cache['images']
            if images:
                # Find primary image first
                primary_image = next((img for img in images if img.is_primary), None)
                image = primary_image or images[0]
                return get_minio_media_url(image.image.name)
        else:
            # Fallback for when prefetch is not available
            if obj.images.exists():
                image = obj.images.filter(is_primary=True).first()
                if not image:
                    image = obj.images.first()
                return get_minio_media_url(image.image.name)
        return None

    class Meta:
        model = Product
        fields = [
            "id",
            "name",
            "slug",
            "description",
            "price",
            "mrp",
            "base_price",
            "gst_amount",
            "gst_rate",
            "category",
            "category_id",
            "stock",
            "is_active",
            "images",
            "variants",
            "average_rating",
            "review_count",
            "created_at",
            "updated_at",
            "brand",
            "image",
        ]

    def get_average_rating(self, obj):
        # Use prefetched aggregated data if available
        if hasattr(obj, 'avg_rating') and obj.avg_rating is not None:
            return round(float(obj.avg_rating), 2)

        # Return None if no aggregated data (avoid calling model property which triggers queries)
        return None

    def get_review_count(self, obj):
        # Use prefetched aggregated data if available
        if hasattr(obj, 'review_count'):
            return obj.review_count

        # Fallback to counting prefetched reviews to avoid additional query
        if hasattr(obj, '_prefetched_objects_cache') and 'reviews' in obj._prefetched_objects_cache:
            return len(obj._prefetched_objects_cache['reviews'])

        # Last resort - this will cause a query
        return obj.reviews.count()

    def get_gst_amount(self, obj):
        """Get GST amount from MRP using prefetched GST data"""
        # Use prefetched GST data to avoid additional queries
        if obj.gst:
            gst_rate_value = obj.gst.rate
        else:
            # Use cached default GST rate to avoid database query
            global _default_gst_rate
            if _default_gst_rate is None:
                from .models import GST
                try:
                    default_gst = GST.objects.filter(is_default=True, is_active=True).first()
                    _default_gst_rate = float(default_gst.rate) if default_gst else 18.00
                except:
                    _default_gst_rate = 18.00
            gst_rate_value = _default_gst_rate

        # Calculate GST from inclusive price (MRP)
        from decimal import Decimal
        mrp = Decimal(str(obj.price))
        rate = Decimal(str(gst_rate_value))
        gst_amount = mrp - (mrp / (1 + (rate / Decimal('100'))))
        return round(float(gst_amount), 2)

    def get_base_price(self, obj):
        """Get base price (excluding GST) calculated from MRP using prefetched GST data"""
        # Use prefetched GST data to avoid additional queries
        if obj.gst:
            gst_rate_value = obj.gst.rate
        else:
            # Use cached default GST rate to avoid database query
            global _default_gst_rate
            if _default_gst_rate is None:
                from .models import GST
                try:
                    default_gst = GST.objects.filter(is_default=True, is_active=True).first()
                    _default_gst_rate = float(default_gst.rate) if default_gst else 18.00
                except:
                    _default_gst_rate = 18.00
            gst_rate_value = _default_gst_rate

        # Calculate base price from inclusive price (MRP)
        from decimal import Decimal
        mrp = Decimal(str(obj.price))
        rate = Decimal(str(gst_rate_value))
        base_price = mrp / (1 + (rate / Decimal('100')))
        return round(float(base_price), 2)

    def get_gst_rate(self, obj):
        """Get GST rate percentage using prefetched GST data"""
        # Use prefetched GST data to avoid additional queries
        if obj.gst:
            return float(obj.gst.rate)
        else:
            # Use cached default GST rate to avoid database query
            global _default_gst_rate
            if _default_gst_rate is None:
                from .models import GST
                try:
                    default_gst = GST.objects.filter(is_default=True, is_active=True).first()
                    _default_gst_rate = float(default_gst.rate) if default_gst else 18.00
                except:
                    _default_gst_rate = 18.00
            return _default_gst_rate


class ProductDetailSerializer(ProductListSerializer):
    reviews = ReviewSerializer(many=True, read_only=True)
    gst_breakdown = serializers.SerializerMethodField()

    class Meta(ProductListSerializer.Meta):
        fields = ProductListSerializer.Meta.fields + ["reviews", "gst_breakdown"]

    def get_gst_breakdown(self, obj):
        """Get detailed GST breakdown from MRP"""
        return obj.calculate_gst_breakdown_from_mrp(quantity=1, is_inter_state=False)


class CategoryDetailSerializer(serializers.ModelSerializer):
    products = ProductSerializer(many=True, read_only=True)
    children = serializers.SerializerMethodField()

    class Meta:
        model = Category
        fields = ["id", "name", "slug", "description", "image", "children", "products"]

    def get_children(self, obj):
        return SubCategorieSerializer(obj.subcategories.all(), many=True).data





class AdminCategorySerializer(serializers.ModelSerializer):
    # Add product count for each category
    product_count = serializers.SerializerMethodField()
    display_order = serializers.IntegerField(
        source="id"
    )  # Use category ID as display order

    class Meta:
        model = Category
        fields = ["id", "name", "description", "display_order", "product_count"]

    def get_product_count(self, obj):
        # Return the count of products in the category
        return Product.objects.filter(category=obj).count()
