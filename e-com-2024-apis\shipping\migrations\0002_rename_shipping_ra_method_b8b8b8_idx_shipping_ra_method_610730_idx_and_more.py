# Generated by Django 5.0.2 on 2025-06-20 09:15

from django.db import migrations


class Migration(migrations.Migration):

    dependencies = [
        ('shipping', '0001_initial'),
    ]

    operations = [
        migrations.RenameIndex(
            model_name='rapidshypapilog',
            new_name='shipping_ra_method_610730_idx',
            old_name='shipping_ra_method_b8b8b8_idx',
        ),
        migrations.RenameIndex(
            model_name='rapidshypapilog',
            new_name='shipping_ra_is_succ_94d493_idx',
            old_name='shipping_ra_is_succ_b8b8b8_idx',
        ),
        migrations.RenameIndex(
            model_name='rapidshypapilog',
            new_name='shipping_ra_created_c86502_idx',
            old_name='shipping_ra_created_b8b8b8_idx',
        ),
        migrations.RenameIndex(
            model_name='rapidshypshipment',
            new_name='shipping_ra_order_i_4768f2_idx',
            old_name='shipping_ra_order_i_b8b8b8_idx',
        ),
        migrations.RenameIndex(
            model_name='rapidshypshipment',
            new_name='shipping_ra_awb_num_5bdfe3_idx',
            old_name='shipping_ra_awb_num_b8b8b8_idx',
        ),
        migrations.RenameIndex(
            model_name='rapidshypshipment',
            new_name='shipping_ra_current_beaeda_idx',
            old_name='shipping_ra_current_b8b8b8_idx',
        ),
        migrations.RenameIndex(
            model_name='rapidshypshipment',
            new_name='shipping_ra_created_7f1835_idx',
            old_name='shipping_ra_created_b8b8b8_idx',
        ),
        migrations.RenameIndex(
            model_name='rapidshypshipment',
            new_name='shipping_ra_rapidsh_619fbc_idx',
            old_name='shipping_ra_rapidsh_b8b8b8_idx',
        ),
        migrations.RenameIndex(
            model_name='shippingratecache',
            new_name='shipping_sh_pickup__570714_idx',
            old_name='shipping_sh_pickup__b8b8b8_idx',
        ),
        migrations.RenameIndex(
            model_name='shippingratecache',
            new_name='shipping_sh_cached__71ec78_idx',
            old_name='shipping_sh_cached__b8b8b8_idx',
        ),
        migrations.RenameIndex(
            model_name='shippingratecache',
            new_name='shipping_sh_expires_0d91cb_idx',
            old_name='shipping_sh_expires_b8b8b8_idx',
        ),
        migrations.RenameIndex(
            model_name='trackingevent',
            new_name='shipping_tr_shipmen_c01c37_idx',
            old_name='shipping_tr_shipme_b8b8b8_idx',
        ),
        migrations.RenameIndex(
            model_name='trackingevent',
            new_name='shipping_tr_status_9f7c81_idx',
            old_name='shipping_tr_status_b8b8b8_idx',
        ),
    ]
