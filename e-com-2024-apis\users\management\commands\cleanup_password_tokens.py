from django.core.management.base import BaseCommand
from django.utils import timezone
from users.models import PasswordResetToken
import logging

logger = logging.getLogger(__name__)


class Command(BaseCommand):
    help = 'Clean up expired password reset tokens'

    def add_arguments(self, parser):
        parser.add_argument(
            '--dry-run',
            action='store_true',
            help='Show what would be deleted without actually deleting',
        )

    def handle(self, *args, **options):
        dry_run = options['dry_run']
        
        if dry_run:
            self.stdout.write(
                self.style.WARNING('DRY RUN MODE - No tokens will be deleted')
            )
        
        # Clean up expired tokens
        expired_count = PasswordResetToken.cleanup_expired_tokens()
        
        if dry_run:
            # For dry run, just count expired tokens
            expired_tokens = PasswordResetToken.objects.filter(
                expires_at__lt=timezone.now()
            )
            expired_count = expired_tokens.count()
            
            if expired_count > 0:
                self.stdout.write(
                    f'Would delete {expired_count} expired password reset tokens'
                )
                for token in expired_tokens[:10]:  # Show first 10
                    self.stdout.write(
                        f'  - Token for {token.user.email}, expired at {token.expires_at}'
                    )
                if expired_count > 10:
                    self.stdout.write(f'  ... and {expired_count - 10} more')
            else:
                self.stdout.write('No expired tokens found')
        else:
            if expired_count > 0:
                self.stdout.write(
                    self.style.SUCCESS(
                        f'Successfully deleted {expired_count} expired password reset tokens'
                    )
                )
                logger.info(f'Cleaned up {expired_count} expired password reset tokens')
            else:
                self.stdout.write('No expired tokens found')
        
        # Show statistics
        total_tokens = PasswordResetToken.objects.count()
        used_tokens = PasswordResetToken.objects.filter(is_used=True).count()
        active_tokens = PasswordResetToken.objects.filter(
            is_used=False,
            expires_at__gt=timezone.now()
        ).count()
        
        self.stdout.write('\n--- Password Reset Token Statistics ---')
        self.stdout.write(f'Total tokens: {total_tokens}')
        self.stdout.write(f'Used tokens: {used_tokens}')
        self.stdout.write(f'Active tokens: {active_tokens}')
        
        if active_tokens > 50:
            self.stdout.write(
                self.style.WARNING(
                    f'Warning: {active_tokens} active tokens found. '
                    'Consider investigating if this is unusually high.'
                )
            )
