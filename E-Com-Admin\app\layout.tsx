import type { Metada<PERSON> } from "next";
import localFont from "next/font/local";
import "./globals.css";
import { Navbar } from "@/components/layout/Navbar";
import { Sidebar } from "@/components/layout/Sidebar";
import { AuthProvider } from "@/provider/AuthProvider";
import { SidebarProvider } from "@/hooks/useSidebar";
import { SessionRecovery } from "@/components/auth/SessionRecovery";
import { TokenDebugger } from "@/components/debug/TokenDebugger";

const geistSans = localFont({
  src: "./fonts/GeistVF.woff",
  variable: "--font-geist-sans",
  weight: "100 900",
});
const geistMono = localFont({
  src: "./fonts/GeistMonoVF.woff",
  variable: "--font-geist-mono",
  weight: "100 900",
});

export const metadata: Metadata = {
  title: "Create Next App",
  description: "Generated by create next app",
};

export default function RootLayout({
  children,
}: Readonly<{
  children: React.ReactNode;
}>) {
  return (
    <html lang="en">
      <head>
        <meta name="viewport" content="width=device-width, initial-scale=1.0" />
      </head>
      <body
        className={`${geistSans.variable} ${geistMono.variable} antialiased`}
      >
        <AuthProvider>
          <SidebarProvider>
            <div className="flex h-screen flex-col">
              <Navbar />
              <div className="flex flex-1 overflow-hidden">
                <Sidebar />
                <main className="flex-1 overflow-y-auto bg-gray-50 p-2 sm:p-4 md:p-6 lg:p-8 min-w-0">
                  <div className="max-w-full overflow-hidden">
                    {children}
                  </div>
                </main>
              </div>
            </div>
            {/* Session recovery for token refresh issues */}
            <SessionRecovery />
            {/* Token debugger for development */}
            <TokenDebugger />
          </SidebarProvider>
        </AuthProvider>
      </body>
    </html>
  );
}
