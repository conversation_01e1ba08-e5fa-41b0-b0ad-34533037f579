import { useState, useCallback } from 'react';
import useApi from './useApi';
import { MAIN_URL, PROMOTIONS } from '@/constant/urls';

// Backend promotion model structure
export interface Promotion {
  name: string;
  value: number;
  type: 'percentage' | 'fixed';
  id: number;
  code: string;
  description: string;
  discount_type: 'PERCENTAGE' | 'FIXED';
  discount_value: string;
  min_purchase_amount: string;
  max_discount_amount?: string;
  start_date: string;
  end_date: string;
  startDate: string;
  endDate: string;
  usage_limit?: number;
  times_used: number;
  is_active: boolean;
  created_at: string;
  updated_at: string;
  status: 'active' | 'expired' | 'scheduled';
}

export interface PromotionCreateRequest {
  code: string;
  description: string;
  discount_type: 'PERCENTAGE' | 'FIXED';
  discount_value: string;
  min_purchase_amount: string;
  max_discount_amount?: string;
  start_date: string;
  end_date: string;
  usage_limit?: number;
  is_active?: boolean;
}

export interface PromotionUpdateRequest extends Partial<PromotionCreateRequest> {}

export interface PromotionsResponse {
  count: number;
  next: string | null;
  previous: string | null;
  results: Promotion[];
}

export const usePromotions = () => {
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const { create, update, remove, read } = useApi<PromotionsResponse | Promotion>(MAIN_URL);

  // Get all promotions
  const getPromotions = useCallback(async (params?: {
    page?: number;
    page_size?: number;
    search?: string;
    is_active?: boolean;
  }): Promise<PromotionsResponse | null> => {
    setLoading(true);
    setError(null);
    
    try {
      const queryParams = new URLSearchParams();
      if (params?.page) queryParams.append('page', params.page.toString());
      if (params?.page_size) queryParams.append('page_size', params.page_size.toString());
      if (params?.search) queryParams.append('search', params.search);
      if (params?.is_active !== undefined) queryParams.append('is_active', params.is_active.toString());
      
      const endpoint = queryParams.toString() ? `${PROMOTIONS}?${queryParams}` : PROMOTIONS;
      const response = await read(endpoint);
      
      if (typeof response === 'string') {
        setError(response);
        return null;
      }
      return response as PromotionsResponse;
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : 'Failed to fetch promotions';
      setError(errorMessage);
      return null;
    } finally {
      setLoading(false);
    }
  }, [read]);

  // Get single promotion by code
  const getPromotion = useCallback(async (code: string): Promise<Promotion | null> => {
    setLoading(true);
    setError(null);
    
    try {
      const response = await read(`${PROMOTIONS}${code}/`);
      if (typeof response === 'string') {
        setError(response);
        return null;
      }
      return response as Promotion;
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : 'Failed to fetch promotion';
      setError(errorMessage);
      return null;
    } finally {
      setLoading(false);
    }
  }, [read]);

  // Create new promotion
  const createPromotion = useCallback(async (promotionData: PromotionCreateRequest): Promise<Promotion | null> => {
    setLoading(true);
    setError(null);
    
    try {
      const response = await create(PROMOTIONS, promotionData);
      if (typeof response === 'string') {
        setError(response);
        return null;
      }
      return response as Promotion;
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : 'Failed to create promotion';
      setError(errorMessage);
      return null;
    } finally {
      setLoading(false);
    }
  }, [create]);

  // Update promotion
  const updatePromotion = useCallback(async (code: string, promotionData: PromotionUpdateRequest): Promise<Promotion | null> => {
    setLoading(true);
    setError(null);
    
    try {
      const response = await update(`${PROMOTIONS}${code}/`, promotionData);
      if (typeof response === 'string') {
        setError(response);
        return null;
      }
      return response as Promotion;
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : 'Failed to update promotion';
      setError(errorMessage);
      return null;
    } finally {
      setLoading(false);
    }
  }, [update]);

  // Delete promotion
  const deletePromotion = useCallback(async (code: string): Promise<boolean> => {
    setLoading(true);
    setError(null);
    
    try {
      const response = await remove(`${PROMOTIONS}${code}/`);
      if (typeof response === 'string') {
        setError(response);
        return false;
      }
      return true;
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : 'Failed to delete promotion';
      setError(errorMessage);
      return false;
    } finally {
      setLoading(false);
    }
  }, [remove]);

  return {
    loading,
    error,
    getPromotions,
    getPromotion,
    createPromotion,
    updatePromotion,
    deletePromotion,
  };
};
