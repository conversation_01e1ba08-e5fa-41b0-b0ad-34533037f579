"use client";

import { useState, useEffect, useCallback } from 'react';
import { useSession } from 'next-auth/react';
import useApi from './useApi';
import { MAIN_URL, ADMIN_ORDERS } from '@/constant/urls';

export interface OrderItem {
  id: string;
  product_name: string;
  unit_price: number;
  total_price: number;
  product: {
    id: string;
    name: string;
    price: number;
  };
  quantity: number;
  price: number;
}

export interface Order {
  id: string;
  user: {
    name:string;
    id: string;
    first_name: string;
    last_name: string;
    email: string;
  };
  status: string;
  gst_amount: number;
  cgst_amount: number;
  sgst_amount: number;
  igst_amount: number;
  items: OrderItem[];
  subtotal: number;
  shipping_cost: number;
  total: number;
  tracking_number?: string;
  estimated_delivery_date?: string;
  notes?: string;
  created_at: string;
  updated_at: string;
  shipping_address?: {
    street_address: string;
    city: string;
    state: string;
    postal_code: string;
    country: string;
  };
  billing_address?: {
    street_address: string;
    city: string;
    state: string;
    postal_code: string;
    country: string;
  };
}

interface OrdersState {
  orders: Order[];
  loading: boolean;
  error: string | null;
  pagination: {
    count: number;
    next: string | null;
    previous: string | null;
    page: number;
    totalPages: number;
  };
}

interface OrderFilters {
  status?: string;
  search?: string;
  date_from?: string;
  date_to?: string;
  page?: number;
  page_size?: number;
}

export const useOrders = () => {
  const { data: session, status } = useSession();
  const [ordersState, setOrdersState] = useState<OrdersState>({
    orders: [],
    loading: true,
    error: null,
    pagination: {
      count: 0,
      next: null,
      previous: null,
      page: 1,
      totalPages: 1,
    },
  });

  const { read, update } = useApi(MAIN_URL);

  const fetchOrders = useCallback(async (filters: OrderFilters = {}) => {
    // Only fetch if authenticated
    if (status !== 'authenticated') {
      return;
    }

    try {
      setOrdersState(prev => ({
        ...prev,
        loading: true,
        error: null
      }));

      // Build query parameters
      const queryParams = new URLSearchParams();
      
      if (filters.status) queryParams.append('status', filters.status);
      if (filters.search) queryParams.append('search', filters.search);
      if (filters.date_from) queryParams.append('date_from', filters.date_from);
      if (filters.date_to) queryParams.append('date_to', filters.date_to);
      if (filters.page) queryParams.append('page', filters.page.toString());
      if (filters.page_size) queryParams.append('page_size', filters.page_size.toString());

      const endpoint = `${ADMIN_ORDERS}${queryParams.toString() ? `?${queryParams.toString()}` : ''}`;
      const result = await read(endpoint);

      if (typeof result === 'string') {
        throw new Error(result);
      }

      const ordersData = result as any;

      setOrdersState(prev => ({
        ...prev,
        orders: ordersData.results || ordersData,
        loading: false,
        pagination: {
          count: ordersData.count || 0,
          next: ordersData.next || null,
          previous: ordersData.previous || null,
          page: filters.page || 1,
          totalPages: Math.ceil((ordersData.count || 0) / (filters.page_size || 20)),
        }
      }));
    } catch (error) {
      console.error('Error fetching orders:', error);
      setOrdersState(prev => ({
        ...prev,
        loading: false,
        error: 'Failed to load orders'
      }));
    }
  }, [read, status]);

  const updateOrderStatus = useCallback(async (orderId: string, newStatus: string) => {
    if (status !== 'authenticated') {
      return false;
    }

    try {
      const result = await update(`${ADMIN_ORDERS}${orderId}/update-status/`, {
        status: newStatus
      });

      if (typeof result === 'string') {
        throw new Error(result);
      }

      // Update the order in the local state
      setOrdersState(prev => ({
        ...prev,
        orders: prev.orders.map(order =>
          order.id === orderId ? { ...order, status: newStatus } : order
        )
      }));

      return true;
    } catch (error) {
      console.error('Error updating order status:', error);
      return false;
    }
  }, [update, status]);

  const refundOrder = useCallback(async (orderId: string) => {
    if (status !== 'authenticated') {
      return false;
    }

    try {
      const result = await update(`${ADMIN_ORDERS}${orderId}/refund/`, {});

      if (typeof result === 'string') {
        throw new Error(result);
      }

      // Update the order status to refunded in local state
      setOrdersState(prev => ({
        ...prev,
        orders: prev.orders.map(order =>
          order.id === orderId ? { ...order, status: 'REFUNDED' } : order
        )
      }));

      return true;
    } catch (error) {
      console.error('Error refunding order:', error);
      return false;
    }
  }, [update, status]);

  useEffect(() => {
    fetchOrders();
  }, [fetchOrders]);

  return {
    ...ordersState,
    fetchOrders,
    updateOrderStatus,
    refundOrder,
    refreshOrders: () => fetchOrders(),
  };
};
