@import url('https://fonts.googleapis.com/css2?family=Inter:wght@400;500;600;700&display=swap');

@tailwind base;
@tailwind components;
@tailwind utilities;

@layer base {
  :root {
    --background: 0 0% 100%;
    --foreground: 222.2 84% 4.9%;
    --card: 0 0% 100%;
    --card-foreground: 222.2 84% 4.9%;
    --popover: 0 0% 100%;
    --popover-foreground: 222.2 84% 4.9%;
    --primary: 221.2 83.2% 53.3%;
    --primary-foreground: 210 40% 98%;
    --secondary: 210 40% 96.1%;
    --secondary-foreground: 222.2 47.4% 11.2%;
    --muted: 210 40% 96.1%;
    --muted-foreground: 215.4 16.3% 46.9%;
    --accent: 210 40% 96.1%;
    --accent-foreground: 222.2 47.4% 11.2%;
    --destructive: 0 84.2% 60.2%;
    --destructive-foreground: 210 40% 98%;
    --border: 214.3 31.8% 91.4%;
    --input: 214.3 31.8% 91.4%;
    --ring: 221.2 83.2% 53.3%;
    --radius: 0.5rem;
  }
}

@layer base {
  * {
    @apply border-border;
  }
  body {
    @apply bg-background text-foreground;
    font-feature-settings: "rlig" 1, "calt" 1;
    font-family: 'Inter', sans-serif;
  }
}

#root {
  max-width: 1280px;
  margin: 0 auto;
  padding: 2rem;
  text-align: center;
}

.logo {
  height: 6em;
  padding: 1.5em;
  will-change: filter;
  transition: filter 300ms;
}
.logo:hover {
  filter: drop-shadow(0 0 2em #646cffaa);
}
.logo.react:hover {
  filter: drop-shadow(0 0 2em #61dafbaa);
}

@keyframes logo-spin {
  from {
    transform: rotate(0deg);
  }
  to {
    transform: rotate(360deg);
  }
}

@media (prefers-reduced-motion: no-preference) {
  a:nth-of-type(2) .logo {
    animation: logo-spin infinite 20s linear;
  }
}

.card {
  padding: 2em;
}

.read-the-docs {
  color: #888;
}

@layer utilities {
  .border-custom {
    border-color: hsl(var(--border));
  }

  /* Responsive text utilities */
  .text-responsive-xs {
    @apply text-xs sm:text-sm;
  }

  .text-responsive-sm {
    @apply text-sm sm:text-base;
  }

  .text-responsive-base {
    @apply text-base sm:text-lg;
  }

  .text-responsive-lg {
    @apply text-lg sm:text-xl;
  }

  .text-responsive-xl {
    @apply text-xl sm:text-2xl;
  }

  /* Responsive spacing utilities */
  .space-responsive {
    @apply space-y-3 sm:space-y-4 lg:space-y-6;
  }

  .gap-responsive {
    @apply gap-3 sm:gap-4 lg:gap-6;
  }

  .p-responsive {
    @apply p-3 sm:p-4 lg:p-6;
  }

  .px-responsive {
    @apply px-3 sm:px-4 lg:px-6;
  }

  .py-responsive {
    @apply py-3 sm:py-4 lg:py-6;
  }

  /* Chart container responsive */
  .chart-container {
    @apply h-[250px] sm:h-[300px] lg:h-[350px];
  }

  .chart-container-sm {
    @apply h-[200px] sm:h-[250px] lg:h-[300px];
  }

  /* Mobile-first grid utilities */
  .grid-responsive-2 {
    @apply grid grid-cols-1 sm:grid-cols-2;
  }

  .grid-responsive-3 {
    @apply grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3;
  }

  .grid-responsive-4 {
    @apply grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-4;
  }

  /* Ensure proper text wrapping and overflow handling */
  .text-safe {
    @apply break-words overflow-hidden;
  }

  .truncate-safe {
    @apply truncate min-w-0;
  }
}

