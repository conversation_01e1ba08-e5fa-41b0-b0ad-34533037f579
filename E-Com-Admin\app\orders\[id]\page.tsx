"use client"
import { useState } from "react";
import { <PERSON>, <PERSON><PERSON><PERSON>nt, CardH<PERSON>er, Card<PERSON>itle } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Separator } from "@/components/ui/separator";
import { Badge } from "@/components/ui/badge";
import { Input } from "@/components/ui/input";
import { useToast } from "@/components/ui/use-toast";
import { OrderStatusSelect } from "@/components/orders/OrderStatusSelect";

import { Package, RefreshCcw, Truck, ArrowLeft, Loader2 } from "lucide-react";
import { useParams, useRouter } from "next/navigation";
import { useOrderDetail } from "@/hooks/useOrderDetail";
import { getOrderStatusColor, canRefundOrder } from "@/lib/orderUtils";

const OrderDetails = () => {
  const { id } = useParams();
  const router = useRouter();
  const { toast } = useToast();
  const [trackingNumber, setTrackingNumber] = useState("");

  // Get order ID from params
  const orderId = Array.isArray(id) ? id[0] : id;

  // Use the order detail hook
  const {
    order,
    loading,
    error,
    updateOrderStatus,
    updateTrackingNumber,
    refundOrder,
    refreshOrder
  } = useOrderDetail(orderId || "");

  const handleStatusUpdate = async (newStatus: string) => {
    const success = await updateOrderStatus(newStatus);
    if (success) {
      toast({
        title: "Order status updated",
        description: `Order ${orderId} status changed to ${newStatus}`,
      });
    } else {
      toast({
        title: "Error",
        description: "Failed to update order status",
        variant: "destructive",
      });
    }
  };

  const handleRefund = async () => {
    const success = await refundOrder();
    if (success) {
      toast({
        title: "Refund initiated",
        description: `Refund process started for order ${orderId}`,
      });
    } else {
      toast({
        title: "Error",
        description: "Failed to process refund",
        variant: "destructive",
      });
    }
  };

  const handleUpdateTracking = async () => {
    if (trackingNumber.trim()) {
      const success = await updateTrackingNumber(trackingNumber.trim());
      if (success) {
        toast({
          title: "Tracking number updated",
          description: `Tracking number ${trackingNumber} added to order ${orderId}`,
        });
        setTrackingNumber(""); // Clear the input after successful update
      } else {
        toast({
          title: "Error",
          description: "Failed to update tracking number",
          variant: "destructive",
        });
      }
    }
  };

  // Helper functions
  const formatCustomerName = () => {
    if (!order?.user) return "N/A";
    return order.user.name || `${order.user.first_name} ${order.user.last_name}`.trim() || order.user.email;
  };

  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString('en-US', {
      year: 'numeric',
      month: 'long',
      day: 'numeric',
      hour: '2-digit',
      minute: '2-digit'
    });
  };

  // Loading state
  if (loading) {
    return (
      <div className="flex items-center justify-center min-h-[400px]">
        <div className="flex items-center space-x-2">
          <Loader2 className="h-6 w-6 animate-spin" />
          <span>Loading order details...</span>
        </div>
      </div>
    );
  }

  // Error state
  if (error) {
    return (
      <div className="flex flex-col items-center justify-center min-h-[400px] space-y-4">
        <div className="text-center">
          <h2 className="text-2xl font-bold text-gray-900">Error Loading Order</h2>
          <p className="text-gray-600 mt-2">{error}</p>
        </div>
        <div className="flex space-x-2">
          <Button onClick={refreshOrder} variant="outline">
            Try Again
          </Button>
          <Button onClick={() => router.push('/orders')} variant="default">
            Back to Orders
          </Button>
        </div>
      </div>
    );
  }

  // Order not found
  if (!order) {
    return (
      <div className="flex flex-col items-center justify-center min-h-[400px] space-y-4">
        <div className="text-center">
          <h2 className="text-2xl font-bold text-gray-900">Order Not Found</h2>
          <p className="text-gray-600 mt-2">The order you&apos;re looking for doesn&apos;t exist or you don&apos;t have permission to view it.</p>
        </div>
        <Button onClick={() => router.push('/orders')} variant="default">
          Back to Orders
        </Button>
      </div>
    );
  }

  return (
    <div className="space-y-6 animate-fadeIn">
      <div className="flex items-center justify-between">
        <div className="flex items-center space-x-4">
          <Button
            variant="ghost"
            size="icon"
            onClick={() => router.push('/orders')}
          >
            <ArrowLeft className="h-4 w-4" />
          </Button>
          <div>
            <h1 className="text-3xl font-bold">Order Details</h1>
            <p className="text-sm text-muted-foreground">
              Order ID: {order.id.slice(0, 8)}... • Created: {formatDate(order.created_at)}
            </p>
          </div>
        </div>
        <div className="flex space-x-2">
          <Button
            variant="outline"
            onClick={handleRefund}
            disabled={!canRefundOrder(order.status)}
          >
            <RefreshCcw className="mr-2 h-4 w-4" />
            Process Refund
          </Button>
          <OrderStatusSelect
            currentStatus={order.status}
            onStatusChange={handleStatusUpdate}
          />
        </div>
      </div>

      <div className="grid gap-6 md:grid-cols-2">
        <div className="space-y-6">
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center justify-between">
                Customer Information
                <Badge className={getOrderStatusColor(order.status)}>
                  {order.status}
                </Badge>
              </CardTitle>
            </CardHeader>
            <CardContent className="space-y-4">
              <div>
                <h3 className="font-medium">{formatCustomerName()}</h3>
                <p className="text-sm text-muted-foreground">{order.user.email}</p>
              </div>
              <Separator />
              {order.shipping_address && (
                <div>
                  <h3 className="font-medium">Shipping Address</h3>
                  <p className="text-sm text-muted-foreground">
                    {order.shipping_address.street_address}
                    <br />
                    {order.shipping_address.city}, {order.shipping_address.state}{" "}
                    {order.shipping_address.postal_code}
                    <br />
                    {order.shipping_address.country}
                  </p>
                </div>
              )}
              {order.billing_address && (
                <>
                  <Separator />
                  <div>
                    <h3 className="font-medium">Billing Address</h3>
                    <p className="text-sm text-muted-foreground">
                      {order.billing_address.street_address}
                      <br />
                      {order.billing_address.city}, {order.billing_address.state}{" "}
                      {order.billing_address.postal_code}
                      <br />
                      {order.billing_address.country}
                    </p>
                  </div>
                </>
              )}
            </CardContent>
          </Card>

          <Card>
            <CardHeader>
              <CardTitle>Payment & Order Summary</CardTitle>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="space-y-2">
                <div className="flex justify-between text-sm">
                  <span>Subtotal</span>
                  <span>₹{Number(order.subtotal).toFixed(2)}</span>
                </div>
                <div className="flex justify-between text-sm">
                  <span>GST</span>
                  <span>₹{Number(order.gst_amount).toFixed(2)}</span>
                </div>
                <div className="flex justify-between text-sm">
                  <span>Shipping</span>
                  <span>₹{Number(order.shipping_cost).toFixed(2)}</span>
                </div>
                <Separator />
                <div className="flex justify-between font-medium">
                  <span>Total</span>
                  <span>₹{Number(order.total).toFixed(2)}</span>
                </div>
              </div>
              {order.notes && (
                <>
                  <Separator />
                  <div>
                    <h3 className="font-medium">Order Notes</h3>
                    <p className="text-sm text-muted-foreground">{order.notes}</p>
                  </div>
                </>
              )}
            </CardContent>
          </Card>
        </div>

        <div className="space-y-6">
          <Card>
            <CardHeader>
              <CardTitle>Order Items ({order.items.length})</CardTitle>
            </CardHeader>
            <CardContent className="space-y-4">
              {order.items.map((item, index) => (
                <div key={item.id || index} className="flex justify-between items-start">
                  <div className="flex-1">
                    <h3 className="font-medium">{item.product_name}</h3>
                    <p className="text-sm text-muted-foreground">
                      Quantity: {item.quantity} × ₹{Number(item.unit_price).toFixed(2)}
                    </p>
                  </div>
                  <div className="text-right">
                    <p className="font-medium">₹{Number(item.total_price).toFixed(2)}</p>
                  </div>
                </div>
              ))}
            </CardContent>
          </Card>

          <Card>
            <CardHeader className="flex flex-row items-center justify-between">
              <CardTitle>Shipment Tracking</CardTitle>
              <Package className="h-5 w-5 text-muted-foreground" />
            </CardHeader>
            <CardContent className="space-y-4">
              {order.tracking_number && (
                <div className="p-3 bg-blue-50 rounded-lg">
                  <div className="flex items-center justify-between">
                    <div>
                      <p className="text-sm font-medium text-blue-900">Current Tracking Number</p>
                      <p className="text-lg font-mono text-blue-700">{order.tracking_number}</p>
                    </div>
                  </div>
                </div>
              )}
              <div className="flex space-x-2">
                <Input
                  type="text"
                  placeholder={order.tracking_number ? "Update tracking number" : "Enter tracking number"}
                  value={trackingNumber}
                  onChange={(e) => setTrackingNumber(e.target.value)}
                />
                <Button onClick={handleUpdateTracking} disabled={!trackingNumber.trim()}>
                  <Truck className="mr-2 h-4 w-4" />
                  {order.tracking_number ? "Update" : "Add"}
                </Button>
              </div>
              {order.estimated_delivery_date && (
                <div className="text-sm text-muted-foreground">
                  <strong>Estimated Delivery:</strong> {formatDate(order.estimated_delivery_date)}
                </div>
              )}
            </CardContent>
          </Card>
        </div>
      </div>
    </div>
  );
};

export default OrderDetails;