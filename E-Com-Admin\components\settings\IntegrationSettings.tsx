"use client";
import { zod<PERSON>esolver } from "@hookform/resolvers/zod";
import { useForm } from "react-hook-form";
import * as z from "zod";
import { Button } from "@/components/ui/button";
import { Switch } from "@/components/ui/switch";
import { Input } from "@/components/ui/input";
import { Form, FormControl, FormDescription, FormField, FormItem, FormLabel, FormMessage } from "@/components/ui/form";
import { useToast } from "@/components/ui/use-toast";

const formSchema = z.object({
  stripeEnabled: z.boolean(),
  stripeKey: z.string().optional(),
  paypalEnabled: z.boolean(),
  paypalKey: z.string().optional(),
  googleAnalyticsEnabled: z.boolean(),
  googleAnalyticsId: z.string().optional(),
});

export const IntegrationSettings = () => {
  const { toast } = useToast();
  const form = useForm<z.infer<typeof formSchema>>({
    resolver: zodResolver(formSchema),
    defaultValues: {
      stripeEnabled: false,
      stripeKey: "",
      paypalEnabled: false,
      paypalKey: "",
      googleAnalyticsEnabled: false,
      googleAnalyticsId: "",
    },
  });

  const onSubmit = (values: z.infer<typeof formSchema>) => {
    console.log(values);
    toast({
      title: "Settings saved",
      description: "Your integration settings have been updated successfully.",
    });
  };

  return (
    <Form {...form}>
      <form onSubmit={form.handleSubmit(onSubmit)} className="space-y-6">
        <div className="space-y-4">
          <div className="rounded-lg border p-4">
            <FormField
              control={form.control}
              name="stripeEnabled"
              render={({ field }) => (
                <FormItem className="flex flex-row items-center justify-between mb-4">
                  <div className="space-y-0.5">
                    <FormLabel className="text-base">Stripe Integration</FormLabel>
                    <FormDescription>
                      Accept payments through Stripe
                    </FormDescription>
                  </div>
                  <FormControl>
                    <Switch
                      checked={field.value}
                      onCheckedChange={field.onChange}
                    />
                  </FormControl>
                </FormItem>
              )}
            />
            {form.watch("stripeEnabled") && (
              <FormField
                control={form.control}
                name="stripeKey"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>API Key</FormLabel>
                    <FormControl>
                      <Input {...field} type="password" />
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />
            )}
          </div>

          <div className="rounded-lg border p-4">
            <FormField
              control={form.control}
              name="paypalEnabled"
              render={({ field }) => (
                <FormItem className="flex flex-row items-center justify-between mb-4">
                  <div className="space-y-0.5">
                    <FormLabel className="text-base">PayPal Integration</FormLabel>
                    <FormDescription>
                      Accept payments through PayPal
                    </FormDescription>
                  </div>
                  <FormControl>
                    <Switch
                      checked={field.value}
                      onCheckedChange={field.onChange}
                    />
                  </FormControl>
                </FormItem>
              )}
            />
            {form.watch("paypalEnabled") && (
              <FormField
                control={form.control}
                name="paypalKey"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>API Key</FormLabel>
                    <FormControl>
                      <Input {...field} type="password" />
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />
            )}
          </div>

          <div className="rounded-lg border p-4">
            <FormField
              control={form.control}
              name="googleAnalyticsEnabled"
              render={({ field }) => (
                <FormItem className="flex flex-row items-center justify-between mb-4">
                  <div className="space-y-0.5">
                    <FormLabel className="text-base">Google Analytics</FormLabel>
                    <FormDescription>
                      Track website analytics
                    </FormDescription>
                  </div>
                  <FormControl>
                    <Switch
                      checked={field.value}
                      onCheckedChange={field.onChange}
                    />
                  </FormControl>
                </FormItem>
              )}
            />
            {form.watch("googleAnalyticsEnabled") && (
              <FormField
                control={form.control}
                name="googleAnalyticsId"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>Tracking ID</FormLabel>
                    <FormControl>
                      <Input {...field} />
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />
            )}
          </div>
        </div>

        <Button type="submit">Save Changes</Button>
      </form>
    </Form>
  );
};