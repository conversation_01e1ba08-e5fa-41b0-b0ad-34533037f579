import React from "react";

export const CartLoading = () => {
  return (
    <div id="webcrumbs">
      <div className="w-full bg-white rounded-lg shadow p-6">
        {" "}
        <h1 className="text-2xl font-title mb-6">Shopping Cart</h1>
        {/* Cart Items List */}
        <div className="flex flex-col lg:flex-row justify-center gap-4">
          <div className="flex w-full lg:w-3/4 flex-col gap-4 animate-pulse">
            {/* Item 1 */}
            <div className="flex justify-between items-center border border-neutral-300 rounded-md p-4">
              <div className="flex items-center gap-4">
                <div className="w-[60px] h-[60px] bg-neutral-200 rounded-md"></div>
                <div>
                  <div className="h-4 w-[200px] bg-neutral-200 rounded-md mb-2"></div>
                  <div className="h-3 w-[150px] bg-neutral-200 rounded-md"></div>
                </div>
              </div>
              <div className="text-right">
                <div className="h-4 w-[60px] bg-neutral-200 rounded-md mb-4"></div>
                <div className="flex items-center gap-4 mt-2">
                  <div className="flex items-center border border-neutral-300 rounded-md px-2 py-1">
                    <div className="h-4 w-[20px] bg-neutral-200"></div>
                    <div className="h-4 w-[20px] bg-neutral-200 mx-2"></div>
                    <div className="h-4 w-[20px] bg-neutral-200"></div>
                  </div>
                  <div className="h-4 w-[20px] bg-neutral-200 rounded-md"></div>
                </div>
              </div>
            </div>

            {/* Item 2 */}
            <div className="flex justify-between items-center border border-neutral-300 rounded-md p-4">
              <div className="flex items-center gap-4">
                <div className="w-[60px] h-[60px] bg-neutral-200 rounded-md"></div>
                <div>
                  <div className="h-4 w-[200px] bg-neutral-200 rounded-md mb-2"></div>
                  <div className="h-3 w-[150px] bg-neutral-200 rounded-md"></div>
                </div>
              </div>
              <div className="text-right">
                <div className="h-4 w-[60px] bg-neutral-200 rounded-md mb-4"></div>
                <div className="flex items-center gap-4 mt-2">
                  <div className="flex items-center border border-neutral-300 rounded-md px-2 py-1">
                    <div className="h-4 w-[20px] bg-neutral-200"></div>
                    <div className="h-4 w-[20px] bg-neutral-200 mx-2"></div>
                    <div className="h-4 w-[20px] bg-neutral-200"></div>
                  </div>
                  <div className="h-4 w-[20px] bg-neutral-200 rounded-md"></div>
                </div>
              </div>
            </div>

            {/* Item 3 */}
            <div className="flex justify-between items-center border border-neutral-300 rounded-md p-4">
              <div className="flex items-center gap-4">
                <div className="w-[60px] h-[60px] bg-neutral-200 rounded-md"></div>
                <div>
                  <div className="h-4 w-[200px] bg-neutral-200 rounded-md mb-2"></div>
                  <div className="h-3 w-[150px] bg-neutral-200 rounded-md"></div>
                </div>
              </div>
              <div className="text-right">
                <div className="h-4 w-[60px] bg-neutral-200 rounded-md mb-4"></div>
                <div className="flex items-center gap-4 mt-2">
                  <div className="flex items-center border border-neutral-300 rounded-md px-2 py-1">
                    <div className="h-4 w-[20px] bg-neutral-200"></div>
                    <div className="h-4 w-[20px] bg-neutral-200 mx-2"></div>
                    <div className="h-4 w-[20px] bg-neutral-200"></div>
                  </div>
                  <div className="h-4 w-[20px] bg-neutral-200 rounded-md"></div>
                </div>
              </div>
            </div>
          </div>
          {/* Shopping and Order Summary */}
          <div className="flex w-full lg:w-1/4 lg:flex-col justify-between items-start mt-6">
            {/* Continue Shopping */}
            <div className="flex items-center gap-2">
              <div className="h-8 w-8 bg-neutral-200 rounded-full"></div>
              <div className="h-4 w-[150px] bg-neutral-200 rounded-md"></div>
            </div>

            {/* Order Summary */}
            <div className="border border-neutral-300 rounded-md p-4 w-[300px] animate-pulse">
              <div className="h-4 w-[150px] bg-neutral-200 rounded-md mb-6"></div>
              <div className="flex justify-between mb-4">
                <div className="h-4 w-[80px] bg-neutral-200 rounded-md"></div>
                <div className="h-4 w-[50px] bg-neutral-200 rounded-md"></div>
              </div>
              <div className="flex justify-between mb-4">
                <div className="h-4 w-[80px] bg-neutral-200 rounded-md"></div>
                <div className="h-4 w-[50px] bg-neutral-200 rounded-md"></div>
              </div>
              <div className="flex justify-between mb-6">
                <div className="h-4 w-[100px] bg-neutral-200 rounded-md"></div>
                <div className="h-4 w-[50px] bg-neutral-200 rounded-md"></div>
              </div>
              <div className="h-10 bg-neutral-200 rounded-md"></div>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};
