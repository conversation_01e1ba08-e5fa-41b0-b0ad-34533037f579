# from rest_framework import serializers
# from .models import Promotion, PromotionUsage

# class PromotionSerializer(serializers.ModelSerializer):
#     is_valid = serializers.BooleanField(read_only=True)

#     class Meta:
#         model = Promotion
#         fields = [
#             'id', 'code', 'description', 'discount_type', 'discount_value',
#             'min_purchase_amount', 'max_discount_amount', 'start_date',
#             'end_date', 'usage_limit', 'times_used', 'is_active', 'is_valid',
#             'created_at', 'updated_at'
#         ]
#         read_only_fields = ['times_used']

#     def validate(self, data):
#         if data['start_date'] >= data['end_date']:
#             raise serializers.ValidationError(
#                 {"end_date": "End date must be after start date"}
#             )
#         if data['discount_type'] == 'PERCENTAGE' and data['discount_value'] > 100:
#             raise serializers.ValidationError(
#                 {"discount_value": "Percentage discount cannot be greater than 100"}
#             )
#         return data

# class PromotionUsageSerializer(serializers.ModelSerializer):
#     promotion_code = serializers.CharField(source='promotion.code', read_only=True)
#     user_email = serializers.CharField(source='user.email', read_only=True)

#     class Meta:
#         model = PromotionUsage
#         fields = ['id', 'promotion', 'promotion_code', 'user', 'user_email',
#                  'order', 'discount_amount', 'created_at']
#         read_only_fields = ['discount_amount']

from rest_framework import serializers
from .models import Promotion, PromotionUsage
from decimal import Decimal


class PromotionSerializer(serializers.ModelSerializer):
    is_valid = serializers.BooleanField(read_only=True)
    times_used = serializers.IntegerField(read_only=True)

    class Meta:
        model = Promotion
        fields = [
            "id",
            "code",
            "description",
            "discount_type",
            "discount_value",
            "min_purchase_amount",
            "max_discount_amount",
            "start_date",
            "end_date",
            "usage_limit",
            "times_used",
            "is_active",
            "is_valid",
            "created_at",
            "updated_at",
        ]

    def validate(self, data):
        if "start_date" in data and "end_date" in data:
            if data["start_date"] >= data["end_date"]:
                raise serializers.ValidationError(
                    {"end_date": "End date must be after start date"}
                )

        if "discount_type" in data and "discount_value" in data:
            if data["discount_type"] == "PERCENTAGE":
                if not (0 <= Decimal(str(data["discount_value"])) <= 100):
                    raise serializers.ValidationError(
                        {
                            "discount_value": "Percentage discount must be between 0 and 100"
                        }
                    )
            else:
                if data["discount_value"] < 0:
                    raise serializers.ValidationError(
                        {"discount_value": "Fixed discount cannot be negative"}
                    )

        if "max_discount_amount" in data and data["max_discount_amount"] is not None:
            if data["max_discount_amount"] < 0:
                raise serializers.ValidationError(
                    {
                        "max_discount_amount": "Maximum discount amount cannot be negative"
                    }
                )

        return data


class PromotionUsageSerializer(serializers.ModelSerializer):
    promotion_code = serializers.CharField(source="promotion.code", read_only=True)
    user_email = serializers.EmailField(source="user.email", read_only=True)

    class Meta:
        model = PromotionUsage
        fields = [
            "id",
            "promotion",
            "promotion_code",
            "user",
            "user_email",
            "order",
            "discount_amount",
            "created_at",
        ]
        read_only_fields = ["discount_amount", "user", "promotion_code", "user_email"]


class ApplyPromotionSerializer(serializers.Serializer):
    code = serializers.CharField(required=True)


class PromotionCodeOnlySerializer(serializers.ModelSerializer):
    is_valid = serializers.BooleanField(read_only=True)

    class Meta:
        model = Promotion
        fields = ["id", "code", "is_valid", "description"]
