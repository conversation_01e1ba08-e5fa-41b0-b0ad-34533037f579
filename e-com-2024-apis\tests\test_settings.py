"""
Test settings for the e-commerce API.
This file overrides settings from the main settings.py for testing purposes.
"""

from backend.settings import *

# Use SQLite for testing - completely isolated from production
DATABASES = {
    'default': {
        'ENGINE': 'django.db.backends.sqlite3',
        'NAME': ':memory:',  # Use in-memory database for faster tests
        'TEST': {
            'NAME': ':memory:',
        },
    }
}

# Force test database creation
DATABASE_ROUTERS = []

# Use a faster password hasher for testing
PASSWORD_HASHERS = [
    'django.contrib.auth.hashers.MD5PasswordHasher',
]

# Use the console email backend for testing
EMAIL_BACKEND = 'django.core.mail.backends.console.EmailBackend'

# Disable problematic middleware for testing
MIDDLEWARE = [
    'django.middleware.security.SecurityMiddleware',
    'corsheaders.middleware.CorsMiddleware',
    'django.contrib.sessions.middleware.SessionMiddleware',
    'django.middleware.common.CommonMiddleware',
    # 'django.middleware.csrf.CsrfViewMiddleware',  # Disabled for testing
    'django.contrib.auth.middleware.AuthenticationMiddleware',
    'django.contrib.messages.middleware.MessageMiddleware',
    # 'django.middleware.clickjacking.XFrameOptionsMiddleware',  # Disabled for testing
]

# Disable trailing slash redirects for tests
APPEND_SLASH = False

# Allow testserver for tests
ALLOWED_HOSTS = ['testserver', 'localhost', '127.0.0.1', '*']

# Test-specific settings
PHONEPE_CLIENT_ID = 'TEST_CLIENT_ID'
PHONEPE_CLIENT_SECRET = 'TEST_CLIENT_SECRET'
PHONEPE_CALLBACK_URL = 'http://testserver/api/v1/payments/phonepe/callback/'
PHONEPE_ENVIRONMENT = 'UAT'
FRONTEND_URL = 'http://testserver'

# Database settings for tests
DB_NAME = 'test_triumph'
DB_USER = 'test_user'
DB_PASSWORD = 'test_password'
DB_HOST = 'localhost'
DB_PORT = '5432'

# Email settings for tests - use console backend
EMAIL_BACKEND = 'django.core.mail.backends.console.EmailBackend'
DEFAULT_FROM_EMAIL = '<EMAIL>'
EMAIL_HOST = 'localhost'
EMAIL_PORT = 25

# Email timeout settings to prevent long hanging connections
EMAIL_TIMEOUT = 30  # 30 seconds timeout

# Use LocMem cache for tests (needed for throttling tests)
CACHES = {
    'default': {
        'BACKEND': 'django.core.cache.backends.locmem.LocMemCache',
        'LOCATION': 'test-cache',
    }
}

# Disable Redis for tests
REDIS_URL = None
USE_REDIS = False

# Disable MinIO for tests
USE_MINIO = False
DEFAULT_FILE_STORAGE = 'django.core.files.storage.FileSystemStorage'
STATICFILES_STORAGE = 'django.contrib.staticfiles.storage.StaticFilesStorage'

# Test-specific logging configuration
LOGGING = {
    'version': 1,
    'disable_existing_loggers': False,
    'handlers': {
        'console': {
            'class': 'logging.StreamHandler',
        },
    },
    'root': {
        'handlers': ['console'],
        'level': 'WARNING',
    },
    'loggers': {
        'django': {
            'handlers': ['console'],
            'level': 'WARNING',
            'propagate': False,
        },
    },
}

# Disable file logging for tests
DISABLE_FILE_LOGGING = True

# Test-specific security settings
SECURE_SSL_REDIRECT = False
SECURE_HSTS_SECONDS = 0
SECURE_HSTS_INCLUDE_SUBDOMAINS = False
SECURE_HSTS_PRELOAD = False
SECURE_CONTENT_TYPE_NOSNIFF = False
SECURE_BROWSER_XSS_FILTER = False
SESSION_COOKIE_SECURE = False
CSRF_COOKIE_SECURE = False

# Ensure test database is created fresh each time
TEST_RUNNER = 'django.test.runner.DiscoverRunner'

# Disable token_blacklist migrations completely for tests
# This is safe because tests use fresh databases with no existing tokens
MIGRATION_MODULES = {
    'token_blacklist': None,
}
