import { useState, useCallback, useEffect } from 'react';
import { useSession } from 'next-auth/react';
import useApi from './useApi';
import { MAIN_URL, ADMIN_ORDERS } from '@/constant/urls';

export interface CustomerOrder {
  id: string;
  status: string;
  total: number;
  subtotal: number;
  shipping_cost: number;
  gst_amount: number;
  cgst_amount: number;
  sgst_amount: number;
  igst_amount: number;
  tracking_number?: string;
  estimated_delivery_date?: string;
  notes?: string;
  created_at: string;
  updated_at: string;
  user: {
    id: string;
    name: string;
    email: string;
    first_name: string;
    last_name: string;
  };
  items: Array<{
    id: string;
    product_name: string;
    variant_name?: string;
    quantity: number;
    unit_price: number;
    total_price: number;
    product_image?: string;
  }>;
  shipping_address?: {
    street_address: string;
    city: string;
    state: string;
    postal_code: string;
    country: string;
  };
  billing_address?: {
    street_address: string;
    city: string;
    state: string;
    postal_code: string;
    country: string;
  };
}

interface CustomerOrdersResponse {
  results: CustomerOrder[];
  count: number;
  next?: string;
  previous?: string;
}

interface UseCustomerOrdersReturn {
  orders: CustomerOrder[];
  loading: boolean;
  error: string | null;
  fetchCustomerOrders: (customerEmail: string) => Promise<void>;
  totalOrders: number;
  totalSpent: number;
}

export const useCustomerOrders = (): UseCustomerOrdersReturn => {
  const [orders, setOrders] = useState<CustomerOrder[]>([]);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [totalOrders, setTotalOrders] = useState(0);
  const [totalSpent, setTotalSpent] = useState(0);
  
  const { data: session, status } = useSession();
  const { read } = useApi<CustomerOrdersResponse>(MAIN_URL);

  const fetchCustomerOrders = useCallback(async (customerEmail: string) => {
    if (status !== 'authenticated' || !customerEmail || !session?.user) {
      console.log('Cannot fetch orders: not authenticated or missing email');
      return;
    }

    setLoading(true);
    setError(null);

    try {
      console.log('Fetching orders for customer:', customerEmail);
      
      // Use the admin orders endpoint with search parameter
      const queryParams = new URLSearchParams();
      queryParams.append('search', customerEmail);
      queryParams.append('page_size', '100'); // Get more orders for complete history
      
      const endpoint = `${ADMIN_ORDERS}?${queryParams.toString()}`;
      const result = await read(endpoint);

      console.log('Customer orders API response:', result);

      if (typeof result === 'string') {
        console.warn('API returned error string:', result);
        // Don't throw error, just set empty state
        setOrders([]);
        setTotalOrders(0);
        setTotalSpent(0);
        return;
      }

      if (result) {
        let ordersData: CustomerOrder[] = [];

        // Handle different response structures
        if (Array.isArray(result)) {
          ordersData = result;
        } else if (result.results && Array.isArray(result.results)) {
          ordersData = result.results;
        } else {
          console.warn('Unexpected orders API response structure:', result);
          ordersData = [];
        }

        console.log('Processed customer orders:', ordersData);
        
        // Calculate totals
        const totalOrdersCount = ordersData.length;
        const totalSpentAmount = ordersData.reduce((sum, order) => sum + (order.total || 0), 0);
        
        setOrders(ordersData);
        setTotalOrders(totalOrdersCount);
        setTotalSpent(totalSpentAmount);
      }
    } catch (err) {
      console.error('Error fetching customer orders:', err);
      setError(err instanceof Error ? err.message : 'Failed to fetch customer orders');
      setOrders([]);
      setTotalOrders(0);
      setTotalSpent(0);
    } finally {
      setLoading(false);
    }
  }, [read, status]);

  return {
    orders,
    loading,
    error,
    fetchCustomerOrders,
    totalOrders,
    totalSpent,
  };
};
