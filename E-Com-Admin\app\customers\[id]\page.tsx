"use client"
import { useState, useEffect } from "react";
import { But<PERSON> } from "@/components/ui/button";
import { <PERSON>, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Textarea } from "@/components/ui/textarea";
import { Separator } from "@/components/ui/separator";
import { Badge } from "@/components/ui/badge";
import { ArrowLeft, Mail, Phone, Loader2, AlertCircle } from "lucide-react";
import { format, isValid, parseISO } from "date-fns";
import { useToast } from "@/components/ui/use-toast";
import { useParams, useRouter } from "next/navigation";
import { useCustomers, useCustomerById } from "@/hooks/useCustomers";
import { useCustomerOrders } from "@/hooks/useCustomerOrders";
import { OrderHistoryCard } from "@/components/customers/OrderHistoryCard";
import { OrderHistorySkeleton } from "@/components/customers/OrderHistorySkeleton";
import { Al<PERSON>, AlertDescription } from "@/components/ui/alert";
import { formatCurrency } from "@/utils/formatters";

 const CustomerProfile = () => {
  const { id } = useParams();
  const router = useRouter();
  const { toast } = useToast();

  // Get customers list and find the specific customer
  const { customers, loading, error } = useCustomers();
  const customer = useCustomerById(typeof id === 'string' ? id : undefined, customers);

  // Get customer orders
  const {
    orders: customerOrders,
    loading: ordersLoading,
    error: ordersError,
    fetchCustomerOrders,
    totalOrders: actualTotalOrders,
    totalSpent: actualTotalSpent
  } = useCustomerOrders();

  const [notes, setNotes] = useState("");

  // Helper function to safely format dates
  const formatDate = (dateString: string | undefined | null): string => {
    if (!dateString) return "N/A";

    try {
      // Try to parse the date string
      let date: Date;

      // If it's an ISO string, use parseISO, otherwise use new Date
      if (dateString.includes('T') || dateString.includes('Z')) {
        date = parseISO(dateString);
      } else {
        date = new Date(dateString);
      }

      // Check if the date is valid
      if (isValid(date)) {
        return format(date, "MMM d, yyyy");
      } else {
        return "Invalid date";
      }
    } catch (error) {
      console.error("Error formatting date:", error, "Date string:", dateString);
      return "Invalid date";
    }
  };
console.log(useCustomerById(typeof id === 'string' ? id : undefined, customers),"useCustomerById(typeof id === 'string' ? id : undefined, customers)")
  // Update notes state when customer data is loaded
  useEffect(() => {
    if (customer) {
      setNotes(customer.notes || "");
    }
  }, [customer]);

  // Fetch customer orders when customer data is available
  useEffect(() => {
    if (customer && customer.email) {
      console.log('Fetching orders for customer:', customer.email);
      fetchCustomerOrders(customer.email);
    }
  }, [customer, fetchCustomerOrders]);

  console.log('Customer ID from URL:', id);
  console.log('Customers array:', customers);
  console.log('Customers array length:', customers.length);
  console.log('Customer found:', customer);
  console.log('Loading state:', loading);
  console.log('Error state:', error);
  console.log('Customer orders:', customerOrders);
  console.log('Orders loading:', ordersLoading);
  console.log('Orders error:', ordersError);
  console.log('Actual total orders:', actualTotalOrders);
  console.log('Actual total spent:', actualTotalSpent);

  const handleNotesUpdate = async () => {
    // For now, just show a toast since we don't have the backend endpoint
    toast({
      title: "Notes updated",
      description: "Customer notes have been saved successfully.",
    });
  };

  // Loading state
  if (loading) {
    return (
      <div className="space-y-6 animate-fadeIn">
        <div className="flex items-center space-x-2">
          <Button
            variant="ghost"
            size="icon"
            onClick={() => router.push("/customers")}
          >
            <ArrowLeft className="h-4 w-4" />
          </Button>
          <h1 className="text-3xl font-bold">Customer Profile</h1>
        </div>
        <div className="flex items-center justify-center py-8">
          <Loader2 className="h-8 w-8 animate-spin" />
          <span className="ml-2">Loading customer details...</span>
        </div>
      </div>
    );
  }

  // Error state
  if (error) {
    return (
      <div className="space-y-6 animate-fadeIn">
        <div className="flex items-center space-x-2">
          <Button
            variant="ghost"
            size="icon"
            onClick={() => router.push("/customers")}
          >
            <ArrowLeft className="h-4 w-4" />
          </Button>
          <h1 className="text-3xl font-bold">Customer Profile</h1>
        </div>
        <Alert variant="destructive">
          <AlertCircle className="h-4 w-4" />
          <AlertDescription>
            Failed to load customer details: {error}
          </AlertDescription>
        </Alert>
      </div>
    );
  }

  // Customer not found
  if (!loading && !customer) {
    return (
      <div className="space-y-6 animate-fadeIn">
        <div className="flex items-center space-x-2">
          <Button
            variant="ghost"
            size="icon"
            onClick={() => router.push("/customers")}
          >
            <ArrowLeft className="h-4 w-4" />
          </Button>
          <h1 className="text-3xl font-bold">Customer Profile</h1>
        </div>
        <div className="text-center py-8">
          <p className="text-muted-foreground">Customer not found.</p>
        </div>
      </div>
    );
  }

  // Additional safety check to ensure customer exists
  if (!customer) {
    return (
      <div className="space-y-6 animate-fadeIn">
        <div className="flex items-center space-x-2">
          <Button
            variant="ghost"
            size="icon"
            onClick={() => router.push("/customers")}
          >
            <ArrowLeft className="h-4 w-4" />
          </Button>
          <h1 className="text-3xl font-bold">Customer Profile</h1>
        </div>
        <div className="text-center py-8">
          <p className="text-muted-foreground">Customer data not available.</p>
        </div>
      </div>
    );
  }

  return (
    <div className="space-y-6 animate-fadeIn">
      <div className="flex items-center space-x-2">
        <Button
          variant="ghost"
          size="icon"
          onClick={() => router.push("/customers")}
        >
          <ArrowLeft className="h-4 w-4" />
        </Button>
        <h1 className="text-3xl font-bold">Customer Profile</h1>
      </div>

      <div className="grid gap-6 md:grid-cols-2">
        <div className="space-y-6">
          <Card>
            <CardHeader>
              <CardTitle>Customer Information</CardTitle>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="space-y-2">
                <h2 className="text-xl font-semibold">{customer.name}</h2>
                <div className="flex items-center space-x-2 text-muted-foreground">
                  <Mail className="h-4 w-4" />
                  <span>{customer.email}</span>
                </div>
                {customer.phone_number && (
                  <div className="flex items-center space-x-2 text-muted-foreground">
                    <Phone className="h-4 w-4" />
                    <span>{customer.phone_number}</span>
                  </div>
                )}
              </div>
              <Separator />
              <div className="space-y-2">
                <div className="flex justify-between">
                  <span className="text-muted-foreground">Customer since</span>
                  <span>{formatDate(customer.date_joined)}</span>
                </div>
                <div className="flex justify-between">
                  <span className="text-muted-foreground">Total orders</span>
                  <span>{actualTotalOrders || customer.totalOrders || 0}</span>
                </div>
                <div className="flex justify-between">
                  <span className="text-muted-foreground">Total spent</span>
                  <span>${formatCurrency(actualTotalSpent || customer.totalSpent || 0)}</span>
                </div>
                <div className="flex justify-between">
                  <span className="text-muted-foreground">Status</span>
                  <Badge variant={customer.status === 'active' ? 'default' : 'secondary'}>
                    {customer.status}
                  </Badge>
                </div>
              </div>
            </CardContent>
          </Card>

          <Card>
            <CardHeader>
              <CardTitle>Notes</CardTitle>
            </CardHeader>
            <CardContent className="space-y-4">
              <Textarea
                value={notes}
                onChange={(e) => setNotes(e.target.value)}
                placeholder="Add notes about this customer..."
                className="min-h-[100px]"
              />
              <Button onClick={handleNotesUpdate}>
                Save Notes
              </Button>
            </CardContent>
          </Card>
        </div>

        <Card>
          <CardHeader>
            <div className="flex items-center justify-between">
              <CardTitle>Order History</CardTitle>
              {customerOrders.length > 0 && (
                <div className="text-sm text-muted-foreground">
                  {customerOrders.length} orders • ${formatCurrency(actualTotalSpent)} total
                </div>
              )}
            </div>
          </CardHeader>
          <CardContent className="space-y-4">
            {ordersLoading ? (
              <OrderHistorySkeleton />
            ) : ordersError ? (
              <Alert variant="destructive">
                <AlertCircle className="h-4 w-4" />
                <AlertDescription>
                  Failed to load order history: {ordersError}
                </AlertDescription>
              </Alert>
            ) : customerOrders.length === 0 ? (
              <div className="text-center py-8">
                <p className="text-muted-foreground">No orders found for this customer.</p>
              </div>
            ) : (
              <div className="space-y-4 max-h-96 overflow-y-auto">
                {customerOrders.map((order) => (
                  <OrderHistoryCard key={order.id} order={order} />
                ))}
              </div>
            )}
          </CardContent>
        </Card>
      </div>
    </div>
  );
};

export default CustomerProfile;