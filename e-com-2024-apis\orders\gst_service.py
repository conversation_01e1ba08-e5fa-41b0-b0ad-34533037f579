"""
GST calculation service for ecommerce orders
"""
from decimal import Decima<PERSON>
from typing import Dict, List, Tuple
from products.models import GST


class GSTCalculationService:
    """Service for calculating GST amounts for orders and products"""
    
    def __init__(self):
        self.default_gst_rate = Decimal('18.00')
        self.default_cgst_rate = Decimal('9.00')
        self.default_sgst_rate = Decimal('9.00')
        self.default_igst_rate = Decimal('18.00')
    
    def get_default_gst(self) -> GST:
        """Get or create default GST rate"""
        default_gst, created = GST.objects.get_or_create(
            name="Default Rate",
            defaults={
                'rate': self.default_gst_rate,
                'cgst_rate': self.default_cgst_rate,
                'sgst_rate': self.default_sgst_rate,
                'igst_rate': self.default_igst_rate,
                'hsn_code': '8471',  # Default HSN for electronics
                'is_active': True
            }
        )
        return default_gst
    
    def calculate_product_gst(self, product, quantity: int = 1) -> Dict[str, Decimal]:
        """
        Calculate GST for a single product (DEPRECATED - use calculate_product_gst_from_mrp)

        Args:
            product: Product instance
            quantity: Quantity of the product

        Returns:
            Dict containing GST calculations
        """
        gst_rate = product.get_gst_rate()
        base_amount = product.price * quantity

        return {
            'base_amount': base_amount,
            'gst_rate': gst_rate.rate,
            'gst_amount': gst_rate.calculate_gst_amount(base_amount),
            'cgst_amount': gst_rate.calculate_cgst_amount(base_amount),
            'sgst_amount': gst_rate.calculate_sgst_amount(base_amount),
            'igst_amount': gst_rate.calculate_igst_amount(base_amount),
            'total_with_gst': base_amount + gst_rate.calculate_gst_amount(base_amount),
            'hsn_code': gst_rate.hsn_code
        }

    def calculate_product_gst_from_mrp(self, product, quantity: int = 1) -> Dict[str, Decimal]:
        """
        Calculate GST for a single product from MRP (GST inclusive price)

        Args:
            product: Product instance
            quantity: Quantity of the product

        Returns:
            Dict containing GST calculations
        """
        gst_rate = product.get_gst_rate()
        total_mrp = product.price * quantity
        base_amount = gst_rate.calculate_base_price_from_inclusive(total_mrp)

        return {
            'base_amount': base_amount,
            'mrp': total_mrp,
            'unit_mrp': product.price,
            'unit_base_price': product.base_price,
            'quantity': quantity,
            'gst_rate': gst_rate.rate,
            'gst_amount': gst_rate.calculate_gst_from_inclusive(total_mrp),
            'cgst_amount': gst_rate.calculate_cgst_from_inclusive(total_mrp),
            'sgst_amount': gst_rate.calculate_sgst_from_inclusive(total_mrp),
            'igst_amount': gst_rate.calculate_igst_from_inclusive(total_mrp),
            'hsn_code': gst_rate.hsn_code
        }
    
    def calculate_cart_gst(self, cart_items) -> Dict[str, Decimal]:
        """
        Calculate total GST for all items in cart
        
        Args:
            cart_items: QuerySet or list of CartItem instances
            
        Returns:
            Dict containing total GST calculations
        """
        total_base_amount = Decimal('0.00')
        total_gst_amount = Decimal('0.00')
        total_cgst_amount = Decimal('0.00')
        total_sgst_amount = Decimal('0.00')
        total_igst_amount = Decimal('0.00')
        
        item_details = []
        
        for item in cart_items:
            product_gst = self.calculate_product_gst(item.product, item.quantity)
            
            total_base_amount += product_gst['base_amount']
            total_gst_amount += product_gst['gst_amount']
            total_cgst_amount += product_gst['cgst_amount']
            total_sgst_amount += product_gst['sgst_amount']
            total_igst_amount += product_gst['igst_amount']
            
            # Convert Decimal values to float for JSON serialization
            serializable_product_gst = {
                key: float(value) if isinstance(value, Decimal) else value
                for key, value in product_gst.items()
            }

            item_details.append({
                'product': {
                    'id': item.product.id,
                    'name': item.product.name,
                    'slug': item.product.slug,
                    'price': float(item.product.price),
                    'gst_rate': float(item.product.get_gst_rate().rate),
                },
                'quantity': item.quantity,
                **serializable_product_gst
            })
        
        return {
            'subtotal': total_base_amount,
            'total_gst_amount': total_gst_amount,
            'total_cgst_amount': total_cgst_amount,
            'total_sgst_amount': total_sgst_amount,
            'total_igst_amount': total_igst_amount,
            'total_with_gst': total_base_amount + total_gst_amount,
            'item_details': item_details
        }

    def calculate_cart_gst_from_mrp(self, cart_items) -> Dict[str, Decimal]:
        """
        Calculate total GST for all items in cart from MRP (GST inclusive pricing)

        Args:
            cart_items: QuerySet or list of CartItem instances

        Returns:
            Dict containing total GST calculations from MRP
        """
        total_base_amount = Decimal('0.00')
        total_gst_amount = Decimal('0.00')
        total_cgst_amount = Decimal('0.00')
        total_sgst_amount = Decimal('0.00')
        total_igst_amount = Decimal('0.00')
        total_mrp = Decimal('0.00')

        item_details = []

        for item in cart_items:
            product_gst = self.calculate_product_gst_from_mrp(item.product, item.quantity)

            total_base_amount += product_gst['base_amount']
            total_gst_amount += product_gst['gst_amount']
            total_cgst_amount += product_gst['cgst_amount']
            total_sgst_amount += product_gst['sgst_amount']
            total_igst_amount += product_gst['igst_amount']
            total_mrp += product_gst['mrp']

            # Convert Decimal values to float for JSON serialization
            serializable_product_gst = {
                key: float(value) if isinstance(value, Decimal) else value
                for key, value in product_gst.items()
            }

            item_details.append({
                'product': {
                    'id': item.product.id,
                    'name': item.product.name,
                    'slug': item.product.slug,
                    'price': float(item.product.price),
                    'gst_rate': float(item.product.get_gst_rate().rate),
                },
                'quantity': item.quantity,
                **serializable_product_gst
            })

        return {
            'subtotal': total_base_amount,
            'total_mrp': total_mrp,
            'total_gst_amount': total_gst_amount,
            'total_cgst_amount': total_cgst_amount,
            'total_sgst_amount': total_sgst_amount,
            'total_igst_amount': total_igst_amount,
            'total_with_gst': total_mrp,  # MRP is already inclusive of GST
            'item_details': item_details
        }
    
    def calculate_order_gst(self, subtotal: Decimal, shipping_cost: Decimal = Decimal('0.00'), 
                           discount: Decimal = Decimal('0.00'), is_inter_state: bool = False) -> Dict[str, Decimal]:
        """
        Calculate GST for an order
        
        Args:
            subtotal: Order subtotal (before GST)
            shipping_cost: Shipping charges
            discount: Discount amount
            is_inter_state: Whether this is an inter-state transaction (for IGST)
            
        Returns:
            Dict containing order GST calculations
        """
        # Apply discount to subtotal
        discounted_subtotal = max(Decimal('0.00'), subtotal - discount)
        
        # Get default GST rate
        default_gst = self.get_default_gst()
        
        # Calculate GST on discounted subtotal
        gst_amount = default_gst.calculate_gst_amount(discounted_subtotal)
        
        if is_inter_state:
            # For inter-state: Use IGST
            cgst_amount = Decimal('0.00')
            sgst_amount = Decimal('0.00')
            igst_amount = default_gst.calculate_igst_amount(discounted_subtotal)
        else:
            # For intra-state: Use CGST + SGST
            cgst_amount = default_gst.calculate_cgst_amount(discounted_subtotal)
            sgst_amount = default_gst.calculate_sgst_amount(discounted_subtotal)
            igst_amount = Decimal('0.00')
        
        # Calculate final total
        total = discounted_subtotal + gst_amount + shipping_cost
        
        return {
            'subtotal': subtotal,
            'discount': discount,
            'discounted_subtotal': discounted_subtotal,
            'gst_amount': gst_amount,
            'cgst_amount': cgst_amount,
            'sgst_amount': sgst_amount,
            'igst_amount': igst_amount,
            'shipping_cost': shipping_cost,
            'total': total,
            'is_inter_state': is_inter_state
        }
    
    def is_inter_state_transaction(self, billing_address, shipping_address) -> bool:
        """
        Determine if transaction is inter-state based on addresses

        Args:
            billing_address: Billing address instance
            shipping_address: Shipping address instance

        Returns:
            bool: True if inter-state, False if intra-state
        """
        if not billing_address or not shipping_address:
            return False

        # Compare states (assuming address model has state field)
        billing_state = getattr(billing_address, 'state', '').strip().lower()
        shipping_state = getattr(shipping_address, 'state', '').strip().lower()

        return billing_state != shipping_state

    def calculate_order_gst_breakdown(self, order) -> Dict[str, Decimal]:
        """
        Calculate GST breakdown for an existing order based on its items and addresses

        Args:
            order: Order instance

        Returns:
            Dict containing GST breakdown with proper intra-state/inter-state logic
        """
        # Determine transaction type
        is_inter_state = self.is_inter_state_transaction(
            order.billing_address,
            order.shipping_address
        )

        total_base_amount = Decimal('0.00')
        total_gst_amount = Decimal('0.00')
        total_cgst_amount = Decimal('0.00')
        total_sgst_amount = Decimal('0.00')
        total_igst_amount = Decimal('0.00')

        # Calculate GST for each item using product-specific rates
        for item in order.items.all():
            if item.product:
                # Use the product's GST calculation method for accuracy
                item_breakdown = item.product.calculate_gst_breakdown_from_mrp(
                    quantity=item.quantity,
                    is_inter_state=is_inter_state
                )

                total_base_amount += Decimal(str(item_breakdown['base_price']))
                total_gst_amount += Decimal(str(item_breakdown['total_gst']))

                if is_inter_state:
                    total_igst_amount += Decimal(str(item_breakdown['igst_amount']))
                else:
                    total_cgst_amount += Decimal(str(item_breakdown['cgst_amount']))
                    total_sgst_amount += Decimal(str(item_breakdown['sgst_amount']))

        return {
            'base_amount': round(total_base_amount, 2),
            'total_gst_amount': round(total_gst_amount, 2),
            'cgst_amount': round(total_cgst_amount, 2),
            'sgst_amount': round(total_sgst_amount, 2),
            'igst_amount': round(total_igst_amount, 2),
            'is_inter_state': is_inter_state
        }

    def calculate_cart_gst_with_addresses(self, cart_items, billing_address=None, shipping_address=None) -> Dict[str, Decimal]:
        """
        Calculate total GST for all items in cart with proper transaction type determination

        Args:
            cart_items: QuerySet or list of CartItem instances
            billing_address: Billing address instance (optional)
            shipping_address: Shipping address instance (optional)

        Returns:
            Dict containing total GST calculations with proper intra-state/inter-state logic
        """
        # Determine transaction type if addresses are provided
        is_inter_state = False
        if billing_address and shipping_address:
            is_inter_state = self.is_inter_state_transaction(billing_address, shipping_address)

        total_base_amount = Decimal('0.00')
        total_gst_amount = Decimal('0.00')
        total_cgst_amount = Decimal('0.00')
        total_sgst_amount = Decimal('0.00')
        total_igst_amount = Decimal('0.00')
        total_mrp = Decimal('0.00')

        item_details = []

        for item in cart_items:
            # Use product-specific GST calculation with proper transaction type
            product_gst = item.product.calculate_gst_breakdown_from_mrp(
                quantity=item.quantity,
                is_inter_state=is_inter_state
            )

            total_base_amount += Decimal(str(product_gst['base_price']))
            total_gst_amount += Decimal(str(product_gst['total_gst']))
            total_mrp += Decimal(str(product_gst['mrp']))

            if is_inter_state:
                total_igst_amount += Decimal(str(product_gst['igst_amount']))
            else:
                total_cgst_amount += Decimal(str(product_gst['cgst_amount']))
                total_sgst_amount += Decimal(str(product_gst['sgst_amount']))

            # Convert values for JSON serialization
            serializable_product_gst = {
                key: float(value) if isinstance(value, (Decimal, int)) else value
                for key, value in product_gst.items()
            }

            item_details.append({
                'product': {
                    'id': item.product.id,
                    'name': item.product.name,
                    'slug': item.product.slug,
                    'price': float(item.product.price),
                    'gst_rate': float(item.product.get_gst_rate().rate),
                },
                'quantity': item.quantity,
                **serializable_product_gst
            })

        return {
            'subtotal': round(total_base_amount, 2),
            'total_mrp': round(total_mrp, 2),
            'total_gst_amount': round(total_gst_amount, 2),
            'total_cgst_amount': round(total_cgst_amount, 2),
            'total_sgst_amount': round(total_sgst_amount, 2),
            'total_igst_amount': round(total_igst_amount, 2),
            'total_with_gst': round(total_mrp, 2),  # MRP is already inclusive of GST
            'is_inter_state': is_inter_state,
            'item_details': item_details
        }
    
    def format_gst_breakdown(self, gst_calculation: Dict[str, Decimal]) -> Dict[str, str]:
        """
        Format GST calculation for display
        
        Args:
            gst_calculation: GST calculation dict
            
        Returns:
            Dict with formatted strings for display
        """
        return {
            'subtotal': f"₹{gst_calculation['subtotal']:.2f}",
            'gst_amount': f"₹{gst_calculation['gst_amount']:.2f}",
            'cgst_amount': f"₹{gst_calculation['cgst_amount']:.2f}",
            'sgst_amount': f"₹{gst_calculation['sgst_amount']:.2f}",
            'igst_amount': f"₹{gst_calculation['igst_amount']:.2f}",
            'shipping_cost': f"₹{gst_calculation['shipping_cost']:.2f}",
            'total': f"₹{gst_calculation['total']:.2f}",
        }


# Global instance for easy import
gst_service = GSTCalculationService()
