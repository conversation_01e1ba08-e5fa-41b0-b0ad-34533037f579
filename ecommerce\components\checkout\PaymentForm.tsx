import { Label } from "../../components/ui/label";
import { Button } from "../ui/button";
import { Card, CardContent } from "../ui/card";
import { CreditCard, ArrowLeft, ArrowRight, Shield } from "lucide-react";

interface PaymentInfo {
  paymentMethod: string;
}

interface PaymentFormProps {
  paymentInfo: PaymentInfo;
  onPaymentInfoChange: (info: PaymentInfo) => void;
  handleNext: (step: number) => void;
  handleBack: (step: number) => void;
}

export const PaymentForm = ({
  paymentInfo,
  onPaymentInfoChange,
  handleNext,
  handleBack,
}: PaymentFormProps) => {
  // Ensure payment method is set to PhonePe
  if (paymentInfo.paymentMethod !== 'phonepe') {
    onPaymentInfoChange({ ...paymentInfo, paymentMethod: 'phonepe' });
  }

  return (
    <div className="space-y-8">
      <h2 className="text-2xl font-semibold text-center mb-6 gradient-text flex items-center justify-center gap-2">
        <CreditCard className="h-6 w-6" />
        Payment Details
      </h2>

      <div className="space-y-6">
        <div className="space-y-5">
          <Label className="text-lg font-medium flex items-center gap-2">
            <Shield className="h-5 w-5 text-primary" />
            Select Payment Method
          </Label>

          <Card className="border-2 border-primary shadow-md hover:shadow-lg transition-all duration-300">
            <CardContent className="pt-8 pb-8">
              <div className="flex items-center justify-between">
                <div className="flex items-center gap-4">
                  <div className="w-14 h-14 bg-blue-100 rounded-full flex items-center justify-center">
                    <svg xmlns="http://www.w3.org/2000/svg" width="28" height="28" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round" className="text-blue-600">
                      <path d="M5 12h14"></path>
                      <path d="M12 5v14"></path>
                    </svg>
                  </div>
                  <div>
                    <h3 className="font-bold text-xl">PhonePe</h3>
                    <p className="text-sm text-muted-foreground mt-1">
                      Fast, secure payments via PhonePe
                    </p>
                  </div>
                </div>
                <div className="w-6 h-6 rounded-full border-2 border-primary flex items-center justify-center">
                  <div className="w-3 h-3 bg-primary rounded-full"></div>
                </div>
              </div>
            </CardContent>
          </Card>

          <Card className="mt-6 bg-blue-50 border-blue-100">
            <CardContent className="pt-5 pb-5">
              <div className="flex items-start gap-3">
                <Shield className="h-5 w-5 text-blue-600 mt-0.5" />
                <div>
                  <p className="text-sm font-medium text-blue-800">Secure Payment Process</p>
                  <p className="text-sm text-blue-700 mt-1">
                    You will be redirected to PhonePe to complete your payment securely after reviewing your order.
                    All transactions are encrypted and protected.
                  </p>
                </div>
              </div>
            </CardContent>
          </Card>
        </div>

        <div className="flex justify-between mt-8">
          <Button
            variant="outline"
            onClick={() => handleBack(2)}
            className="flex items-center gap-2 px-6"
          >
            <ArrowLeft className="h-4 w-4" />
            Back
          </Button>
          <Button
            onClick={() => handleNext(2)}
            className="flex items-center gap-2 px-6"
          >
            Next
            <ArrowRight className="h-4 w-4" />
          </Button>
        </div>
      </div>
    </div>
  );
};