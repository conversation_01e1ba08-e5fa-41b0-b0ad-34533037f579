"use client";

import { useState, useEffect } from "react";
import { <PERSON>, <PERSON><PERSON>onte<PERSON>, <PERSON><PERSON><PERSON><PERSON>, Card<PERSON><PERSON><PERSON> } from "@/components/ui/card";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Skeleton } from "@/components/ui/skeleton";
import { Ta<PERSON>, <PERSON><PERSON><PERSON>ontent, <PERSON><PERSON><PERSON><PERSON>, Ta<PERSON>Trigger } from "@/components/ui/tabs";
import useApi from "@/hooks/useApi";
import {
  MAIN_URL,
  PRIVACY_METRICS,
  PRIVACY_AUDIT,
  PRIVACY_HEALTH,
  ADMIN_IP_MANAGEMENT,
  ADMIN_SECURITY_EVENTS,
  ADMIN_SECURITY_THREATS,
  ADMIN_DELETION_REQUESTS,
  ADMIN_COMPLIANCE_REPORT
} from "@/constant/urls";
import {
  Shield,
  Users,
  CheckCircle,
  Clock,
  Download,
  RefreshCw,
  Eye,
  Trash2,
  Activity,
  BarChart3,
  Ban,
  AlertTriangle,
  <PERSON>Text,
  Filter,
  Plus,
  Edit,
  Trash,
  CheckSquare,
  <PERSON><PERSON><PERSON><PERSON>,
  <PERSON>ert<PERSON><PERSON>cle,
  Globe,
  Lock,
  UserX,
  Database
} from "lucide-react";
import {

  <PERSON><PERSON><PERSON>,
  Responsive<PERSON>ontainer,
  <PERSON><PERSON><PERSON>,
  <PERSON>,
  <PERSON>,
  
} from "recharts";

const COLORS = ['#4F46E5', '#7C3AED', '#EC4899', '#EF4444', '#F59E0B', '#10B981'];

interface PrivacyMetrics {
  consent_metrics: {
    total_consents: number;
    consents_last_24h: number;
    consents_last_7d: number;
    consent_types: Record<string, {
      description: string;
      granted_count: number;
      adoption_rate: number;
    }>;
  };
  deletion_metrics: {
    total_requests: number;
    pending_requests: number;
    requests_last_24h: number;
    requests_last_7d: number;
  };
}

interface BlockedIP {
  id: number;
  ip_address: string;
  reason: string;
  blocked_at: string;
  blocked_until: string;
  is_permanent: boolean;
  is_enabled: boolean;
  is_active: boolean;
  blocked_by: string | null;
  notes: string;
}

interface SecurityEvent {
  id: number;
  event_type: string;
  severity: string;
  ip_address: string;
  user: string | null;
  timestamp: string;
  is_resolved: boolean;
  resolved_by: string | null;
  resolved_at: string | null;
  response_action: string;
  details: Record<string, unknown>;
}

interface SecurityThreat {
  id: number;
  threat_id: string;
  threat_type: string;
  severity: string;
  status: string;
  source_ip: string;
  target_endpoint: string;
  detected_at: string;
  resolved_at: string | null;
  description: string;
  assigned_to: string | null;
  is_quarantined: boolean;
  automated_response: boolean;
  response_actions: string[];
  details: Record<string, unknown>;
}

interface DeletionRequest {
  id: number;
  user: {
    id: number;
    username: string; // This will actually contain the email
    email: string;
    first_name: string;
    last_name: string;
    last_login: string | null;
    is_active: boolean;
  };
  request_date: string;
  status: string;
  reason: string;
  processed_at: string | null;
  processing_notes: string;
  ip_address: string;
  user_agent: string;
  priority: string;
  days_old: number;
}

const PrivacyCompliance = () => {
  const [metrics, setMetrics] = useState<PrivacyMetrics | null>(null);
  // eslint-disable-next-line @typescript-eslint/no-explicit-any
  const [auditResults, setAuditResults] = useState<any>(null);
  // eslint-disable-next-line @typescript-eslint/no-explicit-any
  const [healthStatus, setHealthStatus] = useState<any>(null);

  // New admin data states
  const [blockedIPs, setBlockedIPs] = useState<BlockedIP[]>([]);
  const [securityEvents, setSecurityEvents] = useState<SecurityEvent[]>([]);
  const [securityThreats, setSecurityThreats] = useState<SecurityThreat[]>([]);
  const [deletionRequests, setDeletionRequests] = useState<DeletionRequest[]>([]);
  // eslint-disable-next-line @typescript-eslint/no-explicit-any
  const [complianceReport, setComplianceReport] = useState<any>(null);

  const [loading, setLoading] = useState({
    metrics: true,
    audit: false,
    health: true,
    blockedIPs: false,
    securityEvents: false,
    securityThreats: false,
    deletionRequests: false,
    complianceReport: false
  });

  const [error, setError] = useState<string | null>(null);
  const [activeTab, setActiveTab] = useState("overview");

  const { read } = useApi(MAIN_URL);

  const fetchMetrics = async () => {
    try {
      setLoading(prev => ({ ...prev, metrics: true }));
      const result = await read(PRIVACY_METRICS);
      if (typeof result === 'string') {
        throw new Error(result);
      }
      
      setMetrics((result as { data: PrivacyMetrics })?.data);
    } catch (err) {
      setError(err instanceof Error ? err.message : 'Failed to fetch metrics');
    } finally {
      setLoading(prev => ({ ...prev, metrics: false }));
    }
  };

  const fetchHealthStatus = async () => {
    try {
      setLoading(prev => ({ ...prev, health: true }));
      const result = await read(PRIVACY_HEALTH);
      if (typeof result === 'string') {
        throw new Error(result);
      }
      setHealthStatus((result as { data: unknown }).data);
    } catch (err) {
      console.error('Health check failed:', err);
    } finally {
      setLoading(prev => ({ ...prev, health: false }));
    }
  };

  const runAudit = async () => {
    try {
      setLoading(prev => ({ ...prev, audit: true }));
      const result = await read(PRIVACY_AUDIT);
      if (typeof result === 'string') {
        throw new Error(result);
      }
      setAuditResults((result as { data: unknown }).data);
    } catch (err) {
      setError(err instanceof Error ? err.message : 'Failed to run audit');
    } finally {
      setLoading(prev => ({ ...prev, audit: false }));
    }
  };

  const fetchBlockedIPs = async () => {
    try {
      setLoading(prev => ({ ...prev, blockedIPs: true }));
      const result = await read(ADMIN_IP_MANAGEMENT);
      if (typeof result === 'string') {
        throw new Error(result);
      }
      setBlockedIPs((result as { results: { blocked_ips: BlockedIP[] } }).results.blocked_ips || []);
    } catch (err) {
      console.error('Failed to fetch blocked IPs:', err);
    } finally {
      setLoading(prev => ({ ...prev, blockedIPs: false }));
    }
  };

  const fetchSecurityEvents = async () => {
    try {
      setLoading(prev => ({ ...prev, securityEvents: true }));
      const result = await read(ADMIN_SECURITY_EVENTS);
      if (typeof result === 'string') {
        throw new Error(result);
      }
      setSecurityEvents((result as { results: { events: SecurityEvent[] } }).results.events || []);
    } catch (err) {
      console.error('Failed to fetch security events:', err);
    } finally {
      setLoading(prev => ({ ...prev, securityEvents: false }));
    }
  };

  const fetchSecurityThreats = async () => {
    try {
      setLoading(prev => ({ ...prev, securityThreats: true }));
      const result = await read(ADMIN_SECURITY_THREATS);
      if (typeof result === 'string') {
        throw new Error(result);
      }
      setSecurityThreats((result as { results: { threats: SecurityThreat[] } }).results.threats || []);
    } catch (err) {
      console.error('Failed to fetch security threats:', err);
    } finally {
      setLoading(prev => ({ ...prev, securityThreats: false }));
    }
  };

  const fetchDeletionRequests = async () => {
    try {
      setLoading(prev => ({ ...prev, deletionRequests: true }));
      const result = await read(ADMIN_DELETION_REQUESTS);
      if (typeof result === 'string') {
        throw new Error(result);
      }
      setDeletionRequests((result as { results: { requests: DeletionRequest[] } }).results.requests || []);
    } catch (err) {
      console.error('Failed to fetch deletion requests:', err);
    } finally {
      setLoading(prev => ({ ...prev, deletionRequests: false }));
    }
  };

  const fetchComplianceReport = async () => {
    try {
      setLoading(prev => ({ ...prev, complianceReport: true }));
      const result = await read(ADMIN_COMPLIANCE_REPORT);
      if (typeof result === 'string') {
        throw new Error(result);
      }
      setComplianceReport((result as { report: unknown }).report);
    } catch (err) {
      console.error('Failed to fetch compliance report:', err);
    } finally {
      setLoading(prev => ({ ...prev, complianceReport: false }));
    }
  };

  useEffect(() => {
    fetchMetrics();
    fetchHealthStatus();
    // Load admin data based on active tab
    if (activeTab === "ip-security") {
      fetchBlockedIPs();
    } else if (activeTab === "security-events") {
      fetchSecurityEvents();
    } else if (activeTab === "threats") {
      fetchSecurityThreats();
    } else if (activeTab === "deletion-requests") {
      fetchDeletionRequests();
    } else if (activeTab === "compliance") {
      fetchComplianceReport();
    }
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [activeTab]);

  const consentTypeData = metrics?.consent_metrics.consent_types ? 
    Object.entries(metrics.consent_metrics.consent_types).map(([key, value]) => ({
      name: key,
      value: value.granted_count,
      adoption_rate: value.adoption_rate
    })) : [];

  return (
    <div className="min-h-screen bg-gradient-to-br from-gray-50 to-white">
      <div className="space-y-8 p-6 animate-fadeIn">
        {/* Error Message */}
        {error && (
          <div className="mb-4 p-3 rounded bg-red-100 text-red-700 border border-red-300">
            {error}
          </div>
        )}

        {/* Header */}
        <div className="flex flex-col lg:flex-row lg:items-center lg:justify-between gap-4">
          <div>
            <h1 className="text-4xl font-bold bg-gradient-to-r from-gray-900 to-gray-600 bg-clip-text text-transparent">
              Privacy & Compliance Admin
            </h1>
            <p className="text-gray-600 mt-2">
              Comprehensive admin control over privacy, security, and compliance management
            </p>
          </div>

          <div className="flex items-center gap-3">
            <Button
              variant="outline"
              size="sm"
              onClick={runAudit}
              disabled={loading.audit}
              className="gap-2"
            >
              <Shield className={`h-4 w-4 ${loading.audit ? 'animate-pulse' : ''}`} />
              Run Security Audit
            </Button>
            <Button variant="outline" size="sm" className="gap-2">
              <Download className="h-4 w-4" />
              Export Report
            </Button>
            <Button
              variant="outline"
              size="sm"
              onClick={() => {
                fetchMetrics();
                fetchHealthStatus();
                // Refresh current tab data
                if (activeTab === "ip-security") fetchBlockedIPs();
                else if (activeTab === "security-events") fetchSecurityEvents();
                else if (activeTab === "threats") fetchSecurityThreats();
                else if (activeTab === "deletion-requests") fetchDeletionRequests();
                else if (activeTab === "compliance") fetchComplianceReport();
              }}
              className="gap-2"
            >
              <RefreshCw className="h-4 w-4" />
              Refresh
            </Button>
          </div>
        </div>

        {/* Tabbed Interface */}
        <Tabs value={activeTab} onValueChange={setActiveTab} className="w-full">
          <TabsList className="grid w-full grid-cols-6 lg:w-auto lg:grid-cols-6">
            <TabsTrigger value="overview" className="gap-2">
              <BarChart3 className="h-4 w-4" />
              Overview
            </TabsTrigger>
            <TabsTrigger value="ip-security" className="gap-2">
              <Ban className="h-4 w-4" />
              IP Security
            </TabsTrigger>
            <TabsTrigger value="security-events" className="gap-2">
              <AlertTriangle className="h-4 w-4" />
              Security Events
            </TabsTrigger>
            <TabsTrigger value="threats" className="gap-2">
              <Shield className="h-4 w-4" />
              Threats
            </TabsTrigger>
            <TabsTrigger value="deletion-requests" className="gap-2">
              <UserX className="h-4 w-4" />
              Deletion Requests
            </TabsTrigger>
            <TabsTrigger value="compliance" className="gap-2">
              <FileText className="h-4 w-4" />
              Compliance
            </TabsTrigger>
          </TabsList>

          {/* Overview Tab */}
          <TabsContent value="overview" className="space-y-6">

            {/* Status Cards */}
            <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-4">
              <Card className="border-0 shadow-lg bg-gradient-to-br from-white to-blue-50/30">
                <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                  <CardTitle className="text-sm font-medium text-gray-600">Total Consents</CardTitle>
                  <Users className="h-5 w-5 text-blue-500" />
                </CardHeader>
                <CardContent>
                  {loading.metrics ? (
                    <Skeleton className="h-8 w-24" />
                  ) : (
                    <>
                      <div className="text-2xl font-bold text-gray-900">
                        {metrics?.consent_metrics.total_consents || 0}
                      </div>
                      <p className="text-xs text-green-600 flex items-center gap-1 mt-1">
                        <CheckCircle className="h-3 w-3" />
                        {metrics?.consent_metrics.consents_last_24h || 0} in last 24h
                      </p>
                    </>
                  )}
                </CardContent>
              </Card>

              <Card className="border-0 shadow-lg bg-gradient-to-br from-white to-purple-50/30">
                <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                  <CardTitle className="text-sm font-medium text-gray-600">Deletion Requests</CardTitle>
                  <Trash2 className="h-5 w-5 text-purple-500" />
                </CardHeader>
                <CardContent>
                  {loading.metrics ? (
                    <Skeleton className="h-8 w-24" />
                  ) : (
                    <>
                      <div className="text-2xl font-bold text-gray-900">
                        {metrics?.deletion_metrics.total_requests || 0}
                      </div>
                      <p className="text-xs text-amber-600 flex items-center gap-1 mt-1">
                        <Clock className="h-3 w-3" />
                        {metrics?.deletion_metrics.pending_requests || 0} pending
                      </p>
                    </>
                  )}
                </CardContent>
              </Card>

              <Card className="border-0 shadow-lg bg-gradient-to-br from-white to-green-50/30">
                <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                  <CardTitle className="text-sm font-medium text-gray-600">Compliance Status</CardTitle>
                  <Shield className="h-5 w-5 text-green-500" />
                </CardHeader>
                <CardContent>
                  {loading.health ? (
                    <Skeleton className="h-8 w-24" />
                  ) : healthStatus ? (
                    <>
                      <div className={`text-2xl font-bold ${healthStatus.compliant ? 'text-green-600' : 'text-red-600'}`}>
                        {healthStatus.compliant ? 'Compliant' : 'Non-Compliant'}
                      </div>
                      <p className={`text-xs flex items-center gap-1 mt-1 ${healthStatus.compliant ? 'text-green-600' : 'text-red-600'}`}>
                        <CheckCircle className="h-3 w-3" />
                        {healthStatus.message || (healthStatus.compliant ? 'All checks passed' : 'Issues detected')}
                      </p>
                    </>
                  ) : (
                    <div className="text-xs text-gray-500">No health data available</div>
                  )}
                </CardContent>
              </Card>

              <Card className="border-0 shadow-lg bg-gradient-to-br from-white to-orange-50/30">
                <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                  <CardTitle className="text-sm font-medium text-gray-600">Recent Activity</CardTitle>
                  <Activity className="h-5 w-5 text-orange-500" />
                </CardHeader>
                <CardContent>
                  {loading.metrics ? (
                    <Skeleton className="h-8 w-24" />
                  ) : (
                    <>
                      <div className="text-2xl font-bold text-gray-900">
                        {(metrics?.consent_metrics.consents_last_7d || 0) + (metrics?.deletion_metrics.requests_last_7d || 0)}
                      </div>
                      <p className="text-xs text-gray-600 flex items-center gap-1 mt-1">
                        <BarChart3 className="h-3 w-3" />
                        Last 7 days
                      </p>
                    </>
                  )}
                </CardContent>
              </Card>
            </div>

            {/* Charts */}
            <div className="grid gap-6 lg:grid-cols-2">
              {/* Consent Types Distribution */}
              <Card className="border-0 shadow-lg bg-gradient-to-br from-white to-blue-50/30">
                <CardHeader>
                  <CardTitle className="flex items-center gap-2 text-gray-900">
                    <Eye className="h-5 w-5 text-blue-500" />
                    Consent Types Distribution
                  </CardTitle>
                </CardHeader>
                <CardContent>
                  {loading.metrics ? (
                    <div className="h-[300px] flex items-center justify-center">
                      <Skeleton className="h-48 w-48 rounded-full" />
                    </div>
                  ) : (
                    <div className="h-[300px]">
                      <ResponsiveContainer width="100%" height="100%">
                        <PieChart>
                          <Pie
                            data={consentTypeData}
                            cx="50%"
                            cy="50%"
                            innerRadius={60}
                            outerRadius={100}
                            paddingAngle={5}
                            dataKey="value"
                          >
                            {consentTypeData.map((entry, index) => (
                              <Cell key={`cell-${index}`} fill={COLORS[index % COLORS.length]} />
                            ))}
                          </Pie>
                          <Tooltip />
                        </PieChart>
                      </ResponsiveContainer>
                    </div>
                  )}
                </CardContent>
              </Card>

              {/* Audit Results */}
              <Card className="border-0 shadow-lg bg-gradient-to-br from-white to-green-50/30">
                <CardHeader>
                  <CardTitle className="flex items-center gap-2 text-gray-900">
                    <Shield className="h-5 w-5 text-green-500" />
                    Security Audit Results
                  </CardTitle>
                </CardHeader>
                <CardContent>
                  {loading.audit ? (
                    <div className="space-y-3">
                      <Skeleton className="h-4 w-full" />
                      <Skeleton className="h-4 w-3/4" />
                      <Skeleton className="h-4 w-1/2" />
                    </div>
                  ) : auditResults ? (
                    <div className="space-y-4">
                      <div className="flex items-center gap-2">
                        <CheckCircle className="h-5 w-5 text-green-500" />
                        <span className="text-sm font-medium">Audit completed successfully</span>
                      </div>
                      <div className="text-sm text-gray-600">
                        <p>Last audit: {new Date().toLocaleDateString()}</p>
                        <p>Status: All security checks passed</p>
                      </div>
                    </div>
                  ) : (
                    <div className="text-center py-8">
                      <Shield className="h-12 w-12 mx-auto text-gray-300 mb-4" />
                      <p className="text-sm text-gray-500">Click to Run Security Audit</p>
                    </div>
                  )}
                </CardContent>
              </Card>
            </div>
          </TabsContent>

          {/* IP Security Management Tab */}
          <TabsContent value="ip-security" className="space-y-6">
            <div className="flex flex-col lg:flex-row lg:items-center lg:justify-between gap-4">
              <div>
                <h2 className="text-2xl font-bold text-gray-900">IP Security Management</h2>
                <p className="text-gray-600">Manage blocked IP addresses and security controls</p>
              </div>
              <div className="flex items-center gap-3">
                <Button variant="outline" size="sm" className="gap-2">
                  <Plus className="h-4 w-4" />
                  Block IP
                </Button>
                <Button variant="outline" size="sm" className="gap-2">
                  <Download className="h-4 w-4" />
                  Export List
                </Button>
              </div>
            </div>

            {/* IP Security Stats */}
            <div className="grid gap-4 md:grid-cols-4">
              <Card className="border-0 shadow-lg bg-gradient-to-br from-white to-red-50/30">
                <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                  <CardTitle className="text-sm font-medium text-gray-600">Total Blocked IPs</CardTitle>
                  <Ban className="h-5 w-5 text-red-500" />
                </CardHeader>
                <CardContent>
                  <div className="text-2xl font-bold text-gray-900">
                    {blockedIPs.length}
                  </div>
                  <p className="text-xs text-red-600 flex items-center gap-1 mt-1">
                    <Lock className="h-3 w-3" />
                    {blockedIPs.filter(ip => ip.is_active).length} active
                  </p>
                </CardContent>
              </Card>

              <Card className="border-0 shadow-lg bg-gradient-to-br from-white to-orange-50/30">
                <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                  <CardTitle className="text-sm font-medium text-gray-600">Permanent Blocks</CardTitle>
                  <AlertCircle className="h-5 w-5 text-orange-500" />
                </CardHeader>
                <CardContent>
                  <div className="text-2xl font-bold text-gray-900">
                    {blockedIPs.filter(ip => ip.is_permanent).length}
                  </div>
                  <p className="text-xs text-orange-600 flex items-center gap-1 mt-1">
                    <Globe className="h-3 w-3" />
                    Never expire
                  </p>
                </CardContent>
              </Card>

              <Card className="border-0 shadow-lg bg-gradient-to-br from-white to-blue-50/30">
                <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                  <CardTitle className="text-sm font-medium text-gray-600">Temporary Blocks</CardTitle>
                  <Clock className="h-5 w-5 text-blue-500" />
                </CardHeader>
                <CardContent>
                  <div className="text-2xl font-bold text-gray-900">
                    {blockedIPs.filter(ip => !ip.is_permanent).length}
                  </div>
                  <p className="text-xs text-blue-600 flex items-center gap-1 mt-1">
                    <Clock className="h-3 w-3" />
                    Time-limited
                  </p>
                </CardContent>
              </Card>

              <Card className="border-0 shadow-lg bg-gradient-to-br from-white to-gray-50/30">
                <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                  <CardTitle className="text-sm font-medium text-gray-600">Disabled Blocks</CardTitle>
                  <XSquare className="h-5 w-5 text-gray-500" />
                </CardHeader>
                <CardContent>
                  <div className="text-2xl font-bold text-gray-900">
                    {blockedIPs.filter(ip => !ip.is_enabled).length}
                  </div>
                  <p className="text-xs text-gray-600 flex items-center gap-1 mt-1">
                    <XSquare className="h-3 w-3" />
                    Inactive
                  </p>
                </CardContent>
              </Card>
            </div>

            {/* Blocked IPs Table */}
            <Card className="border-0 shadow-lg">
              <CardHeader>
                <CardTitle className="flex items-center gap-2">
                  <Ban className="h-5 w-5 text-red-500" />
                  Blocked IP Addresses
                </CardTitle>
              </CardHeader>
              <CardContent>
                {loading.blockedIPs ? (
                  <div className="space-y-3">
                    {[...Array(5)].map((_, i) => (
                      <Skeleton key={i} className="h-12 w-full" />
                    ))}
                  </div>
                ) : (
                  <div className="space-y-4">
                    {blockedIPs.length === 0 ? (
                      <div className="text-center py-8">
                        <Ban className="h-12 w-12 mx-auto text-gray-300 mb-4" />
                        <p className="text-sm text-gray-500">No blocked IP addresses found</p>
                      </div>
                    ) : (
                      <div className="space-y-2">
                        {blockedIPs.slice(0, 10).map((ip) => (
                          <div key={ip.id} className="flex items-center justify-between p-4 border rounded-lg hover:bg-gray-50">
                            <div className="flex items-center gap-4">
                              <div className={`w-3 h-3 rounded-full ${ip.is_active ? 'bg-red-500' : 'bg-gray-300'}`} />
                              <div>
                                <div className="font-medium">{ip.ip_address}</div>
                                <div className="text-sm text-gray-500">{ip.reason}</div>
                              </div>
                            </div>
                            <div className="flex items-center gap-2">
                              <span className={`px-2 py-1 text-xs rounded-full ${
                                ip.is_permanent ? 'bg-red-100 text-red-700' : 'bg-blue-100 text-blue-700'
                              }`}>
                                {ip.is_permanent ? 'Permanent' : 'Temporary'}
                              </span>
                              <Button variant="outline" size="sm">
                                <Edit className="h-4 w-4" />
                              </Button>
                              <Button variant="outline" size="sm">
                                <Trash className="h-4 w-4" />
                              </Button>
                            </div>
                          </div>
                        ))}
                      </div>
                    )}
                  </div>
                )}
              </CardContent>
            </Card>
          </TabsContent>

          {/* Security Events Tab */}
          <TabsContent value="security-events" className="space-y-6">
            <div className="flex flex-col lg:flex-row lg:items-center lg:justify-between gap-4">
              <div>
                <h2 className="text-2xl font-bold text-gray-900">Security Events</h2>
                <p className="text-gray-600">Monitor and manage security events and incidents</p>
              </div>
              <div className="flex items-center gap-3">
                <Button variant="outline" size="sm" className="gap-2">
                  <Filter className="h-4 w-4" />
                  Filter Events
                </Button>
                <Button variant="outline" size="sm" className="gap-2">
                  <Download className="h-4 w-4" />
                  Export Events
                </Button>
              </div>
            </div>

            {/* Security Events Stats */}
            <div className="grid gap-4 md:grid-cols-4">
              <Card className="border-0 shadow-lg bg-gradient-to-br from-white to-yellow-50/30">
                <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                  <CardTitle className="text-sm font-medium text-gray-600">Total Events</CardTitle>
                  <AlertTriangle className="h-5 w-5 text-yellow-500" />
                </CardHeader>
                <CardContent>
                  <div className="text-2xl font-bold text-gray-900">
                    {securityEvents.length}
                  </div>
                  <p className="text-xs text-yellow-600 flex items-center gap-1 mt-1">
                    <Activity className="h-3 w-3" />
                    Last 30 days
                  </p>
                </CardContent>
              </Card>

              <Card className="border-0 shadow-lg bg-gradient-to-br from-white to-red-50/30">
                <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                  <CardTitle className="text-sm font-medium text-gray-600">Critical Events</CardTitle>
                  <AlertCircle className="h-5 w-5 text-red-500" />
                </CardHeader>
                <CardContent>
                  <div className="text-2xl font-bold text-gray-900">
                    {securityEvents.filter(event => event.severity === 'CRITICAL').length}
                  </div>
                  <p className="text-xs text-red-600 flex items-center gap-1 mt-1">
                    <AlertCircle className="h-3 w-3" />
                    Needs attention
                  </p>
                </CardContent>
              </Card>

              <Card className="border-0 shadow-lg bg-gradient-to-br from-white to-orange-50/30">
                <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                  <CardTitle className="text-sm font-medium text-gray-600">Unresolved</CardTitle>
                  <Clock className="h-5 w-5 text-orange-500" />
                </CardHeader>
                <CardContent>
                  <div className="text-2xl font-bold text-gray-900">
                    {securityEvents.filter(event => !event.is_resolved).length}
                  </div>
                  <p className="text-xs text-orange-600 flex items-center gap-1 mt-1">
                    <Clock className="h-3 w-3" />
                    Pending action
                  </p>
                </CardContent>
              </Card>

              <Card className="border-0 shadow-lg bg-gradient-to-br from-white to-green-50/30">
                <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                  <CardTitle className="text-sm font-medium text-gray-600">Resolved</CardTitle>
                  <CheckCircle className="h-5 w-5 text-green-500" />
                </CardHeader>
                <CardContent>
                  <div className="text-2xl font-bold text-gray-900">
                    {securityEvents.filter(event => event.is_resolved).length}
                  </div>
                  <p className="text-xs text-green-600 flex items-center gap-1 mt-1">
                    <CheckCircle className="h-3 w-3" />
                    Completed
                  </p>
                </CardContent>
              </Card>
            </div>

            {/* Security Events Table */}
            <Card className="border-0 shadow-lg">
              <CardHeader>
                <CardTitle className="flex items-center gap-2">
                  <AlertTriangle className="h-5 w-5 text-yellow-500" />
                  Recent Security Events
                </CardTitle>
              </CardHeader>
              <CardContent>
                {loading.securityEvents ? (
                  <div className="space-y-3">
                    {[...Array(5)].map((_, i) => (
                      <Skeleton key={i} className="h-16 w-full" />
                    ))}
                  </div>
                ) : (
                  <div className="space-y-4">
                    {securityEvents.length === 0 ? (
                      <div className="text-center py-8">
                        <AlertTriangle className="h-12 w-12 mx-auto text-gray-300 mb-4" />
                        <p className="text-sm text-gray-500">No security events found</p>
                      </div>
                    ) : (
                      <div className="space-y-2">
                        {securityEvents.slice(0, 10).map((event) => (
                          <div key={event.id} className="flex items-center justify-between p-4 border rounded-lg hover:bg-gray-50">
                            <div className="flex items-center gap-4">
                              <div className={`w-3 h-3 rounded-full ${
                                event.severity === 'CRITICAL' ? 'bg-red-500' :
                                event.severity === 'HIGH' ? 'bg-orange-500' :
                                event.severity === 'MEDIUM' ? 'bg-yellow-500' : 'bg-green-500'
                              }`} />
                              <div>
                                <div className="font-medium">{event.event_type.replace('_', ' ')}</div>
                                <div className="text-sm text-gray-500">
                                  {event.ip_address} • {new Date(event.timestamp).toLocaleDateString()}
                                </div>
                              </div>
                            </div>
                            <div className="flex items-center gap-2">
                              <span className={`px-2 py-1 text-xs rounded-full ${
                                event.severity === 'CRITICAL' ? 'bg-red-100 text-red-700' :
                                event.severity === 'HIGH' ? 'bg-orange-100 text-orange-700' :
                                event.severity === 'MEDIUM' ? 'bg-yellow-100 text-yellow-700' :
                                'bg-green-100 text-green-700'
                              }`}>
                                {event.severity}
                              </span>
                              <span className={`px-2 py-1 text-xs rounded-full ${
                                event.is_resolved ? 'bg-green-100 text-green-700' : 'bg-gray-100 text-gray-700'
                              }`}>
                                {event.is_resolved ? 'Resolved' : 'Open'}
                              </span>
                              <Button variant="outline" size="sm">
                                <Eye className="h-4 w-4" />
                              </Button>
                            </div>
                          </div>
                        ))}
                      </div>
                    )}
                  </div>
                )}
              </CardContent>
            </Card>
          </TabsContent>

          {/* Security Threats Tab */}
          <TabsContent value="threats" className="space-y-6">
            <div className="flex flex-col lg:flex-row lg:items-center lg:justify-between gap-4">
              <div>
                <h2 className="text-2xl font-bold text-gray-900">Security Threats</h2>
                <p className="text-gray-600">Manage security threats and incident response</p>
              </div>
              <div className="flex items-center gap-3">
                <Button variant="outline" size="sm" className="gap-2">
                  <Plus className="h-4 w-4" />
                  Add Threat
                </Button>
                <Button variant="outline" size="sm" className="gap-2">
                  <Download className="h-4 w-4" />
                  Export Threats
                </Button>
              </div>
            </div>

            {/* Threats Stats */}
            <div className="grid gap-4 md:grid-cols-4">
              <Card className="border-0 shadow-lg bg-gradient-to-br from-white to-red-50/30">
                <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                  <CardTitle className="text-sm font-medium text-gray-600">Active Threats</CardTitle>
                  <Shield className="h-5 w-5 text-red-500" />
                </CardHeader>
                <CardContent>
                  <div className="text-2xl font-bold text-gray-900">
                    {securityThreats.filter(threat => ['DETECTED', 'INVESTIGATING'].includes(threat.status)).length}
                  </div>
                  <p className="text-xs text-red-600 flex items-center gap-1 mt-1">
                    <AlertTriangle className="h-3 w-3" />
                    Requires action
                  </p>
                </CardContent>
              </Card>

              <Card className="border-0 shadow-lg bg-gradient-to-br from-white to-orange-50/30">
                <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                  <CardTitle className="text-sm font-medium text-gray-600">Critical Threats</CardTitle>
                  <AlertCircle className="h-5 w-5 text-orange-500" />
                </CardHeader>
                <CardContent>
                  <div className="text-2xl font-bold text-gray-900">
                    {securityThreats.filter(threat => threat.severity === 'CRITICAL').length}
                  </div>
                  <p className="text-xs text-orange-600 flex items-center gap-1 mt-1">
                    <AlertCircle className="h-3 w-3" />
                    High priority
                  </p>
                </CardContent>
              </Card>

              <Card className="border-0 shadow-lg bg-gradient-to-br from-white to-yellow-50/30">
                <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                  <CardTitle className="text-sm font-medium text-gray-600">Quarantined</CardTitle>
                  <Lock className="h-5 w-5 text-yellow-500" />
                </CardHeader>
                <CardContent>
                  <div className="text-2xl font-bold text-gray-900">
                    {securityThreats.filter(threat => threat.is_quarantined).length}
                  </div>
                  <p className="text-xs text-yellow-600 flex items-center gap-1 mt-1">
                    <Lock className="h-3 w-3" />
                    Isolated
                  </p>
                </CardContent>
              </Card>

              <Card className="border-0 shadow-lg bg-gradient-to-br from-white to-green-50/30">
                <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                  <CardTitle className="text-sm font-medium text-gray-600">Resolved</CardTitle>
                  <CheckCircle className="h-5 w-5 text-green-500" />
                </CardHeader>
                <CardContent>
                  <div className="text-2xl font-bold text-gray-900">
                    {securityThreats.filter(threat => threat.status === 'RESOLVED').length}
                  </div>
                  <p className="text-xs text-green-600 flex items-center gap-1 mt-1">
                    <CheckCircle className="h-3 w-3" />
                    Completed
                  </p>
                </CardContent>
              </Card>
            </div>

            {/* Threats Table */}
            <Card className="border-0 shadow-lg">
              <CardHeader>
                <CardTitle className="flex items-center gap-2">
                  <Shield className="h-5 w-5 text-red-500" />
                  Security Threats
                </CardTitle>
              </CardHeader>
              <CardContent>
                {loading.securityThreats ? (
                  <div className="space-y-3">
                    {[...Array(5)].map((_, i) => (
                      <Skeleton key={i} className="h-16 w-full" />
                    ))}
                  </div>
                ) : (
                  <div className="space-y-4">
                    {securityThreats.length === 0 ? (
                      <div className="text-center py-8">
                        <Shield className="h-12 w-12 mx-auto text-gray-300 mb-4" />
                        <p className="text-sm text-gray-500">No security threats detected</p>
                      </div>
                    ) : (
                      <div className="space-y-2">
                        {securityThreats.slice(0, 10).map((threat) => (
                          <div key={threat.id} className="flex items-center justify-between p-4 border rounded-lg hover:bg-gray-50">
                            <div className="flex items-center gap-4">
                              <div className={`w-3 h-3 rounded-full ${
                                threat.severity === 'CRITICAL' ? 'bg-red-500' :
                                threat.severity === 'HIGH' ? 'bg-orange-500' :
                                threat.severity === 'MEDIUM' ? 'bg-yellow-500' : 'bg-green-500'
                              }`} />
                              <div>
                                <div className="font-medium">{threat.threat_id}</div>
                                <div className="text-sm text-gray-500">
                                  {threat.threat_type.replace('_', ' ')} • {threat.source_ip}
                                </div>
                              </div>
                            </div>
                            <div className="flex items-center gap-2">
                              <span className={`px-2 py-1 text-xs rounded-full ${
                                threat.severity === 'CRITICAL' ? 'bg-red-100 text-red-700' :
                                threat.severity === 'HIGH' ? 'bg-orange-100 text-orange-700' :
                                threat.severity === 'MEDIUM' ? 'bg-yellow-100 text-yellow-700' :
                                'bg-green-100 text-green-700'
                              }`}>
                                {threat.severity}
                              </span>
                              <span className={`px-2 py-1 text-xs rounded-full ${
                                threat.status === 'RESOLVED' ? 'bg-green-100 text-green-700' :
                                threat.status === 'INVESTIGATING' ? 'bg-blue-100 text-blue-700' :
                                'bg-gray-100 text-gray-700'
                              }`}>
                                {threat.status}
                              </span>
                              {threat.is_quarantined && (
                                <span className="px-2 py-1 text-xs rounded-full bg-yellow-100 text-yellow-700">
                                  Quarantined
                                </span>
                              )}
                              <Button variant="outline" size="sm">
                                <Eye className="h-4 w-4" />
                              </Button>
                            </div>
                          </div>
                        ))}
                      </div>
                    )}
                  </div>
                )}
              </CardContent>
            </Card>
          </TabsContent>

          {/* Deletion Requests Tab */}
          <TabsContent value="deletion-requests" className="space-y-6">
            <div className="flex flex-col lg:flex-row lg:items-center lg:justify-between gap-4">
              <div>
                <h2 className="text-2xl font-bold text-gray-900">Data Deletion Requests</h2>
                <p className="text-gray-600">Manage user data deletion requests and GDPR compliance</p>
              </div>
              <div className="flex items-center gap-3">
                <Button variant="outline" size="sm" className="gap-2">
                  <CheckSquare className="h-4 w-4" />
                  Bulk Approve
                </Button>
                <Button variant="outline" size="sm" className="gap-2">
                  <Download className="h-4 w-4" />
                  Export Requests
                </Button>
              </div>
            </div>

            {/* Deletion Requests Stats */}
            <div className="grid gap-4 md:grid-cols-4">
              <Card className="border-0 shadow-lg bg-gradient-to-br from-white to-blue-50/30">
                <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                  <CardTitle className="text-sm font-medium text-gray-600">Total Requests</CardTitle>
                  <Database className="h-5 w-5 text-blue-500" />
                </CardHeader>
                <CardContent>
                  <div className="text-2xl font-bold text-gray-900">
                    {deletionRequests.length}
                  </div>
                  <p className="text-xs text-blue-600 flex items-center gap-1 mt-1">
                    <Database className="h-3 w-3" />
                    All time
                  </p>
                </CardContent>
              </Card>

              <Card className="border-0 shadow-lg bg-gradient-to-br from-white to-yellow-50/30">
                <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                  <CardTitle className="text-sm font-medium text-gray-600">Pending</CardTitle>
                  <Clock className="h-5 w-5 text-yellow-500" />
                </CardHeader>
                <CardContent>
                  <div className="text-2xl font-bold text-gray-900">
                    {deletionRequests.filter(req => req.status === 'PENDING').length}
                  </div>
                  <p className="text-xs text-yellow-600 flex items-center gap-1 mt-1">
                    <Clock className="h-3 w-3" />
                    Awaiting review
                  </p>
                </CardContent>
              </Card>

              <Card className="border-0 shadow-lg bg-gradient-to-br from-white to-orange-50/30">
                <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                  <CardTitle className="text-sm font-medium text-gray-600">Overdue</CardTitle>
                  <AlertTriangle className="h-5 w-5 text-orange-500" />
                </CardHeader>
                <CardContent>
                  <div className="text-2xl font-bold text-gray-900">
                    {deletionRequests.filter(req => req.priority === 'HIGH').length}
                  </div>
                  <p className="text-xs text-orange-600 flex items-center gap-1 mt-1">
                    <AlertTriangle className="h-3 w-3" />
                    &gt; 7 days old
                  </p>
                </CardContent>
              </Card>

              <Card className="border-0 shadow-lg bg-gradient-to-br from-white to-green-50/30">
                <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                  <CardTitle className="text-sm font-medium text-gray-600">Completed</CardTitle>
                  <CheckCircle className="h-5 w-5 text-green-500" />
                </CardHeader>
                <CardContent>
                  <div className="text-2xl font-bold text-gray-900">
                    {deletionRequests.filter(req => req.status === 'COMPLETED').length}
                  </div>
                  <p className="text-xs text-green-600 flex items-center gap-1 mt-1">
                    <CheckCircle className="h-3 w-3" />
                    Processed
                  </p>
                </CardContent>
              </Card>
            </div>

            {/* Deletion Requests Table */}
            <Card className="border-0 shadow-lg">
              <CardHeader>
                <CardTitle className="flex items-center gap-2">
                  <UserX className="h-5 w-5 text-purple-500" />
                  Data Deletion Requests
                </CardTitle>
              </CardHeader>
              <CardContent>
                {loading.deletionRequests ? (
                  <div className="space-y-3">
                    {[...Array(5)].map((_, i) => (
                      <Skeleton key={i} className="h-20 w-full" />
                    ))}
                  </div>
                ) : (
                  <div className="space-y-4">
                    {deletionRequests.length === 0 ? (
                      <div className="text-center py-8">
                        <UserX className="h-12 w-12 mx-auto text-gray-300 mb-4" />
                        <p className="text-sm text-gray-500">No deletion requests found</p>
                      </div>
                    ) : (
                      <div className="space-y-2">
                        {deletionRequests.slice(0, 10).map((request) => (
                          <div key={request.id} className="flex items-center justify-between p-4 border rounded-lg hover:bg-gray-50">
                            <div className="flex items-center gap-4">
                              <div className={`w-3 h-3 rounded-full ${
                                request.priority === 'HIGH' ? 'bg-red-500' :
                                request.priority === 'MEDIUM' ? 'bg-yellow-500' : 'bg-green-500'
                              }`} />
                              <div>
                                <div className="font-medium">{request.user.email}</div>
                                <div className="text-sm text-gray-500">
                                  {request.user.first_name} {request.user.last_name} • {request.days_old} days ago
                                </div>
                                <div className="text-xs text-gray-400">{request.reason}</div>
                              </div>
                            </div>
                            <div className="flex items-center gap-2">
                              <span className={`px-2 py-1 text-xs rounded-full ${
                                request.priority === 'HIGH' ? 'bg-red-100 text-red-700' :
                                request.priority === 'MEDIUM' ? 'bg-yellow-100 text-yellow-700' :
                                'bg-green-100 text-green-700'
                              }`}>
                                {request.priority}
                              </span>
                              <span className={`px-2 py-1 text-xs rounded-full ${
                                request.status === 'COMPLETED' ? 'bg-green-100 text-green-700' :
                                request.status === 'IN_PROGRESS' ? 'bg-blue-100 text-blue-700' :
                                request.status === 'REJECTED' ? 'bg-red-100 text-red-700' :
                                'bg-gray-100 text-gray-700'
                              }`}>
                                {request.status}
                              </span>
                              <Button variant="outline" size="sm">
                                <CheckSquare className="h-4 w-4" />
                              </Button>
                              <Button variant="outline" size="sm">
                                <XSquare className="h-4 w-4" />
                              </Button>
                            </div>
                          </div>
                        ))}
                      </div>
                    )}
                  </div>
                )}
              </CardContent>
            </Card>
          </TabsContent>

          {/* Compliance Report Tab */}
          <TabsContent value="compliance" className="space-y-6">
            <div className="flex flex-col lg:flex-row lg:items-center lg:justify-between gap-4">
              <div>
                <h2 className="text-2xl font-bold text-gray-900">Compliance Reporting</h2>
                <p className="text-gray-600">Comprehensive compliance reports and analytics</p>
              </div>
              <div className="flex items-center gap-3">
                <Button variant="outline" size="sm" className="gap-2" onClick={fetchComplianceReport}>
                  <RefreshCw className="h-4 w-4" />
                  Generate Report
                </Button>
                <Button variant="outline" size="sm" className="gap-2">
                  <Download className="h-4 w-4" />
                  Export Report
                </Button>
              </div>
            </div>

            {/* Compliance Score */}
            {complianceReport && (
              <Card className="border-0 shadow-lg bg-gradient-to-br from-white to-blue-50/30">
                <CardHeader>
                  <CardTitle className="flex items-center gap-2">
                    <FileText className="h-5 w-5 text-blue-500" />
                    Compliance Score
                  </CardTitle>
                </CardHeader>
                <CardContent>
                  <div className="flex items-center gap-6">
                    <div className="text-6xl font-bold text-blue-600">
                      {complianceReport.compliance_score}
                    </div>
                    <div className="flex-1">
                      <div className="text-lg font-medium text-gray-900">Overall Compliance</div>
                      <div className="text-sm text-gray-600">
                        Based on security events, threats, and deletion request handling
                      </div>
                      <div className="mt-2">
                        <div className="w-full bg-gray-200 rounded-full h-2">
                          <div
                            className={`h-2 rounded-full ${
                              complianceReport.compliance_score >= 90 ? 'bg-green-500' :
                              complianceReport.compliance_score >= 70 ? 'bg-yellow-500' : 'bg-red-500'
                            }`}
                            style={{ width: `${complianceReport.compliance_score}%` }}
                          />
                        </div>
                      </div>
                    </div>
                  </div>
                </CardContent>
              </Card>
            )}

            {/* Compliance Summary */}
            {complianceReport && (
              <div className="grid gap-6 lg:grid-cols-2">
                <Card className="border-0 shadow-lg">
                  <CardHeader>
                    <CardTitle>Security Summary</CardTitle>
                  </CardHeader>
                  <CardContent className="space-y-4">
                    <div className="flex justify-between">
                      <span>Total Security Events</span>
                      <span className="font-medium">{complianceReport.security_events?.total_events || 0}</span>
                    </div>
                    <div className="flex justify-between">
                      <span>Critical Events</span>
                      <span className="font-medium text-red-600">{complianceReport.security_events?.critical_events || 0}</span>
                    </div>
                    <div className="flex justify-between">
                      <span>Unresolved Events</span>
                      <span className="font-medium text-orange-600">{complianceReport.security_events?.unresolved_events || 0}</span>
                    </div>
                    <div className="flex justify-between">
                      <span>Active Threats</span>
                      <span className="font-medium text-red-600">{complianceReport.security_threats?.active_threats || 0}</span>
                    </div>
                  </CardContent>
                </Card>

                <Card className="border-0 shadow-lg">
                  <CardHeader>
                    <CardTitle>Privacy Summary</CardTitle>
                  </CardHeader>
                  <CardContent className="space-y-4">
                    <div className="flex justify-between">
                      <span>Total Deletion Requests</span>
                      <span className="font-medium">{complianceReport.deletion_requests?.total_requests || 0}</span>
                    </div>
                    <div className="flex justify-between">
                      <span>Pending Requests</span>
                      <span className="font-medium text-yellow-600">{complianceReport.deletion_requests?.pending_requests || 0}</span>
                    </div>
                    <div className="flex justify-between">
                      <span>Overdue Requests</span>
                      <span className="font-medium text-red-600">{complianceReport.deletion_requests?.overdue_requests || 0}</span>
                    </div>
                    <div className="flex justify-between">
                      <span>Completed Requests</span>
                      <span className="font-medium text-green-600">{complianceReport.deletion_requests?.completed_requests || 0}</span>
                    </div>
                  </CardContent>
                </Card>
              </div>
            )}

            {loading.complianceReport && (
              <div className="text-center py-8">
                <Skeleton className="h-32 w-full mb-4" />
                <p className="text-sm text-gray-500">Generating compliance report...</p>
              </div>
            )}
          </TabsContent>
        </Tabs>
      </div>
    </div>
  );
};

export default PrivacyCompliance;
