import { Edit, Trash2 } from "lucide-react";
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from "@/components/ui/table";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import { useEffect, useImperativeHandle, forwardRef, useCallback } from "react";
import useApi from "@/hooks/useApi";
import { ADMIN_CATEGORIES, MAIN_URL } from "@/constant/urls";

// TypeScript interface matching the AdminCategorySerializer response
interface Category {
  id: number;
  name: string;
  description?: string;
  display_order: number;
  product_count: number;
}

interface CategoryListProps {
  onEdit: (category: Category) => void;
}

export interface CategoryListRef {
  refreshCategories: () => void;
}

export const CategoryList = forwardRef<CategoryListRef, CategoryListProps>(({ onEdit }, ref) => {
  const {
    read: categoryGet,
    data: categories,

  } = useApi(MAIN_URL);

  const refreshCategories = useCallback(() => {
    categoryGet(ADMIN_CATEGORIES);
  }, [categoryGet]);

  useImperativeHandle(ref, () => ({
    refreshCategories,
  }), [refreshCategories]);

  useEffect(() => {
    refreshCategories();
  }, [refreshCategories]);

  return (
    <div className="rounded-md border">
      <Table>
        <TableHeader>
          <TableRow>
            <TableHead>Name</TableHead>
            <TableHead>Description</TableHead>
            <TableHead className="w-[100px]">Display Order</TableHead>
            <TableHead className="w-[100px]">Products</TableHead>
            <TableHead className="w-[100px]">Actions</TableHead>
          </TableRow>
        </TableHeader>
        <TableBody>
          {Array.isArray(categories) && categories.map((category: Category) => (
            <TableRow key={category.id}>
              <TableCell className="font-medium">{category.name}</TableCell>
              <TableCell>{category.description || "No description"}</TableCell>
              <TableCell>{category.display_order}</TableCell>
              <TableCell>
                <Badge variant="secondary">{category.product_count}</Badge>
              </TableCell>
              <TableCell>
                <div className="flex space-x-2">
                  <Button
                    variant="ghost"
                    size="icon"
                    onClick={() => onEdit(category)}
                  >
                    <Edit className="h-4 w-4" />
                  </Button>
                  <Button variant="ghost" size="icon">
                    <Trash2 className="h-4 w-4 text-destructive" />
                  </Button>
                </div>
              </TableCell>
            </TableRow>
          ))}
        </TableBody>
      </Table>
    </div>
  );
});

CategoryList.displayName = "CategoryList";
