# nixpacks.toml - Production-Ready Django Configuration
# Optimized for reliable deployment with essential security features

# Setup phase - Install required system packages
[phases.setup]
nixPkgs = ["python311", "gcc"]

# Install phase - Install Python dependencies
[phases.install]
cmds = [
  "python3 -m venv /opt/venv",
  "source /opt/venv/bin/activate && python3 -m pip install --upgrade pip",
  "source /opt/venv/bin/activate && python3 -m pip install -r requirements.txt",
  "source /opt/venv/bin/activate && python3 -m pip install gunicorn",
  "source /opt/venv/bin/activate && python3 -m pip install --upgrade phonepe-sdk --extra-index-url https://phonepe.mycloudrepo.io/public/repositories/phonepe-pg-sdk-python",
  "source /opt/venv/bin/activate && python3 -m pip install requests boto3 urllib3"  # For CDN and MinIO sync scripts
]

# Build phase - Collect static files, run migrations, and tests
[phases.build]
cmds = [
  "mkdir -p media staticfiles logs",
  "source /opt/venv/bin/activate && DJANGO_SETTINGS_MODULE=backend.settings python3 manage.py collectstatic --noinput",
  "source /opt/venv/bin/activate && DJANGO_SETTINGS_MODULE=backend.settings python manage.py sync_databases --force",
  "source /opt/venv/bin/activate && DJANGO_SETTINGS_MODULE=backend.settings python verify_sync_setup.py",
  "source /opt/venv/bin/activate && DJANGO_SETTINGS_MODULE=backend.settings python manage.py check_replica_health --detailed",
  "source /opt/venv/bin/activate && DJANGO_SETTINGS_MODULE=backend.settings python3 manage.py makemigrations",
  "source /opt/venv/bin/activate && DJANGO_SETTINGS_MODULE=backend.settings python3 manage.py migrate",
  "source /opt/venv/bin/activate && DJANGO_SETTINGS_MODULE=tests.test_settings python3 manage.py makemigrations",
  "source /opt/venv/bin/activate && DJANGO_SETTINGS_MODULE=tests.test_settings python3 manage.py migrate",
  "source /opt/venv/bin/activate && DJANGO_SETTINGS_MODULE=tests.test_settings python3 -m pytest --cov=. --cov-report=term --tb=short -v || true",
  "source /opt/venv/bin/activate && DJANGO_SETTINGS_MODULE=backend.settings python3 sync_media_to_minio.py --media-dir media --force --skip-errors || true"
]

# Start command - Run Gunicorn with appropriate settings
[start]
cmd = "DISABLE_FILE_LOGGING=false DJANGO_SETTINGS_MODULE=backend.settings gunicorn backend.wsgi:application --bind=0.0.0.0:$PORT --workers=4 --threads=2 --worker-class=gthread --worker-tmp-dir=/dev/shm --log-file=- --access-logfile=- --error-logfile=- --capture-output --enable-stdio-inheritance --forwarded-allow-ips='*'"

# Environment variables
[variables]
PYTHONUNBUFFERED = "1"
PYTHONDONTWRITEBYTECODE = "1"
DEBUG = "0"
DEVELOPMENT = "false"
STATIC_ROOT = "staticfiles"
STATIC_URL = "/static/"

# Security settings - provide valid Fernet encryption key for deployment
# NOTE: This is a deployment key. Set a different ENCRYPTION_KEY in production environment variables
ENCRYPTION_KEY = "HAL3pnvK7sxtefqebWmzY6y_u4Dz58X3FfLj3BlZJw8="

# Disable file logging during build to prevent directory issues
DISABLE_FILE_LOGGING = "true"

# CORS configuration for production
CORS_ALLOW_ALL_ORIGINS = "false"
CORS_ALLOW_CREDENTIALS = "true"



# MinIO configuration
USE_MINIO = "true"
MINIO_STORAGE_ENDPOINT = "minio-triumph.trio.net.in"
MINIO_STORAGE_ACCESS_KEY = "sGMhBtgAJbSCLqTu"
MINIO_STORAGE_SECRET_KEY = "AEcMahOmlUIC1noyDcwuHLh5y5lSY2ju"
MINIO_STORAGE_USE_HTTPS = "true"
MINIO_STORAGE_MEDIA_BUCKET_NAME = "media"
MINIO_STORAGE_AUTO_CREATE_MEDIA_BUCKET = "true"

# Note: Additional security management commands are available but not run during deployment:
# - python manage.py security_audit (generates security reports)
# - python manage.py validate_encryption (validates encryption setup)
# - python manage.py monitor_encryption (monitors encryption health)
# These can be run manually after deployment if needed.

