# Generated by Django 5.0.2 on 2025-06-20 09:25

from django.db import migrations, models


class Migration(migrations.Migration):

    initial = True

    dependencies = [
    ]

    operations = [
        migrations.CreateModel(
            name='SecurityEvent',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('event_type', models.CharField(choices=[('LOGIN_FAILED', 'Login Failed'), ('LOGIN_SUCCESS', 'Login Success'), ('LOGOUT', 'Logout'), ('PASSWORD_CHANGE', 'Password Change'), ('ACCOUNT_LOCKED', 'Account Locked'), ('SUSPICIOUS_ACTIVITY', 'Suspicious Activity'), ('DATA_ACCESS', 'Data Access'), ('ADMIN_ACTION', 'Admin Action')], max_length=20)),
                ('ip_address', models.GenericIPAddressField()),
                ('user_agent', models.TextField(blank=True)),
                ('timestamp', models.DateTimeField(auto_now_add=True)),
                ('details', models.J<PERSON>NField(blank=True, default=dict)),
            ],
            options={
                'ordering': ['-timestamp'],
            },
        ),
        migrations.CreateModel(
            name='BlockedIP',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('ip_address', models.GenericIPAddressField(unique=True)),
                ('reason', models.TextField(default='Multiple failed login attempts')),
                ('blocked_at', models.DateTimeField(auto_now_add=True)),
                ('blocked_until', models.DateTimeField()),
                ('is_permanent', models.BooleanField(default=False)),
            ],
            options={
                'ordering': ['-blocked_at'],
                'indexes': [models.Index(fields=['ip_address', 'blocked_until'], name='backend_blo_ip_addr_081fcc_idx')],
            },
        ),
        migrations.CreateModel(
            name='FailedLoginAttempt',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('ip_address', models.GenericIPAddressField()),
                ('username', models.CharField(blank=True, max_length=150)),
                ('timestamp', models.DateTimeField(auto_now_add=True)),
                ('user_agent', models.TextField(blank=True)),
                ('attempt_count', models.PositiveIntegerField(default=1)),
                ('attempted_email', models.EmailField(blank=True, max_length=254)),
            ],
            options={
                'ordering': ['-timestamp'],
                'indexes': [models.Index(fields=['ip_address', 'timestamp'], name='backend_fai_ip_addr_59e0a6_idx'), models.Index(fields=['username', 'timestamp'], name='backend_fai_usernam_908b02_idx')],
            },
        ),
    ]
