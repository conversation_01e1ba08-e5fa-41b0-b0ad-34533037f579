"""
Comprehensive tests for backend core services and configurations
Tests ASGI, WSGI, CDN settings, MinIO settings, and management commands
"""

import os
from unittest.mock import patch
from django.test import TestCase
from django.core.management import call_command
from django.core.management.base import CommandError
from io import StringIO

# Test ASGI Configuration
class TestASGIConfiguration(TestCase):
    """Test ASGI application configuration"""
    
    def test_asgi_application_import(self):
        """Test that ASGI application can be imported"""
        from backend.asgi import application
        self.assertIsNotNone(application)
    
    @patch.dict(os.environ, {'DJANGO_SETTINGS_MODULE': 'backend.settings'})
    def test_asgi_settings_module(self):
        """Test ASGI with correct settings module"""
        from backend.asgi import application
        self.assertIsNotNone(application)

# Test WSGI Configuration  
class TestWSGIConfiguration(TestCase):
    """Test WSGI application configuration"""
    
    def test_wsgi_application_import(self):
        """Test that WSGI application can be imported"""
        from backend.wsgi import application
        self.assertIsNotNone(application)
    
    @patch.dict(os.environ, {'DJANGO_SETTINGS_MODULE': 'backend.settings'})
    def test_wsgi_settings_module(self):
        """Test WSGI with correct settings module"""
        from backend.wsgi import application
        self.assertIsNotNone(application)

# Test CDN Settings (Basic functionality only)
class TestCDNSettings(TestCase):
    """Test CDN configuration and settings"""

    def test_cdn_settings_import(self):
        """Test CDN settings can be imported"""
        try:
            from backend.cdn_settings import USE_CDN, CDN_BASE_URL, get_cdn_url
            # Test that variables exist
            self.assertIsNotNone(USE_CDN)
            self.assertIsNotNone(CDN_BASE_URL)
            self.assertTrue(callable(get_cdn_url))
        except ImportError:
            self.fail("CDN settings import failed")

    def test_get_cdn_url_function(self):
        """Test CDN URL generation function"""
        from backend.cdn_settings import get_cdn_url

        # Test basic functionality
        url = get_cdn_url('/media/test.jpg')
        self.assertIn('test.jpg', url)

        # Test with static path
        url = get_cdn_url('/static/css/style.css')
        self.assertIn('style.css', url)

# Test MinIO Settings
class TestMinIOSettings(TestCase):
    """Test MinIO storage configuration"""

    def test_minio_settings_import(self):
        """Test MinIO settings can be imported"""
        try:
            from backend.minio_settings import get_minio_media_url, USE_MINIO
            # Test that functions and variables exist
            self.assertTrue(callable(get_minio_media_url))
            self.assertIsNotNone(USE_MINIO)
        except ImportError:
            self.fail("MinIO settings import failed")

    @patch('backend.minio_settings.USE_MINIO', False)
    def test_minio_disabled(self):
        """Test MinIO functionality when disabled"""
        from backend.minio_settings import get_minio_media_url

        url = get_minio_media_url('test.jpg')
        self.assertIn('test.jpg', url)

    @patch('backend.minio_settings.USE_MINIO', True)
    @patch('backend.minio_settings.MINIO_STORAGE_ENDPOINT', 'localhost:9000')
    @patch('backend.minio_settings.MINIO_STORAGE_MEDIA_BUCKET_NAME', 'test-bucket')
    @patch('backend.minio_settings.MINIO_STORAGE_USE_HTTPS', False)
    def test_minio_url_generation(self):
        """Test MinIO URL generation"""
        from backend.minio_settings import get_minio_media_url

        url = get_minio_media_url('test.jpg')
        self.assertIn('test.jpg', url)
        self.assertIn('localhost:9000', url)

    def test_minio_path_cleaning(self):
        """Test MinIO path cleaning functionality"""
        from backend.minio_settings import get_minio_media_url

        # Test path with backslashes
        url = get_minio_media_url('folder\\test.jpg')
        self.assertIn('folder/test.jpg', url)

        # Test path with media prefix
        url = get_minio_media_url('media/test.jpg')
        self.assertIn('test.jpg', url)

# Test Management Commands
class TestManagementCommands(TestCase):
    """Test Django management commands"""

    def test_security_audit_command(self):
        """Test security audit management command"""
        out = StringIO()
        try:
            call_command('security_audit', stdout=out)
            output = out.getvalue()
            self.assertIn('security audit', output.lower())
        except CommandError:
            # Command might not be fully implemented
            pass

    def test_security_cleanup_command(self):
        """Test security cleanup management command"""
        out = StringIO()
        try:
            call_command('security_cleanup', stdout=out)
            output = out.getvalue()
            self.assertIn('cleanup', output.lower())
        except CommandError:
            # Command might not be fully implemented
            pass

    def test_clear_security_blocks_command(self):
        """Test clear security blocks management command"""
        out = StringIO()
        try:
            call_command('clear_security_blocks', '--all', stdout=out)
            output = out.getvalue()
            self.assertIn('cleared', output.lower())
        except CommandError:
            # Command might not be fully implemented
            pass

    def test_test_redis_command(self):
        """Test Redis testing management command"""
        out = StringIO()
        try:
            call_command('test_redis', stdout=out)
            output = out.getvalue()
            self.assertIn('redis', output.lower())
        except CommandError:
            # Command might not be fully implemented
            pass

    def test_test_security_notifications_command(self):
        """Test security notifications testing command"""
        out = StringIO()
        try:
            call_command('test_security_notifications', '--dry-run', stdout=out)
            output = out.getvalue()
            self.assertIn('notification', output.lower())
        except CommandError:
            # Command might not be fully implemented
            pass

# Test Backend Settings
class TestBackendSettings(TestCase):
    """Test backend settings configuration"""

    def test_settings_import(self):
        """Test that settings can be imported"""
        from django.conf import settings
        self.assertIsNotNone(settings.SECRET_KEY)
        self.assertIsNotNone(settings.DATABASES)

    def test_installed_apps(self):
        """Test that all required apps are installed"""
        from django.conf import settings

        required_apps = [
            'django.contrib.admin',
            'django.contrib.auth',
            'rest_framework',
            'users',
            'products',
            'orders',
            'promotions',
            'dashboard',
            'payment_gateway'
        ]

        for app in required_apps:
            self.assertIn(app, settings.INSTALLED_APPS)

    def test_middleware_configuration(self):
        """Test middleware configuration"""
        from django.conf import settings

        required_middleware = [
            'django.middleware.security.SecurityMiddleware',
            'corsheaders.middleware.CorsMiddleware',
            'django.contrib.sessions.middleware.SessionMiddleware'
        ]

        for middleware in required_middleware:
            self.assertIn(middleware, settings.MIDDLEWARE)

    def test_database_configuration(self):
        """Test database configuration"""
        from django.conf import settings

        self.assertIn('default', settings.DATABASES)
        self.assertIn('ENGINE', settings.DATABASES['default'])

    def test_rest_framework_configuration(self):
        """Test REST framework configuration"""
        from django.conf import settings

        self.assertIn('REST_FRAMEWORK', dir(settings))
        if hasattr(settings, 'REST_FRAMEWORK'):
            self.assertIsInstance(settings.REST_FRAMEWORK, dict)
