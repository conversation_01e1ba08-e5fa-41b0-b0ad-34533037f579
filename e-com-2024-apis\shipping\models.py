from django.db import models
from django.core.validators import MinValueValidator
from uuid import uuid4


class RapidshypConfiguration(models.Model):
    """Store Rapidshyp configuration settings"""
    store_name = models.CharField(max_length=100, default='DEFAULT')
    pickup_address_name = models.CharField(max_length=100)
    default_pickup_pincode = models.CharField(max_length=6)
    contact_name = models.CharField(max_length=100)
    contact_phone = models.CharField(max_length=15)
    contact_email = models.EmailField()
    address_line_1 = models.Char<PERSON>ield(max_length=200)
    address_line_2 = models.Char<PERSON>ield(max_length=200, blank=True)
    is_active = models.BooleanField(default=True)
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)
    
    class Meta:
        verbose_name = "Rapidshyp Configuration"
        verbose_name_plural = "Rapidshyp Configurations"
    
    def __str__(self):
        return f"{self.store_name} - {self.pickup_address_name}"


class RapidshypShipment(models.Model):
    """Rapidshyp shipment data - linked to existing Order"""
    
    STATUS_CHOICES = [
        ('SCB', 'Shipment Booked'),
        ('PSH', 'Pickup Scheduled'),
        ('OFP', 'Out for Pickup'),
        ('PUE', 'Pick up Exception'),
        ('PCN', 'Pickup Cancelled'),
        ('PUC', 'Pickup Completed'),
        ('SPD', 'Shipped/Dispatched'),
        ('INT', 'In Transit'),
        ('RAD', 'Reached at Destination'),
        ('DED', 'Delivery Delayed'),
        ('OFD', 'Out for Delivery'),
        ('DEL', 'Delivered'),
        ('UND', 'Undelivered'),
        ('RTO_REQ', 'RTO Requested'),
        ('RTO', 'RTO Confirmed'),
        ('RTO_INT', 'RTO In Transit'),
        ('RTO_RAD', 'RTO - Reached at Destination'),
        ('RTO_OFD', 'RTO Out for Delivery'),
        ('RTO_DEL', 'RTO Delivered'),
        ('RTO_UND', 'RTO Undelivered'),
        ('CAN', 'Shipment Cancelled'),
        ('ONH', 'Shipment On Hold'),
        ('LST', 'Shipment Lost'),
        ('DMG', 'Shipment Damaged'),
        ('MSR', 'Shipment Misrouted'),
        ('DPO', 'Shipment Disposed-Off'),
    ]
    
    id = models.UUIDField(primary_key=True, default=uuid4, editable=False)
    order = models.OneToOneField('orders.Order', on_delete=models.CASCADE, 
                                related_name='rapidshyp_shipment')
    rapidshyp_order_id = models.CharField(max_length=100, unique=True)
    shipment_id = models.CharField(max_length=100, blank=True)
    awb_number = models.CharField(max_length=100, blank=True)
    courier_code = models.CharField(max_length=20)
    courier_name = models.CharField(max_length=100)
    parent_courier_name = models.CharField(max_length=100, blank=True)
    
    # Status and Tracking
    current_status = models.CharField(max_length=20, choices=STATUS_CHOICES, default='SCB')
    status_description = models.TextField(blank=True)
    pickup_scheduled = models.BooleanField(default=False)
    pickup_date = models.DateTimeField(null=True, blank=True)
    expected_delivery_date = models.DateField(null=True, blank=True)
    actual_delivery_date = models.DateTimeField(null=True, blank=True)
    
    # Shipping Details
    freight_mode = models.CharField(max_length=50, blank=True)  # Surface, Air
    total_freight = models.DecimalField(max_digits=10, decimal_places=2, 
                                      validators=[MinValueValidator(0)])
    cutoff_time = models.CharField(max_length=10, blank=True)  # e.g., "14:00"
    max_weight = models.DecimalField(max_digits=8, decimal_places=2, null=True, blank=True)
    min_weight = models.DecimalField(max_digits=8, decimal_places=2, null=True, blank=True)
    
    # Documents and URLs
    label_url = models.URLField(blank=True)
    invoice_url = models.URLField(blank=True)
    manifest_url = models.URLField(blank=True)
    tracking_url = models.URLField(blank=True)
    
    # Metadata
    rapidshyp_response_data = models.JSONField(null=True, blank=True, 
                                             help_text="Raw response from Rapidshyp API")
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)
    
    class Meta:
        ordering = ['-created_at']
        indexes = [
            models.Index(fields=['order']),
            models.Index(fields=['awb_number']),
            models.Index(fields=['current_status']),
            models.Index(fields=['created_at']),
            models.Index(fields=['rapidshyp_order_id']),
        ]
        verbose_name = "Rapidshyp Shipment"
        verbose_name_plural = "Rapidshyp Shipments"
    
    def __str__(self):
        return f"Shipment {self.rapidshyp_order_id} - {self.courier_name}"
    
    @property
    def is_delivered(self):
        """Check if shipment is delivered"""
        return self.current_status == 'DEL'
    
    @property
    def is_in_transit(self):
        """Check if shipment is in transit"""
        return self.current_status in ['SPD', 'INT', 'RAD', 'OFD']
    
    @property
    def is_returned(self):
        """Check if shipment is returned (RTO)"""
        return self.current_status.startswith('RTO')


class ShippingRateCache(models.Model):
    """Cache shipping rates for performance optimization"""
    pickup_pincode = models.CharField(max_length=6)
    delivery_pincode = models.CharField(max_length=6)
    weight = models.DecimalField(max_digits=8, decimal_places=2)
    cod = models.BooleanField(default=False)
    total_order_value = models.DecimalField(max_digits=10, decimal_places=2, default=0)
    
    # Cached rate data
    rates_data = models.JSONField(help_text="Cached response from Rapidshyp serviceability API")
    is_serviceable = models.BooleanField(default=False)
    
    # Cache metadata
    cached_at = models.DateTimeField(auto_now_add=True)
    expires_at = models.DateTimeField()
    
    class Meta:
        unique_together = ['pickup_pincode', 'delivery_pincode', 'weight', 'cod']
        indexes = [
            models.Index(fields=['pickup_pincode', 'delivery_pincode']),
            models.Index(fields=['cached_at']),
            models.Index(fields=['expires_at']),
        ]
        verbose_name = "Shipping Rate Cache"
        verbose_name_plural = "Shipping Rate Cache"
    
    def __str__(self):
        return f"Rate cache: {self.pickup_pincode} → {self.delivery_pincode} ({self.weight}kg)"
    
    @property
    def is_expired(self):
        """Check if cache entry is expired"""
        from django.utils import timezone
        return timezone.now() > self.expires_at


class TrackingEvent(models.Model):
    """Store tracking events for audit trail and detailed tracking"""
    shipment = models.ForeignKey(RapidshypShipment, on_delete=models.CASCADE, 
                               related_name='tracking_events')
    status = models.CharField(max_length=20)
    status_description = models.TextField()
    location = models.CharField(max_length=200, blank=True)
    remarks = models.TextField(blank=True)
    event_timestamp = models.DateTimeField()
    
    # Metadata
    created_at = models.DateTimeField(auto_now_add=True)
    
    class Meta:
        ordering = ['-event_timestamp']
        indexes = [
            models.Index(fields=['shipment', 'event_timestamp']),
            models.Index(fields=['status']),
        ]
        verbose_name = "Tracking Event"
        verbose_name_plural = "Tracking Events"
    
    def __str__(self):
        return f"{self.shipment.rapidshyp_order_id} - {self.status} at {self.event_timestamp}"


class RapidshypAPILog(models.Model):
    """Log all Rapidshyp API calls for debugging and monitoring"""
    
    API_METHODS = [
        ('serviceability_check', 'Serviceability Check'),
        ('create_order', 'Create Order'),
        ('assign_awb', 'Assign AWB'),
        ('schedule_pickup', 'Schedule Pickup'),
        ('track_order', 'Track Order'),
        ('generate_label', 'Generate Label'),
        ('cancel_order', 'Cancel Order'),
    ]
    
    method = models.CharField(max_length=50, choices=API_METHODS)
    endpoint = models.CharField(max_length=200)
    request_data = models.JSONField()
    response_data = models.JSONField(null=True, blank=True)
    response_status_code = models.IntegerField(null=True, blank=True)
    response_time_ms = models.IntegerField(null=True, blank=True)
    
    # Error tracking
    is_success = models.BooleanField(default=False)
    error_message = models.TextField(blank=True)
    
    # Metadata
    created_at = models.DateTimeField(auto_now_add=True)
    
    class Meta:
        ordering = ['-created_at']
        indexes = [
            models.Index(fields=['method', 'created_at']),
            models.Index(fields=['is_success']),
            models.Index(fields=['created_at']),
        ]
        verbose_name = "Rapidshyp API Log"
        verbose_name_plural = "Rapidshyp API Logs"
    
    def __str__(self):
        status = "✓" if self.is_success else "✗"
        return f"{status} {self.method} - {self.created_at.strftime('%Y-%m-%d %H:%M')}"
