from django.contrib.auth.models import AbstractUser
from django.db import models
from django.utils import timezone
from django.core.exceptions import ValidationError
from users.manager import CustomUserManager
from backend.encryption_utils import Encrypted<PERSON>harField, EncryptedEmailField
import json
import secrets
import hashlib
from datetime import timedelta



class Customer(AbstractUser):
    # Replace phone_number with encrypted version
    phone_number = Encrypted<PERSON>harField(max_length=15, blank=True)
    date_of_birth = models.DateField(null=True, blank=True)  # Keep as-is (not sensitive)
    is_verified = models.BooleanField(default=False)
    email = models.EmailField(unique=True)  # Keep as-is (used for login)
    profile_image_url = models.URLField(null=True, blank=True)
    image = models.ImageField(upload_to="profile_images", null=True, blank=True)
    username = None
    first_name = None
    last_name = None
    name = models.CharField(max_length=512)  # Keep as-is (not highly sensitive)
    USERNAME_FIELD = "email"
    REQUIRED_FIELDS = []
    objects = CustomUserManager()

    def __str__(self):
        return self.email

    @property
    def display_image_url(self):
        if self.image:
            return self.image.url
        return self.profile_image_url

    class Meta:
        ordering = ["-date_joined"]
        indexes = [
            models.Index(fields=['email', 'is_active']),
            models.Index(fields=['name']),
            models.Index(fields=['phone_number']),
            models.Index(fields=['date_joined']),
        ]


class Address(models.Model):
    ADDRESS_TYPES = (
        ("BILLING", "Billing"),
        ("SHIPPING", "Shipping"),
    )

    user = models.ForeignKey(
        Customer, on_delete=models.CASCADE, related_name="addresses"
    )
    address_type = models.CharField(max_length=10, choices=ADDRESS_TYPES)
    # Encrypt sensitive address fields
    street_address = EncryptedCharField(max_length=255)
    apartment = EncryptedCharField(max_length=50, blank=True)
    city = EncryptedCharField(max_length=100)
    state = EncryptedCharField(max_length=100)
    country = models.CharField(max_length=100, blank=True)  # Keep as-is (not highly sensitive)
    postal_code = EncryptedCharField(max_length=20)
    order_user_phone = EncryptedCharField(max_length=15, blank=True)
    order_user_email = EncryptedEmailField(blank=True)
    is_default = models.BooleanField(default=False)

    class Meta:
        ordering = ["-is_default"]

    def save(self, *args, **kwargs):
        if self.is_default:
            # Set all other addresses of the same type to non-default
            Address.objects.filter(
                user=self.user, address_type=self.address_type, is_default=True
            ).update(is_default=False)
        super().save(*args, **kwargs)


class PaymentMethod(models.Model):
    user = models.ForeignKey(
        Customer, on_delete=models.CASCADE, related_name="payment_methods"
    )
    card_type = models.CharField(max_length=50)
    last_four = models.CharField(max_length=4)
    stripe_payment_method_id = models.CharField(max_length=100)
    is_default = models.BooleanField(default=False)
    created_at = models.DateTimeField(auto_now_add=True)

    class Meta:
        ordering = ["-is_default", "-created_at"]

    def save(self, *args, **kwargs):
        if self.is_default:
            PaymentMethod.objects.filter(user=self.user, is_default=True).update(
                is_default=False
            )
        super().save(*args, **kwargs)


class Wishlist(models.Model):
    user = models.ForeignKey(
        Customer, on_delete=models.CASCADE, related_name="wishlist"
    )
    product = models.ForeignKey("products.Product", on_delete=models.CASCADE)
    added_at = models.DateTimeField(auto_now_add=True)

    class Meta:
        unique_together = ("user", "product")
        ordering = ["-added_at"]


class ContactMessage(models.Model):
    # Encrypt sensitive contact information
    name = EncryptedCharField(max_length=100)
    email = EncryptedEmailField()
    subject = models.CharField(max_length=200)  # Keep as-is (business requirement)
    message = models.TextField()  # Keep as-is (business requirement)
    created_at = models.DateTimeField(auto_now_add=True)
    is_read = models.BooleanField(default=False)

    class Meta:
        ordering = ['-created_at']

    def __str__(self):
        return f"{self.name} - {self.subject}"


# Privacy and Security Models for Compliance

class UserConsent(models.Model):
    """Track user consent for various data processing activities"""

    CONSENT_TYPES = [
        ('ESSENTIAL', 'Essential - Required for service operation'),
        ('MARKETING', 'Marketing - Email marketing and promotions'),
        ('ANALYTICS', 'Analytics - Usage analytics and tracking'),
        ('PERSONALIZATION', 'Personalization - Personalized content and recommendations'),
        ('THIRD_PARTY', 'Third Party - Data sharing with third parties'),
        ('COOKIES', 'Cookies - Non-essential cookies'),
    ]

    user = models.ForeignKey(Customer, on_delete=models.CASCADE, related_name='consents')
    consent_type = models.CharField(max_length=20, choices=CONSENT_TYPES)
    granted = models.BooleanField(default=False)
    granted_at = models.DateTimeField(null=True, blank=True)
    withdrawn_at = models.DateTimeField(null=True, blank=True)
    ip_address = models.GenericIPAddressField()
    user_agent = models.TextField(blank=True)
    consent_version = models.CharField(max_length=10, default='1.0')

    # Metadata
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)

    class Meta:
        unique_together = ['user', 'consent_type']
        indexes = [
            models.Index(fields=['user', 'consent_type']),
            models.Index(fields=['granted', 'granted_at']),
        ]

    def __str__(self):
        status = "Granted" if self.granted else "Withdrawn"
        return f"{self.user.email} - {self.consent_type} - {status}"

    def grant_consent(self, ip_address, user_agent=''):
        """Grant consent for this type"""
        self.granted = True
        self.granted_at = timezone.now()
        self.withdrawn_at = None
        self.ip_address = ip_address
        self.user_agent = user_agent
        self.save()

    def withdraw_consent(self, ip_address, user_agent=''):
        """Withdraw consent for this type"""
        self.granted = False
        self.withdrawn_at = timezone.now()
        self.ip_address = ip_address
        self.user_agent = user_agent
        self.save()



class DataDeletionRequest(models.Model):
    """Track user requests for data deletion (Right to Erasure)"""

    STATUS_CHOICES = [
        ('PENDING', 'Pending'),
        ('IN_PROGRESS', 'In Progress'),
        ('COMPLETED', 'Completed'),
        ('REJECTED', 'Rejected'),
    ]

    user = models.ForeignKey(Customer, on_delete=models.CASCADE)
    request_date = models.DateTimeField(auto_now_add=True)
    status = models.CharField(max_length=20, choices=STATUS_CHOICES, default='PENDING')
    reason = models.TextField(blank=True)

    # Processing details
    processed_by = models.ForeignKey(Customer, on_delete=models.SET_NULL, null=True, related_name='processed_deletions')
    processed_at = models.DateTimeField(null=True, blank=True)
    processing_notes = models.TextField(blank=True)

    # Technical details
    ip_address = models.GenericIPAddressField()
    user_agent = models.TextField(blank=True)

    class Meta:
        ordering = ['-request_date']

    def __str__(self):
        return f"Deletion request - {self.user.email} - {self.status}"

    def complete_deletion(self, processed_by, notes=''):
        """Mark deletion request as completed"""
        self.status = 'COMPLETED'
        self.processed_by = processed_by
        self.processed_at = timezone.now()
        self.processing_notes = notes
        self.save()

        # Send status update email
        try:
            from .utils import send_deletion_status_update_email
            send_deletion_status_update_email(self)
        except Exception as e:
            import logging
            logger = logging.getLogger(__name__)
            logger.error(f"Error sending deletion status update email: {str(e)}")

    def reject_deletion(self, processed_by, notes=''):
        """Mark deletion request as rejected"""
        self.status = 'REJECTED'
        self.processed_by = processed_by
        self.processed_at = timezone.now()
        self.processing_notes = notes
        self.save()

        # Send status update email
        try:
            from .utils import send_deletion_status_update_email
            send_deletion_status_update_email(self)
        except Exception as e:
            import logging
            logger = logging.getLogger(__name__)
            logger.error(f"Error sending deletion status update email: {str(e)}")

    def update_status(self, new_status, processed_by=None, notes=''):
        """Update deletion request status and send notification"""
        old_status = self.status
        self.status = new_status
        if processed_by:
            self.processed_by = processed_by
        if new_status in ['COMPLETED', 'REJECTED']:
            self.processed_at = timezone.now()
        self.processing_notes = notes
        self.save()

        # Send status update email if status changed to a final state
        if old_status != new_status and new_status in ['COMPLETED', 'REJECTED', 'IN_PROGRESS']:
            try:
                from .utils import send_deletion_status_update_email
                send_deletion_status_update_email(self)
            except Exception as e:
                import logging
                logger = logging.getLogger(__name__)
                logger.error(f"Error sending deletion status update email: {str(e)}")


class PasswordResetToken(models.Model):
    """Model to handle password reset tokens with security features"""
    user = models.ForeignKey(Customer, on_delete=models.CASCADE, related_name='password_reset_tokens')
    token = models.CharField(max_length=100, unique=True)
    token_hash = models.CharField(max_length=64)  # SHA256 hash of the token
    created_at = models.DateTimeField(auto_now_add=True)
    expires_at = models.DateTimeField()
    is_used = models.BooleanField(default=False)
    ip_address = models.GenericIPAddressField()
    user_agent = models.TextField(blank=True)

    class Meta:
        ordering = ['-created_at']
        indexes = [
            models.Index(fields=['token_hash']),
            models.Index(fields=['user', 'created_at']),
            models.Index(fields=['expires_at']),
        ]

    def save(self, *args, **kwargs):
        if not self.token:
            # Generate a secure random token
            self.token = secrets.token_urlsafe(32)
            self.token_hash = hashlib.sha256(self.token.encode()).hexdigest()

        if not self.expires_at:
            # Token expires in 15 minutes
            self.expires_at = timezone.now() + timedelta(minutes=15)

        super().save(*args, **kwargs)

    def is_valid(self):
        """Check if token is still valid"""
        return not self.is_used and timezone.now() < self.expires_at

    def mark_as_used(self):
        """Mark token as used"""
        self.is_used = True
        self.save()

    @classmethod
    def cleanup_expired_tokens(cls):
        """Remove expired tokens"""
        expired_tokens = cls.objects.filter(expires_at__lt=timezone.now())
        count = expired_tokens.count()
        expired_tokens.delete()
        return count


class PasswordHistory(models.Model):
    """Track password history to prevent reuse"""
    user = models.ForeignKey(Customer, on_delete=models.CASCADE, related_name='password_history')
    password_hash = models.CharField(max_length=128)  # Store Django's password hash
    created_at = models.DateTimeField(auto_now_add=True)

    class Meta:
        ordering = ['-created_at']
        indexes = [
            models.Index(fields=['user', 'created_at']),
        ]

    @classmethod
    def add_password(cls, user, password_hash):
        """Add a new password to history and maintain only last 5"""
        # Add new password
        cls.objects.create(user=user, password_hash=password_hash)

        # Keep only last 5 passwords
        user_passwords = cls.objects.filter(user=user).order_by('-created_at')
        if user_passwords.count() > 5:
            # Delete older passwords beyond the last 5
            old_passwords = user_passwords[5:]
            cls.objects.filter(id__in=[p.id for p in old_passwords]).delete()

    @classmethod
    def check_password_reuse(cls, user, new_password):
        """Check if password was used in last 5 passwords"""
        from django.contrib.auth.hashers import check_password

        recent_passwords = cls.objects.filter(user=user).order_by('-created_at')[:5]
        for password_record in recent_passwords:
            if check_password(new_password, password_record.password_hash):
                return True
        return False
