import os
import json
import time
import requests
import concurrent.futures
from selenium import webdriver
from selenium.webdriver.chrome.options import Options
from selenium.webdriver.common.by import By
from selenium.webdriver.support.ui import WebDriverWait
from selenium.webdriver.support import expected_conditions as EC
from urllib.parse import quote_plus
import re
import logging
from PIL import Image
from io import BytesIO

# Set up logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler("simple_image_scraper.log"),
        logging.StreamHandler()
    ]
)
logger = logging.getLogger(__name__)

# Constants
MAX_THREADS = 4
MAX_IMAGES_PER_PRODUCT = 5
TIMEOUT = 10
SEARCH_SOURCES = ["google"]  # Simplified to just use Google for now
OUTPUT_DIR = "output/images"
JSON_FILE_PATH = "output/json/product_data.json"

# Create output directory structure
os.makedirs(OUTPUT_DIR, exist_ok=True)

def setup_driver():
    """Set up and return a Chrome WebDriver with appropriate options."""
    try:
        chrome_options = Options()
        chrome_options.add_argument("--headless")
        chrome_options.add_argument("--disable-gpu")
        chrome_options.add_argument("--no-sandbox")
        chrome_options.add_argument("--disable-dev-shm-usage")
        chrome_options.add_argument("--window-size=1920,1080")
        chrome_options.add_argument("--user-agent=Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36")
        
        # Try to use Chrome directly
        driver = webdriver.Chrome(options=chrome_options)
        logger.info("Successfully initialized Chrome WebDriver")
        return driver
    except Exception as e:
        logger.error(f"Failed to initialize Chrome WebDriver: {str(e)}")
        
        # Try using Edge as a fallback
        try:
            from selenium.webdriver.edge.options import Options as EdgeOptions
            
            edge_options = EdgeOptions()
            edge_options.add_argument("--headless")
            edge_options.add_argument("--disable-gpu")
            edge_options.add_argument("--no-sandbox")
            edge_options.add_argument("--disable-dev-shm-usage")
            
            driver = webdriver.Edge(options=edge_options)
            logger.info("Successfully initialized Edge WebDriver as fallback")
            return driver
        except Exception as e2:
            logger.error(f"Failed to initialize Edge WebDriver: {str(e2)}")
            raise Exception("Could not initialize any WebDriver")

def download_image(url, product_dir, filename):
    """Download an image from URL and save it to the specified directory."""
    try:
        response = requests.get(url, timeout=10)
        if response.status_code == 200:
            # Verify it's an image
            try:
                img = Image.open(BytesIO(response.content))
                img_path = os.path.join(product_dir, filename)
                img.save(img_path)
                logger.info(f"Downloaded image: {img_path}")
                return img_path
            except Exception as e:
                logger.error(f"Not a valid image: {url}, Error: {str(e)}")
                return None
        else:
            logger.error(f"Failed to download image: {url}, Status code: {response.status_code}")
            return None
    except Exception as e:
        logger.error(f"Error downloading image: {url}, Error: {str(e)}")
        return None

def search_google(driver, query):
    """Search for product images on Google Images."""
    image_urls = []
    try:
        search_url = f"https://www.google.com/search?q={quote_plus(query)}&tbm=isch"
        driver.get(search_url)
        time.sleep(2)  # Allow page to load
        
        # Scroll down to load more images
        driver.execute_script("window.scrollTo(0, document.body.scrollHeight);")
        time.sleep(1)
        
        # Find image elements
        image_elements = driver.find_elements(By.CSS_SELECTOR, "img.rg_i, img.Q4LuWd")
        
        # Extract image URLs
        for img in image_elements[:MAX_IMAGES_PER_PRODUCT]:
            try:
                # Get the src directly
                src = img.get_attribute("src")
                if src and src.startswith("http") and src not in image_urls and not src.endswith(".gif"):
                    image_urls.append(src)
                    if len(image_urls) >= MAX_IMAGES_PER_PRODUCT:
                        break
            except Exception as e:
                logger.error(f"Error extracting image URL: {str(e)}")
                continue
                
    except Exception as e:
        logger.error(f"Error in Google search: {str(e)}")
    
    return image_urls

def process_product(product):
    """Process a single product to find and download images."""
    # Create a unique search query for the product
    model_number = product.get("model_number", "")
    name = product.get("name", "")
    brand = product.get("brand", "")
    category = product.get("category", "")
    
    # Create search query with all relevant information
    search_query = f"{brand} {name} {model_number} {category}"
    
    # Create directory for product images
    product_dir = os.path.join(OUTPUT_DIR, f"{brand}_{category}_{model_number}")
    os.makedirs(product_dir, exist_ok=True)
    
    logger.info(f"Processing product: {name} (Model: {model_number})")
    
    # Initialize WebDriver
    try:
        driver = setup_driver()
        
        # Search on Google
        logger.info(f"Searching Google for {name} (Model: {model_number})")
        image_urls = search_google(driver, search_query)
        
        # Download images
        image_paths = []
        for i, url in enumerate(image_urls):
            filename = f"{model_number}_{i+1}.jpg"
            img_path = download_image(url, product_dir, filename)
            if img_path:
                # Convert to relative path
                rel_path = os.path.relpath(img_path, os.getcwd())
                image_paths.append(rel_path)
        
        # Update product with image paths
        product["images"] = image_paths
        
        logger.info(f"Completed processing {name} (Model: {model_number}). Found {len(image_paths)} images.")
        
    except Exception as e:
        logger.error(f"Error processing product {name} (Model: {model_number}): {str(e)}")
    finally:
        # Close the driver
        try:
            driver.quit()
        except:
            pass
    
    return product

def main():
    """Main function to process all products."""
    logger.info("Starting image scraping process")
    
    # Load product data from JSON file
    try:
        with open(JSON_FILE_PATH, 'r', encoding='utf-8') as f:
            data = json.load(f)
            products = data.get("products", [])
            logger.info(f"Loaded {len(products)} products from {JSON_FILE_PATH}")
    except Exception as e:
        logger.error(f"Error loading product data: {str(e)}")
        return
    
    # Process products with multithreading
    updated_products = []
    
    with concurrent.futures.ThreadPoolExecutor(max_workers=MAX_THREADS) as executor:
        # Submit all products for processing
        future_to_product = {executor.submit(process_product, product): product for product in products}
        
        # Process results as they complete
        for future in concurrent.futures.as_completed(future_to_product):
            product = future_to_product[future]
            try:
                updated_product = future.result()
                updated_products.append(updated_product)
                logger.info(f"Completed {len(updated_products)}/{len(products)} products")
            except Exception as e:
                logger.error(f"Error processing product: {str(e)}")
                # Add the original product to maintain data integrity
                updated_products.append(product)
    
    # Update the JSON file with new image paths
    try:
        data["products"] = updated_products
        with open(JSON_FILE_PATH, 'w', encoding='utf-8') as f:
            json.dump(data, f, indent=2)
        logger.info(f"Updated {JSON_FILE_PATH} with image paths")
    except Exception as e:
        logger.error(f"Error updating JSON file: {str(e)}")
        
        # Save a backup in case the main file update fails
        try:
            backup_path = JSON_FILE_PATH.replace(".json", "_updated.json")
            with open(backup_path, 'w', encoding='utf-8') as f:
                json.dump(data, f, indent=2)
            logger.info(f"Saved backup to {backup_path}")
        except Exception as e:
            logger.error(f"Error saving backup file: {str(e)}")
    
    logger.info("Image scraping process completed")

if __name__ == "__main__":
    main()
