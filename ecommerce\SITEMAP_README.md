# Sitemap Configuration for Triumph Enterprises

This document explains how the sitemap is configured and generated for the Triumph Enterprises ecommerce application.

## Overview

The application uses two approaches for sitemap generation:

1. **Dynamic Generation**: Using Next.js App Router's built-in sitemap generation (`app/sitemap.ts`)
2. **Static Generation**: Using a custom script (`scripts/generate-sitemap.js`) that creates a static sitemap.xml file

Both approaches ensure that your sitemap is comprehensive and up-to-date.

## Dynamic Sitemap Generation

The dynamic sitemap is generated by Next.js at request time using the `app/sitemap.ts` file. This approach:

- Generates the sitemap on-the-fly when requested at `/sitemap.xml`
- Fetches the latest data from your API
- Works well during development and for smaller sites

### How it Works

1. When a user or search engine requests `/sitemap.xml`, Next.js executes the `sitemap()` function in `app/sitemap.ts`
2. The function fetches product and category data from your API
3. It combines this data with static routes to create a complete sitemap
4. Next.js serves the generated XML

## Static Sitemap Generation

The static sitemap is generated during the build process using the `scripts/generate-sitemap.js` script. This approach:

- Creates a physical `sitemap.xml` file in the `/public` directory
- Is generated once during build time
- Provides better performance for production environments
- Works as a fallback if the dynamic generation fails

### How it Works

1. During the build process, the `generate-sitemap.js` script is executed
2. The script fetches product and category data from your API
3. It combines this data with static routes to create a complete sitemap
4. The script writes the sitemap to `/public/sitemap.xml`
5. This file is served directly by the web server without executing any code

## Configuration

### Environment Variables

The sitemap generation relies on these environment variables:

- `NEXT_PUBLIC_SITE_URL`: The base URL of your website (e.g., `https://trio.net.in`)
- `NEXT_PUBLIC_API_URL`: The base URL of your API (e.g., `http://api-ecom.trio.net.in`)

### NPM Scripts

The following npm scripts are available for sitemap management:

- `npm run generate-sitemap`: Manually generate the static sitemap.xml file
- `npm run build:seo`: Generate logo sizes, sitemap, and build the application
- `npm run postbuild`: Automatically runs after `npm run build` to generate the sitemap

## Submitting to Search Engines

### Google Search Console

1. Log in to [Google Search Console](https://search.google.com/search-console)
2. Select your property (https://trio.net.in/)
3. In the left sidebar, navigate to "Sitemaps"
4. Enter `sitemap.xml` in the "Add a new sitemap" field
5. Click "Submit"

### Bing Webmaster Tools

1. Log in to [Bing Webmaster Tools](https://www.bing.com/webmasters/)
2. Select your site
3. Navigate to "Sitemaps"
4. Enter the full URL of your sitemap (https://trio.net.in/sitemap.xml)
5. Click "Submit"

## Troubleshooting

If your sitemap isn't working correctly:

1. **Check API Access**: Ensure your API is accessible during build time
2. **Verify File Generation**: Check if `/public/sitemap.xml` exists after building
3. **Test URL Access**: Visit https://trio.net.in/sitemap.xml to ensure it's accessible
4. **Validate XML**: Use [XML Sitemap Validator](https://www.xml-sitemaps.com/validate-xml-sitemap.html) to check for errors
5. **Check Logs**: Review build logs for any errors during sitemap generation

## Manual Generation

If you need to manually generate the sitemap:

1. SSH into your server
2. Navigate to your application directory
3. Run `npm run generate-sitemap`
4. Verify the sitemap at `/public/sitemap.xml`

## Best Practices

- Keep your sitemap under 50,000 URLs (or split into multiple sitemaps)
- Include only canonical URLs
- Set appropriate priorities (homepage highest, less important pages lowest)
- Update your sitemap regularly (our setup does this automatically)
- Include only URLs that return 200 status codes
