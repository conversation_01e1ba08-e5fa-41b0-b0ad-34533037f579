// import {
//   View,
//   Text,
//   StyleSheet,
//   FlatList,
//   TouchableOpacity,
//   ActivityIndicator,
// } from "react-native";
// import { SafeAreaView } from "react-native-safe-area-context";
// import { MaterialIcons } from "@expo/vector-icons";
// import { useAuth } from "../context/AuthContext";
// import { useShippingAddresses } from "@/hooks/useUserDetailHook";

// export default function ShippingAddressScreen({ navigation }) {
//   const { isAuthenticated } = useAuth();
//   const { addresses, loading, error } = useShippingAddresses();

//   if (!isAuthenticated) {
//     return (
//       <SafeAreaView style={styles.container}>
//         <View style={styles.header}>
//           <TouchableOpacity
//             onPress={() => navigation.goBack()}
//             style={styles.backButton}
//           >
//             <MaterialIcons name="arrow-back" size={24} color="#333" />
//           </TouchableOpacity>
//           <Text style={styles.title}>Shipping Addresses</Text>
//           <View style={{ width: 24 }} />
//         </View>
//         <View style={styles.authRequired}>
//           <MaterialIcons name="lock" size={48} color="#666" />
//           <Text style={styles.authTitle}>Authentication Required</Text>
//           <Text style={styles.authText}>
//             Please login to view your addresses
//           </Text>
//           <TouchableOpacity
//             style={styles.loginButton}
//             onPress={() =>
//               navigation.navigate("Auth", {
//                 returnTo: "ShippingAddress",
//               })
//             }
//           >
//             <Text style={styles.loginButtonText}>Login</Text>
//           </TouchableOpacity>
//         </View>
//       </SafeAreaView>
//     );
//   }

//   const renderAddressCard = ({ item }) => (
//     <View style={styles.addressCard}>
//       <View style={styles.addressHeader}>
//         <View style={styles.nameContainer}>
//           <Text style={styles.addressName}>{item.address_type}</Text>
//           {item.is_default && (
//             <View style={styles.defaultBadge}>
//               <Text style={styles.defaultText}>Default</Text>
//             </View>
//           )}
//         </View>
//         <TouchableOpacity>
//           <MaterialIcons name="edit" size={24} color="#666" />
//         </TouchableOpacity>
//       </View>

//       <Text
//         style={styles.addressText}
//       >{`${item.street_address}, ${item.apartment}`}</Text>
//       <Text
//         style={styles.addressText}
//       >{`${item.city}, ${item.state} ${item.postal_code}`}</Text>
//       <Text style={styles.phone}>{item.order_user_phone}</Text>
//       <Text style={styles.email}>{item.order_user_email}</Text>
//     </View>
//   );

//   return (
//     <SafeAreaView style={styles.container}>
//       <View style={styles.header}>
//         <TouchableOpacity
//           onPress={() => navigation.goBack()}
//           style={styles.backButton}
//         >
//           <MaterialIcons name="arrow-back" size={24} color="#333" />
//         </TouchableOpacity>
//         <Text style={styles.title}>Shipping Addresses</Text>
//         <View style={{ width: 24 }} />
//       </View>

//       {loading ? (
//         <View style={styles.loadingContainer}>
//           <ActivityIndicator size="large" color="#2563EB" />
//           <Text>Loading Addresses...</Text>
//         </View>
//       ) : error ? (
//         <Text style={styles.errorText}>Failed to load addresses.</Text>
//       ) : (
//         <>
//           <FlatList
//             data={addresses}
//             renderItem={renderAddressCard}
//             keyExtractor={(item) => item.id.toString()}
//             contentContainerStyle={styles.addressList}
//             ListEmptyComponent={
//               <View style={styles.emptyState}>
//                 <Text style={styles.emptyStateText}>No addresses found</Text>
//               </View>
//             }
//           />
//           <TouchableOpacity style={styles.addButton}>
//             <MaterialIcons name="add" size={24} color="white" />
//             <Text style={styles.addButtonText}>Add New Address</Text>
//           </TouchableOpacity>
//         </>
//       )}
//     </SafeAreaView>
//   );
// }

// const styles = StyleSheet.create({
//   container: {
//     flex: 1,
//     backgroundColor: "#ffffff",
//   },
//   loadingContainer: {
//     flex: 1,
//     alignItems: "center",
//     justifyContent: "center",
//   },
//   header: {
//     flexDirection: "row",
//     alignItems: "center",
//     justifyContent: "space-between",
//     padding: 16,
//     borderBottomWidth: 1,
//     borderBottomColor: "#f3f4f6",
//   },
//   backButton: {
//     padding: 8,
//   },
//   title: {
//     fontSize: 20,
//     fontWeight: "bold",
//   },
//   addressList: {
//     padding: 16,
//   },
//   addressCard: {
//     backgroundColor: "white",
//     borderRadius: 12,
//     padding: 16,
//     marginBottom: 16,
//     shadowColor: "#000",
//     shadowOffset: { width: 0, height: 2 },
//     shadowOpacity: 0.1,
//     shadowRadius: 8,
//     elevation: 3,
//   },
//   addressHeader: {
//     flexDirection: "row",
//     justifyContent: "space-between",
//     alignItems: "center",
//     marginBottom: 12,
//   },
//   nameContainer: {
//     flexDirection: "row",
//     alignItems: "center",
//   },
//   addressName: {
//     fontSize: 18,
//     fontWeight: "600",
//     marginRight: 8,
//   },
//   defaultBadge: {
//     backgroundColor: "#2563EB",
//     paddingHorizontal: 8,
//     paddingVertical: 4,
//     borderRadius: 12,
//   },
//   defaultText: {
//     color: "white",
//     fontSize: 12,
//     fontWeight: "500",
//   },
//   addressText: {
//     fontSize: 14,
//     color: "#666",
//     marginBottom: 2,
//   },
//   phone: {
//     fontSize: 14,
//     color: "#666",
//     marginTop: 4,
//   },
//   email: {
//     fontSize: 14,
//     color: "#666",
//     marginTop: 2,
//   },
//   addButton: {
//     flexDirection: "row",
//     alignItems: "center",
//     justifyContent: "center",
//     backgroundColor: "#2563EB",
//     margin: 16,
//     padding: 16,
//     borderRadius: 12,
//   },
//   addButtonText: {
//     color: "white",
//     fontSize: 16,
//     fontWeight: "600",
//     marginLeft: 8,
//   },
//   authRequired: {
//     flex: 1,
//     alignItems: "center",
//     justifyContent: "center",
//     padding: 16,
//   },
//   authTitle: {
//     fontSize: 20,
//     fontWeight: "bold",
//     marginTop: 16,
//     marginBottom: 8,
//   },
//   authText: {
//     fontSize: 16,
//     color: "#666",
//     textAlign: "center",
//     marginBottom: 24,
//   },
//   loginButton: {
//     backgroundColor: "#2563EB",
//     paddingHorizontal: 32,
//     paddingVertical: 12,
//     borderRadius: 8,
//   },
//   loginButtonText: {
//     color: "white",
//     fontSize: 16,
//     fontWeight: "600",
//   },
//   emptyState: {
//     alignItems: "center",
//     justifyContent: "center",
//     paddingVertical: 32,
//   },
//   emptyStateText: {
//     fontSize: 16,
//     color: "#666",
//   },
//   loadingText: {
//     textAlign: "center",
//     fontSize: 16,
//     marginTop: 20,
//     color: "#666",
//   },
//   errorText: {
//     textAlign: "center",
//     fontSize: 16,
//     marginTop: 20,
//     color: "red",
//   },
// });


import {
  View,
  Text,
  StyleSheet,
  FlatList,
  TouchableOpacity,
  ActivityIndicator,
  Modal,
  SafeAreaView as RNSafeAreaView,
  Platform,
} from "react-native";
import { SafeAreaView } from "react-native-safe-area-context";
import { MaterialIcons } from "@expo/vector-icons";
import { useState } from 'react';
import { useAuth } from "../context/AuthContext";
import { useShippingAddresses } from "@/hooks/useUserDetailHook";
import AddressForm from "../components/components/AddressForm"; // Make sure path is correct

export default function ShippingAddressScreen({ navigation }) {
  const { isAuthenticated } = useAuth();
  const { addresses, loading, error, addAddress, updateAddress } = useShippingAddresses();
  const [modalVisible, setModalVisible] = useState(false);
  const [editingAddress, setEditingAddress] = useState(null);

  const handleAddAddress = () => {
    setEditingAddress(null);
    setModalVisible(true);
  };

  const handleEditAddress = (address) => {
    setEditingAddress(address);
    setModalVisible(true);
  };

  const handleSubmitAddress = async(formData:any) => {
    if (editingAddress) {
      await updateAddress(editingAddress.id, formData);
    } else {
      await addAddress(formData);
    }
    setModalVisible(false);
  };

  const handleCancelForm = () => {
    setModalVisible(false);
  };

  if (!isAuthenticated) {
    return (
      <SafeAreaView style={styles.container}>
        <View style={styles.header}>
          <TouchableOpacity
            onPress={() => navigation.goBack()}
            style={styles.backButton}
          >
            <MaterialIcons name="arrow-back" size={24} color="#333" />
          </TouchableOpacity>
          <Text style={styles.title}>Shipping Addresses</Text>
          <View style={{ width: 24 }} />
        </View>
        <View style={styles.authRequired}>
          <MaterialIcons name="lock" size={48} color="#666" />
          <Text style={styles.authTitle}>Authentication Required</Text>
          <Text style={styles.authText}>
            Please login to view your addresses
          </Text>
          <TouchableOpacity
            style={styles.loginButton}
            onPress={() =>
              navigation.navigate("Auth", {
                returnTo: "ShippingAddress",
              })
            }
          >
            <Text style={styles.loginButtonText}>Login</Text>
          </TouchableOpacity>
        </View>
      </SafeAreaView>
    );
  }

  const renderAddressCard = ({ item }) => (
    <View style={styles.addressCard}>
      <View style={styles.addressHeader}>
        <View style={styles.nameContainer}>
          <Text style={styles.addressName}>{item.address_type}</Text>
          {item.is_default && (
            <View style={styles.defaultBadge}>
              <Text style={styles.defaultText}>Default</Text>
            </View>
          )}
        </View>
        <TouchableOpacity onPress={() => handleEditAddress(item)}>
          <MaterialIcons name="edit" size={24} color="#666" />
        </TouchableOpacity>
      </View>

      <Text
        style={styles.addressText}
      >{`${item.street_address}, ${item.apartment}`}</Text>
      <Text
        style={styles.addressText}
      >{`${item.city}, ${item.state} ${item.postal_code}`}</Text>
      <Text style={styles.phone}>{item.order_user_phone}</Text>
      <Text style={styles.email}>{item.order_user_email}</Text>
    </View>
  );

  return (
    <SafeAreaView style={styles.container}>
      <View style={styles.header}>
        <TouchableOpacity
          onPress={() => navigation.goBack()}
          style={styles.backButton}
        >
          <MaterialIcons name="arrow-back" size={24} color="#333" />
        </TouchableOpacity>
        <Text style={styles.title}>Shipping Addresses</Text>
        <View style={{ width: 24 }} />
      </View>

      {loading ? (
        <View style={styles.loadingContainer}>
          <ActivityIndicator size="large" color="#2563EB" />
          <Text>Loading Addresses...</Text>
        </View>
      ) : error ? (
        <Text style={styles.errorText}>Failed to load addresses.</Text>
      ) : (
        <>
          <FlatList
            data={addresses}
            renderItem={renderAddressCard}
            keyExtractor={(item) => item.id.toString()}
            contentContainerStyle={styles.addressList}
            ListEmptyComponent={
              <View style={styles.emptyState}>
                <Text style={styles.emptyStateText}>No addresses found</Text>
              </View>
            }
          />
          <TouchableOpacity style={styles.addButton} onPress={handleAddAddress}>
            <MaterialIcons name="add" size={24} color="white" />
            <Text style={styles.addButtonText}>Add New Address</Text>
          </TouchableOpacity>
        </>
      )}

      {/* Address Form Modal - Full Screen */}
      <Modal
        animationType="slide"
        transparent={false}
        visible={modalVisible}
        onRequestClose={handleCancelForm}
      >
        <RNSafeAreaView style={styles.modalContainer}>
          <View style={styles.modalHeader}>
            <Text style={styles.modalTitle}>
              {editingAddress ? 'Edit Address' : 'Add New Address'}
            </Text>
            <TouchableOpacity onPress={handleCancelForm} style={styles.closeButton}>
              <MaterialIcons name="close" size={24} color="#333" />
            </TouchableOpacity>
          </View>
          <AddressForm
            initialData={editingAddress}
            onSubmit={handleSubmitAddress}
            onCancel={handleCancelForm}
          />
        </RNSafeAreaView>
      </Modal>
    </SafeAreaView>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: "#ffffff",
  },
  loadingContainer: {
    flex: 1,
    alignItems: "center",
    justifyContent: "center",
  },
  header: {
    flexDirection: "row",
    alignItems: "center",
    justifyContent: "space-between",
    padding: 16,
    borderBottomWidth: 1,
    borderBottomColor: "#f3f4f6",
  },
  backButton: {
    padding: 8,
  },
  title: {
    fontSize: 20,
    fontWeight: "bold",
  },
  addressList: {
    padding: 16,
  },
  addressCard: {
    backgroundColor: "white",
    borderRadius: 12,
    padding: 16,
    marginBottom: 16,
    shadowColor: "#000",
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 8,
    elevation: 3,
  },
  addressHeader: {
    flexDirection: "row",
    justifyContent: "space-between",
    alignItems: "center",
    marginBottom: 12,
  },
  nameContainer: {
    flexDirection: "row",
    alignItems: "center",
  },
  addressName: {
    fontSize: 18,
    fontWeight: "600",
    marginRight: 8,
  },
  defaultBadge: {
    backgroundColor: "#2563EB",
    paddingHorizontal: 8,
    paddingVertical: 4,
    borderRadius: 12,
  },
  defaultText: {
    color: "white",
    fontSize: 12,
    fontWeight: "500",
  },
  addressText: {
    fontSize: 14,
    color: "#666",
    marginBottom: 2,
  },
  phone: {
    fontSize: 14,
    color: "#666",
    marginTop: 4,
  },
  email: {
    fontSize: 14,
    color: "#666",
    marginTop: 2,
  },
  addButton: {
    flexDirection: "row",
    alignItems: "center",
    justifyContent: "center",
    backgroundColor: "#2563EB",
    margin: 16,
    padding: 16,
    borderRadius: 12,
  },
  addButtonText: {
    color: "white",
    fontSize: 16,
    fontWeight: "600",
    marginLeft: 8,
  },
  authRequired: {
    flex: 1,
    alignItems: "center",
    justifyContent: "center",
    padding: 16,
  },
  authTitle: {
    fontSize: 20,
    fontWeight: "bold",
    marginTop: 16,
    marginBottom: 8,
  },
  authText: {
    fontSize: 16,
    color: "#666",
    textAlign: "center",
    marginBottom: 24,
  },
  loginButton: {
    backgroundColor: "#2563EB",
    paddingHorizontal: 32,
    paddingVertical: 12,
    borderRadius: 8,
  },
  loginButtonText: {
    color: "white",
    fontSize: 16,
    fontWeight: "600",
  },
  emptyState: {
    alignItems: "center",
    justifyContent: "center",
    paddingVertical: 32,
  },
  emptyStateText: {
    fontSize: 16,
    color: "#666",
  },
  loadingText: {
    textAlign: "center",
    fontSize: 16,
    marginTop: 20,
    color: "#666",
  },
  errorText: {
    textAlign: "center",
    fontSize: 16,
    marginTop: 20,
    color: "red",
  },
  // Modal styles - redesigned for full screen
  modalContainer: {
    flex: 1,
    backgroundColor: '#FFFFFF',
  },
  modalHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    padding: 16,
    borderBottomWidth: 1,
    borderBottomColor: '#f3f4f6',
    backgroundColor: '#FFFFFF',
  },
  modalTitle: {
    fontSize: 20,
    fontWeight: 'bold',
    textAlign: 'center',
    flex: 1,
  },
  closeButton: {
    padding: 8,
    position: 'absolute',
    right: 16,
    zIndex: 10,
  },
});