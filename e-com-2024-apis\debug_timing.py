#!/usr/bin/env python3
"""
Debug script to check timing issues with IP blocking.
"""

import os
import sys
import django

# Setup Django environment
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'backend.settings')
django.setup()

from django.core.cache import cache
from backend.security_monitoring import (
    block_ip, 
    get_ip_block_remaining_time, 
    get_ip_block_error_message,
    is_ip_blocked
)

def debug_timing():
    test_ip = '192.168.1.999'
    
    # Clear any existing blocks
    cache.delete(f"blocked_ip_{test_ip}")
    cache.delete(f"blocked_ip_expiry_{test_ip}")
    
    print("=== DEBUGGING IP BLOCKING TIMING ===")
    
    # Test 1: Block for 5 minutes
    print(f"\n1. Blocking {test_ip} for 5 minutes...")
    block_ip(test_ip, duration_minutes=5)
    
    # Check immediately
    print(f"   Is blocked: {is_ip_blocked(test_ip)}")
    print(f"   Remaining time: {get_ip_block_remaining_time(test_ip)} minutes")
    print(f"   Error message: {get_ip_block_error_message(test_ip)}")
    
    # Check cache contents
    cache_key = f"blocked_ip_{test_ip}"
    expiry_key = f"blocked_ip_expiry_{test_ip}"
    
    print(f"\n   Cache contents:")
    print(f"   blocked_ip_{test_ip}: {cache.get(cache_key)}")
    print(f"   blocked_ip_expiry_{test_ip}: {cache.get(expiry_key)}")
    
    # Test 2: Block for 1 minute
    print(f"\n2. Blocking {test_ip} for 1 minute...")
    block_ip(test_ip, duration_minutes=1)
    
    print(f"   Is blocked: {is_ip_blocked(test_ip)}")
    print(f"   Remaining time: {get_ip_block_remaining_time(test_ip)} minutes")
    print(f"   Error message: {get_ip_block_error_message(test_ip)}")
    
    # Test 3: Block for 90 minutes
    print(f"\n3. Blocking {test_ip} for 90 minutes...")
    block_ip(test_ip, duration_minutes=90)
    
    print(f"   Is blocked: {is_ip_blocked(test_ip)}")
    print(f"   Remaining time: {get_ip_block_remaining_time(test_ip)} minutes")
    print(f"   Error message: {get_ip_block_error_message(test_ip)}")
    
    # Clean up
    cache.delete(f"blocked_ip_{test_ip}")
    cache.delete(f"blocked_ip_expiry_{test_ip}")
    
    print(f"\n✅ Debug complete")

if __name__ == "__main__":
    debug_timing()
