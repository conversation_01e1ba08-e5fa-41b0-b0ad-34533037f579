#!/usr/bin/env python3
"""
Simple Image Cleaner Runner
Easy-to-use script for cleaning low-quality images
"""

import os
import sys

def main():
    """Main menu for image cleaning"""
    print("Haier Image Quality Cleaner")
    print("="*30)
    print("1. Basic cleanup (recommended)")
    print("2. Advanced cleanup with options")
    print("3. Dry run only (preview changes)")
    print("4. Clean specific directory")
    print("5. Exit")
    
    choice = input("\nSelect option (1-5): ").strip()
    
    if choice == '1':
        # Basic cleanup
        print("\nRunning basic cleanup...")
        os.system("python remove_low_quality_images.py")
    
    elif choice == '2':
        # Advanced cleanup
        print("\nRunning advanced cleanup...")
        os.system("python advanced_image_cleaner.py --interactive")
    
    elif choice == '3':
        # Dry run
        print("\nRunning dry run preview...")
        os.system("python advanced_image_cleaner.py --dry-run")
    
    elif choice == '4':
        # Specific directory
        directory = input("Enter directory path: ").strip()
        if directory:
            print(f"\nCleaning directory: {directory}")
            os.system(f'python advanced_image_cleaner.py --directory "{directory}" --interactive')
        else:
            print("No directory specified.")
    
    elif choice == '5':
        print("Exiting...")
        return
    
    else:
        print("Invalid choice. Please try again.")
        main()

if __name__ == "__main__":
    main()
