"""
Security Email Notification System for Triumph Enterprises E-commerce Platform.

This module provides email notifications for security events while preventing spam
by implementing throttling and priority-based filtering.
"""

import logging
from datetime import datetime, timedelta
from django.core.mail import EmailMultiAlternatives
from django.template.loader import render_to_string
from django.conf import settings
from django.core.cache import cache
from django.utils import timezone
from typing import Dict, List, Optional, Any

logger = logging.getLogger(__name__)
security_logger = logging.getLogger('security')


class SecurityNotificationManager:
    """Manages security email notifications with throttling and priority filtering."""
    
    def __init__(self):
        self.config = getattr(settings, 'SECURITY_EMAIL_NOTIFICATIONS', {})
        self.enabled = self.config.get('ENABLED', True)
        self.recipients = self.config.get('RECIPIENTS', [settings.DEFAULT_FROM_EMAIL])
        self.throttle_minutes = self.config.get('THROTTLE_MINUTES', 30)
        self.high_priority_events = self.config.get('HIGH_PRIORITY_EVENTS', [])
        self.medium_priority_events = self.config.get('MEDIUM_PRIORITY_EVENTS', [])
    
    def should_send_notification(self, event_type: str, ip_address: str = None) -> bool:
        """
        Determine if a notification should be sent based on priority and throttling.
        
        Args:
            event_type: Type of security event
            ip_address: IP address involved (for throttling similar events)
            
        Returns:
            bool: True if notification should be sent
        """
        if not self.enabled:
            return False
        
        # Only send for high or medium priority events
        if event_type not in (self.high_priority_events + self.medium_priority_events):
            return False
        
        # Check throttling to prevent spam
        throttle_key = f"security_email_throttle_{event_type}"
        if ip_address:
            throttle_key += f"_{ip_address}"
        
        if cache.get(throttle_key):
            security_logger.info(
                f"Security email notification throttled for {event_type}",
                extra={'ip': ip_address or 'unknown', 'user': 'system'}
            )
            return False
        
        # Set throttle cache
        cache.set(throttle_key, True, self.throttle_minutes * 60)
        return True
    
    def get_priority_level(self, event_type: str) -> str:
        """Get priority level for an event type."""
        if event_type in self.high_priority_events:
            return 'HIGH'
        elif event_type in self.medium_priority_events:
            return 'MEDIUM'
        return 'LOW'
    
    def send_security_alert(
        self,
        event_type: str,
        event_data: Dict[str, Any],
        ip_address: str = None,
        user_email: str = None
    ) -> bool:
        """
        Send security alert email if conditions are met.
        
        Args:
            event_type: Type of security event
            event_data: Additional event data for the email
            ip_address: IP address involved
            user_email: User email if applicable
            
        Returns:
            bool: True if email was sent successfully
        """
        try:
            # Check if we should send notification
            if not self.should_send_notification(event_type, ip_address):
                return False
            
            priority = self.get_priority_level(event_type)
            
            # Prepare email context
            context = {
                'event_type': event_type,
                'priority': priority,
                'timestamp': timezone.now(),
                'ip_address': ip_address or 'Unknown',
                'user_email': user_email or 'Anonymous',
                'event_data': event_data,
                'site_name': 'Triumph Enterprises',
            }
            
            # Choose template based on event type
            template_name = self._get_template_name(event_type)
            
            # Create subject line
            subject = f"[{priority} PRIORITY] Security Alert: {event_type.replace('_', ' ').title()}"
            
            # Send email
            return self._send_email(subject, template_name, context)
            
        except Exception as e:
            logger.error(f"Error sending security alert email: {str(e)}")
            return False
    
    def _get_template_name(self, event_type: str) -> str:
        """Get appropriate email template for event type."""
        template_map = {
            'IP_BLOCKED': 'emails/security/ip_blocked.html',
            'MULTIPLE_FAILED_LOGINS': 'emails/security/multiple_failed_logins.html',
            'SUSPICIOUS_ACTIVITY': 'emails/security/suspicious_activity.html',
            'ACCOUNT_LOCKED': 'emails/security/account_locked.html',
            'DATA_BREACH_DETECTED': 'emails/security/data_breach.html',
            'UNUSUAL_LOGIN_PATTERN': 'emails/security/unusual_login.html',
            'NEW_DEVICE_LOGIN': 'emails/security/new_device_login.html',
            'ADMIN_ACTION': 'emails/security/admin_action.html',
            'BULK_DATA_ACCESS': 'emails/security/bulk_data_access.html',
        }
        
        return template_map.get(event_type, 'emails/security/generic_security_alert.html')
    
    def _send_email(self, subject: str, template_name: str, context: Dict[str, Any]) -> bool:
        """Send the actual email."""
        try:
            from_email = settings.DEFAULT_FROM_EMAIL
            
            # Render HTML content
            html_content = render_to_string(template_name, context)
            
            # Create email message
            email = EmailMultiAlternatives(subject, "", from_email, self.recipients)
            email.attach_alternative(html_content, "text/html")
            
            # Send email
            email.send()
            
            security_logger.info(
                f"Security alert email sent: {subject}",
                extra={'ip': context.get('ip_address', 'unknown'), 'user': 'system'}
            )
            
            return True
            
        except Exception as e:
            logger.error(f"Failed to send security email: {str(e)}")
            return False


# Global instance for easy access
security_notifier = SecurityNotificationManager()


def send_ip_blocked_alert(ip_address: str, duration_minutes: int, reason: str = None):
    """Send alert when an IP is blocked."""
    event_data = {
        'duration_minutes': duration_minutes,
        'reason': reason or 'Multiple failed login attempts',
        'action_taken': f'IP blocked for {duration_minutes} minutes'
    }
    
    return security_notifier.send_security_alert(
        'IP_BLOCKED',
        event_data,
        ip_address=ip_address
    )


def send_multiple_failed_logins_alert(ip_address: str, username: str, attempt_count: int):
    """Send alert for multiple failed login attempts."""
    event_data = {
        'username': username,
        'attempt_count': attempt_count,
        'time_window': '15 minutes',
        'action_taken': 'IP address monitoring increased'
    }
    
    return security_notifier.send_security_alert(
        'MULTIPLE_FAILED_LOGINS',
        event_data,
        ip_address=ip_address,
        user_email=username
    )


def send_suspicious_activity_alert(ip_address: str, activity_type: str, details: Dict[str, Any]):
    """Send alert for suspicious activity."""
    event_data = {
        'activity_type': activity_type,
        'details': details,
        'action_taken': 'Activity logged and monitored'
    }
    
    return security_notifier.send_security_alert(
        'SUSPICIOUS_ACTIVITY',
        event_data,
        ip_address=ip_address
    )


def send_admin_action_alert(admin_user: str, action: str, target: str, ip_address: str):
    """Send alert for important admin actions."""
    event_data = {
        'admin_user': admin_user,
        'action': action,
        'target': target,
        'action_taken': 'Administrative action logged'
    }
    
    return security_notifier.send_security_alert(
        'ADMIN_ACTION',
        event_data,
        ip_address=ip_address,
        user_email=admin_user
    )
