"""
Security monitoring models for the e-commerce application.
"""

from django.db import models
from django.contrib.auth import get_user_model
from django.utils import timezone

User = get_user_model()

class SecurityEvent(models.Model):
    """Model to track security events"""
    EVENT_TYPES = [
        ('LOGIN_FAILED', 'Login Failed'),
        ('LOGIN_SUCCESS', 'Login Success'),
        ('LOGOUT', 'Logout'),
        ('PASSWORD_CHANGE', 'Password Change'),
        ('ACCOUNT_LOCKED', 'Account Locked'),
        ('SUSPICIOUS_ACTIVITY', 'Suspicious Activity'),
        ('DATA_ACCESS', 'Data Access'),
        ('ADMIN_ACTION', 'Admin Action'),
    ]
    
    user = models.ForeignKey(User, on_delete=models.SET_NULL, null=True, blank=True)
    event_type = models.CharField(max_length=20, choices=EVENT_TYPES)
    ip_address = models.GenericIPAddress<PERSON>ield()
    user_agent = models.TextField(blank=True)
    timestamp = models.DateTimeField(auto_now_add=True)
    details = models.JSONField(default=dict, blank=True)
    
    class Meta:
        ordering = ['-timestamp']
        indexes = [
            models.Index(fields=['event_type', 'timestamp']),
            models.Index(fields=['ip_address', 'timestamp']),
            models.Index(fields=['user', 'timestamp']),
        ]
    
    def __str__(self):
        return f"{self.event_type} - {self.ip_address} - {self.timestamp}"

class FailedLoginAttempt(models.Model):
    """Track failed login attempts for rate limiting"""
    ip_address = models.GenericIPAddressField()
    username = models.CharField(max_length=150, blank=True)
    timestamp = models.DateTimeField(auto_now_add=True)
    user_agent = models.TextField(blank=True)
    attempt_count = models.PositiveIntegerField(default=1)
    attempted_email = models.EmailField(blank=True, max_length=254)

    class Meta:
        ordering = ['-timestamp']
        indexes = [
            models.Index(fields=['ip_address', 'timestamp']),
            models.Index(fields=['username', 'timestamp']),
        ]

class BlockedIP(models.Model):
    """Model to track blocked IP addresses"""
    ip_address = models.GenericIPAddressField(unique=True)
    reason = models.TextField(default='Multiple failed login attempts')
    blocked_at = models.DateTimeField(auto_now_add=True)
    blocked_until = models.DateTimeField()
    is_permanent = models.BooleanField(default=False)
    
    class Meta:
        ordering = ['-blocked_at']
        indexes = [
            models.Index(fields=['ip_address', 'blocked_until']),
        ]
    
    def __str__(self):
        return f"Blocked IP: {self.ip_address} until {self.blocked_until}"
    
    def is_active(self):
        """Check if the IP block is still active"""
        if self.is_permanent:
            return True
        return timezone.now() < self.blocked_until
