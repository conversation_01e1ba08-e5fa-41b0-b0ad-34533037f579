# RapidShyp API Documentation

## Table of Contents
1. [Authentication](#authentication)
2. [Pincode Serviceability API](#pincode-serviceability-api)
3. [Forward Wrapper API](#forward-wrapper-api)
4. [Forward Create Order API](#forward-create-order-api)
5. [Forward Update Order API](#forward-update-order-api)
6. [AWB Assignment API](#awb-assignment-api)
7. [Schedule Pickup API](#schedule-pickup-api)
8. [De-allocate Shipment API](#de-allocate-shipment-api)
9. [Cancel Order API](#cancel-order-api)
10. [Label PDF Generation API](#label-pdf-generation-api)
11. [Create Pickup Location API](#create-pickup-location-api)
12. [Action NDR API](#action-ndr-api)
13. [Tracking API](#tracking-api)
14. [Tracking Status Codes](#tracking-status-codes)

---

## Authentication

Generate API-key from RapidShyp Portal to use it for API validation.

**Path:** Setting > API > Configure

**Usage:** Save the generated API-key and use it in the `rapidshyp-token` header for all API calls.

---

## Pincode Serviceability API

Use this API to check pincode serviceability on the RapidShyp platform.

### Basic Information
- **URL:** `https://api.rapidshyp.com/rapidshyp/apis/v1/serviceabilty_check`
- **Method:** POST
- **Scheme:** HTTPS
- **Headers:**
  - `Content-Type: application/json`
  - `rapidshyp-token: [API-Key]`

### Request Parameters
| Parameter | Type | Required | Description | Validation |
|-----------|------|----------|-------------|------------|
| `Pickup_pincode` | String | Mandatory | Postcode from where the order will be picked | 6 digit pincode |
| `Delivery_pincode` | String | Mandatory | Postcode where the order will be delivered | 6 digit pincode |
| `cod` | Boolean | Mandatory | True for COD order, false for prepaid order | - |
| `total_order_value` | Number | Mandatory | Price of the order shipment in rupees | - |
| `weight` | Number | Mandatory | Weight of shipment in kgs | - |

### Example Request
```bash
curl --location 'https://api.rapidshyp.com/rapidshyp/apis/v1/serviceabilty_check' \
--header 'rapidshyp-token: e779a4*************8b60ba5f09ecd579fa1f34b64805e' \
--header 'Content-Type: application/json' \
--data '{
  "Pickup_pincode": "110068",
  "Delivery_pincode": "110038",
  "cod": true,
  "total_order_value": 2000,
  "weight": 1
}'
```

### Response
```json
{
  "status": true,
  "remark": "Success",
  "serviceable_courier_list": [
    {
      "courier_code": "6001",
      "courier_name": "BlueDart Express",
      "parent_courier_name": "BlueDart",
      "cutoff_time": "14:00",
      "freight_mode": "Surface",
      "max_weight": 5000.0,
      "min_weight": 1.0,
      "total_freight": 11.111
    }
  ]
}
```

---

## Forward Wrapper API

Use this API to perform multiple tasks: create order, create shipment, run serviceability, check courier rules, assign AWB, generate pickup manifest, generate label PDF, generate invoice PDF, and generate manifest PDF.

### Basic Information
- **URL:** `https://api.rapidshyp.com/rapidshyp/apis/v1/wrapper`
- **Method:** POST
- **Scheme:** HTTPS
- **Headers:**
  - `Content-Type: application/json`
  - `rapidshyp-token: [API-Key]`

### Key Features
- Create order
- Create shipment
- Run serviceability
- Check courier rule/priority
- Assign AWB
- Generate pickup manifest
- Generate label PDF
- Generate invoice PDF
- Generate manifest PDF

### Request Parameters
| Parameter | Required | Description | Validation |
|-----------|----------|-------------|------------|
| `orderId` | Mandatory | Seller order ID | Minimum 1 character |
| `orderDate` | Mandatory | Order created date | Format: YYYY-MM-DD |
| `pickupAddressName` | Conditionally-Mandatory | Pickup name created on RapidShyp platform | - |
| `pickupLocation` | Conditionally-Mandatory | Create pickup location on order creation | - |
| `storeName` | Mandatory | Store name created on RapidShyp | Use "DEFAULT" for single channel |
| `billingIsShipping` | Mandatory | True if shipping and billing addresses are same | - |
| `shippingAddress` | Mandatory | Customer shipping address details | - |
| `billingAddress` | Conditionally-Mandatory | Required if billingIsShipping is false | - |
| `orderItems` | Mandatory | Array of order items | - |
| `paymentMethod` | Mandatory | Payment mode | Choose from [COD, PREPAID] |
| `packageDetails` | Mandatory | Package dimensions and weight | - |

### Example Request
```bash
curl --location 'https://api.rapidshyp.com/rapidshyp/apis/v1/wrapper' \
--header 'rapidshyp-token: HQ$f**********oZ' \
--header 'Content-Type: application/json' \
--data-raw '{
  "orderId": "Tango34",
  "orderDate": "2023-06-30",
  "pickupAddressName": "Home",
  "pickupLocation": {
    "contactName": "Mahesh Mehra",
    "pickupName": "New Seller 110001",
    "pickupEmail": "<EMAIL>",
    "pickupPhone": "8094723198",
    "pickupAddress1": "New Delhi Seller",
    "pickupAddress2": "New Delhi 2 Seller",
    "pinCode": "110001"
  },
  "storeName": "DEFAULT",
  "billingIsShipping": true,
  "shippingAddress": {
    "firstName": "Mahesh Mehra",
    "lastName": "EXT",
    "addressLine1": "Delhi",
    "addressLine2": "New Delhi",
    "pinCode": "110001",
    "email": "<EMAIL>",
    "phone": "8094723198"
  },
  "orderItems": [
    {
      "itemName": "Product 1",
      "sku": "SKU123",
      "description": "Description of product 1",
      "units": 5,
      "unitPrice": 10.0,
      "tax": 0.0,
      "hsn": "HSN123",
      "productLength": 10.0,
      "productBreadth": 5.0,
      "productHeight": 2.0,
      "productWeight": 0.5,
      "brand": "Brand A",
      "imageURL": "http://example.com/product1.jpg",
      "isFragile": false,
      "isPersonalisable": false
    }
  ],
  "paymentMethod": "COD",
  "shippingCharges": 100.0,
  "totalOrderValue": 500.0,
  "packageDetails": {
    "packageLength": 20.0,
    "packageBreadth": 10.0,
    "packageHeight": 5.0,
    "packageWeight": 2000.0
  }
}'
```

---

## Forward Create Order API

Use this API to create an order on the RapidShyp platform.

### Basic Information
- **URL:** `https://api.rapidshyp.com/rapidshyp/apis/v1/create_order`
- **Method:** POST
- **Scheme:** HTTPS
- **Headers:**
  - `Content-Type: application/json`
  - `rapidshyp-token: [API-Key]`

### Pickup Location Logic
- Users can enter pickup location at order level or item level
- For order level: can use existing pickup or create new pickup location
- For item level: only pickup location name can be entered
- Multiple shipments can be created if pickup locations are defined at item level

---

## Forward Update Order API

Use this API to update an existing order on the RapidShyp platform.

### Basic Information
- **URL:** `https://api.rapidshyp.com/rapidshyp/apis/v1/order_update`
- **Method:** POST
- **Scheme:** HTTPS
- **Headers:**
  - `Content-Type: application/json`
  - `rapidshyp-token: [API-Key]`

### Key Parameters
| Parameter | Required | Description |
|-----------|----------|-------------|
| `orderId` | Mandatory | Seller order ID for updates |
| `store_name` | Mandatory | Store name of the order |
| `pickupAddressName` | Conditionally-Mandatory | Updated pickup location name |
| `shippingAddress` | Conditionally-Mandatory | Updated shipping address |
| `billingAddress` | Conditionally-Mandatory | Updated billing address |
| `paymentMethod` | Conditionally-Mandatory | Change COD to Prepaid |
| `packageDetails` | Conditionally-Mandatory | Updated package details |

---

## AWB Assignment API

Use this API to assign AWB to order-shipment on the RapidShyp platform.

### Basic Information
- **URL:** `https://api.rapidshyp.com/rapidshyp/apis/v1/assign_awb`
- **Method:** POST
- **Scheme:** HTTPS
- **Headers:**
  - `Content-Type: application/json`
  - `rapidshyp-token: [API-Key]`

### Request Parameters
| Parameter | Required | Description |
|-----------|----------|-------------|
| `shipment_id` | Mandatory | Order shipment ID |
| `courier_code` | Optional | Specific courier service ID |

### Example Request
```bash
curl --location 'https://api.rapidshyp.com/rapidshyp/apis/v1/assign_awb' \
--header 'rapidshyp-token: e779a4656395810c1f***************79fa1f34b64805e' \
--header 'Content-Type: application/json' \
--data '{
  "shipment_id": "S240927365",
  "courier_code": ""
}'
```

---

## Schedule Pickup API

Use this API to schedule pickup for an AWB of order-shipment.

### Basic Information
- **URL:** `https://api.rapidshyp.com/rapidshyp/apis/v1/schedule_pickup`
- **Method:** POST
- **Scheme:** HTTPS
- **Headers:**
  - `Content-Type: application/json`
  - `rapidshyp-token: [API-Key]`

### Request Parameters
| Parameter | Required | Description |
|-----------|----------|-------------|
| `shipment_id` | Mandatory | Order shipment ID |
| `awb` | Optional | AWB assigned to shipment |

---

## De-allocate Shipment API

Use this API to de-allocate AWB from shipment order. You can regenerate a fresh AWB for the same shipment afterward.

### Basic Information
- **URL:** `https://api.rapidshyp.com/rapidshyp/apis/v1/de_allocate_shipment`
- **Method:** POST
- **Scheme:** HTTPS
- **Headers:**
  - `Content-Type: application/json`
  - `rapidshyp-token: [API-Key]`

### Request Parameters
| Parameter | Required | Description |
|-----------|----------|-------------|
| `orderId` | Mandatory | Valid order ID created on RapidShyp |
| `shipmentId` | Mandatory | Valid shipment ID created on RapidShyp |

---

## Cancel Order API

Use this API to cancel a created order.

### Basic Information
- **URL:** `https://api.rapidshyp.com/rapidshyp/apis/v1/cancel_order`
- **Method:** POST
- **Scheme:** HTTPS
- **Headers:**
  - `Content-Type: application/json`
  - `rapidshyp-token: [API-Key]`

### Request Parameters
| Parameter | Required | Description |
|-----------|----------|-------------|
| `orderId` | Mandatory | Valid order ID created on RapidShyp |
| `storeName` | Mandatory | Valid store name |

---

## Label PDF Generation API

Use this API to generate Label PDF.

### Basic Information
- **URL:** `https://api.rapidshyp.com/rapidshyp/apis/v1/generate_label`
- **Method:** POST
- **Scheme:** HTTPS
- **Headers:**
  - `Content-Type: application/json`
  - `rapidshyp-token: [API-Key]`

### Request Parameters
| Parameter | Required | Description |
|-----------|----------|-------------|
| `shipmentId` | Mandatory | Array of shipment IDs |

### Example Request
```bash
curl --location 'https://api.rapidshyp.com/rapidshyp/apis/v1/generate_label' \
--header 'rapidshyp-token: HQ$fS*******KHoZ' \
--header 'Content-Type: application/json' \
--data '{
  "shipmentId": ["S2407199"]
}'
```

---

## Create Pickup Location API

Use this API to create a pickup location at RapidShyp.

### Basic Information
- **URL:** `https://api.rapidshyp.com/rapidshyp/apis/v1/create/pickup_location`
- **Method:** POST
- **Scheme:** HTTPS
- **Headers:**
  - `Content-Type: application/json`
  - `rapidshyp-token: [API-Key]`

### Key Parameters
| Parameter | Required | Description | Validation |
|-----------|----------|-------------|------------|
| `address_name` | Mandatory | Warehouse/store/pickup location name | Minimum 1 character |
| `contact_name` | Mandatory | Warehouse manager name | Only alphabets allowed |
| `contact_number` | Mandatory | Contact number of POC | Should start with 7,8,9 and be 10 digits |
| `email` | Optional | Email of POC | - |
| `address_line` | Mandatory | Address line 1 | 3-100 characters |
| `pincode` | Mandatory | Location pincode | 6 digit valid pincode |
| `use_alt_rto_address` | Mandatory | Map different RTO location | Boolean value |

---

## Action NDR API

Use this API to take action on pending NDR (Non-Delivery Report).

### Basic Information
- **URL:** `https://api.rapidshyp.com/rapidshyp/apis/v1/ndr/action`
- **Method:** POST
- **Scheme:** HTTPS
- **Headers:**
  - `Content-Type: application/json`
  - `rapidshyp-token: [API-Key]`

### Request Parameters
| Parameter | Required | Description |
|-----------|----------|-------------|
| `awb` | Mandatory | AWB for which NDR has been raised |
| `action` | Mandatory | REATTEMPT or RETURN |
| `phone` | Conditionally-Mandatory | Updated phone number (for REATTEMPT) |
| `address1` | Conditionally-Mandatory | Updated address (for REATTEMPT) |
| `address2` | Conditionally-Mandatory | Updated address line 2 (for REATTEMPT) |

---

## Tracking API

Use this API to fetch AWB tracking information.

### Basic Information
- **URL:** `https://api.rapidshyp.com/rapidshyp/apis/v1/track_order`
- **Method:** POST
- **Scheme:** HTTPS
- **Headers:**
  - `Content-Type: application/json`
  - `rapidshyp-token: [API-Key]`

### Request Parameters
| Parameter | Required | Description |
|-----------|----------|-------------|
| `seller_order_id` | Conditionally-Mandatory | Order ID created on RapidShyp |
| `contact` | Optional | Contact number for verification |
| `email` | Optional | Email for verification |
| `awb` | Conditionally-Mandatory | AWB of shipment |

**Note:** Either `seller_order_id` or `awb` is required.

---

## Tracking Status Codes

| Code | Status | Description |
|---------|--------|-------------|
| SCB | Shipment Booked | - |
| PSH | Pickup Scheduled | - |
| OFP | Out for Pickup | - |
| PUE | Pick up Exception | - |
| PCN | Pickup Cancelled | - |
| PUC | Pickup Completed | - |
| SPD | Shipped/Dispatched | - |
| INT | In Transit | - |
| RAD | Reached at Destination | - |
| DED | Delivery Delayed | - |
| OFD | Out for Delivery | - |
| DEL | Delivered | - |
| UND | Undelivered | - |
| RTO_REQ | RTO Requested | - |
| RTO | RTO Confirmed | - |
| RTO_INT | RTO In Transit | - |
| RTO_RAD | RTO - Reached at Destination | - |
| RTO_OFD | RTO Out for Delivery | - |
| RTO_DEL | RTO Delivered | - |
| RTO_UND | RTO Undelivered | - |
| CAN | Shipment Cancelled | - |
| ONH | Shipment On Hold | - |
| LST | Shipment Lost | - |
| DMG | Shipment Damaged | - |
| MSR | Shipment Misrouted | - |
| DPO | Shipment Disposed-Off | - |

---

## General Notes

### Phone Number Validation
- All phone numbers must start with 6, 7, 8, or 9
- Phone numbers must be 10 digits long

### Pincode Validation
- All pincodes must be 6 digits
- Must be valid Indian pincodes

### Address Validation
- Address line 1: 3-100 characters
- Address line 2: 3-100 characters (if provided)
- Name fields: 3-75 characters combined (first + last name)

### Payment Methods
- **COD**: Cash on Delivery
- **PREPAID**: Pre-paid orders

### Common Headers
All API requests require:
```
Content-Type: application/json
rapidshyp-token: [Your-API-Key]
```