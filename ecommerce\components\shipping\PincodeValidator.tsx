/**
 * Pincode Validator Component with Rapidshyp serviceability check
 */

import React, { useEffect, useState } from 'react';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Alert, AlertDescription } from '@/components/ui/alert';
import { CheckCircle, XCircle, Loader2, MapPin } from 'lucide-react';
import { cn } from '@/lib/utils';
import { usePincodeValidation } from '@/hooks/usePincodeValidation';
import type { PincodeValidatorProps } from '@/types/shipping';

export const PincodeValidator: React.FC<PincodeValidatorProps> = ({
  value,
  onChange,
  onValidationChange,
  label = "Delivery Pincode",
  placeholder = "Enter 6-digit pincode",
  className
}) => {
  const [inputValue, setInputValue] = useState(value);
  const {
    isValid,
    isServiceable,
    isValidating,
    message,
    error,
    validatePincode,
    clearValidation
  } = usePincodeValidation();

  // Update input value when prop changes
  useEffect(() => {
    setInputValue(value);
  }, [value]);

  // Validate pincode when input changes
  useEffect(() => {
    if (inputValue !== value) {
      onChange(inputValue);
    }
    
    if (inputValue.length === 6) {
      validatePincode(inputValue);
    } else if (inputValue.length === 0) {
      clearValidation();
    }
  }, [inputValue, onChange, validatePincode, clearValidation, value]);

  // Notify parent of validation changes
  useEffect(() => {
    onValidationChange(isValid, isServiceable);
  }, [isValid, isServiceable, onValidationChange]);

  const handleInputChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const newValue = e.target.value.replace(/\D/g, '').slice(0, 6);
    setInputValue(newValue);
  };

  const getValidationIcon = () => {
    if (isValidating) {
      return <Loader2 className="h-4 w-4 animate-spin text-gray-400" />;
    }
    
    if (inputValue.length === 6) {
      if (isValid && isServiceable) {
        return <CheckCircle className="h-4 w-4 text-green-500" />;
      } else if (isValid && !isServiceable) {
        return <XCircle className="h-4 w-4 text-orange-500" />;
      } else {
        return <XCircle className="h-4 w-4 text-red-500" />;
      }
    }
    
    return <MapPin className="h-4 w-4 text-gray-400" />;
  };

  const getInputClassName = () => {
    if (inputValue.length !== 6) return '';
    
    if (isValid && isServiceable) return 'border-green-500 focus:border-green-500';
    if (isValid && !isServiceable) return 'border-orange-500 focus:border-orange-500';
    return 'border-red-500 focus:border-red-500';
  };

  const getAlertVariant = () => {
    if (error) return 'destructive';
    if (isValid && isServiceable) return 'default';
    if (isValid && !isServiceable) return 'default';
    return 'destructive';
  };

  const getAlertClassName = () => {
    if (error) return 'border-red-200 bg-red-50';
    if (isValid && isServiceable) return 'border-green-200 bg-green-50';
    if (isValid && !isServiceable) return 'border-orange-200 bg-orange-50';
    return 'border-red-200 bg-red-50';
  };

  const shouldShowAlert = () => {
    return (inputValue.length === 6 && (message || error)) || error;
  };

  return (
    <div className={cn("space-y-2", className)}>
      <Label htmlFor="pincode" className="text-sm font-medium">
        {label}
      </Label>
      
      <div className="relative">
        <Input
          id="pincode"
          type="text"
          value={inputValue}
          onChange={handleInputChange}
          placeholder={placeholder}
          className={cn(
            "pr-10 transition-colors",
            getInputClassName()
          )}
          maxLength={6}
          autoComplete="postal-code"
        />
        
        <div className="absolute right-3 top-1/2 transform -translate-y-1/2">
          {getValidationIcon()}
        </div>
      </div>
      
      {shouldShowAlert() && (
        <Alert 
          variant={getAlertVariant()}
          className={cn("py-2", getAlertClassName())}
        >
          <AlertDescription className="text-sm">
            {error || message}
          </AlertDescription>
        </Alert>
      )}
      
      {/* Helper text */}
      {inputValue.length > 0 && inputValue.length < 6 && (
        <p className="text-xs text-gray-500">
          Enter {6 - inputValue.length} more digit{6 - inputValue.length !== 1 ? 's' : ''}
        </p>
      )}
    </div>
  );
};

export default PincodeValidator;
