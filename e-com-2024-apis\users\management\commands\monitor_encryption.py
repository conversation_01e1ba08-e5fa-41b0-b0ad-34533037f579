"""
Management command to monitor encryption health and performance.
This command provides ongoing monitoring of the encryption system.
"""

import time
import logging
from datetime import datetime, timedelta
from django.core.management.base import BaseCommand
from django.db import connection
from django.utils import timezone
from users.models import Customer, Address, ContactMessage
from backend.encryption_utils import encryption_manager
from django.core.mail import send_mail
from django.conf import settings

logger = logging.getLogger(__name__)


class Command(BaseCommand):
    help = 'Monitor encryption health and performance'
    
    def add_arguments(self, parser):
        parser.add_argument(
            '--performance-threshold',
            type=float,
            default=1.0,
            help='Performance threshold in milliseconds (default: 1.0ms)',
        )
        parser.add_argument(
            '--email-alerts',
            action='store_true',
            help='Send email alerts for critical issues',
        )
        parser.add_argument(
            '--detailed',
            action='store_true',
            help='Show detailed monitoring information',
        )
        parser.add_argument(
            '--continuous',
            action='store_true',
            help='Run continuous monitoring (every 5 minutes)',
        )
    
    def handle(self, *args, **options):
        self.performance_threshold = options['performance_threshold']
        self.email_alerts = options['email_alerts']
        self.detailed = options['detailed']
        self.continuous = options['continuous']
        
        if self.continuous:
            self.stdout.write('Starting continuous encryption monitoring...')
            self.stdout.write('Press Ctrl+C to stop')
            
            try:
                while True:
                    self._run_monitoring_cycle()
                    time.sleep(300)  # 5 minutes
            except KeyboardInterrupt:
                self.stdout.write('\nMonitoring stopped by user')
        else:
            self._run_monitoring_cycle()
    
    def _run_monitoring_cycle(self):
        """Run a single monitoring cycle"""
        self.stdout.write(f'\n🔍 Encryption Health Check - {timezone.now().strftime("%Y-%m-%d %H:%M:%S")}')
        self.stdout.write('=' * 60)
        
        issues = []
        
        # Test basic encryption functionality
        encryption_issues = self._test_encryption_functionality()
        issues.extend(encryption_issues)
        
        # Test performance
        performance_issues = self._test_performance()
        issues.extend(performance_issues)
        
        # Check data integrity
        integrity_issues = self._check_data_integrity()
        issues.extend(integrity_issues)
        
        # Check database encryption status
        db_issues = self._check_database_encryption()
        issues.extend(db_issues)
        
        # Check system resources
        resource_issues = self._check_system_resources()
        issues.extend(resource_issues)
        
        # Summary
        self._print_summary(issues)
        
        # Send alerts if needed
        if issues and self.email_alerts:
            self._send_alert_email(issues)
    
    def _test_encryption_functionality(self):
        """Test basic encryption/decryption functionality"""
        self.stdout.write('🔐 Testing encryption functionality...')
        issues = []
        
        test_cases = [
            "test_phone_9876543210",
            "<EMAIL>",
            "123 Test Street",
            "Special chars: !@#$%^&*()",
            "",  # Empty string
        ]
        
        for i, test_data in enumerate(test_cases):
            try:
                encrypted = encryption_manager.encrypt(test_data)
                decrypted = encryption_manager.decrypt(encrypted)
                
                if test_data != decrypted:
                    issue = f"Encryption test {i+1} failed: data mismatch"
                    issues.append(issue)
                    self.stdout.write(self.style.ERROR(f'  ❌ {issue}'))
                elif self.detailed:
                    self.stdout.write(f'  ✅ Test case {i+1}: PASSED')
                    
            except Exception as e:
                issue = f"Encryption test {i+1} failed: {str(e)}"
                issues.append(issue)
                self.stdout.write(self.style.ERROR(f'  ❌ {issue}'))
        
        if not issues:
            self.stdout.write(self.style.SUCCESS('  ✅ All encryption tests passed'))
        
        return issues
    
    def _test_performance(self):
        """Test encryption performance"""
        self.stdout.write('⚡ Testing encryption performance...')
        issues = []
        
        test_data = "performance_test_phone_9876543210"
        iterations = 100
        
        try:
            # Test encryption performance
            start_time = time.time()
            for _ in range(iterations):
                encryption_manager.encrypt(test_data)
            encrypt_time = time.time() - start_time
            
            # Test decryption performance
            encrypted_data = encryption_manager.encrypt(test_data)
            start_time = time.time()
            for _ in range(iterations):
                encryption_manager.decrypt(encrypted_data)
            decrypt_time = time.time() - start_time
            
            avg_encrypt_time = (encrypt_time / iterations) * 1000  # Convert to ms
            avg_decrypt_time = (decrypt_time / iterations) * 1000  # Convert to ms
            
            self.stdout.write(f'  📊 Average encryption time: {avg_encrypt_time:.3f}ms')
            self.stdout.write(f'  📊 Average decryption time: {avg_decrypt_time:.3f}ms')
            
            # Check against thresholds
            if avg_encrypt_time > self.performance_threshold:
                issue = f"Encryption performance degraded: {avg_encrypt_time:.3f}ms (threshold: {self.performance_threshold}ms)"
                issues.append(issue)
                self.stdout.write(self.style.WARNING(f'  ⚠️  {issue}'))
            
            if avg_decrypt_time > self.performance_threshold:
                issue = f"Decryption performance degraded: {avg_decrypt_time:.3f}ms (threshold: {self.performance_threshold}ms)"
                issues.append(issue)
                self.stdout.write(self.style.WARNING(f'  ⚠️  {issue}'))
            
            if not issues:
                self.stdout.write(self.style.SUCCESS('  ✅ Performance tests passed'))
                
        except Exception as e:
            issue = f"Performance test failed: {str(e)}"
            issues.append(issue)
            self.stdout.write(self.style.ERROR(f'  ❌ {issue}'))
        
        return issues
    
    def _check_data_integrity(self):
        """Check data integrity for encrypted fields"""
        self.stdout.write('🔍 Checking data integrity...')
        issues = []
        
        try:
            # Check Customer data
            customers = Customer.objects.exclude(phone_number='')[:10]
            for customer in customers:
                if customer.phone_number:
                    # Basic validation - phone should be numeric after cleaning
                    clean_phone = customer.phone_number.replace('+', '').replace('-', '').replace(' ', '')
                    if not clean_phone.isdigit():
                        issue = f"Customer {customer.id}: phone_number integrity issue"
                        issues.append(issue)
                        if self.detailed:
                            self.stdout.write(self.style.WARNING(f'  ⚠️  {issue}'))
            
            # Check Address data
            addresses = Address.objects.all()[:10]
            for address in addresses:
                if address.street_address and len(address.street_address) < 3:
                    issue = f"Address {address.id}: street_address too short"
                    issues.append(issue)
                    if self.detailed:
                        self.stdout.write(self.style.WARNING(f'  ⚠️  {issue}'))
                
                if address.order_user_email and '@' not in address.order_user_email:
                    issue = f"Address {address.id}: invalid email format"
                    issues.append(issue)
                    if self.detailed:
                        self.stdout.write(self.style.WARNING(f'  ⚠️  {issue}'))
            
            # Check ContactMessage data
            contacts = ContactMessage.objects.all()[:10]
            for contact in contacts:
                if contact.email and '@' not in contact.email:
                    issue = f"ContactMessage {contact.id}: invalid email format"
                    issues.append(issue)
                    if self.detailed:
                        self.stdout.write(self.style.WARNING(f'  ⚠️  {issue}'))
            
            if not issues:
                self.stdout.write(self.style.SUCCESS('  ✅ Data integrity checks passed'))
                
        except Exception as e:
            issue = f"Data integrity check failed: {str(e)}"
            issues.append(issue)
            self.stdout.write(self.style.ERROR(f'  ❌ {issue}'))
        
        return issues
    
    def _check_database_encryption(self):
        """Check that data is actually encrypted in the database"""
        self.stdout.write('🗄️  Checking database encryption status...')
        issues = []
        
        try:
            with connection.cursor() as cursor:
                # Check for unencrypted phone numbers
                cursor.execute("""
                    SELECT COUNT(*) FROM users_customer 
                    WHERE phone_number ~ '^[0-9+\-\s]+$' 
                    AND phone_number != '' 
                    AND phone_number IS NOT NULL
                """)
                unencrypted_phones = cursor.fetchone()[0]
                
                if unencrypted_phones > 0:
                    issue = f"Found {unencrypted_phones} potentially unencrypted phone numbers"
                    issues.append(issue)
                    self.stdout.write(self.style.WARNING(f'  ⚠️  {issue}'))
                
                # Check for unencrypted addresses
                cursor.execute("""
                    SELECT COUNT(*) FROM users_address 
                    WHERE street_address NOT LIKE '%=%' 
                    AND street_address != '' 
                    AND street_address IS NOT NULL
                    AND LENGTH(street_address) < 50
                """)
                unencrypted_addresses = cursor.fetchone()[0]
                
                if unencrypted_addresses > 0:
                    issue = f"Found {unencrypted_addresses} potentially unencrypted addresses"
                    issues.append(issue)
                    self.stdout.write(self.style.WARNING(f'  ⚠️  {issue}'))
            
            if not issues:
                self.stdout.write(self.style.SUCCESS('  ✅ Database encryption status OK'))
                
        except Exception as e:
            issue = f"Database encryption check failed: {str(e)}"
            issues.append(issue)
            self.stdout.write(self.style.ERROR(f'  ❌ {issue}'))
        
        return issues
    
    def _check_system_resources(self):
        """Check system resources related to encryption"""
        self.stdout.write('💻 Checking system resources...')
        issues = []
        
        try:
            # Check if encryption key is set
            if not hasattr(settings, 'ENCRYPTION_KEY') or not settings.ENCRYPTION_KEY:
                issue = "ENCRYPTION_KEY not properly configured"
                issues.append(issue)
                self.stdout.write(self.style.ERROR(f'  ❌ {issue}'))
            
            # Check database connection
            with connection.cursor() as cursor:
                cursor.execute("SELECT 1")
                if self.detailed:
                    self.stdout.write('  ✅ Database connection OK')
            
            if not issues:
                self.stdout.write(self.style.SUCCESS('  ✅ System resources OK'))
                
        except Exception as e:
            issue = f"System resource check failed: {str(e)}"
            issues.append(issue)
            self.stdout.write(self.style.ERROR(f'  ❌ {issue}'))
        
        return issues
    
    def _print_summary(self, issues):
        """Print monitoring summary"""
        self.stdout.write('\n📋 MONITORING SUMMARY')
        self.stdout.write('-' * 30)
        
        if not issues:
            self.stdout.write(self.style.SUCCESS('🎉 All encryption systems are healthy!'))
        else:
            self.stdout.write(self.style.WARNING(f'⚠️  Found {len(issues)} issue(s):'))
            for i, issue in enumerate(issues, 1):
                self.stdout.write(f'  {i}. {issue}')
        
        self.stdout.write(f'\n⏰ Next check: {(timezone.now() + timedelta(minutes=5)).strftime("%H:%M:%S")}')
    
    def _send_alert_email(self, issues):
        """Send email alert for critical issues"""
        try:
            subject = f'Encryption Monitoring Alert - {len(issues)} Issues Found'
            message = f"""
Encryption monitoring has detected {len(issues)} issue(s):

{chr(10).join(f"• {issue}" for issue in issues)}

Time: {timezone.now().strftime("%Y-%m-%d %H:%M:%S")}
Server: {settings.ALLOWED_HOSTS[0] if settings.ALLOWED_HOSTS else 'Unknown'}

Please investigate these issues promptly.
            """
            
            send_mail(
                subject,
                message,
                settings.DEFAULT_FROM_EMAIL,
                ['<EMAIL>'],  # Replace with actual admin emails
                fail_silently=False,
            )
            
            self.stdout.write(self.style.SUCCESS('📧 Alert email sent'))
            
        except Exception as e:
            self.stdout.write(self.style.ERROR(f'📧 Failed to send alert email: {str(e)}'))
