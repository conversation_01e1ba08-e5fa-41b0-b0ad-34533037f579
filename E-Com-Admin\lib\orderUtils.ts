import { ORDER_STATUS } from "@/constant/urls";

/**
 * Get the appropriate color class for an order status badge
 * @param status - The order status
 * @returns The CSS class string for the status color
 */
export function getOrderStatusColor(status: string): string {
  switch (status.toUpperCase()) {
    case ORDER_STATUS.PENDING:
      return "bg-orange-500";
    case ORDER_STATUS.PAYMENT_FAILED:
      return "bg-red-500";
    case ORDER_STATUS.PROCESSING:
      return "bg-yellow-500";
    case ORDER_STATUS.PAID:
      return "bg-green-500";
    case ORDER_STATUS.SHIPPED:
      return "bg-blue-500";
    case ORDER_STATUS.DELIVERED:
      return "bg-emerald-500";
    case ORDER_STATUS.CANCELLED:
      return "bg-red-500";
    case ORDER_STATUS.REFUNDED:
      return "bg-purple-500";
    default:
      return "bg-gray-500";
  }
}

/**
 * Check if an order can be refunded based on its status
 * @param status - The order status
 * @returns True if the order can be refunded, false otherwise
 */
export function canRefundOrder(status: string): boolean {
  return status !== ORDER_STATUS.REFUNDED && status !== ORDER_STATUS.CANCELLED;
}

/**
 * Get a human-readable label for an order status
 * @param status - The order status
 * @returns The human-readable label
 */
export function getOrderStatusLabel(status: string): string {
  switch (status.toUpperCase()) {
    case ORDER_STATUS.PENDING:
      return "Pending";
    case ORDER_STATUS.PROCESSING:
      return "Processing";
    case ORDER_STATUS.PAID:
      return "Paid";
    case ORDER_STATUS.SHIPPED:
      return "Shipped";
    case ORDER_STATUS.DELIVERED:
      return "Delivered";
    case ORDER_STATUS.CANCELLED:
      return "Cancelled";
    case ORDER_STATUS.REFUNDED:
      return "Refunded";
    default:
      return status;
  }
}
