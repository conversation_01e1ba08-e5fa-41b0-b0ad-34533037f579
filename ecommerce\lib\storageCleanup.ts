/**
 * Storage cleanup utilities to handle corrupted data
 */

import { secureLocalStorage, secureSessionStorage } from './secureStorage';

export class StorageCleanup {
  /**
   * Clean up all corrupted storage items
   */
  static cleanupCorruptedData(): void {
    if (typeof window === 'undefined') return;

    console.log('Starting storage cleanup...');
    
    // Clean up localStorage
    this.cleanupStorage(localStorage, 'localStorage');
    
    // Clean up sessionStorage
    this.cleanupStorage(sessionStorage, 'sessionStorage');
    
    // Clean up secure storage
    try {
      secureLocalStorage.cleanupExpired();
      secureSessionStorage.cleanupExpired();
    } catch (error) {
      console.error('Error cleaning secure storage:', error);
    }
    
    console.log('Storage cleanup completed');
  }

  /**
   * Clean up a specific storage instance
   */
  private static cleanupStorage(storage: Storage, storageName: string): void {
    const keysToRemove: string[] = [];
    
    try {
      for (let i = 0; i < storage.length; i++) {
        const key = storage.key(i);
        if (!key) continue;
        
        try {
          const value = storage.getItem(key);
          if (value) {
            // Try to parse the value to check if it's valid
            JSON.parse(value);
          }
        } catch (error) {
          console.warn(`Corrupted ${storageName} item found: ${key}`, error);
          keysToRemove.push(key);
        }
      }
      
      // Remove corrupted items
      keysToRemove.forEach(key => {
        try {
          storage.removeItem(key);
          console.log(`Removed corrupted ${storageName} item: ${key}`);
        } catch (error) {
          console.error(`Failed to remove corrupted ${storageName} item: ${key}`, error);
        }
      });
      
    } catch (error) {
      console.error(`Error cleaning ${storageName}:`, error);
    }
  }

  /**
   * Reset all application storage
   */
  static resetAllStorage(): void {
    if (typeof window === 'undefined') return;

    try {
      localStorage.clear();
      sessionStorage.clear();
      console.log('All storage cleared');
    } catch (error) {
      console.error('Error clearing storage:', error);
    }
  }

  /**
   * Check if storage is working properly
   */
  static testStorage(): boolean {
    if (typeof window === 'undefined') return false;

    try {
      const testKey = '__storage_test__';
      const testValue = 'test';
      
      localStorage.setItem(testKey, testValue);
      const retrieved = localStorage.getItem(testKey);
      localStorage.removeItem(testKey);
      
      return retrieved === testValue;
    } catch (error) {
      console.error('Storage test failed:', error);
      return false;
    }
  }

  /**
   * Get storage usage information
   */
  static getStorageInfo(): { localStorage: number; sessionStorage: number } {
    if (typeof window === 'undefined') return { localStorage: 0, sessionStorage: 0 };

    const getStorageSize = (storage: Storage): number => {
      let total = 0;
      try {
        for (let i = 0; i < storage.length; i++) {
          const key = storage.key(i);
          if (key) {
            const value = storage.getItem(key);
            if (value) {
              total += key.length + value.length;
            }
          }
        }
      } catch (error) {
        console.error('Error calculating storage size:', error);
      }
      return total;
    };

    return {
      localStorage: getStorageSize(localStorage),
      sessionStorage: getStorageSize(sessionStorage)
    };
  }
}

// Auto-cleanup on module load
if (typeof window !== 'undefined') {
  // Run cleanup after a short delay to avoid blocking initial page load
  setTimeout(() => {
    StorageCleanup.cleanupCorruptedData();
  }, 1000);
}

export default StorageCleanup;
