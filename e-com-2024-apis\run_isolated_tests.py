#!/usr/bin/env python3
"""
Isolated test runner for e-commerce API.
This script ensures tests run with proper database isolation using SQLite3.
"""

import os
import sys
import subprocess
import django
from django.core.management import execute_from_command_line

def setup_test_environment():
    """Setup the test environment with proper isolation."""
    
    # Force test settings
    os.environ['DJANGO_SETTINGS_MODULE'] = 'tests.test_settings'
    
    # Setup Django
    django.setup()
    
    from django.conf import settings
    
    print("=== Test Environment Setup ===")
    print(f"Settings module: {settings.SETTINGS_MODULE}")
    print(f"Database engine: {settings.DATABASES['default']['ENGINE']}")
    print(f"Database name: {settings.DATABASES['default']['NAME']}")
    
    # Verify we're using SQLite
    if 'sqlite3' not in settings.DATABASES['default']['ENGINE']:
        print("❌ ERROR: Not using SQLite3 for tests!")
        return False
    
    print("✅ Using SQLite3 for tests")
    return True

def run_migrations():
    """Run migrations for the test database."""
    print("\n=== Running Test Database Migrations ===")
    
    try:
        # Run makemigrations first
        print("Running makemigrations...")
        execute_from_command_line(['manage.py', 'makemigrations'])
        
        # Run migrate
        print("Running migrate...")
        execute_from_command_line(['manage.py', 'migrate'])
        
        print("✅ Migrations completed successfully")
        return True
        
    except Exception as e:
        print(f"❌ Error running migrations: {e}")
        return False

def run_tests(test_args=None):
    """Run the test suite with proper isolation."""
    print("\n=== Running Test Suite ===")
    
    # Default test arguments
    if test_args is None:
        test_args = [
            '--cov=.',
            '--cov-report=term',
            '--tb=short',
            '-v',
            '--disable-warnings'
        ]
    
    # Build pytest command
    cmd = ['python', '-m', 'pytest'] + test_args
    
    print(f"Running command: {' '.join(cmd)}")
    
    try:
        # Set environment variables
        env = os.environ.copy()
        env['DJANGO_SETTINGS_MODULE'] = 'tests.test_settings'
        
        # Run pytest
        result = subprocess.run(cmd, env=env, cwd=os.getcwd())
        
        if result.returncode == 0:
            print("✅ All tests passed!")
            return True
        else:
            print(f"❌ Tests failed with return code: {result.returncode}")
            return False
            
    except Exception as e:
        print(f"❌ Error running tests: {e}")
        return False

def verify_test_isolation():
    """Verify that tests are properly isolated from production."""
    print("\n=== Verifying Test Isolation ===")
    
    try:
        from django.conf import settings
        
        # Check database settings
        db_engine = settings.DATABASES['default']['ENGINE']
        db_name = settings.DATABASES['default']['NAME']
        
        if 'sqlite3' not in db_engine:
            print("❌ Not using SQLite3!")
            return False
        
        if db_name not in [':memory:', 'test_db.sqlite3']:
            print(f"❌ Unexpected database name: {db_name}")
            return False
        
        # Check other settings
        if settings.EMAIL_BACKEND != 'django.core.mail.backends.console.EmailBackend':
            print("❌ Email backend not set to console")
            return False
        
        if getattr(settings, 'USE_MINIO', True):
            print("❌ MinIO should be disabled for tests")
            return False
        
        print("✅ Test isolation verified")
        return True
        
    except Exception as e:
        print(f"❌ Error verifying isolation: {e}")
        return False

def main():
    """Main function to run isolated tests."""
    print("E-commerce API Isolated Test Runner")
    print("=" * 50)
    
    # Parse command line arguments
    test_args = sys.argv[1:] if len(sys.argv) > 1 else None
    
    # Setup test environment
    if not setup_test_environment():
        sys.exit(1)
    
    # Verify test isolation
    if not verify_test_isolation():
        sys.exit(1)
    
    # Run migrations
    if not run_migrations():
        sys.exit(1)
    
    # Run tests
    if not run_tests(test_args):
        sys.exit(1)
    
    print("\n" + "=" * 50)
    print("✅ All tests completed successfully with proper isolation!")

if __name__ == '__main__':
    main()
